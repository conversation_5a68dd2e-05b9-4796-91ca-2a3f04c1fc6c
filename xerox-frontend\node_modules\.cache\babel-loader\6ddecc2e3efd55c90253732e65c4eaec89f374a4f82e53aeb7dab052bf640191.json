{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\contexts\\\\ThemeContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext(undefined);\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s();\n  const [isDarkMode, setIsDarkMode] = useState(() => {\n    // Check localStorage first\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme === 'dark';\n    }\n\n    // Check system preference\n    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n  const toggleTheme = () => {\n    setIsDarkMode(prev => {\n      const newTheme = !prev;\n      localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n      return newTheme;\n    });\n  };\n  useEffect(() => {\n    // Apply theme to document\n    const root = document.documentElement;\n    if (isDarkMode) {\n      root.classList.add('dark-theme');\n      root.classList.remove('light-theme');\n      document.body.setAttribute('data-bs-theme', 'dark');\n    } else {\n      root.classList.add('light-theme');\n      root.classList.remove('dark-theme');\n      document.body.setAttribute('data-bs-theme', 'light');\n    }\n\n    // Update CSS custom properties - Professional Theme\n    root.style.setProperty('--primary-color', isDarkMode ? '#10b981' : '#059669');\n    root.style.setProperty('--secondary-color', isDarkMode ? '#6b7280' : '#4b5563');\n    root.style.setProperty('--accent-color', isDarkMode ? '#f59e0b' : '#d97706');\n    root.style.setProperty('--danger-color', isDarkMode ? '#ef4444' : '#dc2626');\n    root.style.setProperty('--warning-color', isDarkMode ? '#f59e0b' : '#d97706');\n    root.style.setProperty('--success-color', isDarkMode ? '#10b981' : '#059669');\n    root.style.setProperty('--info-color', isDarkMode ? '#6b7280' : '#4b5563');\n    root.style.setProperty('--background-color', isDarkMode ? '#0f172a' : '#ffffff');\n    root.style.setProperty('--surface-color', isDarkMode ? '#1e293b' : '#f8fafc');\n    root.style.setProperty('--surface-hover', isDarkMode ? '#334155' : '#f1f5f9');\n    root.style.setProperty('--card-background', isDarkMode ? '#1e293b' : '#ffffff');\n    root.style.setProperty('--sidebar-background', isDarkMode ? '#0f172a' : '#f8fafc');\n    root.style.setProperty('--text-color', isDarkMode ? '#f8fafc' : '#0f172a');\n    root.style.setProperty('--text-muted', isDarkMode ? '#94a3b8' : '#64748b');\n    root.style.setProperty('--text-secondary', isDarkMode ? '#cbd5e1' : '#475569');\n    root.style.setProperty('--border-color', isDarkMode ? '#334155' : '#e2e8f0');\n    root.style.setProperty('--border-light', isDarkMode ? '#475569' : '#cbd5e1');\n    root.style.setProperty('--shadow-color', isDarkMode ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.1)');\n    root.style.setProperty('--shadow-light', isDarkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)');\n\n    // Professional gradients\n    root.style.setProperty('--gradient-primary', isDarkMode ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)' : 'linear-gradient(135deg, #059669 0%, #047857 100%)');\n    root.style.setProperty('--gradient-secondary', isDarkMode ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)' : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)');\n    root.style.setProperty('--gradient-accent', isDarkMode ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' : 'linear-gradient(135deg, #d97706 0%, #b45309 100%)');\n\n    // Glass effect\n    root.style.setProperty('--glass-background', isDarkMode ? 'rgba(45, 45, 45, 0.8)' : 'rgba(255, 255, 255, 0.8)');\n    root.style.setProperty('--glass-border', isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)');\n  }, [isDarkMode]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = e => {\n      // Only update if user hasn't manually set a preference\n      if (!localStorage.getItem('theme')) {\n        setIsDarkMode(e.matches);\n      }\n    };\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n  const value = {\n    isDarkMode,\n    toggleTheme,\n    theme: isDarkMode ? 'dark' : 'light'\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeProvider, \"4ZisE2pwoZcy0QJKQLT1xdygCdg=\");\n_c = ThemeProvider;\nexport const useTheme = () => {\n  _s2();\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s2(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "undefined", "ThemeProvider", "children", "_s", "isDarkMode", "setIsDarkMode", "savedTheme", "localStorage", "getItem", "window", "matchMedia", "matches", "toggleTheme", "prev", "newTheme", "setItem", "root", "document", "documentElement", "classList", "add", "remove", "body", "setAttribute", "style", "setProperty", "mediaQuery", "handleChange", "e", "addEventListener", "removeEventListener", "value", "theme", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useTheme", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/contexts/ThemeContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\ninterface ThemeContextType {\n  isDarkMode: boolean;\n  toggleTheme: () => void;\n  theme: 'light' | 'dark';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\ninterface ThemeProviderProps {\n  children: ReactNode;\n}\n\nexport const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {\n  const [isDarkMode, setIsDarkMode] = useState<boolean>(() => {\n    // Check localStorage first\n    const savedTheme = localStorage.getItem('theme');\n    if (savedTheme) {\n      return savedTheme === 'dark';\n    }\n    \n    // Check system preference\n    return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;\n  });\n\n  const toggleTheme = () => {\n    setIsDarkMode(prev => {\n      const newTheme = !prev;\n      localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n      return newTheme;\n    });\n  };\n\n  useEffect(() => {\n    // Apply theme to document\n    const root = document.documentElement;\n    \n    if (isDarkMode) {\n      root.classList.add('dark-theme');\n      root.classList.remove('light-theme');\n      document.body.setAttribute('data-bs-theme', 'dark');\n    } else {\n      root.classList.add('light-theme');\n      root.classList.remove('dark-theme');\n      document.body.setAttribute('data-bs-theme', 'light');\n    }\n\n    // Update CSS custom properties - Professional Theme\n    root.style.setProperty('--primary-color', isDarkMode ? '#10b981' : '#059669');\n    root.style.setProperty('--secondary-color', isDarkMode ? '#6b7280' : '#4b5563');\n    root.style.setProperty('--accent-color', isDarkMode ? '#f59e0b' : '#d97706');\n    root.style.setProperty('--danger-color', isDarkMode ? '#ef4444' : '#dc2626');\n    root.style.setProperty('--warning-color', isDarkMode ? '#f59e0b' : '#d97706');\n    root.style.setProperty('--success-color', isDarkMode ? '#10b981' : '#059669');\n    root.style.setProperty('--info-color', isDarkMode ? '#6b7280' : '#4b5563');\n\n    root.style.setProperty('--background-color', isDarkMode ? '#0f172a' : '#ffffff');\n    root.style.setProperty('--surface-color', isDarkMode ? '#1e293b' : '#f8fafc');\n    root.style.setProperty('--surface-hover', isDarkMode ? '#334155' : '#f1f5f9');\n    root.style.setProperty('--card-background', isDarkMode ? '#1e293b' : '#ffffff');\n    root.style.setProperty('--sidebar-background', isDarkMode ? '#0f172a' : '#f8fafc');\n\n    root.style.setProperty('--text-color', isDarkMode ? '#f8fafc' : '#0f172a');\n    root.style.setProperty('--text-muted', isDarkMode ? '#94a3b8' : '#64748b');\n    root.style.setProperty('--text-secondary', isDarkMode ? '#cbd5e1' : '#475569');\n\n    root.style.setProperty('--border-color', isDarkMode ? '#334155' : '#e2e8f0');\n    root.style.setProperty('--border-light', isDarkMode ? '#475569' : '#cbd5e1');\n    root.style.setProperty('--shadow-color', isDarkMode ? 'rgba(0,0,0,0.7)' : 'rgba(0,0,0,0.1)');\n    root.style.setProperty('--shadow-light', isDarkMode ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.05)');\n\n    // Professional gradients\n    root.style.setProperty('--gradient-primary', isDarkMode\n      ? 'linear-gradient(135deg, #10b981 0%, #059669 100%)'\n      : 'linear-gradient(135deg, #059669 0%, #047857 100%)'\n    );\n\n    root.style.setProperty('--gradient-secondary', isDarkMode\n      ? 'linear-gradient(135deg, #1e293b 0%, #334155 100%)'\n      : 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'\n    );\n\n    root.style.setProperty('--gradient-accent', isDarkMode\n      ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'\n      : 'linear-gradient(135deg, #d97706 0%, #b45309 100%)'\n    );\n\n    // Glass effect\n    root.style.setProperty('--glass-background', isDarkMode\n      ? 'rgba(45, 45, 45, 0.8)'\n      : 'rgba(255, 255, 255, 0.8)'\n    );\n\n    root.style.setProperty('--glass-border', isDarkMode\n      ? 'rgba(255, 255, 255, 0.1)'\n      : 'rgba(0, 0, 0, 0.1)'\n    );\n\n  }, [isDarkMode]);\n\n  // Listen for system theme changes\n  useEffect(() => {\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = (e: MediaQueryListEvent) => {\n      // Only update if user hasn't manually set a preference\n      if (!localStorage.getItem('theme')) {\n        setIsDarkMode(e.matches);\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, []);\n\n  const value: ThemeContextType = {\n    isDarkMode,\n    toggleTheme,\n    theme: isDarkMode ? 'dark' : 'light'\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n\nexport const useTheme = (): ThemeContextType => {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQzF,MAAMC,YAAY,gBAAGN,aAAa,CAA+BO,SAAS,CAAC;AAM3E,OAAO,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC3E,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAU,MAAM;IAC1D;IACA,MAAMW,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,IAAIF,UAAU,EAAE;MACd,OAAOA,UAAU,KAAK,MAAM;IAC9B;;IAEA;IACA,OAAOG,MAAM,CAACC,UAAU,IAAID,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;EACvF,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxBP,aAAa,CAACQ,IAAI,IAAI;MACpB,MAAMC,QAAQ,GAAG,CAACD,IAAI;MACtBN,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAED,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;MAC1D,OAAOA,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;EAEDlB,SAAS,CAAC,MAAM;IACd;IACA,MAAMoB,IAAI,GAAGC,QAAQ,CAACC,eAAe;IAErC,IAAId,UAAU,EAAE;MACdY,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;MAChCJ,IAAI,CAACG,SAAS,CAACE,MAAM,CAAC,aAAa,CAAC;MACpCJ,QAAQ,CAACK,IAAI,CAACC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC;IACrD,CAAC,MAAM;MACLP,IAAI,CAACG,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;MACjCJ,IAAI,CAACG,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;MACnCJ,QAAQ,CAACK,IAAI,CAACC,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;IACtD;;IAEA;IACAP,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC7EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC/EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC5EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC5EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC7EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC7EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,cAAc,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAE1EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAChFY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC7EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC7EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC/EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAElFY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,cAAc,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC1EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,cAAc,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC1EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAE9EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC5EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;IAC5EY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;IAC5FY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAAG,iBAAiB,GAAG,kBAAkB,CAAC;;IAE7F;IACAY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAErB,UAAU,GACnD,mDAAmD,GACnD,mDACJ,CAAC;IAEDY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAErB,UAAU,GACrD,mDAAmD,GACnD,mDACJ,CAAC;IAEDY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAErB,UAAU,GAClD,mDAAmD,GACnD,mDACJ,CAAC;;IAED;IACAY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAErB,UAAU,GACnD,uBAAuB,GACvB,0BACJ,CAAC;IAEDY,IAAI,CAACQ,KAAK,CAACC,WAAW,CAAC,gBAAgB,EAAErB,UAAU,GAC/C,0BAA0B,GAC1B,oBACJ,CAAC;EAEH,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;;EAEhB;EACAR,SAAS,CAAC,MAAM;IACd,MAAM8B,UAAU,GAAGjB,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC;IAEpE,MAAMiB,YAAY,GAAIC,CAAsB,IAAK;MAC/C;MACA,IAAI,CAACrB,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClCH,aAAa,CAACuB,CAAC,CAACjB,OAAO,CAAC;MAC1B;IACF,CAAC;IAEDe,UAAU,CAACG,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACnD,OAAO,MAAMD,UAAU,CAACI,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACrE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,KAAuB,GAAG;IAC9B3B,UAAU;IACVQ,WAAW;IACXoB,KAAK,EAAE5B,UAAU,GAAG,MAAM,GAAG;EAC/B,CAAC;EAED,oBACEN,OAAA,CAACC,YAAY,CAACkC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAA7B,QAAA,EACjCA;EAAQ;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAAClC,EAAA,CAjHWF,aAA2C;AAAAqC,EAAA,GAA3CrC,aAA2C;AAmHxD,OAAO,MAAMsC,QAAQ,GAAGA,CAAA,KAAwB;EAAAC,GAAA;EAC9C,MAAMC,OAAO,GAAG/C,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI0C,OAAO,KAAKzC,SAAS,EAAE;IACzB,MAAM,IAAI0C,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}