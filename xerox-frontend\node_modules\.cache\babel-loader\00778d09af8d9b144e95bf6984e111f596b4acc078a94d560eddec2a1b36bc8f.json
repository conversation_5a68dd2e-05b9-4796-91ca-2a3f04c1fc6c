{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n  constructor(visualElement) {\n    this.openDragLock = null;\n    this.isDragging = false;\n    this.currentDirection = null;\n    this.originPoint = {\n      x: 0,\n      y: 0\n    };\n    /**\n     * The permitted boundaries of travel, in pixels.\n     */\n    this.constraints = false;\n    this.hasMutatedConstraints = false;\n    /**\n     * The per-axis resolved elastic values.\n     */\n    this.elastic = createBox();\n    this.visualElement = visualElement;\n  }\n  start(originEvent) {\n    let {\n      snapToCursor = false\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    /**\n     * Don't start dragging if this component is exiting\n     */\n    const {\n      presenceContext\n    } = this.visualElement;\n    if (presenceContext && presenceContext.isPresent === false) return;\n    const onSessionStart = event => {\n      const {\n        dragSnapToOrigin\n      } = this.getProps();\n      // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n      // the component.\n      dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n      if (snapToCursor) {\n        this.snapToCursor(extractEventInfo(event).point);\n      }\n    };\n    const onStart = (event, info) => {\n      // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n      const {\n        drag,\n        dragPropagation,\n        onDragStart\n      } = this.getProps();\n      if (drag && !dragPropagation) {\n        if (this.openDragLock) this.openDragLock();\n        this.openDragLock = setDragLock(drag);\n        // If we don 't have the lock, don't start dragging\n        if (!this.openDragLock) return;\n      }\n      this.isDragging = true;\n      this.currentDirection = null;\n      this.resolveConstraints();\n      if (this.visualElement.projection) {\n        this.visualElement.projection.isAnimationBlocked = true;\n        this.visualElement.projection.target = undefined;\n      }\n      /**\n       * Record gesture origin\n       */\n      eachAxis(axis => {\n        let current = this.getAxisMotionValue(axis).get() || 0;\n        /**\n         * If the MotionValue is a percentage value convert to px\n         */\n        if (percent.test(current)) {\n          const {\n            projection\n          } = this.visualElement;\n          if (projection && projection.layout) {\n            const measuredAxis = projection.layout.layoutBox[axis];\n            if (measuredAxis) {\n              const length = calcLength(measuredAxis);\n              current = length * (parseFloat(current) / 100);\n            }\n          }\n        }\n        this.originPoint[axis] = current;\n      });\n      // Fire onDragStart event\n      if (onDragStart) {\n        frame.postRender(() => onDragStart(event, info));\n      }\n      addValueToWillChange(this.visualElement, \"transform\");\n      const {\n        animationState\n      } = this.visualElement;\n      animationState && animationState.setActive(\"whileDrag\", true);\n    };\n    const onMove = (event, info) => {\n      // latestPointerEvent = event\n      const {\n        dragPropagation,\n        dragDirectionLock,\n        onDirectionLock,\n        onDrag\n      } = this.getProps();\n      // If we didn't successfully receive the gesture lock, early return.\n      if (!dragPropagation && !this.openDragLock) return;\n      const {\n        offset\n      } = info;\n      // Attempt to detect drag direction if directionLock is true\n      if (dragDirectionLock && this.currentDirection === null) {\n        this.currentDirection = getCurrentDirection(offset);\n        // If we've successfully set a direction, notify listener\n        if (this.currentDirection !== null) {\n          onDirectionLock && onDirectionLock(this.currentDirection);\n        }\n        return;\n      }\n      // Update each point with the latest position\n      this.updateAxis(\"x\", info.point, offset);\n      this.updateAxis(\"y\", info.point, offset);\n      /**\n       * Ideally we would leave the renderer to fire naturally at the end of\n       * this frame but if the element is about to change layout as the result\n       * of a re-render we want to ensure the browser can read the latest\n       * bounding box to ensure the pointer and element don't fall out of sync.\n       */\n      this.visualElement.render();\n      /**\n       * This must fire after the render call as it might trigger a state\n       * change which itself might trigger a layout update.\n       */\n      onDrag && onDrag(event, info);\n    };\n    const onSessionEnd = (event, info) => this.stop(event, info);\n    const resumeAnimation = () => eachAxis(axis => {\n      var _this$getAxisMotionVa;\n      return this.getAnimationState(axis) === \"paused\" && ((_this$getAxisMotionVa = this.getAxisMotionValue(axis).animation) === null || _this$getAxisMotionVa === void 0 ? void 0 : _this$getAxisMotionVa.play());\n    });\n    const {\n      dragSnapToOrigin\n    } = this.getProps();\n    this.panSession = new PanSession(originEvent, {\n      onSessionStart,\n      onStart,\n      onMove,\n      onSessionEnd,\n      resumeAnimation\n    }, {\n      transformPagePoint: this.visualElement.getTransformPagePoint(),\n      dragSnapToOrigin,\n      contextWindow: getContextWindow(this.visualElement)\n    });\n  }\n  stop(event, info) {\n    const isDragging = this.isDragging;\n    this.cancel();\n    if (!isDragging) return;\n    const {\n      velocity\n    } = info;\n    this.startAnimation(velocity);\n    const {\n      onDragEnd\n    } = this.getProps();\n    if (onDragEnd) {\n      frame.postRender(() => onDragEnd(event, info));\n    }\n  }\n  cancel() {\n    this.isDragging = false;\n    const {\n      projection,\n      animationState\n    } = this.visualElement;\n    if (projection) {\n      projection.isAnimationBlocked = false;\n    }\n    this.panSession && this.panSession.end();\n    this.panSession = undefined;\n    const {\n      dragPropagation\n    } = this.getProps();\n    if (!dragPropagation && this.openDragLock) {\n      this.openDragLock();\n      this.openDragLock = null;\n    }\n    animationState && animationState.setActive(\"whileDrag\", false);\n  }\n  updateAxis(axis, _point, offset) {\n    const {\n      drag\n    } = this.getProps();\n    // If we're not dragging this axis, do an early return.\n    if (!offset || !shouldDrag(axis, drag, this.currentDirection)) return;\n    const axisValue = this.getAxisMotionValue(axis);\n    let next = this.originPoint[axis] + offset[axis];\n    // Apply constraints\n    if (this.constraints && this.constraints[axis]) {\n      next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n    }\n    axisValue.set(next);\n  }\n  resolveConstraints() {\n    var _this$visualElement$p;\n    const {\n      dragConstraints,\n      dragElastic\n    } = this.getProps();\n    const layout = this.visualElement.projection && !this.visualElement.projection.layout ? this.visualElement.projection.measure(false) : (_this$visualElement$p = this.visualElement.projection) === null || _this$visualElement$p === void 0 ? void 0 : _this$visualElement$p.layout;\n    const prevConstraints = this.constraints;\n    if (dragConstraints && isRefObject(dragConstraints)) {\n      if (!this.constraints) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    } else {\n      if (dragConstraints && layout) {\n        this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n      } else {\n        this.constraints = false;\n      }\n    }\n    this.elastic = resolveDragElastic(dragElastic);\n    /**\n     * If we're outputting to external MotionValues, we want to rebase the measured constraints\n     * from viewport-relative to component-relative.\n     */\n    if (prevConstraints !== this.constraints && layout && this.constraints && !this.hasMutatedConstraints) {\n      eachAxis(axis => {\n        if (this.constraints !== false && this.getAxisMotionValue(axis)) {\n          this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n        }\n      });\n    }\n  }\n  resolveRefConstraints() {\n    const {\n      dragConstraints: constraints,\n      onMeasureDragConstraints\n    } = this.getProps();\n    if (!constraints || !isRefObject(constraints)) return false;\n    const constraintsElement = constraints.current;\n    invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n    const {\n      projection\n    } = this.visualElement;\n    // TODO\n    if (!projection || !projection.layout) return false;\n    const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n    let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n    /**\n     * If there's an onMeasureDragConstraints listener we call it and\n     * if different constraints are returned, set constraints to that\n     */\n    if (onMeasureDragConstraints) {\n      const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n      this.hasMutatedConstraints = !!userConstraints;\n      if (userConstraints) {\n        measuredConstraints = convertBoundingBoxToBox(userConstraints);\n      }\n    }\n    return measuredConstraints;\n  }\n  startAnimation(velocity) {\n    const {\n      drag,\n      dragMomentum,\n      dragElastic,\n      dragTransition,\n      dragSnapToOrigin,\n      onDragTransitionEnd\n    } = this.getProps();\n    const constraints = this.constraints || {};\n    const momentumAnimations = eachAxis(axis => {\n      if (!shouldDrag(axis, drag, this.currentDirection)) {\n        return;\n      }\n      let transition = constraints && constraints[axis] || {};\n      if (dragSnapToOrigin) transition = {\n        min: 0,\n        max: 0\n      };\n      /**\n       * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n       * of spring animations so we should look into adding a disable spring option to `inertia`.\n       * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n       * using the value of `dragElastic`.\n       */\n      const bounceStiffness = dragElastic ? 200 : 1000000;\n      const bounceDamping = dragElastic ? 40 : 10000000;\n      const inertia = _objectSpread(_objectSpread({\n        type: \"inertia\",\n        velocity: dragMomentum ? velocity[axis] : 0,\n        bounceStiffness,\n        bounceDamping,\n        timeConstant: 750,\n        restDelta: 1,\n        restSpeed: 10\n      }, dragTransition), transition);\n      // If we're not animating on an externally-provided `MotionValue` we can use the\n      // component's animation controls which will handle interactions with whileHover (etc),\n      // otherwise we just have to animate the `MotionValue` itself.\n      return this.startAxisValueAnimation(axis, inertia);\n    });\n    // Run all animations and then resolve the new drag constraints.\n    return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n  }\n  startAxisValueAnimation(axis, transition) {\n    const axisValue = this.getAxisMotionValue(axis);\n    addValueToWillChange(this.visualElement, axis);\n    return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n  }\n  stopAnimation() {\n    eachAxis(axis => this.getAxisMotionValue(axis).stop());\n  }\n  pauseAnimation() {\n    eachAxis(axis => {\n      var _this$getAxisMotionVa2;\n      return (_this$getAxisMotionVa2 = this.getAxisMotionValue(axis).animation) === null || _this$getAxisMotionVa2 === void 0 ? void 0 : _this$getAxisMotionVa2.pause();\n    });\n  }\n  getAnimationState(axis) {\n    var _this$getAxisMotionVa3;\n    return (_this$getAxisMotionVa3 = this.getAxisMotionValue(axis).animation) === null || _this$getAxisMotionVa3 === void 0 ? void 0 : _this$getAxisMotionVa3.state;\n  }\n  /**\n   * Drag works differently depending on which props are provided.\n   *\n   * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n   * - Otherwise, we apply the delta to the x/y motion values.\n   */\n  getAxisMotionValue(axis) {\n    const dragKey = \"_drag\".concat(axis.toUpperCase());\n    const props = this.visualElement.getProps();\n    const externalMotionValue = props[dragKey];\n    return externalMotionValue ? externalMotionValue : this.visualElement.getValue(axis, (props.initial ? props.initial[axis] : undefined) || 0);\n  }\n  snapToCursor(point) {\n    eachAxis(axis => {\n      const {\n        drag\n      } = this.getProps();\n      // If we're not dragging this axis, do an early return.\n      if (!shouldDrag(axis, drag, this.currentDirection)) return;\n      const {\n        projection\n      } = this.visualElement;\n      const axisValue = this.getAxisMotionValue(axis);\n      if (projection && projection.layout) {\n        const {\n          min,\n          max\n        } = projection.layout.layoutBox[axis];\n        axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n      }\n    });\n  }\n  /**\n   * When the viewport resizes we want to check if the measured constraints\n   * have changed and, if so, reposition the element within those new constraints\n   * relative to where it was before the resize.\n   */\n  scalePositionWithinConstraints() {\n    if (!this.visualElement.current) return;\n    const {\n      drag,\n      dragConstraints\n    } = this.getProps();\n    const {\n      projection\n    } = this.visualElement;\n    if (!isRefObject(dragConstraints) || !projection || !this.constraints) return;\n    /**\n     * Stop current animations as there can be visual glitching if we try to do\n     * this mid-animation\n     */\n    this.stopAnimation();\n    /**\n     * Record the relative position of the dragged element relative to the\n     * constraints box and save as a progress value.\n     */\n    const boxProgress = {\n      x: 0,\n      y: 0\n    };\n    eachAxis(axis => {\n      const axisValue = this.getAxisMotionValue(axis);\n      if (axisValue && this.constraints !== false) {\n        const latest = axisValue.get();\n        boxProgress[axis] = calcOrigin({\n          min: latest,\n          max: latest\n        }, this.constraints[axis]);\n      }\n    });\n    /**\n     * Update the layout of this element and resolve the latest drag constraints\n     */\n    const {\n      transformTemplate\n    } = this.visualElement.getProps();\n    this.visualElement.current.style.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n    projection.root && projection.root.updateScroll();\n    projection.updateLayout();\n    this.resolveConstraints();\n    /**\n     * For each axis, calculate the current progress of the layout axis\n     * within the new constraints.\n     */\n    eachAxis(axis => {\n      if (!shouldDrag(axis, drag, null)) return;\n      /**\n       * Calculate a new transform based on the previous box progress\n       */\n      const axisValue = this.getAxisMotionValue(axis);\n      const {\n        min,\n        max\n      } = this.constraints[axis];\n      axisValue.set(mixNumber(min, max, boxProgress[axis]));\n    });\n  }\n  addListeners() {\n    if (!this.visualElement.current) return;\n    elementDragControls.set(this.visualElement, this);\n    const element = this.visualElement.current;\n    /**\n     * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n     */\n    const stopPointerListener = addPointerEvent(element, \"pointerdown\", event => {\n      const {\n        drag,\n        dragListener = true\n      } = this.getProps();\n      drag && dragListener && this.start(event);\n    });\n    const measureDragConstraints = () => {\n      const {\n        dragConstraints\n      } = this.getProps();\n      if (isRefObject(dragConstraints) && dragConstraints.current) {\n        this.constraints = this.resolveRefConstraints();\n      }\n    };\n    const {\n      projection\n    } = this.visualElement;\n    const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n    if (projection && !projection.layout) {\n      projection.root && projection.root.updateScroll();\n      projection.updateLayout();\n    }\n    frame.read(measureDragConstraints);\n    /**\n     * Attach a window resize listener to scale the draggable target within its defined\n     * constraints as the window resizes.\n     */\n    const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n    /**\n     * If the element's layout changes, calculate the delta and apply that to\n     * the drag gesture's origin point.\n     */\n    const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", _ref => {\n      let {\n        delta,\n        hasLayoutChanged\n      } = _ref;\n      if (this.isDragging && hasLayoutChanged) {\n        eachAxis(axis => {\n          const motionValue = this.getAxisMotionValue(axis);\n          if (!motionValue) return;\n          this.originPoint[axis] += delta[axis].translate;\n          motionValue.set(motionValue.get() + delta[axis].translate);\n        });\n        this.visualElement.render();\n      }\n    });\n    return () => {\n      stopResizeListener();\n      stopPointerListener();\n      stopMeasureLayoutListener();\n      stopLayoutUpdateListener && stopLayoutUpdateListener();\n    };\n  }\n  getProps() {\n    const props = this.visualElement.getProps();\n    const {\n      drag = false,\n      dragDirectionLock = false,\n      dragPropagation = false,\n      dragConstraints = false,\n      dragElastic = defaultElastic,\n      dragMomentum = true\n    } = props;\n    return _objectSpread(_objectSpread({}, props), {}, {\n      drag,\n      dragDirectionLock,\n      dragPropagation,\n      dragConstraints,\n      dragElastic,\n      dragMomentum\n    });\n  }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n  return (drag === true || drag === direction) && (currentDirection === null || currentDirection === direction);\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset) {\n  let lockThreshold = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 10;\n  let direction = null;\n  if (Math.abs(offset.y) > lockThreshold) {\n    direction = \"y\";\n  } else if (Math.abs(offset.x) > lockThreshold) {\n    direction = \"x\";\n  }\n  return direction;\n}\nexport { VisualElementDragControls, elementDragControls };", "map": {"version": 3, "names": ["frame", "mixNumber", "setDragLock", "percent", "invariant", "animateMotionValue", "addDomEvent", "addPointerEvent", "extractEventInfo", "convertBoxToBoundingBox", "convertBoundingBoxToBox", "calcLength", "createBox", "eachAxis", "measurePageBox", "getContextWindow", "isRefObject", "addValueToWillChange", "PanSession", "applyConstraints", "calcRelativeConstraints", "resolveDragElastic", "rebaseAxisConstraints", "calcViewportConstraints", "calcOrigin", "defaultElastic", "elementDragControls", "WeakMap", "VisualElementDragControls", "constructor", "visualElement", "openDragLock", "isDragging", "currentDirection", "originPoint", "x", "y", "constraints", "hasMutatedConstraints", "elastic", "start", "originEvent", "snapToCursor", "arguments", "length", "undefined", "presenceContext", "isPresent", "onSessionStart", "event", "dragSnapToO<PERSON>in", "getProps", "pauseAnimation", "stopAnimation", "point", "onStart", "info", "drag", "dragPropagation", "onDragStart", "resolveConstraints", "projection", "isAnimationBlocked", "target", "axis", "current", "getAxisMotionValue", "get", "test", "layout", "measuredAxis", "layoutBox", "parseFloat", "postRender", "animationState", "setActive", "onMove", "dragDirectionLock", "onDirectionLock", "onDrag", "offset", "getCurrentDirection", "updateAxis", "render", "onSessionEnd", "stop", "resumeAnimation", "_this$getAxisMotionVa", "getAnimationState", "animation", "play", "panSession", "transformPagePoint", "getTransformPagePoint", "contextWindow", "cancel", "velocity", "startAnimation", "onDragEnd", "end", "_point", "shouldDrag", "axisValue", "next", "set", "_this$visualElement$p", "dragConstraints", "dragElastic", "measure", "prevConstraints", "resolveRefConstraints", "onMeasureDragConstraints", "constraintsElement", "constraintsBox", "root", "measuredConstraints", "userConstraints", "dragMomentum", "dragTransition", "onDragTransitionEnd", "momentumAnimations", "transition", "min", "max", "bounceStiffness", "bounceDamping", "inertia", "_objectSpread", "type", "timeConstant", "restDelta", "restSpeed", "startAxisValueAnimation", "Promise", "all", "then", "_this$getAxisMotionVa2", "pause", "_this$getAxisMotionVa3", "state", "drag<PERSON>ey", "concat", "toUpperCase", "props", "externalMotionValue", "getValue", "initial", "scalePositionWithinConstraints", "boxProgress", "latest", "transformTemplate", "style", "transform", "updateScroll", "updateLayout", "addListeners", "element", "stopPointerListener", "dragListener", "measureDragConstraints", "stopMeasureLayoutListener", "addEventListener", "read", "stopResizeListener", "window", "stopLayoutUpdateListener", "_ref", "delta", "hasLayoutChanged", "motionValue", "translate", "direction", "lockThreshold", "Math", "abs"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs"], "sourcesContent": ["import { frame, mixNumber, setDragLock, percent } from 'motion-dom';\nimport { invariant } from 'motion-utils';\nimport { animateMotionValue } from '../../animation/interfaces/motion-value.mjs';\nimport { addDomEvent } from '../../events/add-dom-event.mjs';\nimport { addPointerEvent } from '../../events/add-pointer-event.mjs';\nimport { extractEventInfo } from '../../events/event-info.mjs';\nimport { convertBoxToBoundingBox, convertBoundingBoxToBox } from '../../projection/geometry/conversion.mjs';\nimport { calcLength } from '../../projection/geometry/delta-calc.mjs';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { eachAxis } from '../../projection/utils/each-axis.mjs';\nimport { measurePageBox } from '../../projection/utils/measure.mjs';\nimport { getContextWindow } from '../../utils/get-context-window.mjs';\nimport { isRefObject } from '../../utils/is-ref-object.mjs';\nimport { addValueToWillChange } from '../../value/use-will-change/add-will-change.mjs';\nimport { PanSession } from '../pan/PanSession.mjs';\nimport { applyConstraints, calcRelativeConstraints, resolveDragElastic, rebaseAxisConstraints, calcViewportConstraints, calcOrigin, defaultElastic } from './utils/constraints.mjs';\n\nconst elementDragControls = new WeakMap();\n/**\n *\n */\n// let latestPointerEvent: PointerEvent\nclass VisualElementDragControls {\n    constructor(visualElement) {\n        this.openDragLock = null;\n        this.isDragging = false;\n        this.currentDirection = null;\n        this.originPoint = { x: 0, y: 0 };\n        /**\n         * The permitted boundaries of travel, in pixels.\n         */\n        this.constraints = false;\n        this.hasMutatedConstraints = false;\n        /**\n         * The per-axis resolved elastic values.\n         */\n        this.elastic = createBox();\n        this.visualElement = visualElement;\n    }\n    start(originEvent, { snapToCursor = false } = {}) {\n        /**\n         * Don't start dragging if this component is exiting\n         */\n        const { presenceContext } = this.visualElement;\n        if (presenceContext && presenceContext.isPresent === false)\n            return;\n        const onSessionStart = (event) => {\n            const { dragSnapToOrigin } = this.getProps();\n            // Stop or pause any animations on both axis values immediately. This allows the user to throw and catch\n            // the component.\n            dragSnapToOrigin ? this.pauseAnimation() : this.stopAnimation();\n            if (snapToCursor) {\n                this.snapToCursor(extractEventInfo(event).point);\n            }\n        };\n        const onStart = (event, info) => {\n            // Attempt to grab the global drag gesture lock - maybe make this part of PanSession\n            const { drag, dragPropagation, onDragStart } = this.getProps();\n            if (drag && !dragPropagation) {\n                if (this.openDragLock)\n                    this.openDragLock();\n                this.openDragLock = setDragLock(drag);\n                // If we don 't have the lock, don't start dragging\n                if (!this.openDragLock)\n                    return;\n            }\n            this.isDragging = true;\n            this.currentDirection = null;\n            this.resolveConstraints();\n            if (this.visualElement.projection) {\n                this.visualElement.projection.isAnimationBlocked = true;\n                this.visualElement.projection.target = undefined;\n            }\n            /**\n             * Record gesture origin\n             */\n            eachAxis((axis) => {\n                let current = this.getAxisMotionValue(axis).get() || 0;\n                /**\n                 * If the MotionValue is a percentage value convert to px\n                 */\n                if (percent.test(current)) {\n                    const { projection } = this.visualElement;\n                    if (projection && projection.layout) {\n                        const measuredAxis = projection.layout.layoutBox[axis];\n                        if (measuredAxis) {\n                            const length = calcLength(measuredAxis);\n                            current = length * (parseFloat(current) / 100);\n                        }\n                    }\n                }\n                this.originPoint[axis] = current;\n            });\n            // Fire onDragStart event\n            if (onDragStart) {\n                frame.postRender(() => onDragStart(event, info));\n            }\n            addValueToWillChange(this.visualElement, \"transform\");\n            const { animationState } = this.visualElement;\n            animationState && animationState.setActive(\"whileDrag\", true);\n        };\n        const onMove = (event, info) => {\n            // latestPointerEvent = event\n            const { dragPropagation, dragDirectionLock, onDirectionLock, onDrag, } = this.getProps();\n            // If we didn't successfully receive the gesture lock, early return.\n            if (!dragPropagation && !this.openDragLock)\n                return;\n            const { offset } = info;\n            // Attempt to detect drag direction if directionLock is true\n            if (dragDirectionLock && this.currentDirection === null) {\n                this.currentDirection = getCurrentDirection(offset);\n                // If we've successfully set a direction, notify listener\n                if (this.currentDirection !== null) {\n                    onDirectionLock && onDirectionLock(this.currentDirection);\n                }\n                return;\n            }\n            // Update each point with the latest position\n            this.updateAxis(\"x\", info.point, offset);\n            this.updateAxis(\"y\", info.point, offset);\n            /**\n             * Ideally we would leave the renderer to fire naturally at the end of\n             * this frame but if the element is about to change layout as the result\n             * of a re-render we want to ensure the browser can read the latest\n             * bounding box to ensure the pointer and element don't fall out of sync.\n             */\n            this.visualElement.render();\n            /**\n             * This must fire after the render call as it might trigger a state\n             * change which itself might trigger a layout update.\n             */\n            onDrag && onDrag(event, info);\n        };\n        const onSessionEnd = (event, info) => this.stop(event, info);\n        const resumeAnimation = () => eachAxis((axis) => this.getAnimationState(axis) === \"paused\" &&\n            this.getAxisMotionValue(axis).animation?.play());\n        const { dragSnapToOrigin } = this.getProps();\n        this.panSession = new PanSession(originEvent, {\n            onSessionStart,\n            onStart,\n            onMove,\n            onSessionEnd,\n            resumeAnimation,\n        }, {\n            transformPagePoint: this.visualElement.getTransformPagePoint(),\n            dragSnapToOrigin,\n            contextWindow: getContextWindow(this.visualElement),\n        });\n    }\n    stop(event, info) {\n        const isDragging = this.isDragging;\n        this.cancel();\n        if (!isDragging)\n            return;\n        const { velocity } = info;\n        this.startAnimation(velocity);\n        const { onDragEnd } = this.getProps();\n        if (onDragEnd) {\n            frame.postRender(() => onDragEnd(event, info));\n        }\n    }\n    cancel() {\n        this.isDragging = false;\n        const { projection, animationState } = this.visualElement;\n        if (projection) {\n            projection.isAnimationBlocked = false;\n        }\n        this.panSession && this.panSession.end();\n        this.panSession = undefined;\n        const { dragPropagation } = this.getProps();\n        if (!dragPropagation && this.openDragLock) {\n            this.openDragLock();\n            this.openDragLock = null;\n        }\n        animationState && animationState.setActive(\"whileDrag\", false);\n    }\n    updateAxis(axis, _point, offset) {\n        const { drag } = this.getProps();\n        // If we're not dragging this axis, do an early return.\n        if (!offset || !shouldDrag(axis, drag, this.currentDirection))\n            return;\n        const axisValue = this.getAxisMotionValue(axis);\n        let next = this.originPoint[axis] + offset[axis];\n        // Apply constraints\n        if (this.constraints && this.constraints[axis]) {\n            next = applyConstraints(next, this.constraints[axis], this.elastic[axis]);\n        }\n        axisValue.set(next);\n    }\n    resolveConstraints() {\n        const { dragConstraints, dragElastic } = this.getProps();\n        const layout = this.visualElement.projection &&\n            !this.visualElement.projection.layout\n            ? this.visualElement.projection.measure(false)\n            : this.visualElement.projection?.layout;\n        const prevConstraints = this.constraints;\n        if (dragConstraints && isRefObject(dragConstraints)) {\n            if (!this.constraints) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        }\n        else {\n            if (dragConstraints && layout) {\n                this.constraints = calcRelativeConstraints(layout.layoutBox, dragConstraints);\n            }\n            else {\n                this.constraints = false;\n            }\n        }\n        this.elastic = resolveDragElastic(dragElastic);\n        /**\n         * If we're outputting to external MotionValues, we want to rebase the measured constraints\n         * from viewport-relative to component-relative.\n         */\n        if (prevConstraints !== this.constraints &&\n            layout &&\n            this.constraints &&\n            !this.hasMutatedConstraints) {\n            eachAxis((axis) => {\n                if (this.constraints !== false &&\n                    this.getAxisMotionValue(axis)) {\n                    this.constraints[axis] = rebaseAxisConstraints(layout.layoutBox[axis], this.constraints[axis]);\n                }\n            });\n        }\n    }\n    resolveRefConstraints() {\n        const { dragConstraints: constraints, onMeasureDragConstraints } = this.getProps();\n        if (!constraints || !isRefObject(constraints))\n            return false;\n        const constraintsElement = constraints.current;\n        invariant(constraintsElement !== null, \"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.\");\n        const { projection } = this.visualElement;\n        // TODO\n        if (!projection || !projection.layout)\n            return false;\n        const constraintsBox = measurePageBox(constraintsElement, projection.root, this.visualElement.getTransformPagePoint());\n        let measuredConstraints = calcViewportConstraints(projection.layout.layoutBox, constraintsBox);\n        /**\n         * If there's an onMeasureDragConstraints listener we call it and\n         * if different constraints are returned, set constraints to that\n         */\n        if (onMeasureDragConstraints) {\n            const userConstraints = onMeasureDragConstraints(convertBoxToBoundingBox(measuredConstraints));\n            this.hasMutatedConstraints = !!userConstraints;\n            if (userConstraints) {\n                measuredConstraints = convertBoundingBoxToBox(userConstraints);\n            }\n        }\n        return measuredConstraints;\n    }\n    startAnimation(velocity) {\n        const { drag, dragMomentum, dragElastic, dragTransition, dragSnapToOrigin, onDragTransitionEnd, } = this.getProps();\n        const constraints = this.constraints || {};\n        const momentumAnimations = eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, this.currentDirection)) {\n                return;\n            }\n            let transition = (constraints && constraints[axis]) || {};\n            if (dragSnapToOrigin)\n                transition = { min: 0, max: 0 };\n            /**\n             * Overdamp the boundary spring if `dragElastic` is disabled. There's still a frame\n             * of spring animations so we should look into adding a disable spring option to `inertia`.\n             * We could do something here where we affect the `bounceStiffness` and `bounceDamping`\n             * using the value of `dragElastic`.\n             */\n            const bounceStiffness = dragElastic ? 200 : 1000000;\n            const bounceDamping = dragElastic ? 40 : 10000000;\n            const inertia = {\n                type: \"inertia\",\n                velocity: dragMomentum ? velocity[axis] : 0,\n                bounceStiffness,\n                bounceDamping,\n                timeConstant: 750,\n                restDelta: 1,\n                restSpeed: 10,\n                ...dragTransition,\n                ...transition,\n            };\n            // If we're not animating on an externally-provided `MotionValue` we can use the\n            // component's animation controls which will handle interactions with whileHover (etc),\n            // otherwise we just have to animate the `MotionValue` itself.\n            return this.startAxisValueAnimation(axis, inertia);\n        });\n        // Run all animations and then resolve the new drag constraints.\n        return Promise.all(momentumAnimations).then(onDragTransitionEnd);\n    }\n    startAxisValueAnimation(axis, transition) {\n        const axisValue = this.getAxisMotionValue(axis);\n        addValueToWillChange(this.visualElement, axis);\n        return axisValue.start(animateMotionValue(axis, axisValue, 0, transition, this.visualElement, false));\n    }\n    stopAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).stop());\n    }\n    pauseAnimation() {\n        eachAxis((axis) => this.getAxisMotionValue(axis).animation?.pause());\n    }\n    getAnimationState(axis) {\n        return this.getAxisMotionValue(axis).animation?.state;\n    }\n    /**\n     * Drag works differently depending on which props are provided.\n     *\n     * - If _dragX and _dragY are provided, we output the gesture delta directly to those motion values.\n     * - Otherwise, we apply the delta to the x/y motion values.\n     */\n    getAxisMotionValue(axis) {\n        const dragKey = `_drag${axis.toUpperCase()}`;\n        const props = this.visualElement.getProps();\n        const externalMotionValue = props[dragKey];\n        return externalMotionValue\n            ? externalMotionValue\n            : this.visualElement.getValue(axis, (props.initial\n                ? props.initial[axis]\n                : undefined) || 0);\n    }\n    snapToCursor(point) {\n        eachAxis((axis) => {\n            const { drag } = this.getProps();\n            // If we're not dragging this axis, do an early return.\n            if (!shouldDrag(axis, drag, this.currentDirection))\n                return;\n            const { projection } = this.visualElement;\n            const axisValue = this.getAxisMotionValue(axis);\n            if (projection && projection.layout) {\n                const { min, max } = projection.layout.layoutBox[axis];\n                axisValue.set(point[axis] - mixNumber(min, max, 0.5));\n            }\n        });\n    }\n    /**\n     * When the viewport resizes we want to check if the measured constraints\n     * have changed and, if so, reposition the element within those new constraints\n     * relative to where it was before the resize.\n     */\n    scalePositionWithinConstraints() {\n        if (!this.visualElement.current)\n            return;\n        const { drag, dragConstraints } = this.getProps();\n        const { projection } = this.visualElement;\n        if (!isRefObject(dragConstraints) || !projection || !this.constraints)\n            return;\n        /**\n         * Stop current animations as there can be visual glitching if we try to do\n         * this mid-animation\n         */\n        this.stopAnimation();\n        /**\n         * Record the relative position of the dragged element relative to the\n         * constraints box and save as a progress value.\n         */\n        const boxProgress = { x: 0, y: 0 };\n        eachAxis((axis) => {\n            const axisValue = this.getAxisMotionValue(axis);\n            if (axisValue && this.constraints !== false) {\n                const latest = axisValue.get();\n                boxProgress[axis] = calcOrigin({ min: latest, max: latest }, this.constraints[axis]);\n            }\n        });\n        /**\n         * Update the layout of this element and resolve the latest drag constraints\n         */\n        const { transformTemplate } = this.visualElement.getProps();\n        this.visualElement.current.style.transform = transformTemplate\n            ? transformTemplate({}, \"\")\n            : \"none\";\n        projection.root && projection.root.updateScroll();\n        projection.updateLayout();\n        this.resolveConstraints();\n        /**\n         * For each axis, calculate the current progress of the layout axis\n         * within the new constraints.\n         */\n        eachAxis((axis) => {\n            if (!shouldDrag(axis, drag, null))\n                return;\n            /**\n             * Calculate a new transform based on the previous box progress\n             */\n            const axisValue = this.getAxisMotionValue(axis);\n            const { min, max } = this.constraints[axis];\n            axisValue.set(mixNumber(min, max, boxProgress[axis]));\n        });\n    }\n    addListeners() {\n        if (!this.visualElement.current)\n            return;\n        elementDragControls.set(this.visualElement, this);\n        const element = this.visualElement.current;\n        /**\n         * Attach a pointerdown event listener on this DOM element to initiate drag tracking.\n         */\n        const stopPointerListener = addPointerEvent(element, \"pointerdown\", (event) => {\n            const { drag, dragListener = true } = this.getProps();\n            drag && dragListener && this.start(event);\n        });\n        const measureDragConstraints = () => {\n            const { dragConstraints } = this.getProps();\n            if (isRefObject(dragConstraints) && dragConstraints.current) {\n                this.constraints = this.resolveRefConstraints();\n            }\n        };\n        const { projection } = this.visualElement;\n        const stopMeasureLayoutListener = projection.addEventListener(\"measure\", measureDragConstraints);\n        if (projection && !projection.layout) {\n            projection.root && projection.root.updateScroll();\n            projection.updateLayout();\n        }\n        frame.read(measureDragConstraints);\n        /**\n         * Attach a window resize listener to scale the draggable target within its defined\n         * constraints as the window resizes.\n         */\n        const stopResizeListener = addDomEvent(window, \"resize\", () => this.scalePositionWithinConstraints());\n        /**\n         * If the element's layout changes, calculate the delta and apply that to\n         * the drag gesture's origin point.\n         */\n        const stopLayoutUpdateListener = projection.addEventListener(\"didUpdate\", (({ delta, hasLayoutChanged }) => {\n            if (this.isDragging && hasLayoutChanged) {\n                eachAxis((axis) => {\n                    const motionValue = this.getAxisMotionValue(axis);\n                    if (!motionValue)\n                        return;\n                    this.originPoint[axis] += delta[axis].translate;\n                    motionValue.set(motionValue.get() + delta[axis].translate);\n                });\n                this.visualElement.render();\n            }\n        }));\n        return () => {\n            stopResizeListener();\n            stopPointerListener();\n            stopMeasureLayoutListener();\n            stopLayoutUpdateListener && stopLayoutUpdateListener();\n        };\n    }\n    getProps() {\n        const props = this.visualElement.getProps();\n        const { drag = false, dragDirectionLock = false, dragPropagation = false, dragConstraints = false, dragElastic = defaultElastic, dragMomentum = true, } = props;\n        return {\n            ...props,\n            drag,\n            dragDirectionLock,\n            dragPropagation,\n            dragConstraints,\n            dragElastic,\n            dragMomentum,\n        };\n    }\n}\nfunction shouldDrag(direction, drag, currentDirection) {\n    return ((drag === true || drag === direction) &&\n        (currentDirection === null || currentDirection === direction));\n}\n/**\n * Based on an x/y offset determine the current drag direction. If both axis' offsets are lower\n * than the provided threshold, return `null`.\n *\n * @param offset - The x/y offset from origin.\n * @param lockThreshold - (Optional) - the minimum absolute offset before we can determine a drag direction.\n */\nfunction getCurrentDirection(offset, lockThreshold = 10) {\n    let direction = null;\n    if (Math.abs(offset.y) > lockThreshold) {\n        direction = \"y\";\n    }\n    else if (Math.abs(offset.x) > lockThreshold) {\n        direction = \"x\";\n    }\n    return direction;\n}\n\nexport { VisualElementDragControls, elementDragControls };\n"], "mappings": ";AAAA,SAASA,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,YAAY;AACnE,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,kBAAkB,QAAQ,6CAA6C;AAChF,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,eAAe,QAAQ,oCAAoC;AACpE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,uBAAuB,EAAEC,uBAAuB,QAAQ,0CAA0C;AAC3G,SAASC,UAAU,QAAQ,0CAA0C;AACrE,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,QAAQ,QAAQ,sCAAsC;AAC/D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,gBAAgB,QAAQ,oCAAoC;AACrE,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,SAASC,oBAAoB,QAAQ,iDAAiD;AACtF,SAASC,UAAU,QAAQ,uBAAuB;AAClD,SAASC,gBAAgB,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,cAAc,QAAQ,yBAAyB;AAEnL,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;AACzC;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,WAAW,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACjC;AACR;AACA;IACQ,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,OAAO,GAAG3B,SAAS,CAAC,CAAC;IAC1B,IAAI,CAACkB,aAAa,GAAGA,aAAa;EACtC;EACAU,KAAKA,CAACC,WAAW,EAAiC;IAAA,IAA/B;MAAEC,YAAY,GAAG;IAAM,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5C;AACR;AACA;IACQ,MAAM;MAAEG;IAAgB,CAAC,GAAG,IAAI,CAAChB,aAAa;IAC9C,IAAIgB,eAAe,IAAIA,eAAe,CAACC,SAAS,KAAK,KAAK,EACtD;IACJ,MAAMC,cAAc,GAAIC,KAAK,IAAK;MAC9B,MAAM;QAAEC;MAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC5C;MACA;MACAD,gBAAgB,GAAG,IAAI,CAACE,cAAc,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;MAC/D,IAAIX,YAAY,EAAE;QACd,IAAI,CAACA,YAAY,CAAClC,gBAAgB,CAACyC,KAAK,CAAC,CAACK,KAAK,CAAC;MACpD;IACJ,CAAC;IACD,MAAMC,OAAO,GAAGA,CAACN,KAAK,EAAEO,IAAI,KAAK;MAC7B;MACA,MAAM;QAAEC,IAAI;QAAEC,eAAe;QAAEC;MAAY,CAAC,GAAG,IAAI,CAACR,QAAQ,CAAC,CAAC;MAC9D,IAAIM,IAAI,IAAI,CAACC,eAAe,EAAE;QAC1B,IAAI,IAAI,CAAC3B,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC,CAAC;QACvB,IAAI,CAACA,YAAY,GAAG7B,WAAW,CAACuD,IAAI,CAAC;QACrC;QACA,IAAI,CAAC,IAAI,CAAC1B,YAAY,EAClB;MACR;MACA,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACC,gBAAgB,GAAG,IAAI;MAC5B,IAAI,CAAC2B,kBAAkB,CAAC,CAAC;MACzB,IAAI,IAAI,CAAC9B,aAAa,CAAC+B,UAAU,EAAE;QAC/B,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACC,kBAAkB,GAAG,IAAI;QACvD,IAAI,CAAChC,aAAa,CAAC+B,UAAU,CAACE,MAAM,GAAGlB,SAAS;MACpD;MACA;AACZ;AACA;MACYhC,QAAQ,CAAEmD,IAAI,IAAK;QACf,IAAIC,OAAO,GAAG,IAAI,CAACC,kBAAkB,CAACF,IAAI,CAAC,CAACG,GAAG,CAAC,CAAC,IAAI,CAAC;QACtD;AAChB;AACA;QACgB,IAAIhE,OAAO,CAACiE,IAAI,CAACH,OAAO,CAAC,EAAE;UACvB,MAAM;YAAEJ;UAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;UACzC,IAAI+B,UAAU,IAAIA,UAAU,CAACQ,MAAM,EAAE;YACjC,MAAMC,YAAY,GAAGT,UAAU,CAACQ,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;YACtD,IAAIM,YAAY,EAAE;cACd,MAAM1B,MAAM,GAAGjC,UAAU,CAAC2D,YAAY,CAAC;cACvCL,OAAO,GAAGrB,MAAM,IAAI4B,UAAU,CAACP,OAAO,CAAC,GAAG,GAAG,CAAC;YAClD;UACJ;QACJ;QACA,IAAI,CAAC/B,WAAW,CAAC8B,IAAI,CAAC,GAAGC,OAAO;MACpC,CAAC,CAAC;MACF;MACA,IAAIN,WAAW,EAAE;QACb3D,KAAK,CAACyE,UAAU,CAAC,MAAMd,WAAW,CAACV,KAAK,EAAEO,IAAI,CAAC,CAAC;MACpD;MACAvC,oBAAoB,CAAC,IAAI,CAACa,aAAa,EAAE,WAAW,CAAC;MACrD,MAAM;QAAE4C;MAAe,CAAC,GAAG,IAAI,CAAC5C,aAAa;MAC7C4C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;IACjE,CAAC;IACD,MAAMC,MAAM,GAAGA,CAAC3B,KAAK,EAAEO,IAAI,KAAK;MAC5B;MACA,MAAM;QAAEE,eAAe;QAAEmB,iBAAiB;QAAEC,eAAe;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAAC5B,QAAQ,CAAC,CAAC;MACxF;MACA,IAAI,CAACO,eAAe,IAAI,CAAC,IAAI,CAAC3B,YAAY,EACtC;MACJ,MAAM;QAAEiD;MAAO,CAAC,GAAGxB,IAAI;MACvB;MACA,IAAIqB,iBAAiB,IAAI,IAAI,CAAC5C,gBAAgB,KAAK,IAAI,EAAE;QACrD,IAAI,CAACA,gBAAgB,GAAGgD,mBAAmB,CAACD,MAAM,CAAC;QACnD;QACA,IAAI,IAAI,CAAC/C,gBAAgB,KAAK,IAAI,EAAE;UAChC6C,eAAe,IAAIA,eAAe,CAAC,IAAI,CAAC7C,gBAAgB,CAAC;QAC7D;QACA;MACJ;MACA;MACA,IAAI,CAACiD,UAAU,CAAC,GAAG,EAAE1B,IAAI,CAACF,KAAK,EAAE0B,MAAM,CAAC;MACxC,IAAI,CAACE,UAAU,CAAC,GAAG,EAAE1B,IAAI,CAACF,KAAK,EAAE0B,MAAM,CAAC;MACxC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAAClD,aAAa,CAACqD,MAAM,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;MACYJ,MAAM,IAAIA,MAAM,CAAC9B,KAAK,EAAEO,IAAI,CAAC;IACjC,CAAC;IACD,MAAM4B,YAAY,GAAGA,CAACnC,KAAK,EAAEO,IAAI,KAAK,IAAI,CAAC6B,IAAI,CAACpC,KAAK,EAAEO,IAAI,CAAC;IAC5D,MAAM8B,eAAe,GAAGA,CAAA,KAAMzE,QAAQ,CAAEmD,IAAI;MAAA,IAAAuB,qBAAA;MAAA,OAAK,IAAI,CAACC,iBAAiB,CAACxB,IAAI,CAAC,KAAK,QAAQ,MAAAuB,qBAAA,GACtF,IAAI,CAACrB,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,cAAAF,qBAAA,uBAAvCA,qBAAA,CAAyCG,IAAI,CAAC,CAAC;IAAA,EAAC;IACpD,MAAM;MAAExC;IAAiB,CAAC,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;IAC5C,IAAI,CAACwC,UAAU,GAAG,IAAIzE,UAAU,CAACuB,WAAW,EAAE;MAC1CO,cAAc;MACdO,OAAO;MACPqB,MAAM;MACNQ,YAAY;MACZE;IACJ,CAAC,EAAE;MACCM,kBAAkB,EAAE,IAAI,CAAC9D,aAAa,CAAC+D,qBAAqB,CAAC,CAAC;MAC9D3C,gBAAgB;MAChB4C,aAAa,EAAE/E,gBAAgB,CAAC,IAAI,CAACe,aAAa;IACtD,CAAC,CAAC;EACN;EACAuD,IAAIA,CAACpC,KAAK,EAAEO,IAAI,EAAE;IACd,MAAMxB,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAI,CAAC+D,MAAM,CAAC,CAAC;IACb,IAAI,CAAC/D,UAAU,EACX;IACJ,MAAM;MAAEgE;IAAS,CAAC,GAAGxC,IAAI;IACzB,IAAI,CAACyC,cAAc,CAACD,QAAQ,CAAC;IAC7B,MAAM;MAAEE;IAAU,CAAC,GAAG,IAAI,CAAC/C,QAAQ,CAAC,CAAC;IACrC,IAAI+C,SAAS,EAAE;MACXlG,KAAK,CAACyE,UAAU,CAAC,MAAMyB,SAAS,CAACjD,KAAK,EAAEO,IAAI,CAAC,CAAC;IAClD;EACJ;EACAuC,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC/D,UAAU,GAAG,KAAK;IACvB,MAAM;MAAE6B,UAAU;MAAEa;IAAe,CAAC,GAAG,IAAI,CAAC5C,aAAa;IACzD,IAAI+B,UAAU,EAAE;MACZA,UAAU,CAACC,kBAAkB,GAAG,KAAK;IACzC;IACA,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAACA,UAAU,CAACQ,GAAG,CAAC,CAAC;IACxC,IAAI,CAACR,UAAU,GAAG9C,SAAS;IAC3B,MAAM;MAAEa;IAAgB,CAAC,GAAG,IAAI,CAACP,QAAQ,CAAC,CAAC;IAC3C,IAAI,CAACO,eAAe,IAAI,IAAI,CAAC3B,YAAY,EAAE;MACvC,IAAI,CAACA,YAAY,CAAC,CAAC;MACnB,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;IACA2C,cAAc,IAAIA,cAAc,CAACC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC;EAClE;EACAO,UAAUA,CAAClB,IAAI,EAAEoC,MAAM,EAAEpB,MAAM,EAAE;IAC7B,MAAM;MAAEvB;IAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAChC;IACA,IAAI,CAAC6B,MAAM,IAAI,CAACqB,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EACzD;IACJ,MAAMqE,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;IAC/C,IAAIuC,IAAI,GAAG,IAAI,CAACrE,WAAW,CAAC8B,IAAI,CAAC,GAAGgB,MAAM,CAAChB,IAAI,CAAC;IAChD;IACA,IAAI,IAAI,CAAC3B,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC2B,IAAI,CAAC,EAAE;MAC5CuC,IAAI,GAAGpF,gBAAgB,CAACoF,IAAI,EAAE,IAAI,CAAClE,WAAW,CAAC2B,IAAI,CAAC,EAAE,IAAI,CAACzB,OAAO,CAACyB,IAAI,CAAC,CAAC;IAC7E;IACAsC,SAAS,CAACE,GAAG,CAACD,IAAI,CAAC;EACvB;EACA3C,kBAAkBA,CAAA,EAAG;IAAA,IAAA6C,qBAAA;IACjB,MAAM;MAAEC,eAAe;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACxD,QAAQ,CAAC,CAAC;IACxD,MAAMkB,MAAM,GAAG,IAAI,CAACvC,aAAa,CAAC+B,UAAU,IACxC,CAAC,IAAI,CAAC/B,aAAa,CAAC+B,UAAU,CAACQ,MAAM,GACnC,IAAI,CAACvC,aAAa,CAAC+B,UAAU,CAAC+C,OAAO,CAAC,KAAK,CAAC,IAAAH,qBAAA,GAC5C,IAAI,CAAC3E,aAAa,CAAC+B,UAAU,cAAA4C,qBAAA,uBAA7BA,qBAAA,CAA+BpC,MAAM;IAC3C,MAAMwC,eAAe,GAAG,IAAI,CAACxE,WAAW;IACxC,IAAIqE,eAAe,IAAI1F,WAAW,CAAC0F,eAAe,CAAC,EAAE;MACjD,IAAI,CAAC,IAAI,CAACrE,WAAW,EAAE;QACnB,IAAI,CAACA,WAAW,GAAG,IAAI,CAACyE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC,MACI;MACD,IAAIJ,eAAe,IAAIrC,MAAM,EAAE;QAC3B,IAAI,CAAChC,WAAW,GAAGjB,uBAAuB,CAACiD,MAAM,CAACE,SAAS,EAAEmC,eAAe,CAAC;MACjF,CAAC,MACI;QACD,IAAI,CAACrE,WAAW,GAAG,KAAK;MAC5B;IACJ;IACA,IAAI,CAACE,OAAO,GAAGlB,kBAAkB,CAACsF,WAAW,CAAC;IAC9C;AACR;AACA;AACA;IACQ,IAAIE,eAAe,KAAK,IAAI,CAACxE,WAAW,IACpCgC,MAAM,IACN,IAAI,CAAChC,WAAW,IAChB,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7BzB,QAAQ,CAAEmD,IAAI,IAAK;QACf,IAAI,IAAI,CAAC3B,WAAW,KAAK,KAAK,IAC1B,IAAI,CAAC6B,kBAAkB,CAACF,IAAI,CAAC,EAAE;UAC/B,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,GAAG1C,qBAAqB,CAAC+C,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC2B,IAAI,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;IACN;EACJ;EACA8C,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAEJ,eAAe,EAAErE,WAAW;MAAE0E;IAAyB,CAAC,GAAG,IAAI,CAAC5D,QAAQ,CAAC,CAAC;IAClF,IAAI,CAACd,WAAW,IAAI,CAACrB,WAAW,CAACqB,WAAW,CAAC,EACzC,OAAO,KAAK;IAChB,MAAM2E,kBAAkB,GAAG3E,WAAW,CAAC4B,OAAO;IAC9C7D,SAAS,CAAC4G,kBAAkB,KAAK,IAAI,EAAE,wGAAwG,CAAC;IAChJ,MAAM;MAAEnD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC;IACA,IAAI,CAAC+B,UAAU,IAAI,CAACA,UAAU,CAACQ,MAAM,EACjC,OAAO,KAAK;IAChB,MAAM4C,cAAc,GAAGnG,cAAc,CAACkG,kBAAkB,EAAEnD,UAAU,CAACqD,IAAI,EAAE,IAAI,CAACpF,aAAa,CAAC+D,qBAAqB,CAAC,CAAC,CAAC;IACtH,IAAIsB,mBAAmB,GAAG5F,uBAAuB,CAACsC,UAAU,CAACQ,MAAM,CAACE,SAAS,EAAE0C,cAAc,CAAC;IAC9F;AACR;AACA;AACA;IACQ,IAAIF,wBAAwB,EAAE;MAC1B,MAAMK,eAAe,GAAGL,wBAAwB,CAACtG,uBAAuB,CAAC0G,mBAAmB,CAAC,CAAC;MAC9F,IAAI,CAAC7E,qBAAqB,GAAG,CAAC,CAAC8E,eAAe;MAC9C,IAAIA,eAAe,EAAE;QACjBD,mBAAmB,GAAGzG,uBAAuB,CAAC0G,eAAe,CAAC;MAClE;IACJ;IACA,OAAOD,mBAAmB;EAC9B;EACAlB,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAEvC,IAAI;MAAE4D,YAAY;MAAEV,WAAW;MAAEW,cAAc;MAAEpE,gBAAgB;MAAEqE;IAAqB,CAAC,GAAG,IAAI,CAACpE,QAAQ,CAAC,CAAC;IACnH,MAAMd,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IAC1C,MAAMmF,kBAAkB,GAAG3G,QAAQ,CAAEmD,IAAI,IAAK;MAC1C,IAAI,CAACqC,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAAE;QAChD;MACJ;MACA,IAAIwF,UAAU,GAAIpF,WAAW,IAAIA,WAAW,CAAC2B,IAAI,CAAC,IAAK,CAAC,CAAC;MACzD,IAAId,gBAAgB,EAChBuE,UAAU,GAAG;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnC;AACZ;AACA;AACA;AACA;AACA;MACY,MAAMC,eAAe,GAAGjB,WAAW,GAAG,GAAG,GAAG,OAAO;MACnD,MAAMkB,aAAa,GAAGlB,WAAW,GAAG,EAAE,GAAG,QAAQ;MACjD,MAAMmB,OAAO,GAAAC,aAAA,CAAAA,aAAA;QACTC,IAAI,EAAE,SAAS;QACfhC,QAAQ,EAAEqB,YAAY,GAAGrB,QAAQ,CAAChC,IAAI,CAAC,GAAG,CAAC;QAC3C4D,eAAe;QACfC,aAAa;QACbI,YAAY,EAAE,GAAG;QACjBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE;MAAE,GACVb,cAAc,GACdG,UAAU,CAChB;MACD;MACA;MACA;MACA,OAAO,IAAI,CAACW,uBAAuB,CAACpE,IAAI,EAAE8D,OAAO,CAAC;IACtD,CAAC,CAAC;IACF;IACA,OAAOO,OAAO,CAACC,GAAG,CAACd,kBAAkB,CAAC,CAACe,IAAI,CAAChB,mBAAmB,CAAC;EACpE;EACAa,uBAAuBA,CAACpE,IAAI,EAAEyD,UAAU,EAAE;IACtC,MAAMnB,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;IAC/C/C,oBAAoB,CAAC,IAAI,CAACa,aAAa,EAAEkC,IAAI,CAAC;IAC9C,OAAOsC,SAAS,CAAC9D,KAAK,CAACnC,kBAAkB,CAAC2D,IAAI,EAAEsC,SAAS,EAAE,CAAC,EAAEmB,UAAU,EAAE,IAAI,CAAC3F,aAAa,EAAE,KAAK,CAAC,CAAC;EACzG;EACAuB,aAAaA,CAAA,EAAG;IACZxC,QAAQ,CAAEmD,IAAI,IAAK,IAAI,CAACE,kBAAkB,CAACF,IAAI,CAAC,CAACqB,IAAI,CAAC,CAAC,CAAC;EAC5D;EACAjC,cAAcA,CAAA,EAAG;IACbvC,QAAQ,CAAEmD,IAAI;MAAA,IAAAwE,sBAAA;MAAA,QAAAA,sBAAA,GAAK,IAAI,CAACtE,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,cAAA+C,sBAAA,uBAAvCA,sBAAA,CAAyCC,KAAK,CAAC,CAAC;IAAA,EAAC;EACxE;EACAjD,iBAAiBA,CAACxB,IAAI,EAAE;IAAA,IAAA0E,sBAAA;IACpB,QAAAA,sBAAA,GAAO,IAAI,CAACxE,kBAAkB,CAACF,IAAI,CAAC,CAACyB,SAAS,cAAAiD,sBAAA,uBAAvCA,sBAAA,CAAyCC,KAAK;EACzD;EACA;AACJ;AACA;AACA;AACA;AACA;EACIzE,kBAAkBA,CAACF,IAAI,EAAE;IACrB,MAAM4E,OAAO,WAAAC,MAAA,CAAW7E,IAAI,CAAC8E,WAAW,CAAC,CAAC,CAAE;IAC5C,MAAMC,KAAK,GAAG,IAAI,CAACjH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAM6F,mBAAmB,GAAGD,KAAK,CAACH,OAAO,CAAC;IAC1C,OAAOI,mBAAmB,GACpBA,mBAAmB,GACnB,IAAI,CAAClH,aAAa,CAACmH,QAAQ,CAACjF,IAAI,EAAE,CAAC+E,KAAK,CAACG,OAAO,GAC5CH,KAAK,CAACG,OAAO,CAAClF,IAAI,CAAC,GACnBnB,SAAS,KAAK,CAAC,CAAC;EAC9B;EACAH,YAAYA,CAACY,KAAK,EAAE;IAChBzC,QAAQ,CAAEmD,IAAI,IAAK;MACf,MAAM;QAAEP;MAAK,CAAC,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;MAChC;MACA,IAAI,CAACkD,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAACxB,gBAAgB,CAAC,EAC9C;MACJ,MAAM;QAAE4B;MAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;MACzC,MAAMwE,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIH,UAAU,IAAIA,UAAU,CAACQ,MAAM,EAAE;QACjC,MAAM;UAAEqD,GAAG;UAAEC;QAAI,CAAC,GAAG9D,UAAU,CAACQ,MAAM,CAACE,SAAS,CAACP,IAAI,CAAC;QACtDsC,SAAS,CAACE,GAAG,CAAClD,KAAK,CAACU,IAAI,CAAC,GAAG/D,SAAS,CAACyH,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC,CAAC;MACzD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIwB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,CAAC,IAAI,CAACrH,aAAa,CAACmC,OAAO,EAC3B;IACJ,MAAM;MAAER,IAAI;MAAEiD;IAAgB,CAAC,GAAG,IAAI,CAACvD,QAAQ,CAAC,CAAC;IACjD,MAAM;MAAEU;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,IAAI,CAACd,WAAW,CAAC0F,eAAe,CAAC,IAAI,CAAC7C,UAAU,IAAI,CAAC,IAAI,CAACxB,WAAW,EACjE;IACJ;AACR;AACA;AACA;IACQ,IAAI,CAACgB,aAAa,CAAC,CAAC;IACpB;AACR;AACA;AACA;IACQ,MAAM+F,WAAW,GAAG;MAAEjH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IAClCvB,QAAQ,CAAEmD,IAAI,IAAK;MACf,MAAMsC,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,IAAIsC,SAAS,IAAI,IAAI,CAACjE,WAAW,KAAK,KAAK,EAAE;QACzC,MAAMgH,MAAM,GAAG/C,SAAS,CAACnC,GAAG,CAAC,CAAC;QAC9BiF,WAAW,CAACpF,IAAI,CAAC,GAAGxC,UAAU,CAAC;UAAEkG,GAAG,EAAE2B,MAAM;UAAE1B,GAAG,EAAE0B;QAAO,CAAC,EAAE,IAAI,CAAChH,WAAW,CAAC2B,IAAI,CAAC,CAAC;MACxF;IACJ,CAAC,CAAC;IACF;AACR;AACA;IACQ,MAAM;MAAEsF;IAAkB,CAAC,GAAG,IAAI,CAACxH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3D,IAAI,CAACrB,aAAa,CAACmC,OAAO,CAACsF,KAAK,CAACC,SAAS,GAAGF,iBAAiB,GACxDA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;IACZzF,UAAU,CAACqD,IAAI,IAAIrD,UAAU,CAACqD,IAAI,CAACuC,YAAY,CAAC,CAAC;IACjD5F,UAAU,CAAC6F,YAAY,CAAC,CAAC;IACzB,IAAI,CAAC9F,kBAAkB,CAAC,CAAC;IACzB;AACR;AACA;AACA;IACQ/C,QAAQ,CAAEmD,IAAI,IAAK;MACf,IAAI,CAACqC,UAAU,CAACrC,IAAI,EAAEP,IAAI,EAAE,IAAI,CAAC,EAC7B;MACJ;AACZ;AACA;MACY,MAAM6C,SAAS,GAAG,IAAI,CAACpC,kBAAkB,CAACF,IAAI,CAAC;MAC/C,MAAM;QAAE0D,GAAG;QAAEC;MAAI,CAAC,GAAG,IAAI,CAACtF,WAAW,CAAC2B,IAAI,CAAC;MAC3CsC,SAAS,CAACE,GAAG,CAACvG,SAAS,CAACyH,GAAG,EAAEC,GAAG,EAAEyB,WAAW,CAACpF,IAAI,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC;EACN;EACA2F,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC7H,aAAa,CAACmC,OAAO,EAC3B;IACJvC,mBAAmB,CAAC8E,GAAG,CAAC,IAAI,CAAC1E,aAAa,EAAE,IAAI,CAAC;IACjD,MAAM8H,OAAO,GAAG,IAAI,CAAC9H,aAAa,CAACmC,OAAO;IAC1C;AACR;AACA;IACQ,MAAM4F,mBAAmB,GAAGtJ,eAAe,CAACqJ,OAAO,EAAE,aAAa,EAAG3G,KAAK,IAAK;MAC3E,MAAM;QAAEQ,IAAI;QAAEqG,YAAY,GAAG;MAAK,CAAC,GAAG,IAAI,CAAC3G,QAAQ,CAAC,CAAC;MACrDM,IAAI,IAAIqG,YAAY,IAAI,IAAI,CAACtH,KAAK,CAACS,KAAK,CAAC;IAC7C,CAAC,CAAC;IACF,MAAM8G,sBAAsB,GAAGA,CAAA,KAAM;MACjC,MAAM;QAAErD;MAAgB,CAAC,GAAG,IAAI,CAACvD,QAAQ,CAAC,CAAC;MAC3C,IAAInC,WAAW,CAAC0F,eAAe,CAAC,IAAIA,eAAe,CAACzC,OAAO,EAAE;QACzD,IAAI,CAAC5B,WAAW,GAAG,IAAI,CAACyE,qBAAqB,CAAC,CAAC;MACnD;IACJ,CAAC;IACD,MAAM;MAAEjD;IAAW,CAAC,GAAG,IAAI,CAAC/B,aAAa;IACzC,MAAMkI,yBAAyB,GAAGnG,UAAU,CAACoG,gBAAgB,CAAC,SAAS,EAAEF,sBAAsB,CAAC;IAChG,IAAIlG,UAAU,IAAI,CAACA,UAAU,CAACQ,MAAM,EAAE;MAClCR,UAAU,CAACqD,IAAI,IAAIrD,UAAU,CAACqD,IAAI,CAACuC,YAAY,CAAC,CAAC;MACjD5F,UAAU,CAAC6F,YAAY,CAAC,CAAC;IAC7B;IACA1J,KAAK,CAACkK,IAAI,CAACH,sBAAsB,CAAC;IAClC;AACR;AACA;AACA;IACQ,MAAMI,kBAAkB,GAAG7J,WAAW,CAAC8J,MAAM,EAAE,QAAQ,EAAE,MAAM,IAAI,CAACjB,8BAA8B,CAAC,CAAC,CAAC;IACrG;AACR;AACA;AACA;IACQ,MAAMkB,wBAAwB,GAAGxG,UAAU,CAACoG,gBAAgB,CAAC,WAAW,EAAGK,IAAA,IAAiC;MAAA,IAAhC;QAAEC,KAAK;QAAEC;MAAiB,CAAC,GAAAF,IAAA;MACnG,IAAI,IAAI,CAACtI,UAAU,IAAIwI,gBAAgB,EAAE;QACrC3J,QAAQ,CAAEmD,IAAI,IAAK;UACf,MAAMyG,WAAW,GAAG,IAAI,CAACvG,kBAAkB,CAACF,IAAI,CAAC;UACjD,IAAI,CAACyG,WAAW,EACZ;UACJ,IAAI,CAACvI,WAAW,CAAC8B,IAAI,CAAC,IAAIuG,KAAK,CAACvG,IAAI,CAAC,CAAC0G,SAAS;UAC/CD,WAAW,CAACjE,GAAG,CAACiE,WAAW,CAACtG,GAAG,CAAC,CAAC,GAAGoG,KAAK,CAACvG,IAAI,CAAC,CAAC0G,SAAS,CAAC;QAC9D,CAAC,CAAC;QACF,IAAI,CAAC5I,aAAa,CAACqD,MAAM,CAAC,CAAC;MAC/B;IACJ,CAAE,CAAC;IACH,OAAO,MAAM;MACTgF,kBAAkB,CAAC,CAAC;MACpBN,mBAAmB,CAAC,CAAC;MACrBG,yBAAyB,CAAC,CAAC;MAC3BK,wBAAwB,IAAIA,wBAAwB,CAAC,CAAC;IAC1D,CAAC;EACL;EACAlH,QAAQA,CAAA,EAAG;IACP,MAAM4F,KAAK,GAAG,IAAI,CAACjH,aAAa,CAACqB,QAAQ,CAAC,CAAC;IAC3C,MAAM;MAAEM,IAAI,GAAG,KAAK;MAAEoB,iBAAiB,GAAG,KAAK;MAAEnB,eAAe,GAAG,KAAK;MAAEgD,eAAe,GAAG,KAAK;MAAEC,WAAW,GAAGlF,cAAc;MAAE4F,YAAY,GAAG;IAAM,CAAC,GAAG0B,KAAK;IAC/J,OAAAhB,aAAA,CAAAA,aAAA,KACOgB,KAAK;MACRtF,IAAI;MACJoB,iBAAiB;MACjBnB,eAAe;MACfgD,eAAe;MACfC,WAAW;MACXU;IAAY;EAEpB;AACJ;AACA,SAAShB,UAAUA,CAACsE,SAAS,EAAElH,IAAI,EAAExB,gBAAgB,EAAE;EACnD,OAAQ,CAACwB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKkH,SAAS,MACvC1I,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK0I,SAAS,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS1F,mBAAmBA,CAACD,MAAM,EAAsB;EAAA,IAApB4F,aAAa,GAAAjI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;EACnD,IAAIgI,SAAS,GAAG,IAAI;EACpB,IAAIE,IAAI,CAACC,GAAG,CAAC9F,MAAM,CAAC5C,CAAC,CAAC,GAAGwI,aAAa,EAAE;IACpCD,SAAS,GAAG,GAAG;EACnB,CAAC,MACI,IAAIE,IAAI,CAACC,GAAG,CAAC9F,MAAM,CAAC7C,CAAC,CAAC,GAAGyI,aAAa,EAAE;IACzCD,SAAS,GAAG,GAAG;EACnB;EACA,OAAOA,SAAS;AACpB;AAEA,SAAS/I,yBAAyB,EAAEF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}