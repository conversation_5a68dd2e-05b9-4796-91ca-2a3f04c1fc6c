{"ast": null, "code": "import $670gB$react, { useContext as $670gB$useContext, useState as $670gB$useState, useMemo as $670gB$useMemo, useLayoutEffect as $670gB$useLayoutEffect, useRef as $670gB$useRef } from \"react\";\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst $b5e257d569688ac6$var$defaultContext = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\nconst $b5e257d569688ac6$var$SSRContext = /*#__PURE__*/(0, $670gB$react).createContext($b5e257d569688ac6$var$defaultContext);\nconst $b5e257d569688ac6$var$IsSSRContext = /*#__PURE__*/(0, $670gB$react).createContext(false);\n// This is only used in React < 18.\nfunction $b5e257d569688ac6$var$LegacySSRProvider(props) {\n  let cur = (0, $670gB$useContext)($b5e257d569688ac6$var$SSRContext);\n  let counter = $b5e257d569688ac6$var$useCounter(cur === $b5e257d569688ac6$var$defaultContext);\n  let [isSSR, setIsSSR] = (0, $670gB$useState)(true);\n  let value = (0, $670gB$useMemo)(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === $b5e257d569688ac6$var$defaultContext ? '' : \"\".concat(cur.prefix, \"-\").concat(counter),\n    current: 0\n  }), [cur, counter]);\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined')\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, $670gB$useLayoutEffect)(() => {\n      setIsSSR(false);\n    }, []);\n  return /*#__PURE__*/(0, $670gB$react).createElement($b5e257d569688ac6$var$SSRContext.Provider, {\n    value: value\n  }, /*#__PURE__*/(0, $670gB$react).createElement($b5e257d569688ac6$var$IsSSRContext.Provider, {\n    value: isSSR\n  }, props.children));\n}\nlet $b5e257d569688ac6$var$warnedAboutSSRProvider = false;\nfunction $b5e257d569688ac6$export$9f8ac96af4b1b2ae(props) {\n  if (typeof (0, $670gB$react)['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !$b5e257d569688ac6$var$warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      $b5e257d569688ac6$var$warnedAboutSSRProvider = true;\n    }\n    return /*#__PURE__*/(0, $670gB$react).createElement((0, $670gB$react).Fragment, null, props.children);\n  }\n  return /*#__PURE__*/(0, $670gB$react).createElement($b5e257d569688ac6$var$LegacySSRProvider, props);\n}\nlet $b5e257d569688ac6$var$canUseDOM = Boolean(typeof window !== 'undefined' && window.document && window.document.createElement);\nlet $b5e257d569688ac6$var$componentIds = new WeakMap();\nfunction $b5e257d569688ac6$var$useCounter() {\n  let isDisabled = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  let ctx = (0, $670gB$useContext)($b5e257d569688ac6$var$SSRContext);\n  let ref = (0, $670gB$useRef)(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    var _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner, _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = (0, $670gB$react).__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === void 0 ? void 0 : (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner = _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner === void 0 ? void 0 : _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner.current;\n    if (currentOwner) {\n      let prevComponentValue = $b5e257d569688ac6$var$componentIds.get(currentOwner);\n      if (prevComponentValue == null)\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        $b5e257d569688ac6$var$componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        $b5e257d569688ac6$var$componentIds.delete(currentOwner);\n      }\n    }\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\nfunction $b5e257d569688ac6$var$useLegacySSRSafeId(defaultId) {\n  let ctx = (0, $670gB$useContext)($b5e257d569688ac6$var$SSRContext);\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === $b5e257d569688ac6$var$defaultContext && !$b5e257d569688ac6$var$canUseDOM && process.env.NODE_ENV !== 'production') console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  let counter = $b5e257d569688ac6$var$useCounter(!!defaultId);\n  let prefix = ctx === $b5e257d569688ac6$var$defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : \"react-aria\".concat(ctx.prefix);\n  return defaultId || \"\".concat(prefix, \"-\").concat(counter);\n}\nfunction $b5e257d569688ac6$var$useModernSSRSafeId(defaultId) {\n  let id = (0, $670gB$react).useId();\n  let [didSSR] = (0, $670gB$useState)($b5e257d569688ac6$export$535bd6ca7f90a273());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : \"react-aria\".concat($b5e257d569688ac6$var$defaultContext.prefix);\n  return defaultId || \"\".concat(prefix, \"-\").concat(id);\n}\nconst $b5e257d569688ac6$export$619500959fc48b26 = typeof (0, $670gB$react)['useId'] === 'function' ? $b5e257d569688ac6$var$useModernSSRSafeId : $b5e257d569688ac6$var$useLegacySSRSafeId;\nfunction $b5e257d569688ac6$var$getSnapshot() {\n  return false;\n}\nfunction $b5e257d569688ac6$var$getServerSnapshot() {\n  return true;\n}\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction $b5e257d569688ac6$var$subscribe(onStoreChange) {\n  // noop\n  return () => {};\n}\nfunction $b5e257d569688ac6$export$535bd6ca7f90a273() {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof (0, $670gB$react)['useSyncExternalStore'] === 'function') return (0, $670gB$react)['useSyncExternalStore']($b5e257d569688ac6$var$subscribe, $b5e257d569688ac6$var$getSnapshot, $b5e257d569688ac6$var$getServerSnapshot);\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return (0, $670gB$useContext)($b5e257d569688ac6$var$IsSSRContext);\n}\nexport { $b5e257d569688ac6$export$9f8ac96af4b1b2ae as SSRProvider, $b5e257d569688ac6$export$535bd6ca7f90a273 as useIsSSR, $b5e257d569688ac6$export$619500959fc48b26 as useSSRSafeId };", "map": {"version": 3, "names": ["$b5e257d569688ac6$var$defaultContext", "prefix", "String", "Math", "round", "random", "current", "$b5e257d569688ac6$var$SSRContext", "$670gB$react", "createContext", "$b5e257d569688ac6$var$IsSSRContext", "$b5e257d569688ac6$var$LegacySSRProvider", "props", "cur", "$670gB$useContext", "counter", "$b5e257d569688ac6$var$useCounter", "isSSR", "setIsSSR", "$670gB$useState", "value", "$670gB$useMemo", "concat", "document", "$670gB$useLayoutEffect", "createElement", "Provider", "children", "$b5e257d569688ac6$var$warnedAboutSSRProvider", "$b5e257d569688ac6$export$9f8ac96af4b1b2ae", "process", "env", "NODE_ENV", "console", "warn", "Fragment", "$b5e257d569688ac6$var$canUseDOM", "Boolean", "window", "$b5e257d569688ac6$var$componentIds", "WeakMap", "isDisabled", "arguments", "length", "undefined", "ctx", "ref", "$670gB$useRef", "_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner", "_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "current<PERSON>wner", "__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED", "ReactCurrentOwner", "prevComponentValue", "get", "set", "id", "state", "memoizedState", "delete", "$b5e257d569688ac6$var$useLegacySSRSafeId", "defaultId", "$b5e257d569688ac6$var$useModernSSRSafeId", "useId", "didSSR", "$b5e257d569688ac6$export$535bd6ca7f90a273", "$b5e257d569688ac6$export$619500959fc48b26", "$b5e257d569688ac6$var$getSnapshot", "$b5e257d569688ac6$var$getServerSnapshot", "$b5e257d569688ac6$var$subscribe", "onStoreChange"], "sources": ["C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\packages\\@react-aria\\ssr\\src\\SSRProvider.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\nimport React, {JSX, ReactNode, useContext, useLayoutEffect, useMemo, useRef, useState} from 'react';\n\n// To support SSR, the auto incrementing id counter is stored in a context. This allows\n// it to be reset on every request to ensure the client and server are consistent.\n// There is also a prefix string that is used to support async loading components\n// Each async boundary must be wrapped in an SSR provider, which appends to the prefix\n// and resets the current id counter. This ensures that async loaded components have\n// consistent ids regardless of the loading order.\ninterface SSRContextValue {\n  prefix: string,\n  current: number\n}\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst defaultContext: SSRContextValue = {\n  prefix: String(Math.round(Math.random() * 10000000000)),\n  current: 0\n};\n\nconst SSRContext = React.createContext<SSRContextValue>(defaultContext);\nconst IsSSRContext = React.createContext(false);\n\nexport interface SSRProviderProps {\n  /** Your application here. */\n  children: ReactNode\n}\n\n// This is only used in React < 18.\nfunction LegacySSRProvider(props: SSRProviderProps): JSX.Element {\n  let cur = useContext(SSRContext);\n  let counter = useCounter(cur === defaultContext);\n  let [isSSR, setIsSSR] = useState(true);\n  let value: SSRContextValue = useMemo(() => ({\n    // If this is the first SSRProvider, start with an empty string prefix, otherwise\n    // append and increment the counter.\n    prefix: cur === defaultContext ? '' : `${cur.prefix}-${counter}`,\n    current: 0\n  }), [cur, counter]);\n\n  // If on the client, and the component was initially server rendered,\n  // then schedule a layout effect to update the component after hydration.\n  if (typeof document !== 'undefined') {\n    // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useLayoutEffect(() => {\n      setIsSSR(false);\n    }, []);\n  }\n\n  return (\n    <SSRContext.Provider value={value}>\n      <IsSSRContext.Provider value={isSSR}>\n        {props.children}\n      </IsSSRContext.Provider>\n    </SSRContext.Provider>\n  );\n}\n\nlet warnedAboutSSRProvider = false;\n\n/**\n * When using SSR with React Aria in React 16 or 17, applications must be wrapped in an SSRProvider.\n * This ensures that auto generated ids are consistent between the client and server.\n */\nexport function SSRProvider(props: SSRProviderProps): JSX.Element {\n  if (typeof React['useId'] === 'function') {\n    if (process.env.NODE_ENV !== 'test' && process.env.NODE_ENV !== 'production' && !warnedAboutSSRProvider) {\n      console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n      warnedAboutSSRProvider = true;\n    }\n    return <>{props.children}</>;\n  }\n  return <LegacySSRProvider {...props} />;\n}\n\nlet canUseDOM = Boolean(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\nlet componentIds = new WeakMap();\n\nfunction useCounter(isDisabled = false) {\n  let ctx = useContext(SSRContext);\n  let ref = useRef<number | null>(null);\n  // eslint-disable-next-line rulesdir/pure-render\n  if (ref.current === null && !isDisabled) {\n    // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n    // This means our id counter will be incremented twice instead of once. This is a problem because on the\n    // server, components are only rendered once and so ids generated on the server won't match the client.\n    // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n    // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n    // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n    // To ensure that we only increment the global counter once, we store the starting id for this component in\n    // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n    // Since React runs the second render immediately after the first, this is safe.\n    // @ts-ignore\n    let currentOwner = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current;\n    if (currentOwner) {\n      let prevComponentValue = componentIds.get(currentOwner);\n      if (prevComponentValue == null) {\n        // On the first render, and first call to useId, store the id and state in our weak map.\n        componentIds.set(currentOwner, {\n          id: ctx.current,\n          state: currentOwner.memoizedState\n        });\n      } else if (currentOwner.memoizedState !== prevComponentValue.state) {\n        // On the second render, the memoizedState gets reset by React.\n        // Reset the counter, and remove from the weak map so we don't\n        // do this for subsequent useId calls.\n        ctx.current = prevComponentValue.id;\n        componentIds.delete(currentOwner);\n      }\n    }\n\n    // eslint-disable-next-line rulesdir/pure-render\n    ref.current = ++ctx.current;\n  }\n\n  // eslint-disable-next-line rulesdir/pure-render\n  return ref.current;\n}\n\nfunction useLegacySSRSafeId(defaultId?: string): string {\n  let ctx = useContext(SSRContext);\n\n  // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n  // provide a warning to hint to the developer to add one.\n  if (ctx === defaultContext && !canUseDOM && process.env.NODE_ENV !== 'production') {\n    console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n  }\n\n  let counter = useCounter(!!defaultId);\n  let prefix = ctx === defaultContext && process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${ctx.prefix}`;\n  return defaultId || `${prefix}-${counter}`;\n}\n\nfunction useModernSSRSafeId(defaultId?: string): string {\n  let id = React.useId();\n  let [didSSR] = useState(useIsSSR());\n  let prefix = didSSR || process.env.NODE_ENV === 'test' ? 'react-aria' : `react-aria${defaultContext.prefix}`;\n  return defaultId || `${prefix}-${id}`;\n}\n\n// Use React.useId in React 18 if available, otherwise fall back to our old implementation.\n/** @private */\nexport const useSSRSafeId = typeof React['useId'] === 'function' ? useModernSSRSafeId : useLegacySSRSafeId;\n\nfunction getSnapshot() {\n  return false;\n}\n\nfunction getServerSnapshot() {\n  return true;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction subscribe(onStoreChange: () => void): () => void {\n  // noop\n  return () => {};\n}\n\n/**\n * Returns whether the component is currently being server side rendered or\n * hydrated on the client. Can be used to delay browser-specific rendering\n * until after hydration.\n */\nexport function useIsSSR(): boolean {\n  // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n  if (typeof React['useSyncExternalStore'] === 'function') {\n    return React['useSyncExternalStore'](subscribe, getSnapshot, getServerSnapshot);\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(IsSSRContext);\n}\n"], "mappings": ";;AAAA;;;;;;;;;;GAAA,CAYA;AACA;AACA;;AAcA;AACA;AACA;AACA;AACA;AACA,MAAMA,oCAAA,GAAkC;EACtCC,MAAA,EAAQC,MAAA,CAAOC,IAAA,CAAKC,KAAK,CAACD,IAAA,CAAKE,MAAM,KAAK;EAC1CC,OAAA,EAAS;AACX;AAEA,MAAMC,gCAAA,gBAAa,IAAAC,YAAI,EAAEC,aAAa,CAAkBT,oCAAA;AACxD,MAAMU,kCAAA,gBAAe,IAAAF,YAAI,EAAEC,aAAa,CAAC;AAOzC;AACA,SAASE,wCAAkBC,KAAuB;EAChD,IAAIC,GAAA,GAAM,IAAAC,iBAAS,EAAEP,gCAAA;EACrB,IAAIQ,OAAA,GAAUC,gCAAA,CAAWH,GAAA,KAAQb,oCAAA;EACjC,IAAI,CAACiB,KAAA,EAAOC,QAAA,CAAS,GAAG,IAAAC,eAAO,EAAE;EACjC,IAAIC,KAAA,GAAyB,IAAAC,cAAM,EAAE,OAAO;IAC1C;IACA;IACApB,MAAA,EAAQY,GAAA,KAAQb,oCAAA,GAAiB,QAAAsB,MAAA,CAAQT,GAAA,CAAIZ,MAAM,OAAAqB,MAAA,CAAIP,OAAA,CAAS;IAChET,OAAA,EAAS;EACX,IAAI,CAACO,GAAA,EAAKE,OAAA,CAAQ;EAElB;EACA;EACA,IAAI,OAAOQ,QAAA,KAAa;IACtB;IACA;IACA;IACA,IAAAC,sBAAc,EAAE;MACdN,QAAA,CAAS;IACX,GAAG,EAAE;EAGP,oBACE,IAAAV,YAAA,EAAAiB,aAAA,CAAClB,gCAAA,CAAWmB,QAAQ;IAACN,KAAA,EAAOA;kBAC1B,IAAAZ,YAAA,EAAAiB,aAAA,CAACf,kCAAA,CAAagB,QAAQ;IAACN,KAAA,EAAOH;KAC3BL,KAAA,CAAMe,QAAQ;AAIvB;AAEA,IAAIC,4CAAA,GAAyB;AAMtB,SAASC,0CAAYjB,KAAuB;EACjD,IAAI,OAAO,IAAAJ,YAAI,EAAE,QAAQ,KAAK,YAAY;IACxC,IAAIsB,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,UAAUF,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACJ,4CAAA,EAAwB;MACvGK,OAAA,CAAQC,IAAI,CAAC;MACbN,4CAAA,GAAyB;IAC3B;IACA,oBAAO,IAAApB,YAAA,EAAAiB,aAAA,KAAAjB,YAAA,EAAA2B,QAAA,QAAGvB,KAAA,CAAMe,QAAQ;EAC1B;EACA,oBAAO,IAAAnB,YAAA,EAAAiB,aAAA,CAACd,uCAAA,EAAsBC,KAAA;AAChC;AAEA,IAAIwB,+BAAA,GAAYC,OAAA,CACd,OAAOC,MAAA,KAAW,eAClBA,MAAA,CAAOf,QAAQ,IACfe,MAAA,CAAOf,QAAQ,CAACE,aAAa;AAG/B,IAAIc,kCAAA,GAAe,IAAIC,OAAA;AAEvB,SAASxB,iCAAA,EAA6B;EAAA,IAAlByB,UAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAa,KAAK;EACpC,IAAIG,GAAA,GAAM,IAAA/B,iBAAS,EAAEP,gCAAA;EACrB,IAAIuC,GAAA,GAAM,IAAAC,aAAK,EAAiB;EAChC;EACA,IAAID,GAAA,CAAIxC,OAAO,KAAK,QAAQ,CAACmC,UAAA,EAAY;QAWpBO,2EAAA,EAAAC,yDAAA;IAVnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,YAAA,IAAeD,yDAAA,OAAAzC,YAAI,EAAE2C,kDAAkD,cAAxDF,yDAAA,wBAAAD,2EAAA,GAAAC,yDAAA,CAA0DG,iBAAiB,cAA3EJ,2EAAA,uBAAAA,2EAAA,CAA6E1C,OAAO;IACvG,IAAI4C,YAAA,EAAc;MAChB,IAAIG,kBAAA,GAAqBd,kCAAA,CAAae,GAAG,CAACJ,YAAA;MAC1C,IAAIG,kBAAA,IAAsB;QACxB;QACAd,kCAAA,CAAagB,GAAG,CAACL,YAAA,EAAc;UAC7BM,EAAA,EAAIX,GAAA,CAAIvC,OAAO;UACfmD,KAAA,EAAOP,YAAA,CAAaQ;QACtB,QACK,IAAIR,YAAA,CAAaQ,aAAa,KAAKL,kBAAA,CAAmBI,KAAK,EAAE;QAClE;QACA;QACA;QACAZ,GAAA,CAAIvC,OAAO,GAAG+C,kBAAA,CAAmBG,EAAE;QACnCjB,kCAAA,CAAaoB,MAAM,CAACT,YAAA;MACtB;IACF;IAEA;IACAJ,GAAA,CAAIxC,OAAO,GAAG,EAAEuC,GAAA,CAAIvC,OAAO;EAC7B;EAEA;EACA,OAAOwC,GAAA,CAAIxC,OAAO;AACpB;AAEA,SAASsD,yCAAmBC,SAAkB;EAC5C,IAAIhB,GAAA,GAAM,IAAA/B,iBAAS,EAAEP,gCAAA;EAErB;EACA;EACA,IAAIsC,GAAA,KAAQ7C,oCAAA,IAAkB,CAACoC,+BAAA,IAAaN,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,cACnEC,OAAA,CAAQC,IAAI,CAAC;EAGf,IAAInB,OAAA,GAAUC,gCAAA,CAAW,CAAC,CAAC6C,SAAA;EAC3B,IAAI5D,MAAA,GAAS4C,GAAA,KAAQ7C,oCAAA,IAAkB8B,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,SAAS,4BAAAV,MAAA,CAA4BuB,GAAA,CAAI5C,MAAM,CAAE;EACjH,OAAO4D,SAAA,OAAAvC,MAAA,CAAgBrB,MAAA,OAAAqB,MAAA,CAAUP,OAAA,CAAS;AAC5C;AAEA,SAAS+C,yCAAmBD,SAAkB;EAC5C,IAAIL,EAAA,GAAK,IAAAhD,YAAI,EAAEuD,KAAK;EACpB,IAAI,CAACC,MAAA,CAAO,GAAG,IAAA7C,eAAO,EAAE8C,yCAAA;EACxB,IAAIhE,MAAA,GAAS+D,MAAA,IAAUlC,OAAA,CAAQC,GAAG,CAACC,QAAQ,KAAK,SAAS,4BAAAV,MAAA,CAA4BtB,oCAAA,CAAeC,MAAM,CAAE;EAC5G,OAAO4D,SAAA,OAAAvC,MAAA,CAAgBrB,MAAA,OAAAqB,MAAA,CAAUkC,EAAA,CAAI;AACvC;AAIO,MAAMU,yCAAA,GAAe,OAAO,IAAA1D,YAAI,EAAE,QAAQ,KAAK,aAAasD,wCAAA,GAAqBF,wCAAA;AAExF,SAASO,kCAAA;EACP,OAAO;AACT;AAEA,SAASC,wCAAA;EACP,OAAO;AACT;AAEA;AACA,SAASC,gCAAUC,aAAyB;EAC1C;EACA,OAAO,OAAO;AAChB;AAOO,SAASL,0CAAA;EACd;EACA,IAAI,OAAO,IAAAzD,YAAI,EAAE,uBAAuB,KAAK,YAC3C,OAAO,IAAAA,YAAI,EAAE,uBAAuB,CAAC6D,+BAAA,EAAWF,iCAAA,EAAaC,uCAAA;EAG/D;EACA,OAAO,IAAAtD,iBAAS,EAAEJ,kCAAA;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}