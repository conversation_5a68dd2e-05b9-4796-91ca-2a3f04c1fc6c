[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts": "17", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityRegister.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityStudentDashboard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityXeroxDashboard.tsx": "20"}, {"size": 771, "mtime": 1749882534472, "results": "21", "hashOfConfig": "22"}, {"size": 425, "mtime": 1749882098393, "results": "23", "hashOfConfig": "22"}, {"size": 2423, "mtime": 1750064028262, "results": "24", "hashOfConfig": "22"}, {"size": 4915, "mtime": 1749883065172, "results": "25", "hashOfConfig": "22"}, {"size": 3438, "mtime": 1749882354078, "results": "26", "hashOfConfig": "22"}, {"size": 10390, "mtime": 1750052659783, "results": "27", "hashOfConfig": "22"}, {"size": 18864, "mtime": 1750052673200, "results": "28", "hashOfConfig": "22"}, {"size": 39028, "mtime": 1750059496440, "results": "29", "hashOfConfig": "22"}, {"size": 23512, "mtime": 1750062802281, "results": "30", "hashOfConfig": "22"}, {"size": 2873, "mtime": 1749893029450, "results": "31", "hashOfConfig": "22"}, {"size": 3587, "mtime": 1750063887582, "results": "32", "hashOfConfig": "22"}, {"size": 10026, "mtime": 1750063875065, "results": "33", "hashOfConfig": "22"}, {"size": 3741, "mtime": 1750062869460, "results": "34", "hashOfConfig": "22"}, {"size": 9433, "mtime": 1750054614147, "results": "35", "hashOfConfig": "22"}, {"size": 6413, "mtime": 1750054575361, "results": "36", "hashOfConfig": "22"}, {"size": 11570, "mtime": 1750054533527, "results": "37", "hashOfConfig": "22"}, {"size": 169, "mtime": 1750054541955, "results": "38", "hashOfConfig": "22"}, {"size": 21234, "mtime": 1750065646802, "results": "39", "hashOfConfig": "22"}, {"size": 70653, "mtime": 1750066299168, "results": "40", "hashOfConfig": "22"}, {"size": 17112, "mtime": 1750064746426, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", [], ["102"], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", ["103", "104", "105", "106", "107", "108"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx", ["109", "110", "111", "112", "113", "114"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityRegister.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityStudentDashboard.tsx", ["115", "116", "117", "118", "119", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityXeroxDashboard.tsx", ["131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193"], [], {"ruleId": "194", "severity": 1, "message": "195", "line": 76, "column": 6, "nodeType": "196", "endLine": 76, "endColumn": 13, "suggestions": "197", "suppressions": "198"}, {"ruleId": "199", "severity": 1, "message": "200", "line": 2, "column": 91, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 95}, {"ruleId": "199", "severity": 1, "message": "203", "line": 2, "column": 97, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 100}, {"ruleId": "199", "severity": 1, "message": "204", "line": 2, "column": 102, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 113}, {"ruleId": "199", "severity": 1, "message": "205", "line": 2, "column": 115, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 123}, {"ruleId": "199", "severity": 1, "message": "206", "line": 4, "column": 23, "nodeType": "201", "messageId": "202", "endLine": 4, "endColumn": 37}, {"ruleId": "194", "severity": 1, "message": "207", "line": 73, "column": 6, "nodeType": "196", "endLine": 73, "endColumn": 8, "suggestions": "208"}, {"ruleId": "199", "severity": 1, "message": "209", "line": 3, "column": 18, "nodeType": "201", "messageId": "202", "endLine": 3, "endColumn": 33}, {"ruleId": "199", "severity": 1, "message": "210", "line": 21, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 21, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "211", "line": 22, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 22, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "212", "line": 36, "column": 25, "nodeType": "201", "messageId": "202", "endLine": 36, "endColumn": 41}, {"ruleId": "199", "severity": 1, "message": "213", "line": 38, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 38, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "214", "line": 38, "column": 26, "nodeType": "201", "messageId": "202", "endLine": 38, "endColumn": 43}, {"ruleId": "199", "severity": 1, "message": "203", "line": 2, "column": 82, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 85}, {"ruleId": "199", "severity": 1, "message": "215", "line": 13, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 13, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "216", "line": 16, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 16, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "217", "line": 24, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 24, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "218", "line": 25, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 25, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "219", "line": 29, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 29, "endColumn": 6}, {"ruleId": "199", "severity": 1, "message": "220", "line": 32, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 32, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "221", "line": 34, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 34, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "222", "line": 37, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 37, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "223", "line": 40, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 40, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "224", "line": 41, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 41, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "225", "line": 44, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 44, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "226", "line": 45, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 45, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "227", "line": 92, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 92, "endColumn": 27}, {"ruleId": "199", "severity": 1, "message": "228", "line": 92, "column": 29, "nodeType": "201", "messageId": "202", "endLine": 92, "endColumn": 49}, {"ruleId": "199", "severity": 1, "message": "229", "line": 94, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 94, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "230", "line": 2, "column": 21, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "231", "line": 2, "column": 26, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 29}, {"ruleId": "199", "severity": 1, "message": "232", "line": 2, "column": 52, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 56}, {"ruleId": "199", "severity": 1, "message": "233", "line": 2, "column": 58, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 63}, {"ruleId": "199", "severity": 1, "message": "234", "line": 2, "column": 65, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 75}, {"ruleId": "199", "severity": 1, "message": "235", "line": 2, "column": 82, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 87}, {"ruleId": "199", "severity": 1, "message": "204", "line": 2, "column": 89, "nodeType": "201", "messageId": "202", "endLine": 2, "endColumn": 100}, {"ruleId": "199", "severity": 1, "message": "209", "line": 3, "column": 18, "nodeType": "201", "messageId": "202", "endLine": 3, "endColumn": 33}, {"ruleId": "199", "severity": 1, "message": "236", "line": 7, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 7, "endColumn": 6}, {"ruleId": "199", "severity": 1, "message": "210", "line": 9, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 9, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "237", "line": 10, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 10, "endColumn": 16}, {"ruleId": "199", "severity": 1, "message": "238", "line": 12, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 12, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "239", "line": 14, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 14, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "240", "line": 15, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 15, "endColumn": 4}, {"ruleId": "199", "severity": 1, "message": "241", "line": 16, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 16, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "217", "line": 17, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 17, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "242", "line": 18, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 18, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "243", "line": 19, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 19, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "244", "line": 20, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 20, "endColumn": 13}, {"ruleId": "199", "severity": 1, "message": "245", "line": 23, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 23, "endColumn": 10}, {"ruleId": "199", "severity": 1, "message": "218", "line": 26, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 26, "endColumn": 11}, {"ruleId": "199", "severity": 1, "message": "246", "line": 27, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 27, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "247", "line": 29, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 29, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "248", "line": 31, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 31, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "249", "line": 32, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 32, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "219", "line": 33, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 33, "endColumn": 6}, {"ruleId": "199", "severity": 1, "message": "222", "line": 35, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 35, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "250", "line": 38, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 38, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "216", "line": 40, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 40, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "251", "line": 41, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 41, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "225", "line": 42, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 42, "endColumn": 7}, {"ruleId": "199", "severity": 1, "message": "226", "line": 43, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 43, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "224", "line": 44, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 44, "endColumn": 8}, {"ruleId": "199", "severity": 1, "message": "223", "line": 45, "column": 3, "nodeType": "201", "messageId": "202", "endLine": 45, "endColumn": 9}, {"ruleId": "199", "severity": 1, "message": "206", "line": 48, "column": 23, "nodeType": "201", "messageId": "202", "endLine": 48, "endColumn": 37}, {"ruleId": "199", "severity": 1, "message": "252", "line": 73, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 73, "endColumn": 22}, {"ruleId": "199", "severity": 1, "message": "253", "line": 74, "column": 22, "nodeType": "201", "messageId": "202", "endLine": 74, "endColumn": 35}, {"ruleId": "199", "severity": 1, "message": "254", "line": 75, "column": 24, "nodeType": "201", "messageId": "202", "endLine": 75, "endColumn": 39}, {"ruleId": "199", "severity": 1, "message": "255", "line": 76, "column": 26, "nodeType": "201", "messageId": "202", "endLine": 76, "endColumn": 43}, {"ruleId": "199", "severity": 1, "message": "256", "line": 77, "column": 18, "nodeType": "201", "messageId": "202", "endLine": 77, "endColumn": 27}, {"ruleId": "199", "severity": 1, "message": "257", "line": 78, "column": 25, "nodeType": "201", "messageId": "202", "endLine": 78, "endColumn": 41}, {"ruleId": "199", "severity": 1, "message": "258", "line": 79, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 79, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "259", "line": 79, "column": 20, "nodeType": "201", "messageId": "202", "endLine": 79, "endColumn": 31}, {"ruleId": "199", "severity": 1, "message": "260", "line": 80, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 80, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "261", "line": 81, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 81, "endColumn": 29}, {"ruleId": "199", "severity": 1, "message": "262", "line": 81, "column": 31, "nodeType": "201", "messageId": "202", "endLine": 81, "endColumn": 53}, {"ruleId": "199", "severity": 1, "message": "263", "line": 82, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 82, "endColumn": 23}, {"ruleId": "199", "severity": 1, "message": "227", "line": 83, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 83, "endColumn": 27}, {"ruleId": "199", "severity": 1, "message": "228", "line": 83, "column": 29, "nodeType": "201", "messageId": "202", "endLine": 83, "endColumn": 49}, {"ruleId": "199", "severity": 1, "message": "264", "line": 88, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 88, "endColumn": 18}, {"ruleId": "199", "severity": 1, "message": "265", "line": 91, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 91, "endColumn": 15}, {"ruleId": "199", "severity": 1, "message": "266", "line": 102, "column": 10, "nodeType": "201", "messageId": "202", "endLine": 102, "endColumn": 24}, {"ruleId": "199", "severity": 1, "message": "267", "line": 102, "column": 26, "nodeType": "201", "messageId": "202", "endLine": 102, "endColumn": 43}, {"ruleId": "194", "severity": 1, "message": "268", "line": 142, "column": 6, "nodeType": "196", "endLine": 142, "endColumn": 77, "suggestions": "269"}, {"ruleId": "199", "severity": 1, "message": "270", "line": 216, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 216, "endColumn": 23}, {"ruleId": "199", "severity": 1, "message": "271", "line": 241, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 241, "endColumn": 25}, {"ruleId": "199", "severity": 1, "message": "272", "line": 258, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 258, "endColumn": 27}, {"ruleId": "199", "severity": 1, "message": "273", "line": 267, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 267, "endColumn": 27}, {"ruleId": "199", "severity": 1, "message": "274", "line": 284, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 284, "endColumn": 23}, {"ruleId": "199", "severity": 1, "message": "275", "line": 296, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 296, "endColumn": 26}, {"ruleId": "199", "severity": 1, "message": "276", "line": 311, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 311, "endColumn": 26}, {"ruleId": "199", "severity": 1, "message": "277", "line": 323, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 323, "endColumn": 19}, {"ruleId": "199", "severity": 1, "message": "278", "line": 333, "column": 9, "nodeType": "201", "messageId": "202", "endLine": 333, "endColumn": 23}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["279"], ["280"], "@typescript-eslint/no-unused-vars", "'Tabs' is defined but never used.", "Identifier", "unusedVar", "'Tab' is defined but never used.", "'ProgressBar' is defined but never used.", "'Dropdown' is defined but never used.", "'xeroxCenterApi' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshInterval'. Either include it or remove the dependency array.", ["281"], "'AnimatePresence' is defined but never used.", "'Download' is defined but never used.", "'Clock' is defined but never used.", "'setNotifications' is assigned a value but never used.", "'activeDropdown' is assigned a value but never used.", "'setActiveDropdown' is assigned a value but never used.", "'Plus' is defined but never used.", "'MapPin' is defined but never used.", "'Filter' is defined but never used.", "'Calendar' is defined but never used.", "'Zap' is defined but never used.", "'Bookmark' is defined but never used.", "'Share2' is defined but never used.", "'Info' is defined but never used.", "'Trash2' is defined but never used.", "'Edit3' is defined but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'showSettingsModal' is assigned a value but never used.", "'setShowSettingsModal' is assigned a value but never used.", "'selectedCenter' is assigned a value but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Form' is defined but never used.", "'Modal' is defined but never used.", "'InputGroup' is defined but never used.", "'Table' is defined but never used.", "'Cog' is defined but never used.", "'MessageCircle' is defined but never used.", "'Play' is defined but never used.", "'Truck' is defined but never used.", "'X' is defined but never used.", "'Search' is defined but never used.", "'Grid3X3' is defined but never used.", "'List' is defined but never used.", "'TrendingUp' is defined but never used.", "'Printer' is defined but never used.", "'Star' is defined but never used.", "'Bell' is defined but never used.", "'Target' is defined but never used.", "'Award' is defined but never used.", "'Send' is defined but never used.", "'Phone' is defined but never used.", "'filteredJobs' is assigned a value but never used.", "'setSearchTerm' is assigned a value but never used.", "'setStatusFilter' is assigned a value but never used.", "'setPriorityFilter' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setSortDirection' is assigned a value but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'showQuoteModal' is assigned a value but never used.", "'showJobDetailsModal' is assigned a value but never used.", "'setShowJobDetailsModal' is assigned a value but never used.", "'showChatModal' is assigned a value but never used.", "'messages' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'centerSettings' is assigned a value but never used.", "'setCenterSettings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterAndSortJobs'. Either include it or remove the dependency array.", ["282"], "'getStatusBadge' is assigned a value but never used.", "'getPriorityBadge' is assigned a value but never used.", "'handleStatusUpdate' is assigned a value but never used.", "'handleDownloadFile' is assigned a value but never used.", "'handleOpenChat' is assigned a value but never used.", "'handleSubmitQuote' is assigned a value but never used.", "'handleSendMessage' is assigned a value but never used.", "'getTimeAgo' is assigned a value but never used.", "'formatFileSize' is assigned a value but never used.", {"desc": "283", "fix": "284"}, {"kind": "285", "justification": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "291", "text": "292"}, "directive", "", "Update the dependencies array to be: [refreshInterval]", {"range": "293", "text": "294"}, "Update the dependencies array to be: [jobs, searchTerm, statusFilter, priorityFilter, sortBy, sortDirection, filterAndSortJobs]", {"range": "295", "text": "296"}, [2058, 2065], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]", [2552, 2554], "[refreshInterval]", [3493, 3564], "[jobs, searchTerm, statusFilter, priorityFilter, sortBy, sortDirection, filterAndSortJobs]"]