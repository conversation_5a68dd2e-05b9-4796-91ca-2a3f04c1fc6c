[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts": "10"}, {"size": 771, "mtime": 1749882534472, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1749882098393, "results": "13", "hashOfConfig": "12"}, {"size": 2049, "mtime": 1749882321465, "results": "14", "hashOfConfig": "12"}, {"size": 4915, "mtime": 1749883065172, "results": "15", "hashOfConfig": "12"}, {"size": 3438, "mtime": 1749882354078, "results": "16", "hashOfConfig": "12"}, {"size": 3870, "mtime": 1749882371894, "results": "17", "hashOfConfig": "12"}, {"size": 15188, "mtime": 1749883578004, "results": "18", "hashOfConfig": "12"}, {"size": 39028, "mtime": 1750051579980, "results": "19", "hashOfConfig": "12"}, {"size": 23512, "mtime": 1749894169515, "results": "20", "hashOfConfig": "12"}, {"size": 2873, "mtime": 1749893029450, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", [], ["52"], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", ["53", "54", "55", "56", "57", "58"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts", [], [], {"ruleId": "59", "severity": 1, "message": "60", "line": 76, "column": 6, "nodeType": "61", "endLine": 76, "endColumn": 13, "suggestions": "62", "suppressions": "63"}, {"ruleId": "64", "severity": 1, "message": "65", "line": 2, "column": 91, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 95}, {"ruleId": "64", "severity": 1, "message": "68", "line": 2, "column": 97, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 100}, {"ruleId": "64", "severity": 1, "message": "69", "line": 2, "column": 102, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 113}, {"ruleId": "64", "severity": 1, "message": "70", "line": 2, "column": 115, "nodeType": "66", "messageId": "67", "endLine": 2, "endColumn": 123}, {"ruleId": "64", "severity": 1, "message": "71", "line": 4, "column": 23, "nodeType": "66", "messageId": "67", "endLine": 4, "endColumn": 37}, {"ruleId": "59", "severity": 1, "message": "72", "line": 73, "column": 6, "nodeType": "61", "endLine": 73, "endColumn": 8, "suggestions": "73"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["74"], ["75"], "@typescript-eslint/no-unused-vars", "'Tabs' is defined but never used.", "Identifier", "unusedVar", "'Tab' is defined but never used.", "'ProgressBar' is defined but never used.", "'Dropdown' is defined but never used.", "'xeroxCenterApi' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshInterval'. Either include it or remove the dependency array.", ["76"], {"desc": "77", "fix": "78"}, {"kind": "79", "justification": "80"}, {"desc": "81", "fix": "82"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "83", "text": "84"}, "directive", "", "Update the dependencies array to be: [refreshInterval]", {"range": "85", "text": "86"}, [2058, 2065], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]", [2552, 2554], "[refreshInterval]"]