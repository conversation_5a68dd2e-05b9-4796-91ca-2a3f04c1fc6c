[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx": "13"}, {"size": 771, "mtime": 1749882534472, "results": "14", "hashOfConfig": "15"}, {"size": 425, "mtime": 1749882098393, "results": "16", "hashOfConfig": "15"}, {"size": 2212, "mtime": 1750051972287, "results": "17", "hashOfConfig": "15"}, {"size": 4915, "mtime": 1749883065172, "results": "18", "hashOfConfig": "15"}, {"size": 3438, "mtime": 1749882354078, "results": "19", "hashOfConfig": "15"}, {"size": 10390, "mtime": 1750052659783, "results": "20", "hashOfConfig": "15"}, {"size": 18864, "mtime": 1750052673200, "results": "21", "hashOfConfig": "15"}, {"size": 39028, "mtime": 1750051579980, "results": "22", "hashOfConfig": "15"}, {"size": 23512, "mtime": 1749894169515, "results": "23", "hashOfConfig": "15"}, {"size": 2873, "mtime": 1749893029450, "results": "24", "hashOfConfig": "15"}, {"size": 3579, "mtime": 1750052715367, "results": "25", "hashOfConfig": "15"}, {"size": 10026, "mtime": 1750052579262, "results": "26", "hashOfConfig": "15"}, {"size": 3741, "mtime": 1750051853422, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", [], ["67"], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", ["68", "69", "70", "71", "72", "73"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx", ["74", "75", "76", "77", "78", "79"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 76, "column": 6, "nodeType": "82", "endLine": 76, "endColumn": 13, "suggestions": "83", "suppressions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 2, "column": 91, "nodeType": "87", "messageId": "88", "endLine": 2, "endColumn": 95}, {"ruleId": "85", "severity": 1, "message": "89", "line": 2, "column": 97, "nodeType": "87", "messageId": "88", "endLine": 2, "endColumn": 100}, {"ruleId": "85", "severity": 1, "message": "90", "line": 2, "column": 102, "nodeType": "87", "messageId": "88", "endLine": 2, "endColumn": 113}, {"ruleId": "85", "severity": 1, "message": "91", "line": 2, "column": 115, "nodeType": "87", "messageId": "88", "endLine": 2, "endColumn": 123}, {"ruleId": "85", "severity": 1, "message": "92", "line": 4, "column": 23, "nodeType": "87", "messageId": "88", "endLine": 4, "endColumn": 37}, {"ruleId": "80", "severity": 1, "message": "93", "line": 73, "column": 6, "nodeType": "82", "endLine": 73, "endColumn": 8, "suggestions": "94"}, {"ruleId": "85", "severity": 1, "message": "95", "line": 3, "column": 18, "nodeType": "87", "messageId": "88", "endLine": 3, "endColumn": 33}, {"ruleId": "85", "severity": 1, "message": "96", "line": 21, "column": 3, "nodeType": "87", "messageId": "88", "endLine": 21, "endColumn": 11}, {"ruleId": "85", "severity": 1, "message": "97", "line": 22, "column": 3, "nodeType": "87", "messageId": "88", "endLine": 22, "endColumn": 8}, {"ruleId": "85", "severity": 1, "message": "98", "line": 36, "column": 25, "nodeType": "87", "messageId": "88", "endLine": 36, "endColumn": 41}, {"ruleId": "85", "severity": 1, "message": "99", "line": 38, "column": 10, "nodeType": "87", "messageId": "88", "endLine": 38, "endColumn": 24}, {"ruleId": "85", "severity": 1, "message": "100", "line": 38, "column": 26, "nodeType": "87", "messageId": "88", "endLine": 38, "endColumn": 43}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["101"], ["102"], "@typescript-eslint/no-unused-vars", "'Tabs' is defined but never used.", "Identifier", "unusedVar", "'Tab' is defined but never used.", "'ProgressBar' is defined but never used.", "'Dropdown' is defined but never used.", "'xeroxCenterApi' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshInterval'. Either include it or remove the dependency array.", ["103"], "'AnimatePresence' is defined but never used.", "'Download' is defined but never used.", "'Clock' is defined but never used.", "'setNotifications' is assigned a value but never used.", "'activeDropdown' is assigned a value but never used.", "'setActiveDropdown' is assigned a value but never used.", {"desc": "104", "fix": "105"}, {"kind": "106", "justification": "107"}, {"desc": "108", "fix": "109"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "110", "text": "111"}, "directive", "", "Update the dependencies array to be: [refreshInterval]", {"range": "112", "text": "113"}, [2058, 2065], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]", [2552, 2554], "[refreshInterval]"]