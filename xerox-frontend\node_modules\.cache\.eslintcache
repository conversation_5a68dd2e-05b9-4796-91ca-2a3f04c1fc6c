[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts": "17"}, {"size": 771, "mtime": 1749882534472, "results": "18", "hashOfConfig": "19"}, {"size": 425, "mtime": 1749882098393, "results": "20", "hashOfConfig": "19"}, {"size": 2212, "mtime": 1750058023900, "results": "21", "hashOfConfig": "19"}, {"size": 4915, "mtime": 1749883065172, "results": "22", "hashOfConfig": "19"}, {"size": 3438, "mtime": 1749882354078, "results": "23", "hashOfConfig": "19"}, {"size": 10390, "mtime": 1750052659783, "results": "24", "hashOfConfig": "19"}, {"size": 18864, "mtime": 1750052673200, "results": "25", "hashOfConfig": "19"}, {"size": 39028, "mtime": 1750059496440, "results": "26", "hashOfConfig": "19"}, {"size": 23512, "mtime": 1750062802281, "results": "27", "hashOfConfig": "19"}, {"size": 2873, "mtime": 1749893029450, "results": "28", "hashOfConfig": "19"}, {"size": 3587, "mtime": 1750063887582, "results": "29", "hashOfConfig": "19"}, {"size": 10026, "mtime": 1750063875065, "results": "30", "hashOfConfig": "19"}, {"size": 3741, "mtime": 1750062869460, "results": "31", "hashOfConfig": "19"}, {"size": 9433, "mtime": 1750054614147, "results": "32", "hashOfConfig": "19"}, {"size": 6413, "mtime": 1750054575361, "results": "33", "hashOfConfig": "19"}, {"size": 11570, "mtime": 1750054533527, "results": "34", "hashOfConfig": "19"}, {"size": 169, "mtime": 1750054541955, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", [], ["87"], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", ["88", "89", "90", "91", "92", "93"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx", ["94", "95", "96", "97", "98", "99"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts", [], [], {"ruleId": "100", "severity": 1, "message": "101", "line": 76, "column": 6, "nodeType": "102", "endLine": 76, "endColumn": 13, "suggestions": "103", "suppressions": "104"}, {"ruleId": "105", "severity": 1, "message": "106", "line": 2, "column": 91, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 95}, {"ruleId": "105", "severity": 1, "message": "109", "line": 2, "column": 97, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 100}, {"ruleId": "105", "severity": 1, "message": "110", "line": 2, "column": 102, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 113}, {"ruleId": "105", "severity": 1, "message": "111", "line": 2, "column": 115, "nodeType": "107", "messageId": "108", "endLine": 2, "endColumn": 123}, {"ruleId": "105", "severity": 1, "message": "112", "line": 4, "column": 23, "nodeType": "107", "messageId": "108", "endLine": 4, "endColumn": 37}, {"ruleId": "100", "severity": 1, "message": "113", "line": 73, "column": 6, "nodeType": "102", "endLine": 73, "endColumn": 8, "suggestions": "114"}, {"ruleId": "105", "severity": 1, "message": "115", "line": 3, "column": 18, "nodeType": "107", "messageId": "108", "endLine": 3, "endColumn": 33}, {"ruleId": "105", "severity": 1, "message": "116", "line": 21, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 21, "endColumn": 11}, {"ruleId": "105", "severity": 1, "message": "117", "line": 22, "column": 3, "nodeType": "107", "messageId": "108", "endLine": 22, "endColumn": 8}, {"ruleId": "105", "severity": 1, "message": "118", "line": 36, "column": 25, "nodeType": "107", "messageId": "108", "endLine": 36, "endColumn": 41}, {"ruleId": "105", "severity": 1, "message": "119", "line": 38, "column": 10, "nodeType": "107", "messageId": "108", "endLine": 38, "endColumn": 24}, {"ruleId": "105", "severity": 1, "message": "120", "line": 38, "column": 26, "nodeType": "107", "messageId": "108", "endLine": 38, "endColumn": 43}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["121"], ["122"], "@typescript-eslint/no-unused-vars", "'Tabs' is defined but never used.", "Identifier", "unusedVar", "'Tab' is defined but never used.", "'ProgressBar' is defined but never used.", "'Dropdown' is defined but never used.", "'xeroxCenterApi' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshInterval'. Either include it or remove the dependency array.", ["123"], "'AnimatePresence' is defined but never used.", "'Download' is defined but never used.", "'Clock' is defined but never used.", "'setNotifications' is assigned a value but never used.", "'activeDropdown' is assigned a value but never used.", "'setActiveDropdown' is assigned a value but never used.", {"desc": "124", "fix": "125"}, {"kind": "126", "justification": "127"}, {"desc": "128", "fix": "129"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "130", "text": "131"}, "directive", "", "Update the dependencies array to be: [refreshInterval]", {"range": "132", "text": "133"}, [2058, 2065], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]", [2552, 2554], "[refreshInterval]"]