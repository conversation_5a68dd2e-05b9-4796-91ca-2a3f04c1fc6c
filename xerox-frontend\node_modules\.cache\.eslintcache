[{"C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts": "10", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts": "17", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityRegister.tsx": "18", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityStudentDashboard.tsx": "19", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityXeroxDashboard.tsx": "20", "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\SimpleAceternityStudentDashboard.tsx": "21"}, {"size": 771, "mtime": 1749882534472, "results": "22", "hashOfConfig": "23"}, {"size": 425, "mtime": 1749882098393, "results": "24", "hashOfConfig": "23"}, {"size": 2200, "mtime": 1750067416577, "results": "25", "hashOfConfig": "23"}, {"size": 4915, "mtime": 1749883065172, "results": "26", "hashOfConfig": "23"}, {"size": 3438, "mtime": 1749882354078, "results": "27", "hashOfConfig": "23"}, {"size": 10390, "mtime": 1750052659783, "results": "28", "hashOfConfig": "23"}, {"size": 18864, "mtime": 1750052673200, "results": "29", "hashOfConfig": "23"}, {"size": 39028, "mtime": 1750059496440, "results": "30", "hashOfConfig": "23"}, {"size": 23512, "mtime": 1750062802281, "results": "31", "hashOfConfig": "23"}, {"size": 2873, "mtime": 1749893029450, "results": "32", "hashOfConfig": "23"}, {"size": 3587, "mtime": 1750063887582, "results": "33", "hashOfConfig": "23"}, {"size": 10026, "mtime": 1750063875065, "results": "34", "hashOfConfig": "23"}, {"size": 3741, "mtime": 1750062869460, "results": "35", "hashOfConfig": "23"}, {"size": 9433, "mtime": 1750054614147, "results": "36", "hashOfConfig": "23"}, {"size": 6413, "mtime": 1750054575361, "results": "37", "hashOfConfig": "23"}, {"size": 11570, "mtime": 1750054533527, "results": "38", "hashOfConfig": "23"}, {"size": 169, "mtime": 1750054541955, "results": "39", "hashOfConfig": "23"}, {"size": 21234, "mtime": 1750065646802, "results": "40", "hashOfConfig": "23"}, {"size": 70811, "mtime": 1750067014478, "results": "41", "hashOfConfig": "23"}, {"size": 17112, "mtime": 1750064746426, "results": "42", "hashOfConfig": "23"}, {"size": 21552, "mtime": 1750067380305, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1gad31y", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 63, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\App.tsx", ["107"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\AuthContext.tsx", [], ["108"], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Login.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Register.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\XeroxCenterDashboard.tsx", ["109", "110", "111", "112", "113", "114"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\StudentDashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\services\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ProfessionalNavbar.tsx", ["115", "116", "117", "118", "119", "120"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\contexts\\ThemeContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLogin.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityNavbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityRegister.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityStudentDashboard.tsx", ["121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\AceternityXeroxDashboard.tsx", ["137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199"], [], "C:\\Users\\<USER>\\Desktop\\Xerox Module\\xerox-frontend\\src\\components\\ui\\SimpleAceternityStudentDashboard.tsx", ["200", "201"], [], {"ruleId": "202", "severity": 1, "message": "203", "line": 5, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 5, "endColumn": 24}, {"ruleId": "206", "severity": 1, "message": "207", "line": 76, "column": 6, "nodeType": "208", "endLine": 76, "endColumn": 13, "suggestions": "209", "suppressions": "210"}, {"ruleId": "202", "severity": 1, "message": "211", "line": 2, "column": 91, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 95}, {"ruleId": "202", "severity": 1, "message": "212", "line": 2, "column": 97, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 100}, {"ruleId": "202", "severity": 1, "message": "213", "line": 2, "column": 102, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 113}, {"ruleId": "202", "severity": 1, "message": "214", "line": 2, "column": 115, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 123}, {"ruleId": "202", "severity": 1, "message": "215", "line": 4, "column": 23, "nodeType": "204", "messageId": "205", "endLine": 4, "endColumn": 37}, {"ruleId": "206", "severity": 1, "message": "216", "line": 73, "column": 6, "nodeType": "208", "endLine": 73, "endColumn": 8, "suggestions": "217"}, {"ruleId": "202", "severity": 1, "message": "218", "line": 3, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 3, "endColumn": 33}, {"ruleId": "202", "severity": 1, "message": "219", "line": 21, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 21, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "220", "line": 22, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 22, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "221", "line": 36, "column": 25, "nodeType": "204", "messageId": "205", "endLine": 36, "endColumn": 41}, {"ruleId": "202", "severity": 1, "message": "222", "line": 38, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 38, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "223", "line": 38, "column": 26, "nodeType": "204", "messageId": "205", "endLine": 38, "endColumn": 43}, {"ruleId": "202", "severity": 1, "message": "212", "line": 2, "column": 82, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 85}, {"ruleId": "202", "severity": 1, "message": "224", "line": 13, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 13, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "225", "line": 16, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 16, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "226", "line": 24, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 24, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "227", "line": 25, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 25, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "228", "line": 29, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 29, "endColumn": 6}, {"ruleId": "202", "severity": 1, "message": "229", "line": 32, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 32, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "230", "line": 34, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 34, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "231", "line": 37, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 37, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "232", "line": 40, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 40, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "233", "line": 41, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 41, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "234", "line": 44, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 44, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "235", "line": 45, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 45, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "236", "line": 92, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 92, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "237", "line": 92, "column": 29, "nodeType": "204", "messageId": "205", "endLine": 92, "endColumn": 49}, {"ruleId": "202", "severity": 1, "message": "238", "line": 94, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 94, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "239", "line": 2, "column": 21, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "240", "line": 2, "column": 26, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 29}, {"ruleId": "202", "severity": 1, "message": "241", "line": 2, "column": 52, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 56}, {"ruleId": "202", "severity": 1, "message": "242", "line": 2, "column": 58, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 63}, {"ruleId": "202", "severity": 1, "message": "243", "line": 2, "column": 65, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 75}, {"ruleId": "202", "severity": 1, "message": "244", "line": 2, "column": 82, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 87}, {"ruleId": "202", "severity": 1, "message": "213", "line": 2, "column": 89, "nodeType": "204", "messageId": "205", "endLine": 2, "endColumn": 100}, {"ruleId": "202", "severity": 1, "message": "218", "line": 3, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 3, "endColumn": 33}, {"ruleId": "202", "severity": 1, "message": "245", "line": 7, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 7, "endColumn": 6}, {"ruleId": "202", "severity": 1, "message": "219", "line": 9, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 9, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "246", "line": 10, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 10, "endColumn": 16}, {"ruleId": "202", "severity": 1, "message": "247", "line": 12, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 12, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "248", "line": 14, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 14, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "249", "line": 15, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 15, "endColumn": 4}, {"ruleId": "202", "severity": 1, "message": "250", "line": 16, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 16, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "226", "line": 17, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 17, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "251", "line": 18, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 18, "endColumn": 10}, {"ruleId": "202", "severity": 1, "message": "252", "line": 19, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 19, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "253", "line": 20, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 20, "endColumn": 13}, {"ruleId": "202", "severity": 1, "message": "254", "line": 23, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 23, "endColumn": 10}, {"ruleId": "202", "severity": 1, "message": "227", "line": 26, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 26, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "255", "line": 27, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 27, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "256", "line": 29, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 29, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "257", "line": 31, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 31, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "258", "line": 32, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 32, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "228", "line": 33, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 33, "endColumn": 6}, {"ruleId": "202", "severity": 1, "message": "231", "line": 35, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 35, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "259", "line": 38, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 38, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "225", "line": 40, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 40, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "260", "line": 41, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 41, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "234", "line": 42, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 42, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "235", "line": 43, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 43, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "233", "line": 44, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 44, "endColumn": 8}, {"ruleId": "202", "severity": 1, "message": "232", "line": 45, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 45, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "215", "line": 48, "column": 23, "nodeType": "204", "messageId": "205", "endLine": 48, "endColumn": 37}, {"ruleId": "202", "severity": 1, "message": "261", "line": 73, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 73, "endColumn": 22}, {"ruleId": "202", "severity": 1, "message": "262", "line": 74, "column": 22, "nodeType": "204", "messageId": "205", "endLine": 74, "endColumn": 35}, {"ruleId": "202", "severity": 1, "message": "263", "line": 75, "column": 24, "nodeType": "204", "messageId": "205", "endLine": 75, "endColumn": 39}, {"ruleId": "202", "severity": 1, "message": "264", "line": 76, "column": 26, "nodeType": "204", "messageId": "205", "endLine": 76, "endColumn": 43}, {"ruleId": "202", "severity": 1, "message": "265", "line": 77, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 77, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "266", "line": 78, "column": 25, "nodeType": "204", "messageId": "205", "endLine": 78, "endColumn": 41}, {"ruleId": "202", "severity": 1, "message": "267", "line": 79, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 79, "endColumn": 18}, {"ruleId": "202", "severity": 1, "message": "268", "line": 79, "column": 20, "nodeType": "204", "messageId": "205", "endLine": 79, "endColumn": 31}, {"ruleId": "202", "severity": 1, "message": "269", "line": 80, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 80, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "270", "line": 81, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 81, "endColumn": 29}, {"ruleId": "202", "severity": 1, "message": "271", "line": 81, "column": 31, "nodeType": "204", "messageId": "205", "endLine": 81, "endColumn": 53}, {"ruleId": "202", "severity": 1, "message": "272", "line": 82, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 82, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "236", "line": 83, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 83, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "237", "line": 83, "column": 29, "nodeType": "204", "messageId": "205", "endLine": 83, "endColumn": 49}, {"ruleId": "202", "severity": 1, "message": "273", "line": 88, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 88, "endColumn": 18}, {"ruleId": "202", "severity": 1, "message": "274", "line": 91, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 91, "endColumn": 15}, {"ruleId": "202", "severity": 1, "message": "275", "line": 102, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 102, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "276", "line": 102, "column": 26, "nodeType": "204", "messageId": "205", "endLine": 102, "endColumn": 43}, {"ruleId": "206", "severity": 1, "message": "277", "line": 142, "column": 6, "nodeType": "208", "endLine": 142, "endColumn": 77, "suggestions": "278"}, {"ruleId": "202", "severity": 1, "message": "279", "line": 216, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 216, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "280", "line": 241, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 241, "endColumn": 25}, {"ruleId": "202", "severity": 1, "message": "281", "line": 258, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 258, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "282", "line": 267, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 267, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "283", "line": 284, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 284, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "284", "line": 296, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 296, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "285", "line": 311, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 311, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "286", "line": 323, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 323, "endColumn": 19}, {"ruleId": "202", "severity": 1, "message": "287", "line": 333, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 333, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "255", "line": 15, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 15, "endColumn": 7}, {"ruleId": "202", "severity": 1, "message": "288", "line": 55, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 55, "endColumn": 22}, "@typescript-eslint/no-unused-vars", "'AceternityLayout' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCurrentUser'. Either include it or remove the dependency array.", "ArrayExpression", ["289"], ["290"], "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'ProgressBar' is defined but never used.", "'Dropdown' is defined but never used.", "'xeroxCenterApi' is defined but never used.", "React Hook useEffect has a missing dependency: 'refreshInterval'. Either include it or remove the dependency array.", ["291"], "'AnimatePresence' is defined but never used.", "'Download' is defined but never used.", "'Clock' is defined but never used.", "'setNotifications' is assigned a value but never used.", "'activeDropdown' is assigned a value but never used.", "'setActiveDropdown' is assigned a value but never used.", "'Plus' is defined but never used.", "'MapPin' is defined but never used.", "'Filter' is defined but never used.", "'Calendar' is defined but never used.", "'Zap' is defined but never used.", "'Bookmark' is defined but never used.", "'Share2' is defined but never used.", "'Info' is defined but never used.", "'Trash2' is defined but never used.", "'Edit3' is defined but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'showSettingsModal' is assigned a value but never used.", "'setShowSettingsModal' is assigned a value but never used.", "'selectedCenter' is assigned a value but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Form' is defined but never used.", "'Modal' is defined but never used.", "'InputGroup' is defined but never used.", "'Table' is defined but never used.", "'Cog' is defined but never used.", "'MessageCircle' is defined but never used.", "'Play' is defined but never used.", "'Truck' is defined but never used.", "'X' is defined but never used.", "'Search' is defined but never used.", "'Grid3X3' is defined but never used.", "'List' is defined but never used.", "'TrendingUp' is defined but never used.", "'Printer' is defined but never used.", "'Star' is defined but never used.", "'Bell' is defined but never used.", "'Target' is defined but never used.", "'Award' is defined but never used.", "'Send' is defined but never used.", "'Phone' is defined but never used.", "'filteredJobs' is assigned a value but never used.", "'setSearchTerm' is assigned a value but never used.", "'setStatusFilter' is assigned a value but never used.", "'setPriorityFilter' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setSortDirection' is assigned a value but never used.", "'viewMode' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", "'showQuoteModal' is assigned a value but never used.", "'showJobDetailsModal' is assigned a value but never used.", "'setShowJobDetailsModal' is assigned a value but never used.", "'showChatModal' is assigned a value but never used.", "'messages' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'centerSettings' is assigned a value but never used.", "'setCenterSettings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterAndSortJobs'. Either include it or remove the dependency array.", ["292"], "'getStatusBadge' is assigned a value but never used.", "'getPriorityBadge' is assigned a value but never used.", "'handleStatusUpdate' is assigned a value but never used.", "'handleDownloadFile' is assigned a value but never used.", "'handleOpenChat' is assigned a value but never used.", "'handleSubmitQuote' is assigned a value but never used.", "'handleSendMessage' is assigned a value but never used.", "'getTimeAgo' is assigned a value but never used.", "'formatFileSize' is assigned a value but never used.", "'xeroxCenters' is assigned a value but never used.", {"desc": "293", "fix": "294"}, {"kind": "295", "justification": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, "Update the dependencies array to be: [fetchCurrentUser, token]", {"range": "301", "text": "302"}, "directive", "", "Update the dependencies array to be: [refreshInterval]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [jobs, searchTerm, statusFilter, priorityFilter, sortBy, sortDirection, filterAndSortJobs]", {"range": "305", "text": "306"}, [2058, 2065], "[fetch<PERSON><PERSON><PERSON><PERSON><PERSON>, token]", [2552, 2554], "[refreshInterval]", [3493, 3564], "[jobs, searchTerm, statusFilter, priorityFilter, sortBy, sortDirection, filterAndSortJobs]"]