using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class Message
    {
        public int MessageID { get; set; }
        
        public int PrintJobID { get; set; }
        
        public int SenderUserID { get; set; }
        
        public int ReceiverUserID { get; set; }
        
        [Required]
        [StringLength(1000)]
        public string MessageText { get; set; } = string.Empty;
        
        public bool IsRead { get; set; } = false;
        
        public DateTime SentAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public PrintJob PrintJob { get; set; } = null!;
        public User SenderUser { get; set; } = null!;
        public User ReceiverUser { get; set; } = null!;
        
        // Computed properties
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.UtcNow - SentAt;
                
                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} minutes ago";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} hours ago";
                if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays} days ago";
                
                return SentAt.ToString("MMM dd, yyyy");
            }
        }
    }
}
