using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class FileUpload
    {
        public int FileUploadID { get; set; }
        
        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        
        [Required]
        [StringLength(50)]
        public string FileType { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Remarks { get; set; }
        
        public int StudentID { get; set; }
        
        public int? PreferredXeroxCenterID { get; set; }
        
        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;
        
        [Required]
        [StringLength(50)]
        public string PrintType { get; set; } = string.Empty; // 'Print', 'Xerox', 'Binding', etc.
        
        public int Copies { get; set; } = 1;
        
        [Required]
        [StringLength(20)]
        public string ColorType { get; set; } = "BlackWhite"; // 'BlackWhite', 'Color'
        
        [Required]
        [StringLength(20)]
        public string PaperSize { get; set; } = "A4"; // 'A4', 'A3', 'Letter', etc.
        
        public int CreatedUserID { get; set; }
        
        public int? ModifiedUserID { get; set; }
        
        public DateTime Created { get; set; } = DateTime.UtcNow;
        
        public DateTime? Modified { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        // Navigation properties
        public Student Student { get; set; } = null!;
        public XeroxCenter? PreferredXeroxCenter { get; set; }
        public User CreatedUser { get; set; } = null!;
        public User? ModifiedUser { get; set; }
        public ICollection<PrintJob> PrintJobs { get; set; } = new List<PrintJob>();
        
        // Computed properties
        public string FileSizeFormatted => FormatFileSize(FileSize);
        public bool IsImageFile => FileType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
        public bool IsPdfFile => FileType.Equals("application/pdf", StringComparison.OrdinalIgnoreCase);
        
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
