{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Container,Row,Col,Form,Button,Alert,Nav}from'react-bootstrap';import{Link,useNavigate}from'react-router-dom';import{motion,AnimatePresence}from'framer-motion';import{User,Building,Mail,Lock,Eye,EyeOff,UserPlus,Sparkles}from'lucide-react';import{authApi}from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AceternityRegister=()=>{const[userType,setUserType]=useState('Student');const[formData,setFormData]=useState({username:'',email:'',password:'',confirmPassword:'',name:'',location:''});const[showPassword,setShowPassword]=useState(false);const[showConfirmPassword,setShowConfirmPassword]=useState(false);const[error,setError]=useState('');const[loading,setLoading]=useState(false);const navigate=useNavigate();const handleInputChange=e=>{setFormData(_objectSpread(_objectSpread({},formData),{},{[e.target.name]:e.target.value}));};const handleSubmit=async e=>{e.preventDefault();setError('');if(formData.password!==formData.confirmPassword){setError('Passwords do not match');return;}if(formData.password.length<6){setError('Password must be at least 6 characters long');return;}setLoading(true);try{const registrationData=_objectSpread({username:formData.username,email:formData.email,password:formData.password,userType},userType==='XeroxCenter'?{name:formData.name,location:formData.location}:{});if(userType==='Student'){await authApi.registerStudent(registrationData);}else{await authApi.registerXeroxCenter(registrationData);}navigate('/login',{state:{message:'Registration successful! Please login with your credentials.',email:formData.email}});}catch(error){var _error$response,_error$response$data;setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||'Registration failed. Please try again.');}finally{setLoading(false);}};const containerVariants={hidden:{opacity:0},visible:{opacity:1,transition:{duration:0.6,staggerChildren:0.1}}};const itemVariants={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5}}};const floatingShapes=Array.from({length:6},(_,i)=>/*#__PURE__*/_jsx(motion.div,{className:\"absolute rounded-full opacity-20\",style:{background:\"linear-gradient(135deg, \".concat(i%2===0?'#667eea':'#764ba2',\", \").concat(i%2===0?'#764ba2':'#667eea',\")\"),width:\"\".concat(Math.random()*100+50,\"px\"),height:\"\".concat(Math.random()*100+50,\"px\"),left:\"\".concat(Math.random()*100,\"%\"),top:\"\".concat(Math.random()*100,\"%\")},animate:{x:[0,Math.random()*100-50],y:[0,Math.random()*100-50],rotate:[0,360]},transition:{duration:Math.random()*10+10,repeat:Infinity,repeatType:\"reverse\"}},i));return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen position-relative overflow-hidden\",style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"position-absolute w-100 h-100\",children:[floatingShapes,/*#__PURE__*/_jsx(\"div\",{className:\"position-absolute w-100 h-100\",style:{background:'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)'}})]}),/*#__PURE__*/_jsx(Container,{fluid:true,className:\"min-h-screen d-flex align-items-center justify-content-center position-relative\",style:{zIndex:1},children:/*#__PURE__*/_jsx(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",className:\"w-100\",style:{maxWidth:'500px'},children:/*#__PURE__*/_jsx(Row,{className:\"justify-content-center\",children:/*#__PURE__*/_jsxs(Col,{children:[/*#__PURE__*/_jsxs(motion.div,{variants:itemVariants,className:\"text-center mb-5\",children:[/*#__PURE__*/_jsx(motion.div,{animate:{rotate:[0,360],scale:[1,1.1,1]},transition:{duration:3,repeat:Infinity,ease:\"easeInOut\"},className:\"d-inline-flex align-items-center justify-content-center mb-4\",style:{width:'80px',height:'80px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'20px',boxShadow:'0 20px 40px rgba(102, 126, 234, 0.3)'},children:/*#__PURE__*/_jsx(UserPlus,{className:\"text-white\",size:40})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-white fw-bold mb-2\",style:{fontSize:'2.5rem'},children:\"Join Xerox Hub\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50 fs-5\",children:\"Create your account and start printing\"})]}),/*#__PURE__*/_jsxs(motion.div,{variants:itemVariants,className:\"p-4 rounded-4 position-relative\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',border:'1px solid rgba(255, 255, 255, 0.2)',boxShadow:'0 25px 50px rgba(0, 0, 0, 0.1)'},children:[/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,className:\"mb-4\",children:/*#__PURE__*/_jsxs(Nav,{variant:\"pills\",className:\"justify-content-center\",children:[/*#__PURE__*/_jsx(Nav.Item,{children:/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.05},whileTap:{scale:0.95},children:/*#__PURE__*/_jsxs(Nav.Link,{active:userType==='Student',onClick:()=>setUserType('Student'),className:\"px-4 py-3 rounded-3 fw-semibold \".concat(userType==='Student'?'bg-white text-primary':'text-white-50'),style:{border:'none',background:userType==='Student'?'rgba(255, 255, 255, 0.9)':'transparent',transition:'all 0.3s ease'},children:[/*#__PURE__*/_jsx(User,{size:18,className:\"me-2\"}),\"Student\"]})})}),/*#__PURE__*/_jsx(Nav.Item,{className:\"ms-2\",children:/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.05},whileTap:{scale:0.95},children:/*#__PURE__*/_jsxs(Nav.Link,{active:userType==='XeroxCenter',onClick:()=>setUserType('XeroxCenter'),className:\"px-4 py-3 rounded-3 fw-semibold \".concat(userType==='XeroxCenter'?'bg-white text-primary':'text-white-50'),style:{border:'none',background:userType==='XeroxCenter'?'rgba(255, 255, 255, 0.9)':'transparent',transition:'all 0.3s ease'},children:[/*#__PURE__*/_jsx(Building,{size:18,className:\"me-2\"}),\"Xerox Center\"]})})})]})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:error&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:\"mb-3\",children:/*#__PURE__*/_jsx(Alert,{variant:\"danger\",className:\"border-0 rounded-3\",style:{background:'rgba(220, 53, 69, 0.1)',backdropFilter:'blur(10px)',color:'#fff'},children:error})})}),/*#__PURE__*/_jsxs(Form,{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(User,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",name:\"username\",placeholder:\"Username\",value:formData.username,onChange:handleInputChange,required:true,className:\"ps-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}})]})})}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Mail,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:\"email\",name:\"email\",placeholder:\"Email Address\",value:formData.email,onChange:handleInputChange,required:true,className:\"ps-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}})]})})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:userType==='XeroxCenter'&&/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},exit:{opacity:0,height:0},transition:{duration:0.3},children:[/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Building,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",name:\"name\",placeholder:\"Center Name\",value:formData.name,onChange:handleInputChange,required:userType==='XeroxCenter',className:\"ps-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}})]})})}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Sparkles,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:\"text\",name:\"location\",placeholder:\"Location\",value:formData.location,onChange:handleInputChange,required:userType==='XeroxCenter',className:\"ps-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}})]})})})]})}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Lock,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:showPassword?'text':'password',name:\"password\",placeholder:\"Password\",value:formData.password,onChange:handleInputChange,required:true,className:\"ps-5 pe-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowPassword(!showPassword),className:\"position-absolute border-0 bg-transparent text-white-50\",style:{right:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2},children:showPassword?/*#__PURE__*/_jsx(EyeOff,{size:20}):/*#__PURE__*/_jsx(Eye,{size:20})})]})})}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(Form.Group,{className:\"mb-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"position-relative\",children:[/*#__PURE__*/_jsx(Lock,{className:\"position-absolute text-white-50\",size:20,style:{left:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2}}),/*#__PURE__*/_jsx(Form.Control,{type:showConfirmPassword?'text':'password',name:\"confirmPassword\",placeholder:\"Confirm Password\",value:formData.confirmPassword,onChange:handleInputChange,required:true,className:\"ps-5 pe-5 py-3 rounded-3 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(10px)',color:'#fff',fontSize:'16px'}}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",onClick:()=>setShowConfirmPassword(!showConfirmPassword),className:\"position-absolute border-0 bg-transparent text-white-50\",style:{right:'15px',top:'50%',transform:'translateY(-50%)',zIndex:2},children:showConfirmPassword?/*#__PURE__*/_jsx(EyeOff,{size:20}):/*#__PURE__*/_jsx(Eye,{size:20})})]})})}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.02},whileTap:{scale:0.98},children:/*#__PURE__*/_jsx(Button,{type:\"submit\",disabled:loading,className:\"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\",style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',boxShadow:'0 10px 30px rgba(102, 126, 234, 0.3)',transition:'all 0.3s ease'},children:loading?/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"spinner-border spinner-border-sm me-2\",role:\"status\"}),\"Creating Account...\"]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(UserPlus,{size:20,className:\"me-2\"}),\"Create Account\"]})})})})]}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,className:\"text-center mt-4\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-white-50 mb-0\",children:[\"Already have an account?\",' ',/*#__PURE__*/_jsx(Link,{to:\"/login\",className:\"text-white fw-semibold text-decoration-none\",style:{transition:'all 0.3s ease'},onMouseEnter:e=>e.target.style.color='#764ba2',onMouseLeave:e=>e.target.style.color='#fff',children:\"Sign In\"})]})})]})]})})})})]});};export default AceternityRegister;", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nav", "Link", "useNavigate", "motion", "AnimatePresence", "User", "Building", "Mail", "Lock", "Eye", "Eye<PERSON>ff", "UserPlus", "<PERSON><PERSON><PERSON>", "authApi", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AceternityRegister", "userType", "setUserType", "formData", "setFormData", "username", "email", "password", "confirmPassword", "name", "location", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "error", "setError", "loading", "setLoading", "navigate", "handleInputChange", "e", "_objectSpread", "target", "value", "handleSubmit", "preventDefault", "length", "registrationData", "registerStudent", "registerXeroxCenter", "state", "message", "_error$response", "_error$response$data", "response", "data", "containerVariants", "hidden", "opacity", "visible", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "floatingShapes", "Array", "from", "_", "i", "div", "className", "style", "background", "concat", "width", "Math", "random", "height", "left", "top", "animate", "x", "rotate", "repeat", "Infinity", "repeatType", "children", "fluid", "zIndex", "variants", "initial", "max<PERSON><PERSON><PERSON>", "scale", "ease", "borderRadius", "boxShadow", "size", "fontSize", "<PERSON><PERSON>ilter", "border", "variant", "<PERSON><PERSON>", "whileHover", "whileTap", "active", "onClick", "mode", "exit", "color", "onSubmit", "Group", "transform", "Control", "type", "placeholder", "onChange", "required", "right", "disabled", "role", "to", "onMouseEnter", "onMouseLeave"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityRegister.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Form, Button, Alert, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { User, Building, Mail, Lock, Eye, EyeOff, UserPlus, Sparkles } from 'lucide-react';\nimport { authApi } from '../../services/api';\n\nconst AceternityRegister: React.FC = () => {\n  const [userType, setUserType] = useState<'Student' | 'XeroxCenter'>('Student');\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    name: '',\n    location: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const registrationData = {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        userType,\n        ...(userType === 'XeroxCenter' ? {\n          name: formData.name,\n          location: formData.location\n        } : {})\n      };\n\n      if (userType === 'Student') {\n        await authApi.registerStudent(registrationData);\n      } else {\n        await authApi.registerXeroxCenter(registrationData);\n      }\n      navigate('/login', { \n        state: { \n          message: 'Registration successful! Please login with your credentials.',\n          email: formData.email \n        } \n      });\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const floatingShapes = Array.from({ length: 6 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"absolute rounded-full opacity-20\"\n      style={{\n        background: `linear-gradient(135deg, ${i % 2 === 0 ? '#667eea' : '#764ba2'}, ${i % 2 === 0 ? '#764ba2' : '#667eea'})`,\n        width: `${Math.random() * 100 + 50}px`,\n        height: `${Math.random() * 100 + 50}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n      }}\n      transition={{\n        duration: Math.random() * 10 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n      }}\n    />\n  ));\n\n  return (\n    <div className=\"min-h-screen position-relative overflow-hidden\" style={{\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    }}>\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)',\n        }} />\n      </div>\n\n      <Container fluid className=\"min-h-screen d-flex align-items-center justify-content-center position-relative\" style={{ zIndex: 1 }}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"w-100\"\n          style={{ maxWidth: '500px' }}\n        >\n          <Row className=\"justify-content-center\">\n            <Col>\n              <motion.div\n                variants={itemVariants}\n                className=\"text-center mb-5\"\n              >\n                <motion.div\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [1, 1.1, 1],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"d-inline-flex align-items-center justify-content-center mb-4\"\n                  style={{\n                    width: '80px',\n                    height: '80px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '20px',\n                    boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n                  }}\n                >\n                  <UserPlus className=\"text-white\" size={40} />\n                </motion.div>\n                <h1 className=\"text-white fw-bold mb-2\" style={{ fontSize: '2.5rem' }}>\n                  Join Xerox Hub\n                </h1>\n                <p className=\"text-white-50 fs-5\">\n                  Create your account and start printing\n                </p>\n              </motion.div>\n\n              <motion.div\n                variants={itemVariants}\n                className=\"p-4 rounded-4 position-relative\"\n                style={{\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  backdropFilter: 'blur(20px)',\n                  border: '1px solid rgba(255, 255, 255, 0.2)',\n                  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1)'\n                }}\n              >\n                {/* User Type Tabs */}\n                <motion.div variants={itemVariants} className=\"mb-4\">\n                  <Nav variant=\"pills\" className=\"justify-content-center\">\n                    <Nav.Item>\n                      <motion.div\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Nav.Link\n                          active={userType === 'Student'}\n                          onClick={() => setUserType('Student')}\n                          className={`px-4 py-3 rounded-3 fw-semibold ${\n                            userType === 'Student'\n                              ? 'bg-white text-primary'\n                              : 'text-white-50'\n                          }`}\n                          style={{\n                            border: 'none',\n                            background: userType === 'Student' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                            transition: 'all 0.3s ease'\n                          }}\n                        >\n                          <User size={18} className=\"me-2\" />\n                          Student\n                        </Nav.Link>\n                      </motion.div>\n                    </Nav.Item>\n                    <Nav.Item className=\"ms-2\">\n                      <motion.div\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Nav.Link\n                          active={userType === 'XeroxCenter'}\n                          onClick={() => setUserType('XeroxCenter')}\n                          className={`px-4 py-3 rounded-3 fw-semibold ${\n                            userType === 'XeroxCenter'\n                              ? 'bg-white text-primary'\n                              : 'text-white-50'\n                          }`}\n                          style={{\n                            border: 'none',\n                            background: userType === 'XeroxCenter' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                            transition: 'all 0.3s ease'\n                          }}\n                        >\n                          <Building size={18} className=\"me-2\" />\n                          Xerox Center\n                        </Nav.Link>\n                      </motion.div>\n                    </Nav.Item>\n                  </Nav>\n                </motion.div>\n\n                <AnimatePresence mode=\"wait\">\n                  {error && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                      className=\"mb-3\"\n                    >\n                      <Alert variant=\"danger\" className=\"border-0 rounded-3\" style={{\n                        background: 'rgba(220, 53, 69, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        color: '#fff'\n                      }}>\n                        {error}\n                      </Alert>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <Form onSubmit={handleSubmit}>\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <User className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type=\"text\"\n                          name=\"username\"\n                          placeholder=\"Username\"\n                          value={formData.username}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <Mail className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type=\"email\"\n                          name=\"email\"\n                          placeholder=\"Email Address\"\n                          value={formData.email}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <AnimatePresence mode=\"wait\">\n                    {userType === 'XeroxCenter' && (\n                      <motion.div\n                        initial={{ opacity: 0, height: 0 }}\n                        animate={{ opacity: 1, height: 'auto' }}\n                        exit={{ opacity: 0, height: 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <motion.div variants={itemVariants}>\n                          <Form.Group className=\"mb-3\">\n                            <div className=\"position-relative\">\n                              <Building className=\"position-absolute text-white-50\" size={20} style={{\n                                left: '15px',\n                                top: '50%',\n                                transform: 'translateY(-50%)',\n                                zIndex: 2\n                              }} />\n                              <Form.Control\n                                type=\"text\"\n                                name=\"name\"\n                                placeholder=\"Center Name\"\n                                value={formData.name}\n                                onChange={handleInputChange}\n                                required={userType === 'XeroxCenter'}\n                                className=\"ps-5 py-3 rounded-3 border-0\"\n                                style={{\n                                  background: 'rgba(255, 255, 255, 0.1)',\n                                  backdropFilter: 'blur(10px)',\n                                  color: '#fff',\n                                  fontSize: '16px'\n                                }}\n                              />\n                            </div>\n                          </Form.Group>\n                        </motion.div>\n\n                        <motion.div variants={itemVariants}>\n                          <Form.Group className=\"mb-3\">\n                            <div className=\"position-relative\">\n                              <Sparkles className=\"position-absolute text-white-50\" size={20} style={{\n                                left: '15px',\n                                top: '50%',\n                                transform: 'translateY(-50%)',\n                                zIndex: 2\n                              }} />\n                              <Form.Control\n                                type=\"text\"\n                                name=\"location\"\n                                placeholder=\"Location\"\n                                value={formData.location}\n                                onChange={handleInputChange}\n                                required={userType === 'XeroxCenter'}\n                                className=\"ps-5 py-3 rounded-3 border-0\"\n                                style={{\n                                  background: 'rgba(255, 255, 255, 0.1)',\n                                  backdropFilter: 'blur(10px)',\n                                  color: '#fff',\n                                  fontSize: '16px'\n                                }}\n                              />\n                            </div>\n                          </Form.Group>\n                        </motion.div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <Lock className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type={showPassword ? 'text' : 'password'}\n                          name=\"password\"\n                          placeholder=\"Password\"\n                          value={formData.password}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 pe-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowPassword(!showPassword)}\n                          className=\"position-absolute border-0 bg-transparent text-white-50\"\n                          style={{\n                            right: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2\n                          }}\n                        >\n                          {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                        </button>\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-4\">\n                      <div className=\"position-relative\">\n                        <Lock className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type={showConfirmPassword ? 'text' : 'password'}\n                          name=\"confirmPassword\"\n                          placeholder=\"Confirm Password\"\n                          value={formData.confirmPassword}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 pe-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                          className=\"position-absolute border-0 bg-transparent text-white-50\"\n                          style={{\n                            right: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2\n                          }}\n                        >\n                          {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                        </button>\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <motion.div\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Button\n                        type=\"submit\"\n                        disabled={loading}\n                        className=\"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\"\n                        style={{\n                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                          boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)',\n                          transition: 'all 0.3s ease'\n                        }}\n                      >\n                        {loading ? (\n                          <div className=\"d-flex align-items-center justify-content-center\">\n                            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\" />\n                            Creating Account...\n                          </div>\n                        ) : (\n                          <>\n                            <UserPlus size={20} className=\"me-2\" />\n                            Create Account\n                          </>\n                        )}\n                      </Button>\n                    </motion.div>\n                  </motion.div>\n                </Form>\n\n                <motion.div variants={itemVariants} className=\"text-center mt-4\">\n                  <p className=\"text-white-50 mb-0\">\n                    Already have an account?{' '}\n                    <Link\n                      to=\"/login\"\n                      className=\"text-white fw-semibold text-decoration-none\"\n                      style={{\n                        transition: 'all 0.3s ease'\n                      }}\n                      onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}\n                      onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#fff'}\n                    >\n                      Sign In\n                    </Link>\n                  </p>\n                </motion.div>\n              </motion.div>\n            </Col>\n          </Row>\n        </motion.div>\n      </Container>\n    </div>\n  );\n};\n\nexport default AceternityRegister;\n"], "mappings": "uIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,GAAG,KAAQ,iBAAiB,CAC/E,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,IAAI,CAAEC,QAAQ,CAAEC,IAAI,CAAEC,IAAI,CAAEC,GAAG,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,QAAQ,KAAQ,cAAc,CAC1F,OAASC,OAAO,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7C,KAAM,CAAAC,kBAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAG7B,QAAQ,CAA4B,SAAS,CAAC,CAC9E,KAAM,CAAC8B,QAAQ,CAAEC,WAAW,CAAC,CAAG/B,QAAQ,CAAC,CACvCgC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,QAAQ,CAAE,EAAE,CACZC,eAAe,CAAE,EAAE,CACnBC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAAC0C,KAAK,CAAEC,QAAQ,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4C,OAAO,CAAEC,UAAU,CAAC,CAAG7C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAA8C,QAAQ,CAAGrC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAsC,iBAAiB,CAAIC,CAAsC,EAAK,CACpEjB,WAAW,CAAAkB,aAAA,CAAAA,aAAA,IACNnB,QAAQ,MACX,CAACkB,CAAC,CAACE,MAAM,CAACd,IAAI,EAAGY,CAAC,CAACE,MAAM,CAACC,KAAK,EAChC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAJ,CAAkB,EAAK,CACjDA,CAAC,CAACK,cAAc,CAAC,CAAC,CAClBV,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAIb,QAAQ,CAACI,QAAQ,GAAKJ,QAAQ,CAACK,eAAe,CAAE,CAClDQ,QAAQ,CAAC,wBAAwB,CAAC,CAClC,OACF,CAEA,GAAIb,QAAQ,CAACI,QAAQ,CAACoB,MAAM,CAAG,CAAC,CAAE,CAChCX,QAAQ,CAAC,6CAA6C,CAAC,CACvD,OACF,CAEAE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAU,gBAAgB,CAAAN,aAAA,EACpBjB,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,KAAK,CAAEH,QAAQ,CAACG,KAAK,CACrBC,QAAQ,CAAEJ,QAAQ,CAACI,QAAQ,CAC3BN,QAAQ,EACJA,QAAQ,GAAK,aAAa,CAAG,CAC/BQ,IAAI,CAAEN,QAAQ,CAACM,IAAI,CACnBC,QAAQ,CAAEP,QAAQ,CAACO,QACrB,CAAC,CAAG,CAAC,CAAC,CACP,CAED,GAAIT,QAAQ,GAAK,SAAS,CAAE,CAC1B,KAAM,CAAAR,OAAO,CAACoC,eAAe,CAACD,gBAAgB,CAAC,CACjD,CAAC,IAAM,CACL,KAAM,CAAAnC,OAAO,CAACqC,mBAAmB,CAACF,gBAAgB,CAAC,CACrD,CACAT,QAAQ,CAAC,QAAQ,CAAE,CACjBY,KAAK,CAAE,CACLC,OAAO,CAAE,8DAA8D,CACvE1B,KAAK,CAAEH,QAAQ,CAACG,KAClB,CACF,CAAC,CAAC,CACJ,CAAE,MAAOS,KAAU,CAAE,KAAAkB,eAAA,CAAAC,oBAAA,CACnBlB,QAAQ,CAAC,EAAAiB,eAAA,CAAAlB,KAAK,CAACoB,QAAQ,UAAAF,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBG,IAAI,UAAAF,oBAAA,iBAApBA,oBAAA,CAAsBF,OAAO,GAAI,wCAAwC,CAAC,CACrF,CAAC,OAAS,CACRd,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmB,iBAAiB,CAAG,CACxBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbC,eAAe,CAAE,GACnB,CACF,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBN,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,EAAG,CAAC,CAC7BL,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVM,CAAC,CAAE,CAAC,CACJJ,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED,KAAM,CAAAI,cAAc,CAAGC,KAAK,CAACC,IAAI,CAAC,CAAErB,MAAM,CAAE,CAAE,CAAC,CAAE,CAACsB,CAAC,CAAEC,CAAC,gBACpDvD,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAETC,SAAS,CAAC,kCAAkC,CAC5CC,KAAK,CAAE,CACLC,UAAU,4BAAAC,MAAA,CAA6BL,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAG,SAAS,OAAAK,MAAA,CAAKL,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAG,SAAS,KAAG,CACrHM,KAAK,IAAAD,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,MAAI,CACtCC,MAAM,IAAAJ,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,MAAI,CACvCE,IAAI,IAAAL,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/BG,GAAG,IAAAN,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAC7B,CAAE,CACFI,OAAO,CAAE,CACPC,CAAC,CAAE,CAAC,CAAC,CAAEN,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,CAChCb,CAAC,CAAE,CAAC,CAAC,CAAEY,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,CAChCM,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CACjB,CAAE,CACFvB,UAAU,CAAE,CACVC,QAAQ,CAAEe,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CACjCO,MAAM,CAAEC,QAAQ,CAChBC,UAAU,CAAE,SACd,CAAE,EAlBGjB,CAmBN,CACF,CAAC,CAEF,mBACErD,KAAA,QAAKuD,SAAS,CAAC,gDAAgD,CAACC,KAAK,CAAE,CACrEC,UAAU,CAAE,mDACd,CAAE,CAAAc,QAAA,eAEAvE,KAAA,QAAKuD,SAAS,CAAC,+BAA+B,CAAAgB,QAAA,EAC3CtB,cAAc,cACfnD,IAAA,QAAKyD,SAAS,CAAC,+BAA+B,CAACC,KAAK,CAAE,CACpDC,UAAU,CAAE,oKACd,CAAE,CAAE,CAAC,EACF,CAAC,cAEN3D,IAAA,CAACrB,SAAS,EAAC+F,KAAK,MAACjB,SAAS,CAAC,iFAAiF,CAACC,KAAK,CAAE,CAAEiB,MAAM,CAAE,CAAE,CAAE,CAAAF,QAAA,cAChIzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACToB,QAAQ,CAAElC,iBAAkB,CAC5BmC,OAAO,CAAC,QAAQ,CAChBV,OAAO,CAAC,SAAS,CACjBV,SAAS,CAAC,OAAO,CACjBC,KAAK,CAAE,CAAEoB,QAAQ,CAAE,OAAQ,CAAE,CAAAL,QAAA,cAE7BzE,IAAA,CAACpB,GAAG,EAAC6E,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,cACrCvE,KAAA,CAACrB,GAAG,EAAA4F,QAAA,eACFvE,KAAA,CAACd,MAAM,CAACoE,GAAG,EACToB,QAAQ,CAAE3B,YAAa,CACvBQ,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,eAE5BzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTW,OAAO,CAAE,CACPE,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAC,CAChBU,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CACnB,CAAE,CACFjC,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXuB,MAAM,CAAEC,QAAQ,CAChBS,IAAI,CAAE,WACR,CAAE,CACFvB,SAAS,CAAC,8DAA8D,CACxEC,KAAK,CAAE,CACLG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DsB,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,sCACb,CAAE,CAAAT,QAAA,cAEFzE,IAAA,CAACJ,QAAQ,EAAC6D,SAAS,CAAC,YAAY,CAAC0B,IAAI,CAAE,EAAG,CAAE,CAAC,CACnC,CAAC,cACbnF,IAAA,OAAIyD,SAAS,CAAC,yBAAyB,CAACC,KAAK,CAAE,CAAE0B,QAAQ,CAAE,QAAS,CAAE,CAAAX,QAAA,CAAC,gBAEvE,CAAI,CAAC,cACLzE,IAAA,MAAGyD,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,CAAC,wCAElC,CAAG,CAAC,EACM,CAAC,cAEbvE,KAAA,CAACd,MAAM,CAACoE,GAAG,EACToB,QAAQ,CAAE3B,YAAa,CACvBQ,SAAS,CAAC,iCAAiC,CAC3CC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BC,MAAM,CAAE,oCAAoC,CAC5CJ,SAAS,CAAE,gCACb,CAAE,CAAAT,QAAA,eAGFzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAACQ,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAClDvE,KAAA,CAACjB,GAAG,EAACsG,OAAO,CAAC,OAAO,CAAC9B,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,eACrDzE,IAAA,CAACf,GAAG,CAACuG,IAAI,EAAAf,QAAA,cACPzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTiC,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAAAN,QAAA,cAE1BvE,KAAA,CAACjB,GAAG,CAACC,IAAI,EACPyG,MAAM,CAAErF,QAAQ,GAAK,SAAU,CAC/BsF,OAAO,CAAEA,CAAA,GAAMrF,WAAW,CAAC,SAAS,CAAE,CACtCkD,SAAS,oCAAAG,MAAA,CACPtD,QAAQ,GAAK,SAAS,CAClB,uBAAuB,CACvB,eAAe,CAClB,CACHoD,KAAK,CAAE,CACL4B,MAAM,CAAE,MAAM,CACd3B,UAAU,CAAErD,QAAQ,GAAK,SAAS,CAAG,0BAA0B,CAAG,aAAa,CAC/EwC,UAAU,CAAE,eACd,CAAE,CAAA2B,QAAA,eAEFzE,IAAA,CAACV,IAAI,EAAC6F,IAAI,CAAE,EAAG,CAAC1B,SAAS,CAAC,MAAM,CAAE,CAAC,UAErC,EAAU,CAAC,CACD,CAAC,CACL,CAAC,cACXzD,IAAA,CAACf,GAAG,CAACuG,IAAI,EAAC/B,SAAS,CAAC,MAAM,CAAAgB,QAAA,cACxBzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTiC,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAAAN,QAAA,cAE1BvE,KAAA,CAACjB,GAAG,CAACC,IAAI,EACPyG,MAAM,CAAErF,QAAQ,GAAK,aAAc,CACnCsF,OAAO,CAAEA,CAAA,GAAMrF,WAAW,CAAC,aAAa,CAAE,CAC1CkD,SAAS,oCAAAG,MAAA,CACPtD,QAAQ,GAAK,aAAa,CACtB,uBAAuB,CACvB,eAAe,CAClB,CACHoD,KAAK,CAAE,CACL4B,MAAM,CAAE,MAAM,CACd3B,UAAU,CAAErD,QAAQ,GAAK,aAAa,CAAG,0BAA0B,CAAG,aAAa,CACnFwC,UAAU,CAAE,eACd,CAAE,CAAA2B,QAAA,eAEFzE,IAAA,CAACT,QAAQ,EAAC4F,IAAI,CAAE,EAAG,CAAC1B,SAAS,CAAC,MAAM,CAAE,CAAC,eAEzC,EAAU,CAAC,CACD,CAAC,CACL,CAAC,EACR,CAAC,CACI,CAAC,cAEbzD,IAAA,CAACX,eAAe,EAACwG,IAAI,CAAC,MAAM,CAAApB,QAAA,CACzBrD,KAAK,eACJpB,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTqB,OAAO,CAAE,CAAEjC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCiB,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAE,CAAE,CAC9B4C,IAAI,CAAE,CAAElD,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BO,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAEhBzE,IAAA,CAAChB,KAAK,EAACuG,OAAO,CAAC,QAAQ,CAAC9B,SAAS,CAAC,oBAAoB,CAACC,KAAK,CAAE,CAC5DC,UAAU,CAAE,wBAAwB,CACpC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MACT,CAAE,CAAAtB,QAAA,CACCrD,KAAK,CACD,CAAC,CACE,CACb,CACc,CAAC,cAElBlB,KAAA,CAACpB,IAAI,EAACkH,QAAQ,CAAElE,YAAa,CAAA2C,QAAA,eAC3BzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACV,IAAI,EAACmE,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACjEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAC,MAAM,CACXtF,IAAI,CAAC,UAAU,CACfuF,WAAW,CAAC,UAAU,CACtBxE,KAAK,CAAErB,QAAQ,CAACE,QAAS,CACzB4F,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,MACR9C,SAAS,CAAC,8BAA8B,CACxCC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,EACC,CAAC,CACI,CAAC,CACH,CAAC,cAEbpF,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACR,IAAI,EAACiE,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACjEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAC,OAAO,CACZtF,IAAI,CAAC,OAAO,CACZuF,WAAW,CAAC,eAAe,CAC3BxE,KAAK,CAAErB,QAAQ,CAACG,KAAM,CACtB2F,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,MACR9C,SAAS,CAAC,8BAA8B,CACxCC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,EACC,CAAC,CACI,CAAC,CACH,CAAC,cAEbpF,IAAA,CAACX,eAAe,EAACwG,IAAI,CAAC,MAAM,CAAApB,QAAA,CACzBnE,QAAQ,GAAK,aAAa,eACzBJ,KAAA,CAACd,MAAM,CAACoE,GAAG,EACTqB,OAAO,CAAE,CAAEjC,OAAO,CAAE,CAAC,CAAEoB,MAAM,CAAE,CAAE,CAAE,CACnCG,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEoB,MAAM,CAAE,MAAO,CAAE,CACxC8B,IAAI,CAAE,CAAElD,OAAO,CAAE,CAAC,CAAEoB,MAAM,CAAE,CAAE,CAAE,CAChClB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAA0B,QAAA,eAE9BzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACT,QAAQ,EAACkE,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACrEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAC,MAAM,CACXtF,IAAI,CAAC,MAAM,CACXuF,WAAW,CAAC,aAAa,CACzBxE,KAAK,CAAErB,QAAQ,CAACM,IAAK,CACrBwF,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,CAAEjG,QAAQ,GAAK,aAAc,CACrCmD,SAAS,CAAC,8BAA8B,CACxCC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,EACC,CAAC,CACI,CAAC,CACH,CAAC,cAEbpF,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACH,QAAQ,EAAC4D,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACrEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAC,MAAM,CACXtF,IAAI,CAAC,UAAU,CACfuF,WAAW,CAAC,UAAU,CACtBxE,KAAK,CAAErB,QAAQ,CAACO,QAAS,CACzBuF,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,CAAEjG,QAAQ,GAAK,aAAc,CACrCmD,SAAS,CAAC,8BAA8B,CACxCC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,EACC,CAAC,CACI,CAAC,CACH,CAAC,EACH,CACb,CACc,CAAC,cAElBpF,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACP,IAAI,EAACgE,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACjEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAEpF,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCF,IAAI,CAAC,UAAU,CACfuF,WAAW,CAAC,UAAU,CACtBxE,KAAK,CAAErB,QAAQ,CAACI,QAAS,CACzB0F,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,MACR9C,SAAS,CAAC,mCAAmC,CAC7CC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,cACFpF,IAAA,WACEoG,IAAI,CAAC,QAAQ,CACbR,OAAO,CAAEA,CAAA,GAAM3E,eAAe,CAAC,CAACD,YAAY,CAAE,CAC9CyC,SAAS,CAAC,yDAAyD,CACnEC,KAAK,CAAE,CACL8C,KAAK,CAAE,MAAM,CACbtC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAAF,QAAA,CAEDzD,YAAY,cAAGhB,IAAA,CAACL,MAAM,EAACwF,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGnF,IAAA,CAACN,GAAG,EAACyF,IAAI,CAAE,EAAG,CAAE,CAAC,CAClD,CAAC,EACN,CAAC,CACI,CAAC,CACH,CAAC,cAEbnF,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAAClB,IAAI,CAACmH,KAAK,EAACxC,SAAS,CAAC,MAAM,CAAAgB,QAAA,cAC1BvE,KAAA,QAAKuD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChCzE,IAAA,CAACP,IAAI,EAACgE,SAAS,CAAC,iCAAiC,CAAC0B,IAAI,CAAE,EAAG,CAACzB,KAAK,CAAE,CACjEO,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAE,CAAC,cACL3E,IAAA,CAAClB,IAAI,CAACqH,OAAO,EACXC,IAAI,CAAElF,mBAAmB,CAAG,MAAM,CAAG,UAAW,CAChDJ,IAAI,CAAC,iBAAiB,CACtBuF,WAAW,CAAC,kBAAkB,CAC9BxE,KAAK,CAAErB,QAAQ,CAACK,eAAgB,CAChCyF,QAAQ,CAAE7E,iBAAkB,CAC5B8E,QAAQ,MACR9C,SAAS,CAAC,mCAAmC,CAC7CC,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC0B,cAAc,CAAE,YAAY,CAC5BU,KAAK,CAAE,MAAM,CACbX,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,cACFpF,IAAA,WACEoG,IAAI,CAAC,QAAQ,CACbR,OAAO,CAAEA,CAAA,GAAMzE,sBAAsB,CAAC,CAACD,mBAAmB,CAAE,CAC5DuC,SAAS,CAAC,yDAAyD,CACnEC,KAAK,CAAE,CACL8C,KAAK,CAAE,MAAM,CACbtC,GAAG,CAAE,KAAK,CACVgC,SAAS,CAAE,kBAAkB,CAC7BvB,MAAM,CAAE,CACV,CAAE,CAAAF,QAAA,CAEDvD,mBAAmB,cAAGlB,IAAA,CAACL,MAAM,EAACwF,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGnF,IAAA,CAACN,GAAG,EAACyF,IAAI,CAAE,EAAG,CAAE,CAAC,CACzD,CAAC,EACN,CAAC,CACI,CAAC,CACH,CAAC,cAEbnF,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAAAwB,QAAA,cACjCzE,IAAA,CAACZ,MAAM,CAACoE,GAAG,EACTiC,UAAU,CAAE,CAAEV,KAAK,CAAE,IAAK,CAAE,CAC5BW,QAAQ,CAAE,CAAEX,KAAK,CAAE,IAAK,CAAE,CAAAN,QAAA,cAE1BzE,IAAA,CAACjB,MAAM,EACLqH,IAAI,CAAC,QAAQ,CACbK,QAAQ,CAAEnF,OAAQ,CAClBmC,SAAS,CAAC,gDAAgD,CAC1DC,KAAK,CAAE,CACLC,UAAU,CAAE,mDAAmD,CAC/DuB,SAAS,CAAE,sCAAsC,CACjDpC,UAAU,CAAE,eACd,CAAE,CAAA2B,QAAA,CAEDnD,OAAO,cACNpB,KAAA,QAAKuD,SAAS,CAAC,kDAAkD,CAAAgB,QAAA,eAC/DzE,IAAA,QAAKyD,SAAS,CAAC,uCAAuC,CAACiD,IAAI,CAAC,QAAQ,CAAE,CAAC,sBAEzE,EAAK,CAAC,cAENxG,KAAA,CAAAE,SAAA,EAAAqE,QAAA,eACEzE,IAAA,CAACJ,QAAQ,EAACuF,IAAI,CAAE,EAAG,CAAC1B,SAAS,CAAC,MAAM,CAAE,CAAC,iBAEzC,EAAE,CACH,CACK,CAAC,CACC,CAAC,CACH,CAAC,EACT,CAAC,cAEPzD,IAAA,CAACZ,MAAM,CAACoE,GAAG,EAACoB,QAAQ,CAAE3B,YAAa,CAACQ,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,cAC9DvE,KAAA,MAAGuD,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,EAAC,0BACR,CAAC,GAAG,cAC5BzE,IAAA,CAACd,IAAI,EACHyH,EAAE,CAAC,QAAQ,CACXlD,SAAS,CAAC,6CAA6C,CACvDC,KAAK,CAAE,CACLZ,UAAU,CAAE,eACd,CAAE,CACF8D,YAAY,CAAGlF,CAAC,EAAMA,CAAC,CAACE,MAAM,CAAiB8B,KAAK,CAACqC,KAAK,CAAG,SAAU,CACvEc,YAAY,CAAGnF,CAAC,EAAMA,CAAC,CAACE,MAAM,CAAiB8B,KAAK,CAACqC,KAAK,CAAG,MAAO,CAAAtB,QAAA,CACrE,SAED,CAAM,CAAC,EACN,CAAC,CACM,CAAC,EACH,CAAC,EACV,CAAC,CACH,CAAC,CACI,CAAC,CACJ,CAAC,EACT,CAAC,CAEV,CAAC,CAED,cAAe,CAAApE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}