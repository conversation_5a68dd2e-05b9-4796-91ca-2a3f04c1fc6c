using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using XeroxModule.Core.Entities;
using XeroxModule.Core.Interfaces;

namespace XeroxModule.Application.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _configuration;

        public AuthService(IUnitOfWork unitOfWork, IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;
            _configuration = configuration;
        }

        public async Task<AuthResult> LoginAsync(string email, string password)
        {
            try
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == email && u.IsActive);
                if (user == null)
                {
                    return new AuthResult { Success = false, ErrorMessage = "Invalid email or password." };
                }

                if (!VerifyPassword(password, user.PasswordHash))
                {
                    return new AuthResult { Success = false, ErrorMessage = "Invalid email or password." };
                }

                // Update last login
                user.LastLoginAt = DateTime.UtcNow;
                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                var token = await GenerateJwtTokenAsync(user);
                var expiresAt = DateTime.UtcNow.AddHours(24); // Token expires in 24 hours

                return new AuthResult
                {
                    Success = true,
                    Token = token,
                    User = user,
                    ExpiresAt = expiresAt
                };
            }
            catch (Exception ex)
            {
                return new AuthResult { Success = false, ErrorMessage = "An error occurred during login." };
            }
        }

        public async Task<AuthResult> RegisterStudentAsync(StudentRegistrationRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == request.Email || u.Username == request.Username);
                if (existingUser != null)
                {
                    return new AuthResult { Success = false, ErrorMessage = "User with this email or username already exists." };
                }

                // Check if student number already exists
                var existingStudent = await _unitOfWork.Students.FirstOrDefaultAsync(s => s.StudentNumber == request.StudentNumber);
                if (existingStudent != null)
                {
                    return new AuthResult { Success = false, ErrorMessage = "Student with this student number already exists." };
                }

                await _unitOfWork.BeginTransactionAsync();

                // Create user
                var user = new User
                {
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = HashPassword(request.Password),
                    UserType = "Student",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Create student
                var student = new Student
                {
                    UserID = user.UserID,
                    StudentNumber = request.StudentNumber,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    PhoneNumber = request.PhoneNumber,
                    Department = request.Department,
                    Year = request.Year,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.Students.AddAsync(student);
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var token = await GenerateJwtTokenAsync(user);
                var expiresAt = DateTime.UtcNow.AddHours(24);

                return new AuthResult
                {
                    Success = true,
                    Token = token,
                    User = user,
                    ExpiresAt = expiresAt
                };
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return new AuthResult { Success = false, ErrorMessage = "An error occurred during registration." };
            }
        }

        public async Task<AuthResult> RegisterXeroxCenterAsync(XeroxCenterRegistrationRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == request.Email || u.Username == request.Username);
                if (existingUser != null)
                {
                    return new AuthResult { Success = false, ErrorMessage = "User with this email or username already exists." };
                }

                await _unitOfWork.BeginTransactionAsync();

                // Create user
                var user = new User
                {
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = HashPassword(request.Password),
                    UserType = "XeroxCenter",
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Create xerox center
                var xeroxCenter = new XeroxCenter
                {
                    UserID = user.UserID,
                    XeroxCenterName = request.XeroxCenterName,
                    Location = request.Location,
                    ContactPerson = request.ContactPerson,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber,
                    Description = request.Description,
                    IsActive = true,
                    CreatedUserID = user.UserID,
                    Created = DateTime.UtcNow
                };

                await _unitOfWork.XeroxCenters.AddAsync(xeroxCenter);
                await _unitOfWork.SaveChangesAsync();
                await _unitOfWork.CommitTransactionAsync();

                var token = await GenerateJwtTokenAsync(user);
                var expiresAt = DateTime.UtcNow.AddHours(24);

                return new AuthResult
                {
                    Success = true,
                    Token = token,
                    User = user,
                    ExpiresAt = expiresAt
                };
            }
            catch (Exception ex)
            {
                await _unitOfWork.RollbackTransactionAsync();
                return new AuthResult { Success = false, ErrorMessage = "An error occurred during registration." };
            }
        }

        public async Task<bool> LogoutAsync(int userId)
        {
            // In a more sophisticated implementation, you might maintain a blacklist of tokens
            // For now, we'll just return true as JWT tokens are stateless
            return await Task.FromResult(true);
        }

        public async Task<string> GenerateJwtTokenAsync(User user)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"] ?? "YourDefaultSecretKeyHere");

            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.UserID.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email),
                new Claim(ClaimTypes.Role, user.UserType),
                new Claim("UserType", user.UserType)
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(24),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature),
                Issuer = jwtSettings["Issuer"],
                Audience = jwtSettings["Audience"]
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);
            return await Task.FromResult(tokenHandler.WriteToken(token));
        }

        public async Task<bool> ValidateTokenAsync(string token)
        {
            try
            {
                var user = await GetUserFromTokenAsync(token);
                return user != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<User?> GetUserFromTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var jwtSettings = _configuration.GetSection("JwtSettings");
                var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"] ?? "YourDefaultSecretKeyHere");

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidateAudience = true,
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);

                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                {
                    return await _unitOfWork.Users.GetByIdAsync(userId);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);
                if (user == null || !VerifyPassword(currentPassword, user.PasswordHash))
                {
                    return false;
                }

                user.PasswordHash = HashPassword(newPassword);
                user.ModifiedAt = DateTime.UtcNow;

                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ResetPasswordAsync(string email)
        {
            try
            {
                var user = await _unitOfWork.Users.FirstOrDefaultAsync(u => u.Email == email && u.IsActive);
                if (user == null)
                {
                    return false;
                }

                // In a real implementation, you would send an email with a reset link
                // For now, we'll just generate a temporary password
                var tempPassword = GenerateRandomPassword();
                user.PasswordHash = HashPassword(tempPassword);
                user.ModifiedAt = DateTime.UtcNow;

                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // TODO: Send email with temporary password
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string HashPassword(string password)
        {
            using var rng = RandomNumberGenerator.Create();
            var salt = new byte[16];
            rng.GetBytes(salt);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
            var hash = pbkdf2.GetBytes(32);

            var hashBytes = new byte[48];
            Array.Copy(salt, 0, hashBytes, 0, 16);
            Array.Copy(hash, 0, hashBytes, 16, 32);

            return Convert.ToBase64String(hashBytes);
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            var hashBytes = Convert.FromBase64String(hashedPassword);
            var salt = new byte[16];
            Array.Copy(hashBytes, 0, salt, 0, 16);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
            var hash = pbkdf2.GetBytes(32);

            for (int i = 0; i < 32; i++)
            {
                if (hashBytes[i + 16] != hash[i])
                    return false;
            }

            return true;
        }

        private string GenerateRandomPassword()
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            using var rng = RandomNumberGenerator.Create();
            var result = new char[12];
            var buffer = new byte[4];

            for (int i = 0; i < 12; i++)
            {
                rng.GetBytes(buffer);
                var randomIndex = Math.Abs(BitConverter.ToInt32(buffer, 0)) % chars.Length;
                result[i] = chars[randomIndex];
            }

            return new string(result);
        }
    }
}
