using Microsoft.EntityFrameworkCore.Storage;
using XeroxModule.Core.Entities;
using XeroxModule.Core.Interfaces;
using XeroxModule.Infrastructure.Data;

namespace XeroxModule.Infrastructure.Repositories
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly XeroxDbContext _context;
        private IDbContextTransaction? _transaction;
        
        private IRepository<User>? _users;
        private IRepository<Student>? _students;
        private IRepository<XeroxCenter>? _xeroxCenters;
        private IRepository<FileUpload>? _fileUploads;
        private IRepository<PrintJob>? _printJobs;
        private IRepository<Message>? _messages;
        private IRepository<Notification>? _notifications;
        private IRepository<Rating>? _ratings;

        public UnitOfWork(XeroxDbContext context)
        {
            _context = context;
        }

        public IRepository<User> Users => _users ??= new Repository<User>(_context);
        public IRepository<Student> Students => _students ??= new Repository<Student>(_context);
        public IRepository<XeroxCenter> XeroxCenters => _xeroxCenters ??= new Repository<XeroxCenter>(_context);
        public IRepository<FileUpload> FileUploads => _fileUploads ??= new Repository<FileUpload>(_context);
        public IRepository<PrintJob> PrintJobs => _printJobs ??= new Repository<PrintJob>(_context);
        public IRepository<Message> Messages => _messages ??= new Repository<Message>(_context);
        public IRepository<Notification> Notifications => _notifications ??= new Repository<Notification>(_context);
        public IRepository<Rating> Ratings => _ratings ??= new Repository<Rating>(_context);

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public async Task BeginTransactionAsync()
        {
            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync();
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
