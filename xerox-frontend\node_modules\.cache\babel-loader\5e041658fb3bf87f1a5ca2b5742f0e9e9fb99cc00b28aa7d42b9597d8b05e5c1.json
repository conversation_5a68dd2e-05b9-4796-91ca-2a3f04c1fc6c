{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\XeroxCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, fileUploadApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XeroxCenterDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  };\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n        await printJobApi.setJobQuote(selectedJob.id, parseFloat(quoteData.cost), estimatedCompletion.toISOString(), quoteData.notes);\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({\n      cost: '',\n      estimatedHours: '',\n      notes: ''\n    });\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const filteredJobs = filterStatus === 'all' ? printJobs : printJobs.filter(job => job.status === filterStatus);\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-store me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), \"Xerox Center Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-primary\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: stats.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-warning\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: stats.pending\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-info\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cog fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: stats.inProgress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-success\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: [\"$\", stats.revenue.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tasks me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), \"Job Queue\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n          style: {\n            width: 'auto'\n          },\n          value: filterStatus,\n          onChange: e => setFilterStatus(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Requested\",\n            children: \"Requested\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"UnderReview\",\n            children: \"Under Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Quoted\",\n            children: \"Quoted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"WaitingConfirmation\",\n            children: \"Waiting Confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Confirmed\",\n            children: \"Confirmed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"InProgress\",\n            children: \"In Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Completed\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: filteredJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n          responsive: true,\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job #\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"File Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: job.jobNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: new Date(job.created).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: job.studentName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: job.studentEmail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-pdf me-2 text-danger\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 23\n                }, this), job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 30\n                    }, this), \" \", job.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 30\n                    }, this), \" \", job.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 30\n                    }, this), \" \", job.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 30\n                    }, this), \" \", job.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), job.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Remarks:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 32\n                    }, this), \" \", job.remarks]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStatusBadge(job.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"btn-group-vertical\",\n                  role: \"group\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-info\",\n                    size: \"sm\",\n                    onClick: () => handleDownloadFile(job.id, job.fileName),\n                    title: \"Download File\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-download me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 27\n                    }, this), \"Download\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this), job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: () => {\n                        setSelectedJob(job);\n                        setShowQuoteModal(true);\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-dollar-sign me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 31\n                      }, this), \"Quote\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-danger\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-times me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 31\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-info\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-play me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 29\n                    }, this), \"Start\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 27\n                  }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 29\n                    }, this), \"Complete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 27\n                  }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-truck me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 29\n                    }, this), \"Deliver\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), \"No jobs found for the selected filter.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showQuoteModal,\n      onHide: () => setShowQuoteModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-dollar-sign me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), \"Provide Quote - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 p-3 bg-light rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Job Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.fileName, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 66\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.printType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.colorType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 68\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.paperSize, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Student:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.studentName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Remarks:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), \" \", selectedJob.remarks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Cost ($)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                    children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                      children: \"$\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      step: \"0.01\",\n                      placeholder: \"0.00\",\n                      value: quoteData.cost,\n                      onChange: e => setQuoteData(prev => ({\n                        ...prev,\n                        cost: e.target.value\n                      })),\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Estimated Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"number\",\n                    min: \"1\",\n                    placeholder: \"Hours to complete\",\n                    value: quoteData.estimatedHours,\n                    onChange: e => setQuoteData(prev => ({\n                      ...prev,\n                      estimatedHours: e.target.value\n                    })),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Notes (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                placeholder: \"Any additional notes for the student...\",\n                value: quoteData.notes,\n                onChange: e => setQuoteData(prev => ({\n                  ...prev,\n                  notes: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowQuoteModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleQuoteSubmit,\n          disabled: !quoteData.cost || !quoteData.estimatedHours,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-paper-plane me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), \"Send Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 5\n  }, this);\n};\n_s(XeroxCenterDashboard, \"VJHAFSPjU/jS1C7Ma9QlRu7N0Bo=\", false, function () {\n  return [useAuth];\n});\n_c = XeroxCenterDashboard;\nexport default XeroxCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"XeroxCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "InputGroup", "useAuth", "printJobApi", "fileUploadApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XeroxCenterDashboard", "_s", "user", "printJobs", "setPrintJobs", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "showQuoteModal", "setShowQuoteModal", "quoteData", "setQuoteData", "cost", "estimatedHours", "notes", "filterStatus", "setFilterStatus", "fetchData", "printJobsResponse", "getXeroxCenterJobs", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleQuoteSubmit", "estimatedCompletion", "Date", "setHours", "getHours", "parseInt", "setJobQuote", "id", "parseFloat", "toISOString", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "handleDownloadFile", "response", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "filteredJobs", "filter", "job", "stats", "total", "length", "pending", "includes", "inProgress", "completed", "revenue", "reduce", "sum", "fluid", "username", "md", "Body", "toFixed", "Header", "Select", "style", "width", "value", "onChange", "e", "target", "responsive", "hover", "map", "jobNumber", "created", "toLocaleDateString", "studentName", "studentEmail", "slice", "printType", "copies", "colorType", "paperSize", "remarks", "role", "size", "onClick", "title", "show", "onHide", "closeButton", "Title", "Group", "Label", "Text", "Control", "type", "step", "placeholder", "prev", "required", "min", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/XeroxCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n}\n\nconst XeroxCenterDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n\n        await printJobApi.setJobQuote(\n          selectedJob.id,\n          parseFloat(quoteData.cost),\n          estimatedCompletion.toISOString(),\n          quoteData.notes\n        );\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({ cost: '', estimatedHours: '', notes: '' });\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n\n      // Refresh print jobs after update\n      const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error updating status:', error);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const filteredJobs = filterStatus === 'all' \n    ? printJobs \n    : printJobs.filter(job => job.status === filterStatus);\n\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-store me-2\"></i>\n            Xerox Center Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center border-left-primary\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{stats.total}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-warning\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>Pending</h5>\n              <h3 className=\"text-warning\">{stats.pending}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-info\">\n            <Card.Body>\n              <i className=\"fas fa-cog fa-2x text-info mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-info\">{stats.inProgress}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-success\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-success mb-2\"></i>\n              <h5>Revenue</h5>\n              <h3 className=\"text-success\">${stats.revenue.toFixed(2)}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Job Queue */}\n      <Card>\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-tasks me-2\"></i>\n            Job Queue\n          </h5>\n          <Form.Select \n            style={{ width: 'auto' }}\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n          >\n            <option value=\"all\">All Jobs</option>\n            <option value=\"Requested\">Requested</option>\n            <option value=\"UnderReview\">Under Review</option>\n            <option value=\"Quoted\">Quoted</option>\n            <option value=\"WaitingConfirmation\">Waiting Confirmation</option>\n            <option value=\"Confirmed\">Confirmed</option>\n            <option value=\"InProgress\">In Progress</option>\n            <option value=\"Completed\">Completed</option>\n          </Form.Select>\n        </Card.Header>\n        <Card.Body>\n          {filteredJobs.length > 0 ? (\n            <Table responsive hover>\n              <thead>\n                <tr>\n                  <th>Job #</th>\n                  <th>Student</th>\n                  <th>File Name</th>\n                  <th>Details</th>\n                  <th>Status</th>\n                  <th>Cost</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredJobs.map(job => (\n                  <tr key={job.id}>\n                    <td>\n                      <strong>{job.jobNumber}</strong>\n                      <br />\n                      <small className=\"text-muted\">\n                        {new Date(job.created).toLocaleDateString()}\n                      </small>\n                    </td>\n                    <td>\n                      <div>\n                        <strong>{job.studentName}</strong>\n                        <br />\n                        <small className=\"text-muted\">{job.studentEmail}</small>\n                      </div>\n                    </td>\n                    <td>\n                      <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                      {job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName}\n                    </td>\n                    <td>\n                      <div className=\"small\">\n                        <div><strong>Type:</strong> {job.printType}</div>\n                        <div><strong>Copies:</strong> {job.copies}</div>\n                        <div><strong>Color:</strong> {job.colorType}</div>\n                        <div><strong>Size:</strong> {job.paperSize}</div>\n                        {job.remarks && (\n                          <div><strong>Remarks:</strong> {job.remarks}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td>{getStatusBadge(job.status)}</td>\n                    <td>\n                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                    </td>\n                    <td>\n                      <div className=\"btn-group-vertical\" role=\"group\">\n                        {/* Download button - always available */}\n                        <Button\n                          variant=\"outline-info\"\n                          size=\"sm\"\n                          onClick={() => handleDownloadFile(job.id, job.fileName)}\n                          title=\"Download File\"\n                        >\n                          <i className=\"fas fa-download me-1\"></i>\n                          Download\n                        </Button>\n\n                        {job.status === 'Requested' && (\n                          <>\n                            <Button\n                              variant=\"outline-primary\"\n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                            >\n                              <i className=\"fas fa-dollar-sign me-1\"></i>\n                              Quote\n                            </Button>\n                            <Button\n                              variant=\"outline-danger\"\n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                            >\n                              <i className=\"fas fa-times me-1\"></i>\n                              Reject\n                            </Button>\n                          </>\n                        )}\n\n                        {job.status === 'Confirmed' && (\n                          <Button\n                            variant=\"outline-info\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                          >\n                            <i className=\"fas fa-play me-1\"></i>\n                            Start\n                          </Button>\n                        )}\n\n                        {job.status === 'InProgress' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                          >\n                            <i className=\"fas fa-check me-1\"></i>\n                            Complete\n                          </Button>\n                        )}\n\n                        {job.status === 'Completed' && (\n                          <Button\n                            variant=\"outline-success\"\n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                          >\n                            <i className=\"fas fa-truck me-1\"></i>\n                            Deliver\n                          </Button>\n                        )}\n\n                        <Button variant=\"outline-secondary\" size=\"sm\">\n                          <i className=\"fas fa-comment\"></i>\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </Table>\n          ) : (\n            <Alert variant=\"info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              No jobs found for the selected filter.\n            </Alert>\n          )}\n        </Card.Body>\n      </Card>\n\n      {/* Quote Modal */}\n      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-dollar-sign me-2\"></i>\n            Provide Quote - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <>\n              <div className=\"mb-3 p-3 bg-light rounded\">\n                <h6>Job Details:</h6>\n                <div className=\"row\">\n                  <div className=\"col-6\">\n                    <strong>File:</strong> {selectedJob.fileName}<br />\n                    <strong>Type:</strong> {selectedJob.printType}<br />\n                    <strong>Copies:</strong> {selectedJob.copies}\n                  </div>\n                  <div className=\"col-6\">\n                    <strong>Color:</strong> {selectedJob.colorType}<br />\n                    <strong>Size:</strong> {selectedJob.paperSize}<br />\n                    <strong>Student:</strong> {selectedJob.studentName}\n                  </div>\n                </div>\n                {selectedJob.remarks && (\n                  <div className=\"mt-2\">\n                    <strong>Remarks:</strong> {selectedJob.remarks}\n                  </div>\n                )}\n              </div>\n\n              <Form>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Cost ($)</Form.Label>\n                      <InputGroup>\n                        <InputGroup.Text>$</InputGroup.Text>\n                        <Form.Control\n                          type=\"number\"\n                          step=\"0.01\"\n                          placeholder=\"0.00\"\n                          value={quoteData.cost}\n                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}\n                          required\n                        />\n                      </InputGroup>\n                    </Form.Group>\n                  </Col>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Estimated Hours</Form.Label>\n                      <Form.Control\n                        type=\"number\"\n                        min=\"1\"\n                        placeholder=\"Hours to complete\"\n                        value={quoteData.estimatedHours}\n                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}\n                        required\n                      />\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Notes (Optional)</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={3}\n                    placeholder=\"Any additional notes for the student...\"\n                    value={quoteData.notes}\n                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}\n                  />\n                </Form.Group>\n              </Form>\n            </>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"primary\" \n            onClick={handleQuoteSubmit}\n            disabled={!quoteData.cost || !quoteData.estimatedHours}\n          >\n            <i className=\"fas fa-paper-plane me-2\"></i>\n            Send Quote\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default XeroxCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AACjH,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,EAAkBC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAmB7E,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC;IACzC+B,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAMmC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMvB,WAAW,CAACwB,kBAAkB,CAAC,CAAC;QAChEd,YAAY,CAACa,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDhB,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACE7B,OAAA,CAACT,KAAK;MAACwC,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxBhC,OAAA;QAAGiC,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CX,MAAM;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI9B,WAAW,EAAE;MACf,IAAI;QACF,MAAM+B,mBAAmB,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtCD,mBAAmB,CAACE,QAAQ,CAACF,mBAAmB,CAACG,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAAC/B,SAAS,CAACG,cAAc,CAAC,CAAC;QAEjG,MAAMlB,WAAW,CAAC+C,WAAW,CAC3BpC,WAAW,CAACqC,EAAE,EACdC,UAAU,CAAClC,SAAS,CAACE,IAAI,CAAC,EAC1ByB,mBAAmB,CAACQ,WAAW,CAAC,CAAC,EACjCnC,SAAS,CAACI,KACZ,CAAC;;QAED;QACA,MAAMI,iBAAiB,GAAG,MAAMvB,WAAW,CAACwB,kBAAkB,CAAC,CAAC;QAChEd,YAAY,CAACa,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;IAEAZ,iBAAiB,CAAC,KAAK,CAAC;IACxBF,cAAc,CAAC,IAAI,CAAC;IACpBI,YAAY,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,cAAc,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMgC,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMrD,WAAW,CAACsD,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;;MAEnD;MACA,MAAM9B,iBAAiB,GAAG,MAAMvB,WAAW,CAACwB,kBAAkB,CAAC,CAAC;MAChEd,YAAY,CAACa,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAG,MAAAA,CAAOH,KAAa,EAAEf,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMvD,aAAa,CAACwD,YAAY,CAACL,KAAK,CAAC;;MAExD;MACA,MAAMM,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,QAAQ,CAAC/B,IAAI,CAAC,CAAC;MACtC,MAAMmC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG/B,QAAQ;MACxB4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMgD,YAAY,GAAGtD,YAAY,KAAK,KAAK,GACvCX,SAAS,GACTA,SAAS,CAACkE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC/C,MAAM,KAAKT,YAAY,CAAC;EAExD,MAAMyD,KAAK,GAAG;IACZC,KAAK,EAAErE,SAAS,CAACsE,MAAM;IACvBC,OAAO,EAAEvE,SAAS,CAACkE,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAACK,QAAQ,CAACL,GAAG,CAAC/C,MAAM,CAAC,CAAC,CAACkD,MAAM;IAC3HG,UAAU,EAAEzE,SAAS,CAACkE,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACK,QAAQ,CAACL,GAAG,CAAC/C,MAAM,CAAC,CAAC,CAACkD,MAAM;IAC5FI,SAAS,EAAE1E,SAAS,CAACkE,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACK,QAAQ,CAACL,GAAG,CAAC/C,MAAM,CAAC,CAAC,CAACkD,MAAM;IAC1FK,OAAO,EAAE3E,SAAS,CAAC4E,MAAM,CAAC,CAACC,GAAG,EAAEV,GAAG,KAAKU,GAAG,IAAIV,GAAG,CAAC3D,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EAED,oBACEd,OAAA,CAACf,SAAS;IAACmG,KAAK;IAAApD,QAAA,gBACdhC,OAAA,CAACd,GAAG;MAAC+C,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBhC,OAAA,CAACb,GAAG;QAAA6C,QAAA,gBACFhC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAGiC,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0BAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA;UAAGiC,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgF,QAAQ,EAAC,GAAC;QAAA;UAAAnD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA,CAACd,GAAG;MAAC+C,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBhC,OAAA,CAACb,GAAG;QAACmG,EAAE,EAAE,CAAE;QAAAtD,QAAA,eACThC,OAAA,CAACZ,IAAI;UAAC6C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACZ,IAAI,CAACmG,IAAI;YAAAvD,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DrC,OAAA;cAAAgC,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAE0C,KAAK,CAACC;YAAK;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACb,GAAG;QAACmG,EAAE,EAAE,CAAE;QAAAtD,QAAA,eACThC,OAAA,CAACZ,IAAI;UAAC6C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACZ,IAAI,CAACmG,IAAI;YAAAvD,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDrC,OAAA;cAAAgC,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAE0C,KAAK,CAACG;YAAO;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACb,GAAG;QAACmG,EAAE,EAAE,CAAE;QAAAtD,QAAA,eACThC,OAAA,CAACZ,IAAI;UAAC6C,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eAC5ChC,OAAA,CAACZ,IAAI,CAACmG,IAAI;YAAAvD,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDrC,OAAA;cAAAgC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrC,OAAA;cAAIiC,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAE0C,KAAK,CAACK;YAAU;cAAA7C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACb,GAAG;QAACmG,EAAE,EAAE,CAAE;QAAAtD,QAAA,eACThC,OAAA,CAACZ,IAAI;UAAC6C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACZ,IAAI,CAACmG,IAAI;YAAAvD,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DrC,OAAA;cAAAgC,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,GAAC,GAAC,EAAC0C,KAAK,CAACO,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA,CAACZ,IAAI;MAAA4C,QAAA,gBACHhC,OAAA,CAACZ,IAAI,CAACqG,MAAM;QAACxD,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBACxEhC,OAAA;UAAIiC,SAAS,EAAC,MAAM;UAAAD,QAAA,gBAClBhC,OAAA;YAAGiC,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,aAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA,CAACP,IAAI,CAACiG,MAAM;UACVC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UACzBC,KAAK,EAAE5E,YAAa;UACpB6E,QAAQ,EAAGC,CAAC,IAAK7E,eAAe,CAAC6E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAA7D,QAAA,gBAEjDhC,OAAA;YAAQ6F,KAAK,EAAC,KAAK;YAAA7D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCrC,OAAA;YAAQ6F,KAAK,EAAC,WAAW;YAAA7D,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CrC,OAAA;YAAQ6F,KAAK,EAAC,aAAa;YAAA7D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjDrC,OAAA;YAAQ6F,KAAK,EAAC,QAAQ;YAAA7D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCrC,OAAA;YAAQ6F,KAAK,EAAC,qBAAqB;YAAA7D,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjErC,OAAA;YAAQ6F,KAAK,EAAC,WAAW;YAAA7D,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CrC,OAAA;YAAQ6F,KAAK,EAAC,YAAY;YAAA7D,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/CrC,OAAA;YAAQ6F,KAAK,EAAC,WAAW;YAAA7D,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACdrC,OAAA,CAACZ,IAAI,CAACmG,IAAI;QAAAvD,QAAA,EACPuC,YAAY,CAACK,MAAM,GAAG,CAAC,gBACtB5E,OAAA,CAACV,KAAK;UAAC2G,UAAU;UAACC,KAAK;UAAAlE,QAAA,gBACrBhC,OAAA;YAAAgC,QAAA,eACEhC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAAgC,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA;gBAAAgC,QAAA,EAAI;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClBrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA;gBAAAgC,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfrC,OAAA;gBAAAgC,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrC,OAAA;YAAAgC,QAAA,EACGuC,YAAY,CAAC4B,GAAG,CAAC1B,GAAG,iBACnBzE,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAAgC,QAAA,gBACEhC,OAAA;kBAAAgC,QAAA,EAASyC,GAAG,CAAC2B;gBAAS;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAChCrC,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNrC,OAAA;kBAAOiC,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAC1B,IAAIQ,IAAI,CAACiC,GAAG,CAAC4B,OAAO,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAAgC,QAAA,EAASyC,GAAG,CAAC8B;kBAAW;oBAAArE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAClCrC,OAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrC,OAAA;oBAAOiC,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAEyC,GAAG,CAAC+B;kBAAY;oBAAAtE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,gBACEhC,OAAA;kBAAGiC,SAAS,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACnDoC,GAAG,CAACvC,QAAQ,CAAC0C,MAAM,GAAG,EAAE,GAAGH,GAAG,CAACvC,QAAQ,CAACuE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGhC,GAAG,CAACvC,QAAQ;cAAA;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAKiC,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBhC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,GAAG,CAACiC,SAAS;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,GAAG,CAACkC,MAAM;kBAAA;oBAAAzE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,GAAG,CAACmC,SAAS;kBAAA;oBAAA1E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,GAAG,CAACoC,SAAS;kBAAA;oBAAA3E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAChDoC,GAAG,CAACqC,OAAO,iBACV9G,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACoC,GAAG,CAACqC,OAAO;kBAAA;oBAAA5E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,EAAKP,cAAc,CAACgD,GAAG,CAAC/C,MAAM;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrCrC,OAAA;gBAAAgC,QAAA,EACGyC,GAAG,CAAC3D,IAAI,GAAG,IAAI2D,GAAG,CAAC3D,IAAI,CAAC0E,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;cAAG;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAKiC,SAAS,EAAC,oBAAoB;kBAAC8E,IAAI,EAAC,OAAO;kBAAA/E,QAAA,gBAE9ChC,OAAA,CAACX,MAAM;oBACLuC,OAAO,EAAC,cAAc;oBACtBoF,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAM7D,kBAAkB,CAACqB,GAAG,CAAC5B,EAAE,EAAE4B,GAAG,CAACvC,QAAQ,CAAE;oBACxDgF,KAAK,EAAC,eAAe;oBAAAlF,QAAA,gBAErBhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAE1C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EAERoC,GAAG,CAAC/C,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAAAE,SAAA;oBAAA8B,QAAA,gBACEhC,OAAA,CAACX,MAAM;sBACLuC,OAAO,EAAC,iBAAiB;sBACzBoF,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAM;wBACbxG,cAAc,CAACgE,GAAG,CAAC;wBACnB9D,iBAAiB,CAAC,IAAI,CAAC;sBACzB,CAAE;sBAAAqB,QAAA,gBAEFhC,OAAA;wBAAGiC,SAAS,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SAE7C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrC,OAAA,CAACX,MAAM;sBACLuC,OAAO,EAAC,gBAAgB;sBACxBoF,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAACyB,GAAG,CAAC5B,EAAE,EAAE,UAAU,CAAE;sBAAAb,QAAA,gBAEtDhC,OAAA;wBAAGiC,SAAS,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,UAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH,EAEAoC,GAAG,CAAC/C,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAACX,MAAM;oBACLuC,OAAO,EAAC,cAAc;oBACtBoF,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAACyB,GAAG,CAAC5B,EAAE,EAAE,YAAY,CAAE;oBAAAb,QAAA,gBAExDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,SAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAoC,GAAG,CAAC/C,MAAM,KAAK,YAAY,iBAC1B1B,OAAA,CAACX,MAAM;oBACLuC,OAAO,EAAC,iBAAiB;oBACzBoF,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAACyB,GAAG,CAAC5B,EAAE,EAAE,WAAW,CAAE;oBAAAb,QAAA,gBAEvDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAoC,GAAG,CAAC/C,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAACX,MAAM;oBACLuC,OAAO,EAAC,iBAAiB;oBACzBoF,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAACyB,GAAG,CAAC5B,EAAE,EAAE,WAAW,CAAE;oBAAAb,QAAA,gBAEvDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eAEDrC,OAAA,CAACX,MAAM;oBAACuC,OAAO,EAAC,mBAAmB;oBAACoF,IAAI,EAAC,IAAI;oBAAAhF,QAAA,eAC3ChC,OAAA;sBAAGiC,SAAS,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GA5GEoC,GAAG,CAAC5B,EAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA6GX,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAERrC,OAAA,CAACR,KAAK;UAACoC,OAAO,EAAC,MAAM;UAAAI,QAAA,gBACnBhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0CAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGPrC,OAAA,CAACN,KAAK;MAACyH,IAAI,EAAEzG,cAAe;MAAC0G,MAAM,EAAEA,CAAA,KAAMzG,iBAAiB,CAAC,KAAK,CAAE;MAAAqB,QAAA,gBAClEhC,OAAA,CAACN,KAAK,CAAC+F,MAAM;QAAC4B,WAAW;QAAArF,QAAA,eACvBhC,OAAA,CAACN,KAAK,CAAC4H,KAAK;UAAAtF,QAAA,gBACVhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3B,EAAC7B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4F,SAAS;QAAA;UAAAlE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrC,OAAA,CAACN,KAAK,CAAC6F,IAAI;QAAAvD,QAAA,EACRxB,WAAW,iBACVR,OAAA,CAAAE,SAAA;UAAA8B,QAAA,gBACEhC,OAAA;YAAKiC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxChC,OAAA;cAAAgC,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBrC,OAAA;cAAKiC,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClBhC,OAAA;gBAAKiC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBhC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAAC0B,QAAQ,eAAClC,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACkG,SAAS,eAAC1G,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACmG,MAAM;cAAA;gBAAAzE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNrC,OAAA;gBAAKiC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBhC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACoG,SAAS,eAAC5G,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACqG,SAAS,eAAC7G,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAAC+F,WAAW;cAAA;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL7B,WAAW,CAACsG,OAAO,iBAClB9G,OAAA;cAAKiC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhC,OAAA;gBAAAgC,QAAA,EAAQ;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACsG,OAAO;YAAA;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrC,OAAA,CAACP,IAAI;YAAAuC,QAAA,gBACHhC,OAAA,CAACd,GAAG;cAAA8C,QAAA,gBACFhC,OAAA,CAACb,GAAG;gBAACmG,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eACThC,OAAA,CAACP,IAAI,CAAC8H,KAAK;kBAACtF,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhC,OAAA,CAACP,IAAI,CAAC+H,KAAK;oBAAAxF,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjCrC,OAAA,CAACL,UAAU;oBAAAqC,QAAA,gBACThC,OAAA,CAACL,UAAU,CAAC8H,IAAI;sBAAAzF,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eACpCrC,OAAA,CAACP,IAAI,CAACiI,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,MAAM;sBAClBhC,KAAK,EAAEjF,SAAS,CAACE,IAAK;sBACtBgF,QAAQ,EAAGC,CAAC,IAAKlF,YAAY,CAACiH,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEhH,IAAI,EAAEiF,CAAC,CAACC,MAAM,CAACH;sBAAM,CAAC,CAAC,CAAE;sBAC3EkC,QAAQ;oBAAA;sBAAA7F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrC,OAAA,CAACb,GAAG;gBAACmG,EAAE,EAAE,CAAE;gBAAAtD,QAAA,eACThC,OAAA,CAACP,IAAI,CAAC8H,KAAK;kBAACtF,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhC,OAAA,CAACP,IAAI,CAAC+H,KAAK;oBAAAxF,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCrC,OAAA,CAACP,IAAI,CAACiI,OAAO;oBACXC,IAAI,EAAC,QAAQ;oBACbK,GAAG,EAAC,GAAG;oBACPH,WAAW,EAAC,mBAAmB;oBAC/BhC,KAAK,EAAEjF,SAAS,CAACG,cAAe;oBAChC+E,QAAQ,EAAGC,CAAC,IAAKlF,YAAY,CAACiH,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAE/G,cAAc,EAAEgF,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAC,CAAE;oBACrFkC,QAAQ;kBAAA;oBAAA7F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA,CAACP,IAAI,CAAC8H,KAAK;cAACtF,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhC,OAAA,CAACP,IAAI,CAAC+H,KAAK;gBAAAxF,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCrC,OAAA,CAACP,IAAI,CAACiI,OAAO;gBACXO,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRL,WAAW,EAAC,yCAAyC;gBACrDhC,KAAK,EAAEjF,SAAS,CAACI,KAAM;gBACvB8E,QAAQ,EAAGC,CAAC,IAAKlF,YAAY,CAACiH,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAE9G,KAAK,EAAE+E,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAC;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbrC,OAAA,CAACN,KAAK,CAACyI,MAAM;QAAAnG,QAAA,gBACXhC,OAAA,CAACX,MAAM;UAACuC,OAAO,EAAC,WAAW;UAACqF,OAAO,EAAEA,CAAA,KAAMtG,iBAAiB,CAAC,KAAK,CAAE;UAAAqB,QAAA,EAAC;QAErE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA,CAACX,MAAM;UACLuC,OAAO,EAAC,SAAS;UACjBqF,OAAO,EAAE3E,iBAAkB;UAC3B8F,QAAQ,EAAE,CAACxH,SAAS,CAACE,IAAI,IAAI,CAACF,SAAS,CAACG,cAAe;UAAAiB,QAAA,gBAEvDhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,cAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACjC,EAAA,CA5aID,oBAA8B;EAAA,QACjBP,OAAO;AAAA;AAAAyI,EAAA,GADpBlI,oBAA8B;AA8apC,eAAeA,oBAAoB;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}