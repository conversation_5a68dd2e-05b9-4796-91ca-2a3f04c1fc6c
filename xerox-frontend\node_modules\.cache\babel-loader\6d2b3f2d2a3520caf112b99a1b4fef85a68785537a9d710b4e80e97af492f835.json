{"ast": null, "code": "import { createPopper, popperGenerator, detectOverflow } from \"./createPopper.js\";\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, detectOverflow };", "map": {"version": 3, "names": ["createPopper", "popperGenerator", "detectOverflow"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@popperjs/core/lib/popper-base.js"], "sourcesContent": ["import { createPopper, popperGenerator, detectOverflow } from \"./createPopper.js\";\n// eslint-disable-next-line import/no-unused-modules\nexport { createPopper, popperGenerator, detectOverflow };"], "mappings": "AAAA,SAASA,YAAY,EAAEC,eAAe,EAAEC,cAAc,QAAQ,mBAAmB;AACjF;AACA,SAASF,YAAY,EAAEC,eAAe,EAAEC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}