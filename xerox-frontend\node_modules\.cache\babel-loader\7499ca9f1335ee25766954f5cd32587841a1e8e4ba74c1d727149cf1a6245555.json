{"ast": null, "code": "/**\n * Triggers an event on a given element.\n * \n * @param node the element\n * @param eventName the event name to trigger\n * @param bubbles whether the event should bubble up\n * @param cancelable whether the event should be cancelable\n */\nexport default function triggerEvent(node, eventName, bubbles, cancelable) {\n  if (bubbles === void 0) {\n    bubbles = false;\n  }\n  if (cancelable === void 0) {\n    cancelable = true;\n  }\n  if (node) {\n    var event = document.createEvent('HTMLEvents');\n    event.initEvent(eventName, bubbles, cancelable);\n    node.dispatchEvent(event);\n  }\n}", "map": {"version": 3, "names": ["triggerEvent", "node", "eventName", "bubbles", "cancelable", "event", "document", "createEvent", "initEvent", "dispatchEvent"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/dom-helpers/esm/triggerEvent.js"], "sourcesContent": ["/**\n * Triggers an event on a given element.\n * \n * @param node the element\n * @param eventName the event name to trigger\n * @param bubbles whether the event should bubble up\n * @param cancelable whether the event should be cancelable\n */\nexport default function triggerEvent(node, eventName, bubbles, cancelable) {\n  if (bubbles === void 0) {\n    bubbles = false;\n  }\n\n  if (cancelable === void 0) {\n    cancelable = true;\n  }\n\n  if (node) {\n    var event = document.createEvent('HTMLEvents');\n    event.initEvent(eventName, bubbles, cancelable);\n    node.dispatchEvent(event);\n  }\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,YAAYA,CAACC,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,EAAE;EACzE,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,KAAK;EACjB;EAEA,IAAIC,UAAU,KAAK,KAAK,CAAC,EAAE;IACzBA,UAAU,GAAG,IAAI;EACnB;EAEA,IAAIH,IAAI,EAAE;IACR,IAAII,KAAK,GAAGC,QAAQ,CAACC,WAAW,CAAC,YAAY,CAAC;IAC9CF,KAAK,CAACG,SAAS,CAACN,SAAS,EAAEC,OAAO,EAAEC,UAAU,CAAC;IAC/CH,IAAI,CAACQ,aAAa,CAACJ,KAAK,CAAC;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}