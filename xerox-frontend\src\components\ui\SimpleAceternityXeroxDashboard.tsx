import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  DollarSign, 
  Download, 
  MessageCircle, 
  Eye, 
  Activity,
  Users,
  Printer,
  Star,
  Settings,
  Building,
  BarChart3,
  RefreshCw,
  PieChart,
  TrendingUp,
  Moon,
  Sun,
  Play,
  Truck,
  X
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { printJobApi } from '../../services/api';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  studentName: string;
  studentEmail: string;
  created: string;
  printType: string;
  copies: number;
  colorType: string;
  paperSize: string;
  priority: string;
}

const SimpleAceternityXeroxDashboard: React.FC = () => {
  const { user } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [jobs, setJobs] = useState<PrintJob[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Professional color scheme
  const colors = {
    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue
    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray
    accent: isDarkMode ? '#10B981' : '#059669', // Emerald
    warning: isDarkMode ? '#F59E0B' : '#D97706', // Amber
    danger: isDarkMode ? '#EF4444' : '#DC2626', // Red
    success: isDarkMode ? '#10B981' : '#059669', // Emerald
    background: isDarkMode ? '#111827' : '#F9FAFB',
    surface: isDarkMode ? '#1F2937' : '#FFFFFF',
    text: isDarkMode ? '#F9FAFB' : '#111827',
    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'
  };

  useEffect(() => {
    fetchJobs();
    const interval = setInterval(fetchJobs, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchJobs = async () => {
    setIsRefreshing(true);
    try {
      const response = await printJobApi.getXeroxCenterJobs();
      setJobs(response.data || []);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      setJobs([]);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: { [key: string]: { bg: string; color: string; icon: any } } = {
      'Requested': { bg: colors.secondary + '20', color: colors.secondary, icon: Clock },
      'UnderReview': { bg: colors.primary + '20', color: colors.primary, icon: Eye },
      'Quoted': { bg: colors.warning + '20', color: colors.warning, icon: DollarSign },
      'Confirmed': { bg: colors.accent + '20', color: colors.accent, icon: CheckCircle },
      'InProgress': { bg: colors.primary + '20', color: colors.primary, icon: Activity },
      'Completed': { bg: colors.success + '20', color: colors.success, icon: CheckCircle },
      'Delivered': { bg: colors.success + '20', color: colors.success, icon: Truck }
    };

    const config = statusConfig[status] || { bg: colors.secondary + '20', color: colors.secondary, icon: Clock };
    const IconComponent = config.icon;
    
    return (
      <Badge 
        className="d-flex align-items-center gap-1 px-3 py-2"
        style={{
          background: config.bg,
          color: config.color,
          border: 'none'
        }}
      >
        <IconComponent size={14} />
        {status}
      </Badge>
    );
  };

  const handleStatusUpdate = async (jobId: number, newStatus: string) => {
    try {
      await printJobApi.updateJobStatus(jobId, newStatus);
      fetchJobs();
    } catch (error) {
      console.error('Error updating job status:', error);
    }
  };

  // Calculate statistics
  const totalJobs = jobs.length;
  const pendingJobs = jobs.filter(job => ['Requested', 'UnderReview'].includes(job.status)).length;
  const inProgressJobs = jobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length;
  const completedJobs = jobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;
  const totalRevenue = jobs.reduce((sum, job) => sum + (job.cost || 0), 0);

  const floatingShapes = Array.from({ length: 6 }, (_, i) => (
    <motion.div
      key={i}
      className="position-absolute rounded-circle"
      style={{
        background: `linear-gradient(135deg, ${colors.primary}15, ${colors.accent}15)`,
        width: `${Math.random() * 60 + 20}px`,
        height: `${Math.random() * 60 + 20}px`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        opacity: 0.3,
        zIndex: 0
      }}
      animate={{
        x: [0, Math.random() * 100 - 50],
        y: [0, Math.random() * 100 - 50],
        rotate: [0, 360],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: Math.random() * 20 + 10,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
    />
  ));

  return (
    <div 
      className="min-h-screen position-relative"
      style={{
        background: isDarkMode 
          ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)'
          : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)',
        minHeight: '100vh'
      }}
    >
      {/* Animated Background */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {floatingShapes}
        <div className="position-absolute w-100 h-100" style={{
          background: isDarkMode
            ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'
        }} />
      </div>

      {/* Theme Toggle Button */}
      <motion.button
        onClick={toggleTheme}
        className="position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center"
        style={{
          width: '50px',
          height: '50px',
          background: colors.surface,
          boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',
          color: colors.text,
          zIndex: 1000
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
      </motion.button>

      <Container fluid className="position-relative py-4" style={{ zIndex: 1 }}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-5"
          >
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="d-inline-flex align-items-center justify-content-center mb-3"
              style={{
                width: '80px',
                height: '80px',
                background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                borderRadius: '20px',
                boxShadow: `0 20px 40px ${colors.primary}30`
              }}
            >
              <Building className="text-white" size={40} />
            </motion.div>
            <h1 className="fw-bold mb-2" style={{ fontSize: '2.5rem', color: colors.text }}>
              Xerox Center Dashboard
            </h1>
            <p className="fs-5 mb-4" style={{ color: colors.textSecondary }}>
              Welcome back, <span className="fw-semibold">{user?.username}</span>! Manage your printing business
            </p>
            
            <div className="d-flex align-items-center justify-content-center gap-3 flex-wrap">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  size="lg"
                  onClick={fetchJobs}
                  disabled={isRefreshing}
                  className="px-4 py-3 rounded-4 fw-semibold border-0"
                  style={{
                    background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                    color: '#fff'
                  }}
                >
                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />
                  Refresh
                </Button>
              </motion.div>
              
              <Badge 
                className="px-3 py-2 fs-6"
                style={{
                  background: colors.surface,
                  color: colors.text,
                  boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.2)' : '0 4px 20px rgba(0,0,0,0.1)'
                }}
              >
                Last updated: {new Date().toLocaleTimeString()}
              </Badge>
            </div>
          </motion.div>

          {/* Navigation Tabs */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-4"
          >
            <Card 
              className="border-0 shadow-lg"
              style={{
                background: colors.surface,
                backdropFilter: 'blur(20px)',
                borderRadius: '20px',
                boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'
              }}
            >
              <Card.Body className="p-2">
                <Nav variant="pills" className="justify-content-center flex-wrap">
                  {[
                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                    { key: 'jobs', label: 'Job Queue', icon: FileText },
                    { key: 'analytics', label: 'Analytics', icon: PieChart },
                    { key: 'customers', label: 'Customers', icon: Users },
                    { key: 'settings', label: 'Settings', icon: Settings }
                  ].map(tab => {
                    const IconComponent = tab.icon;
                    return (
                      <Nav.Item key={tab.key} className="m-1">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Nav.Link
                            active={activeTab === tab.key}
                            onClick={() => setActiveTab(tab.key)}
                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center`}
                            style={{
                              background: activeTab === tab.key 
                                ? `linear-gradient(135deg, ${colors.primary}, ${colors.accent})` 
                                : 'transparent',
                              border: 'none',
                              transition: 'all 0.3s ease',
                              color: activeTab === tab.key ? '#fff' : colors.textSecondary,
                              boxShadow: activeTab === tab.key 
                                ? `0 10px 30px ${colors.primary}20` 
                                : 'none'
                            }}
                          >
                            <IconComponent size={18} className="me-2" />
                            <span className="d-none d-md-inline">{tab.label}</span>
                          </Nav.Link>
                        </motion.div>
                      </Nav.Item>
                    );
                  })}
                </Nav>
              </Card.Body>
            </Card>
          </motion.div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'dashboard' && (
              <Row className="g-4">
                {/* Statistics Cards */}
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card 
                      className="h-100 border-0 shadow-lg"
                      style={{ 
                        background: colors.surface,
                        borderRadius: '20px',
                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'
                      }}
                    >
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div 
                            className="d-inline-flex align-items-center justify-content-center"
                            style={{
                              width: '60px',
                              height: '60px',
                              background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                              borderRadius: '15px'
                            }}
                          >
                            <FileText className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="mb-1" style={{ color: colors.textSecondary }}>Total Jobs</h6>
                        <h2 className="fw-bold" style={{ color: colors.text }}>{totalJobs}</h2>
                        <small style={{ color: colors.success }}>
                          <TrendingUp size={12} className="me-1" />
                          +12% vs last month
                        </small>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card 
                      className="h-100 border-0 shadow-lg"
                      style={{ 
                        background: colors.surface,
                        borderRadius: '20px',
                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'
                      }}
                    >
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div 
                            className="d-inline-flex align-items-center justify-content-center"
                            style={{
                              width: '60px',
                              height: '60px',
                              background: `linear-gradient(135deg, ${colors.warning}, #F59E0B)`,
                              borderRadius: '15px'
                            }}
                          >
                            <Clock className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="mb-1" style={{ color: colors.textSecondary }}>Pending</h6>
                        <h2 className="fw-bold" style={{ color: colors.text }}>{pendingJobs}</h2>
                        <small style={{ color: colors.warning }}>
                          <Activity size={12} className="me-1" />
                          Needs attention
                        </small>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card 
                      className="h-100 border-0 shadow-lg"
                      style={{ 
                        background: colors.surface,
                        borderRadius: '20px',
                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'
                      }}
                    >
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div 
                            className="d-inline-flex align-items-center justify-content-center"
                            style={{
                              width: '60px',
                              height: '60px',
                              background: `linear-gradient(135deg, ${colors.success}, #10B981)`,
                              borderRadius: '15px'
                            }}
                          >
                            <CheckCircle className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="mb-1" style={{ color: colors.textSecondary }}>Completed</h6>
                        <h2 className="fw-bold" style={{ color: colors.text }}>{completedJobs}</h2>
                        <small style={{ color: colors.success }}>
                          <Star size={12} className="me-1" />
                          Finished jobs
                        </small>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card 
                      className="h-100 border-0 shadow-lg"
                      style={{ 
                        background: colors.surface,
                        borderRadius: '20px',
                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'
                      }}
                    >
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div 
                            className="d-inline-flex align-items-center justify-content-center"
                            style={{
                              width: '60px',
                              height: '60px',
                              background: `linear-gradient(135deg, ${colors.accent}, #059669)`,
                              borderRadius: '15px'
                            }}
                          >
                            <DollarSign className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="mb-1" style={{ color: colors.textSecondary }}>Revenue</h6>
                        <h2 className="fw-bold" style={{ color: colors.text }}>${totalRevenue.toFixed(2)}</h2>
                        <small style={{ color: colors.accent }}>
                          <TrendingUp size={12} className="me-1" />
                          This month
                        </small>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>

                {/* Recent Jobs Table */}
                <Col lg={12}>
                  <Card 
                    className="border-0 shadow-lg"
                    style={{ 
                      background: colors.surface,
                      borderRadius: '20px',
                      boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'
                    }}
                  >
                    <Card.Header className="border-0 bg-transparent">
                      <div className="d-flex align-items-center justify-content-between">
                        <h4 className="fw-bold mb-0" style={{ color: colors.text }}>
                          <Activity className="me-2" size={20} />
                          Recent Jobs
                        </h4>
                        <Badge 
                          className="px-3 py-2"
                          style={{
                            background: `${colors.primary}20`,
                            color: colors.primary
                          }}
                        >
                          {jobs.length} total
                        </Badge>
                      </div>
                    </Card.Header>
                    <Card.Body>
                      {jobs.length > 0 ? (
                        <div className="table-responsive">
                          <Table className={isDarkMode ? 'table-dark' : ''} hover>
                            <thead>
                              <tr>
                                <th>Job Number</th>
                                <th>Student</th>
                                <th>File Name</th>
                                <th>Status</th>
                                <th>Cost</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {jobs.slice(0, 10).map((job: PrintJob) => (
                                <tr key={job.id}>
                                  <td className="fw-semibold">{job.jobNumber}</td>
                                  <td>{job.studentName}</td>
                                  <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>
                                  <td>{getStatusBadge(job.status)}</td>
                                  <td className="fw-semibold" style={{ color: colors.success }}>
                                    {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                                  </td>
                                  <td>
                                    <div className="d-flex gap-1">
                                      <Button 
                                        variant="outline-primary" 
                                        size="sm"
                                        style={{ borderRadius: '8px' }}
                                      >
                                        <Eye size={14} />
                                      </Button>
                                      <Button 
                                        variant="outline-success" 
                                        size="sm"
                                        style={{ borderRadius: '8px' }}
                                        onClick={() => handleStatusUpdate(job.id, 'InProgress')}
                                      >
                                        <Play size={14} />
                                      </Button>
                                      <Button 
                                        variant="outline-info" 
                                        size="sm"
                                        style={{ borderRadius: '8px' }}
                                      >
                                        <MessageCircle size={14} />
                                      </Button>
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="text-center py-5"
                        >
                          <div className="mb-4">
                            <div 
                              className="d-inline-flex align-items-center justify-content-center"
                              style={{
                                width: '80px',
                                height: '80px',
                                background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                                borderRadius: '20px'
                              }}
                            >
                              <FileText className="text-white" size={40} />
                            </div>
                          </div>
                          <h5 className="fw-semibold mb-2" style={{ color: colors.text }}>No jobs yet</h5>
                          <p style={{ color: colors.textSecondary }}>Jobs will appear here when students submit them.</p>
                        </motion.div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}

            {/* Other tabs content */}
            {activeTab !== 'dashboard' && (
              <Card 
                className="border-0 shadow-lg"
                style={{ 
                  background: colors.surface,
                  borderRadius: '20px',
                  boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'
                }}
              >
                <Card.Body className="text-center py-5">
                  <h4 className="mb-3" style={{ color: colors.text }}>
                    {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Tab
                  </h4>
                  <p style={{ color: colors.textSecondary }}>
                    This tab is under development. Coming soon!
                  </p>
                </Card.Body>
              </Card>
            )}
          </motion.div>
        </motion.div>
      </Container>
    </div>
  );
};

export default SimpleAceternityXeroxDashboard;
