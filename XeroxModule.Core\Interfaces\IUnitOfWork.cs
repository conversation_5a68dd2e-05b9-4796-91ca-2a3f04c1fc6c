using XeroxModule.Core.Entities;

namespace XeroxModule.Core.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        IRepository<User> Users { get; }
        IRepository<Student> Students { get; }
        IRepository<XeroxCenter> XeroxCenters { get; }
        IRepository<FileUpload> FileUploads { get; }
        IRepository<PrintJob> PrintJobs { get; }
        IRepository<Message> Messages { get; }
        IRepository<Notification> Notifications { get; }
        IRepository<Rating> Ratings { get; }
        
        Task<int> SaveChangesAsync();
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
    }
}
