using Microsoft.EntityFrameworkCore;
using XeroxModule.Core.Entities;

namespace XeroxModule.Infrastructure.Data
{
    public class XeroxDbContext : DbContext
    {
        public XeroxDbContext(DbContextOptions<XeroxDbContext> options) : base(options)
        {
        }
        
        public DbSet<User> Users { get; set; }
        public DbSet<Student> Students { get; set; }
        public DbSet<XeroxCenter> XeroxCenters { get; set; }
        public DbSet<FileUpload> FileUploads { get; set; }
        public DbSet<PrintJob> PrintJobs { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<Rating> Ratings { get; set; }
        
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure table names to match the database schema
            modelBuilder.Entity<User>().ToTable("ZRX_User");
            modelBuilder.Entity<Student>().ToTable("STU_Student");
            modelBuilder.Entity<XeroxCenter>().ToTable("ZRX_XeroxCenter");
            modelBuilder.Entity<FileUpload>().ToTable("ZRX_FileUpload");
            modelBuilder.Entity<PrintJob>().ToTable("ZRX_PrintJob");
            modelBuilder.Entity<Message>().ToTable("ZRX_Message");
            modelBuilder.Entity<Notification>().ToTable("ZRX_Notification");
            modelBuilder.Entity<Rating>().ToTable("ZRX_Rating");
            
            // Configure User entity
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.UserID);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.UserType).HasMaxLength(20);
            });
            
            // Configure Student entity
            modelBuilder.Entity<Student>(entity =>
            {
                entity.HasKey(e => e.StudentID);
                entity.HasIndex(e => e.StudentNumber).IsUnique();
                entity.HasOne(e => e.User)
                    .WithOne(u => u.Student)
                    .HasForeignKey<Student>(e => e.UserID)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Configure XeroxCenter entity
            modelBuilder.Entity<XeroxCenter>(entity =>
            {
                entity.HasKey(e => e.XeroxCenterID);
                entity.HasOne(e => e.User)
                    .WithOne(u => u.XeroxCenter)
                    .HasForeignKey<XeroxCenter>(e => e.UserID)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.CreatedUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.ModifiedUser)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.Property(e => e.AverageRating).HasColumnType("decimal(3,2)");
            });
            
            // Configure FileUpload entity
            modelBuilder.Entity<FileUpload>(entity =>
            {
                entity.HasKey(e => e.FileUploadID);
                entity.HasOne(e => e.Student)
                    .WithMany(s => s.FileUploads)
                    .HasForeignKey(e => e.StudentID)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.PreferredXeroxCenter)
                    .WithMany(x => x.PreferredFileUploads)
                    .HasForeignKey(e => e.PreferredXeroxCenterID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.CreatedUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.ModifiedUser)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure PrintJob entity
            modelBuilder.Entity<PrintJob>(entity =>
            {
                entity.HasKey(e => e.PrintJobID);
                entity.HasIndex(e => e.JobNumber).IsUnique();
                entity.Property(e => e.Cost).HasColumnType("decimal(10,2)");
                
                entity.HasOne(e => e.XeroxCenter)
                    .WithMany(x => x.PrintJobs)
                    .HasForeignKey(e => e.XeroxCenterID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.FileUpload)
                    .WithMany(f => f.PrintJobs)
                    .HasForeignKey(e => e.FileUploadID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.CreatedUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.ModifiedUser)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedUserID)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure Message entity
            modelBuilder.Entity<Message>(entity =>
            {
                entity.HasKey(e => e.MessageID);
                entity.HasOne(e => e.PrintJob)
                    .WithMany(p => p.Messages)
                    .HasForeignKey(e => e.PrintJobID)
                    .OnDelete(DeleteBehavior.Cascade);
                    
                entity.HasOne(e => e.SenderUser)
                    .WithMany(u => u.SentMessages)
                    .HasForeignKey(e => e.SenderUserID)
                    .OnDelete(DeleteBehavior.Restrict);
                    
                entity.HasOne(e => e.ReceiverUser)
                    .WithMany(u => u.ReceivedMessages)
                    .HasForeignKey(e => e.ReceiverUserID)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure Notification entity
            modelBuilder.Entity<Notification>(entity =>
            {
                entity.HasKey(e => e.NotificationID);
                entity.HasOne(e => e.User)
                    .WithMany(u => u.Notifications)
                    .HasForeignKey(e => e.UserID)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Configure Rating entity
            modelBuilder.Entity<Rating>(entity =>
            {
                entity.HasKey(e => e.RatingID);
                entity.HasIndex(e => new { e.PrintJobID, e.StudentID }).IsUnique();

                // Map RatingValue property to Rating column in database
                entity.Property(e => e.RatingValue).HasColumnName("Rating");

                entity.HasOne(e => e.PrintJob)
                    .WithMany(p => p.Ratings)
                    .HasForeignKey(e => e.PrintJobID)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Student)
                    .WithMany(s => s.Ratings)
                    .HasForeignKey(e => e.StudentID)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.XeroxCenter)
                    .WithMany(x => x.Ratings)
                    .HasForeignKey(e => e.XeroxCenterID)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
