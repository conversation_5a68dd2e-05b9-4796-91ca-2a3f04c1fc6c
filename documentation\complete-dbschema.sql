-- Complete Database Schema for Xerox Module System
-- This includes all missing tables referenced in the original schema

-- Users table (base table for authentication)
CREATE TABLE ZRX_User (
    UserID                  INT IDENTITY(1,1) PRIMARY KEY,
    Username                VARCHAR(50)       NOT NULL UNIQUE,
    Email                   VARCHAR(100)      NOT NULL UNIQUE,
    PasswordHash            VARCHAR(255)      NOT NULL,
    UserType                VARCHAR(20)       NOT NULL, -- 'Student' or 'XeroxCenter'
    IsActive                BIT               NOT NULL DEFAULT 1,
    CreatedAt               DATETIME          NOT NULL DEFAULT GETDATE(),
    ModifiedAt              DATETIME          NULL,
    LastLoginAt             DATETIME          NULL
);

-- Students table
CREATE TABLE STU_Student (
    StudentID               INT IDENTITY(1,1) PRIMARY KEY,
    UserID                  INT               NOT NULL,
    StudentNumber           VARCHAR(20)       NOT NULL UNIQUE,
    FirstName               VARCHAR(50)       NOT NULL,
    LastName                VARCHAR(50)       NOT NULL,
    PhoneNumber             VARCHAR(20)       NULL,
    Department              VARCHAR(100)      NULL,
    Year                    INT               NULL,
    IsActive                BIT               NOT NULL DEFAULT 1,
    CreatedAt               DATETIME          NOT NULL DEFAULT GETDATE(),
    ModifiedAt              DATETIME          NULL,
    
    CONSTRAINT FK_STU_Student_ZRX_User_UserID FOREIGN KEY (UserID) REFERENCES ZRX_User(UserID)
);

-- Xerox Centers table (updated from original)
CREATE TABLE ZRX_XeroxCenter (
    XeroxCenterID           INT IDENTITY(1,1) PRIMARY KEY,
    UserID                  INT               NOT NULL,
    XeroxCenterName         VARCHAR(100)      NOT NULL,
    Location                VARCHAR(200)      NOT NULL,
    ContactPerson           VARCHAR(100)      NULL,
    Email                   VARCHAR(100)      NULL,
    PhoneNumber             VARCHAR(20)       NOT NULL,
    TotalJobs               INT               NOT NULL DEFAULT 0,
    PendingJobs             INT               NOT NULL DEFAULT 0,
    CompletedJobs           INT               NOT NULL DEFAULT 0,
    AverageRating           DECIMAL(3,2)      NULL,
    TotalRatings            INT               NOT NULL DEFAULT 0,
    IsActive                BIT               NOT NULL DEFAULT 1,
    CreatedUserID           INT               NOT NULL,
    ModifiedUserID          INT               NULL,
    Created                 DATETIME          NOT NULL DEFAULT GETDATE(),
    Modified                DATETIME          NULL,
    Description             VARCHAR(500)      NULL,
    
    CONSTRAINT FK_ZRX_XeroxCenter_ZRX_User_UserID FOREIGN KEY (UserID) REFERENCES ZRX_User(UserID),
    CONSTRAINT FK_ZRX_XeroxCenter_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
    CONSTRAINT FK_ZRX_XeroxCenter_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);

-- File Upload table (updated from original)
CREATE TABLE ZRX_FileUpload (
    FileUploadID            INT IDENTITY(1,1) PRIMARY KEY,
    FileName                VARCHAR(255)      NOT NULL,
    OriginalFileName        VARCHAR(255)      NOT NULL,
    FilePath                VARCHAR(500)      NOT NULL,
    FileSize                BIGINT            NOT NULL,
    FileType                VARCHAR(50)       NOT NULL,
    Remarks                 VARCHAR(500)      NULL,
    StudentID               INT               NOT NULL,
    PreferredXeroxCenterID  INT               NULL,
    UploadedAt              DATETIME          NOT NULL DEFAULT GETDATE(),
    PrintType               VARCHAR(50)       NOT NULL, -- 'Print', 'Xerox', 'Binding', etc.
    Copies                  INT               NOT NULL DEFAULT 1,
    ColorType               VARCHAR(20)       NOT NULL DEFAULT 'BlackWhite', -- 'BlackWhite', 'Color'
    PaperSize               VARCHAR(20)       NOT NULL DEFAULT 'A4', -- 'A4', 'A3', 'Letter', etc.
    CreatedUserID           INT               NOT NULL,
    ModifiedUserID          INT               NULL,
    Created                 DATETIME          NOT NULL DEFAULT GETDATE(),
    Modified                DATETIME          NULL,
    Description             VARCHAR(500)      NULL,
    
    CONSTRAINT FK_ZRX_FileUpload_STU_Student_StudentID FOREIGN KEY (StudentID) REFERENCES STU_Student(StudentID),
    CONSTRAINT FK_ZRX_FileUpload_ZRX_XeroxCenter_PreferredXeroxCenterID FOREIGN KEY (PreferredXeroxCenterID) REFERENCES ZRX_XeroxCenter(XeroxCenterID),
    CONSTRAINT FK_ZRX_FileUpload_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
    CONSTRAINT FK_ZRX_FileUpload_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);

-- Print Job table (updated from original)
CREATE TABLE ZRX_PrintJob (
    PrintJobID              INT IDENTITY(1,1) PRIMARY KEY,
    JobNumber               VARCHAR(50)       NOT NULL UNIQUE,
    XeroxCenterID           INT               NOT NULL,
    FileUploadID            INT               NOT NULL,
    Cost                    DECIMAL(10,2)     NULL,
    Status                  VARCHAR(50)       NOT NULL DEFAULT 'Requested', -- 'Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation', 'Confirmed', 'InProgress', 'Completed', 'Delivered', 'Rejected', 'Cancelled'
    EstimatedCompletionTime DATETIME          NULL,
    ActualCompletionTime    DATETIME          NULL,
    DeliveredAt             DATETIME          NULL,
    Priority                VARCHAR(20)       NOT NULL DEFAULT 'Normal', -- 'Low', 'Normal', 'High', 'Urgent'
    CreatedUserID           INT               NOT NULL,
    ModifiedUserID          INT               NULL,
    Created                 DATETIME          NOT NULL DEFAULT GETDATE(),
    Modified                DATETIME          NULL,
    Description             VARCHAR(500)      NULL,
    
    CONSTRAINT FK_ZRX_PrintJob_ZRX_XeroxCenter_XeroxCenterID FOREIGN KEY (XeroxCenterID) REFERENCES ZRX_XeroxCenter(XeroxCenterID),
    CONSTRAINT FK_ZRX_PrintJob_ZRX_FileUpload_FileUploadID FOREIGN KEY (FileUploadID) REFERENCES ZRX_FileUpload(FileUploadID),
    CONSTRAINT FK_ZRX_PrintJob_ZRX_User_CreatedUserID FOREIGN KEY (CreatedUserID) REFERENCES ZRX_User(UserID),
    CONSTRAINT FK_ZRX_PrintJob_ZRX_User_ModifiedUserID FOREIGN KEY (ModifiedUserID) REFERENCES ZRX_User(UserID)
);

-- Messages table for communication between students and xerox centers
CREATE TABLE ZRX_Message (
    MessageID               INT IDENTITY(1,1) PRIMARY KEY,
    PrintJobID              INT               NOT NULL,
    SenderUserID            INT               NOT NULL,
    ReceiverUserID          INT               NOT NULL,
    MessageText             VARCHAR(1000)     NOT NULL,
    IsRead                  BIT               NOT NULL DEFAULT 0,
    SentAt                  DATETIME          NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ZRX_Message_ZRX_PrintJob_PrintJobID FOREIGN KEY (PrintJobID) REFERENCES ZRX_PrintJob(PrintJobID),
    CONSTRAINT FK_ZRX_Message_ZRX_User_SenderUserID FOREIGN KEY (SenderUserID) REFERENCES ZRX_User(UserID),
    CONSTRAINT FK_ZRX_Message_ZRX_User_ReceiverUserID FOREIGN KEY (ReceiverUserID) REFERENCES ZRX_User(UserID)
);

-- Notifications table
CREATE TABLE ZRX_Notification (
    NotificationID          INT IDENTITY(1,1) PRIMARY KEY,
    UserID                  INT               NOT NULL,
    Title                   VARCHAR(200)      NOT NULL,
    Message                 VARCHAR(500)      NOT NULL,
    Type                    VARCHAR(50)       NOT NULL, -- 'JobUpdate', 'NewMessage', 'System', etc.
    RelatedEntityID         INT               NULL, -- Could be PrintJobID, MessageID, etc.
    RelatedEntityType       VARCHAR(50)       NULL, -- 'PrintJob', 'Message', etc.
    IsRead                  BIT               NOT NULL DEFAULT 0,
    CreatedAt               DATETIME          NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ZRX_Notification_ZRX_User_UserID FOREIGN KEY (UserID) REFERENCES ZRX_User(UserID)
);

-- Ratings table for students to rate xerox centers
CREATE TABLE ZRX_Rating (
    RatingID                INT IDENTITY(1,1) PRIMARY KEY,
    PrintJobID              INT               NOT NULL,
    StudentID               INT               NOT NULL,
    XeroxCenterID           INT               NOT NULL,
    Rating                  INT               NOT NULL CHECK (Rating >= 1 AND Rating <= 5),
    Review                  VARCHAR(1000)     NULL,
    CreatedAt               DATETIME          NOT NULL DEFAULT GETDATE(),
    
    CONSTRAINT FK_ZRX_Rating_ZRX_PrintJob_PrintJobID FOREIGN KEY (PrintJobID) REFERENCES ZRX_PrintJob(PrintJobID),
    CONSTRAINT FK_ZRX_Rating_STU_Student_StudentID FOREIGN KEY (StudentID) REFERENCES STU_Student(StudentID),
    CONSTRAINT FK_ZRX_Rating_ZRX_XeroxCenter_XeroxCenterID FOREIGN KEY (XeroxCenterID) REFERENCES ZRX_XeroxCenter(XeroxCenterID),
    CONSTRAINT UK_ZRX_Rating_PrintJobID_StudentID UNIQUE (PrintJobID, StudentID)
);

-- Create indexes for better performance
CREATE INDEX IX_ZRX_PrintJob_Status ON ZRX_PrintJob(Status);
CREATE INDEX IX_ZRX_PrintJob_XeroxCenterID ON ZRX_PrintJob(XeroxCenterID);
CREATE INDEX IX_ZRX_PrintJob_Created ON ZRX_PrintJob(Created);
CREATE INDEX IX_ZRX_Message_PrintJobID ON ZRX_Message(PrintJobID);
CREATE INDEX IX_ZRX_Message_IsRead ON ZRX_Message(IsRead);
CREATE INDEX IX_ZRX_Notification_UserID_IsRead ON ZRX_Notification(UserID, IsRead);
CREATE INDEX IX_ZRX_FileUpload_StudentID ON ZRX_FileUpload(StudentID);
CREATE INDEX IX_ZRX_FileUpload_UploadedAt ON ZRX_FileUpload(UploadedAt);
