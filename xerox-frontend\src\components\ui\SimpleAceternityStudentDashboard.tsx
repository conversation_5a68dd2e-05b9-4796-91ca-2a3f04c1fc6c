import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';
import { motion } from 'framer-motion';
import {
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Download,
  MessageCircle,
  Eye,
  Upload,
  Activity,
  Printer,
  Star,
  History,
  Settings,
  User,
  BarChart3,
  RefreshCw,
  PieChart,
  Heart,
  Moon,
  Sun,
  TrendingUp
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { printJobApi, xeroxCenterApi } from '../../services/api';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  xeroxCenterName: string;
  created: string;
  printType: string;
  copies: number;
  colorType: string;
  paperSize: string;
  priority: string;
}

interface XeroxCenter {
  id: number;
  name: string;
  location: string;
  pendingJobs: number;
  averageRating: number;
  isActive: boolean;
}

const SimpleAceternityStudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Professional color scheme
  const colors = {
    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue
    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray
    accent: isDarkMode ? '#10B981' : '#059669', // Emerald
    warning: isDarkMode ? '#F59E0B' : '#D97706', // Amber
    danger: isDarkMode ? '#EF4444' : '#DC2626', // Red
    success: isDarkMode ? '#10B981' : '#059669', // Emerald
    background: isDarkMode ? '#111827' : '#F9FAFB',
    surface: isDarkMode ? '#1F2937' : '#FFFFFF',
    text: isDarkMode ? '#F9FAFB' : '#111827',
    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'
  };

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    setIsRefreshing(true);
    try {
      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([
        printJobApi.getStudentJobs(),
        xeroxCenterApi.getAll()
      ]);
      
      setPrintJobs(printJobsResponse.data || []);
      setXeroxCenters(xeroxCentersResponse.data || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setPrintJobs([]);
      setXeroxCenters([]);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: { [key: string]: { bg: string; color: string; icon: any } } = {
      'Requested': { bg: colors.secondary + '20', color: colors.secondary, icon: Clock },
      'UnderReview': { bg: colors.primary + '20', color: colors.primary, icon: Eye },
      'Quoted': { bg: colors.warning + '20', color: colors.warning, icon: DollarSign },
      'Confirmed': { bg: colors.accent + '20', color: colors.accent, icon: CheckCircle },
      'InProgress': { bg: colors.primary + '20', color: colors.primary, icon: Activity },
      'Completed': { bg: colors.success + '20', color: colors.success, icon: CheckCircle },
      'Delivered': { bg: colors.success + '20', color: colors.success, icon: CheckCircle }
    };

    const config = statusConfig[status] || { bg: colors.secondary + '20', color: colors.secondary, icon: Clock };
    const IconComponent = config.icon;

    return (
      <Badge
        className="d-flex align-items-center gap-1 px-3 py-2"
        style={{
          background: config.bg,
          color: config.color,
          border: 'none'
        }}
      >
        <IconComponent size={14} />
        {status}
      </Badge>
    );
  };

  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);
  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;
  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Floating shapes for background
  const floatingShapes = Array.from({ length: 6 }, (_, i) => (
    <motion.div
      key={i}
      className="position-absolute rounded-circle"
      style={{
        background: `linear-gradient(135deg, ${colors.primary}15, ${colors.accent}15)`,
        width: `${Math.random() * 60 + 20}px`,
        height: `${Math.random() * 60 + 20}px`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        opacity: 0.3,
        zIndex: 0
      }}
      animate={{
        x: [0, Math.random() * 100 - 50],
        y: [0, Math.random() * 100 - 50],
        rotate: [0, 360],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: Math.random() * 20 + 10,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
    />
  ));

  return (
    <div
      className="min-h-screen position-relative"
      style={{
        background: isDarkMode
          ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)'
          : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)',
        minHeight: '100vh'
      }}
    >
      {/* Animated Background */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {floatingShapes}
        <div className="position-absolute w-100 h-100" style={{
          background: isDarkMode
            ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'
        }} />
      </div>

      {/* Theme Toggle Button */}
      <motion.button
        onClick={toggleTheme}
        className="position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center"
        style={{
          width: '50px',
          height: '50px',
          background: colors.surface,
          boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',
          color: colors.text,
          zIndex: 1000
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
      </motion.button>

      <Container fluid className="position-relative py-4" style={{ zIndex: 1 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-5">
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="d-inline-flex align-items-center justify-content-center mb-3"
              style={{
                width: '80px',
                height: '80px',
                background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                borderRadius: '20px',
                boxShadow: `0 20px 40px ${colors.primary}30`
              }}
            >
              <User className="text-white" size={40} />
            </motion.div>
            <h1 className="fw-bold mb-2" style={{ fontSize: '2.5rem', color: colors.text }}>
              Welcome back, {user?.username}!
            </h1>
            <p className="fs-5 mb-4" style={{ color: colors.textSecondary }}>
              Manage your printing jobs and explore xerox centers
            </p>
            
            <div className="d-flex align-items-center justify-content-center gap-3 flex-wrap">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  size="lg"
                  className="px-5 py-3 rounded-4 border-0 fw-semibold"
                  style={{
                    background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                    color: '#fff',
                    boxShadow: `0 10px 30px ${colors.primary}30`
                  }}
                >
                  <Upload className="me-2" size={20} />
                  Upload Files
                </Button>
              </motion.div>
              
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline-light"
                  size="lg"
                  onClick={fetchData}
                  disabled={isRefreshing}
                  className="px-4 py-3 rounded-4 fw-semibold"
                >
                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />
                  Refresh
                </Button>
              </motion.div>
            </div>
          </motion.div>

          {/* Navigation Tabs */}
          <motion.div variants={itemVariants} className="mb-4">
            <Card className="border-0 shadow-lg" style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px'
            }}>
              <Card.Body className="p-2">
                <Nav variant="pills" className="justify-content-center flex-wrap">
                  {[
                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                    { key: 'jobs', label: 'My Jobs', icon: FileText },
                    { key: 'centers', label: 'Xerox Centers', icon: Printer },
                    { key: 'history', label: 'History', icon: History },
                    { key: 'favorites', label: 'Favorites', icon: Heart },
                    { key: 'analytics', label: 'Analytics', icon: PieChart },
                    { key: 'settings', label: 'Settings', icon: Settings }
                  ].map(tab => {
                    const IconComponent = tab.icon;
                    return (
                      <Nav.Item key={tab.key} className="m-1">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Nav.Link
                            active={activeTab === tab.key}
                            onClick={() => setActiveTab(tab.key)}
                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${
                              activeTab === tab.key
                                ? 'text-primary'
                                : 'text-white-50'
                            }`}
                            style={{
                              background: activeTab === tab.key 
                                ? 'rgba(255, 255, 255, 0.9)' 
                                : 'transparent',
                              border: 'none',
                              transition: 'all 0.3s ease',
                              boxShadow: activeTab === tab.key 
                                ? '0 10px 30px rgba(255, 255, 255, 0.2)' 
                                : 'none'
                            }}
                          >
                            <IconComponent size={18} className="me-2" />
                            <span className="d-none d-md-inline">{tab.label}</span>
                          </Nav.Link>
                        </motion.div>
                      </Nav.Item>
                    );
                  })}
                </Nav>
              </Card.Body>
            </Card>
          </motion.div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'dashboard' && (
              <Row className="g-4">
                {/* Statistics Cards */}
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card className="h-100 border-0 shadow-lg" style={{ 
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      borderRadius: '20px'
                    }}>
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div className="d-inline-flex align-items-center justify-content-center" style={{
                            width: '60px',
                            height: '60px',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            borderRadius: '15px'
                          }}>
                            <FileText className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="text-white-50 mb-1">Total Jobs</h6>
                        <h2 className="fw-bold text-white">{printJobs.length}</h2>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card className="h-100 border-0 shadow-lg" style={{ 
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      borderRadius: '20px'
                    }}>
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div className="d-inline-flex align-items-center justify-content-center" style={{
                            width: '60px',
                            height: '60px',
                            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                            borderRadius: '15px'
                          }}>
                            <Clock className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="text-white-50 mb-1">In Progress</h6>
                        <h2 className="fw-bold text-white">{inProgressJobs}</h2>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card className="h-100 border-0 shadow-lg" style={{ 
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      borderRadius: '20px'
                    }}>
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div className="d-inline-flex align-items-center justify-content-center" style={{
                            width: '60px',
                            height: '60px',
                            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                            borderRadius: '15px'
                          }}>
                            <CheckCircle className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="text-white-50 mb-1">Completed</h6>
                        <h2 className="fw-bold text-white">{completedJobs}</h2>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>
                
                <Col md={3}>
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    whileHover={{ y: -5, scale: 1.02 }}
                  >
                    <Card className="h-100 border-0 shadow-lg" style={{ 
                      background: 'rgba(255, 255, 255, 0.1)',
                      backdropFilter: 'blur(20px)',
                      borderRadius: '20px'
                    }}>
                      <Card.Body className="text-center p-4">
                        <div className="mb-3">
                          <div className="d-inline-flex align-items-center justify-content-center" style={{
                            width: '60px',
                            height: '60px',
                            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                            borderRadius: '15px'
                          }}>
                            <DollarSign className="text-white" size={24} />
                          </div>
                        </div>
                        <h6 className="text-white-50 mb-1">Total Spent</h6>
                        <h2 className="fw-bold text-white">${totalSpent.toFixed(2)}</h2>
                      </Card.Body>
                    </Card>
                  </motion.div>
                </Col>

                {/* Recent Jobs Table */}
                <Col lg={12}>
                  <Card className="border-0 shadow-lg" style={{ 
                    background: 'rgba(255, 255, 255, 0.1)',
                    backdropFilter: 'blur(20px)',
                    borderRadius: '20px'
                  }}>
                    <Card.Header className="border-0 bg-transparent">
                      <div className="d-flex align-items-center justify-content-between">
                        <h4 className="fw-bold mb-0 text-white">
                          <Activity className="me-2" size={20} />
                          Recent Jobs
                        </h4>
                        <Badge bg="light" text="dark" className="px-3 py-2">
                          {printJobs.length} total
                        </Badge>
                      </div>
                    </Card.Header>
                    <Card.Body>
                      {printJobs.length > 0 ? (
                        <div className="table-responsive">
                          <Table className="table-dark table-hover">
                            <thead>
                              <tr>
                                <th>Job Number</th>
                                <th>File Name</th>
                                <th>Status</th>
                                <th>Cost</th>
                                <th>Center</th>
                                <th>Actions</th>
                              </tr>
                            </thead>
                            <tbody>
                              {printJobs.slice(0, 10).map((job: PrintJob) => (
                                <tr key={job.id}>
                                  <td className="fw-semibold">{job.jobNumber}</td>
                                  <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>
                                  <td>{getStatusBadge(job.status)}</td>
                                  <td className="text-success fw-semibold">
                                    {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                                  </td>
                                  <td>{job.xeroxCenterName}</td>
                                  <td>
                                    <div className="d-flex gap-1">
                                      <Button variant="outline-light" size="sm">
                                        <Download size={14} />
                                      </Button>
                                      <Button variant="outline-light" size="sm">
                                        <Eye size={14} />
                                      </Button>
                                      <Button variant="outline-light" size="sm">
                                        <MessageCircle size={14} />
                                      </Button>
                                    </div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="text-center py-5"
                        >
                          <div className="mb-4">
                            <div className="d-inline-flex align-items-center justify-content-center" style={{
                              width: '80px',
                              height: '80px',
                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                              borderRadius: '20px'
                            }}>
                              <Upload className="text-white" size={40} />
                            </div>
                          </div>
                          <h5 className="fw-semibold mb-2 text-white">No jobs yet</h5>
                          <p className="text-white-50 mb-4">Upload your first file to get started!</p>
                        </motion.div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>
            )}

            {/* Other tabs content can be added here */}
            {activeTab !== 'dashboard' && (
              <Card className="border-0 shadow-lg" style={{ 
                background: 'rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(20px)',
                borderRadius: '20px'
              }}>
                <Card.Body className="text-center py-5">
                  <h4 className="text-white mb-3">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Tab</h4>
                  <p className="text-white-50">This tab is under development. Coming soon!</p>
                </Card.Body>
              </Card>
            )}
          </motion.div>
        </motion.div>
      </Container>
    </div>
  );
};

export default SimpleAceternityStudentDashboard;
