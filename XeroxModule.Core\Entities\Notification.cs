using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class Notification
    {
        public int NotificationID { get; set; }
        
        public int UserID { get; set; }
        
        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Message { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string Type { get; set; } = string.Empty; // 'JobUpdate', 'NewMessage', 'System', etc.
        
        public int? RelatedEntityID { get; set; } // Could be PrintJobID, MessageID, etc.
        
        [StringLength(50)]
        public string? RelatedEntityType { get; set; } // 'PrintJob', 'Message', etc.
        
        public bool IsRead { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public User User { get; set; } = null!;
        
        // Computed properties
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.UtcNow - CreatedAt;
                
                if (timeSpan.TotalMinutes < 1)
                    return "Just now";
                if (timeSpan.TotalMinutes < 60)
                    return $"{(int)timeSpan.TotalMinutes} minutes ago";
                if (timeSpan.TotalHours < 24)
                    return $"{(int)timeSpan.TotalHours} hours ago";
                if (timeSpan.TotalDays < 7)
                    return $"{(int)timeSpan.TotalDays} days ago";
                
                return CreatedAt.ToString("MMM dd, yyyy");
            }
        }
        
        public string IconClass => Type switch
        {
            "JobUpdate" => "fas fa-print",
            "NewMessage" => "fas fa-envelope",
            "System" => "fas fa-cog",
            "Payment" => "fas fa-credit-card",
            "Delivery" => "fas fa-truck",
            _ => "fas fa-bell"
        };
        
        public string ColorClass => Type switch
        {
            "JobUpdate" => "text-primary",
            "NewMessage" => "text-info",
            "System" => "text-warning",
            "Payment" => "text-success",
            "Delivery" => "text-success",
            _ => "text-secondary"
        };
    }
    
    public static class NotificationType
    {
        public const string JobUpdate = "JobUpdate";
        public const string NewMessage = "NewMessage";
        public const string System = "System";
        public const string Payment = "Payment";
        public const string Delivery = "Delivery";
        public const string Rating = "Rating";
        
        public static readonly string[] AllTypes = 
        {
            JobUpdate, NewMessage, System, Payment, Delivery, Rating
        };
    }
}
