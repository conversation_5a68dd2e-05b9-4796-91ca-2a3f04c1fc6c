{"ast": null, "code": "/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\nexport { supportsFlags };", "map": {"version": 3, "names": ["supportsFlags"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/utils/supports/flags.mjs"], "sourcesContent": ["/**\n * Add the ability for test suites to manually set support flags\n * to better test more environments.\n */\nconst supportsFlags = {};\n\nexport { supportsFlags };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,MAAMA,aAAa,GAAG,CAAC,CAAC;AAExB,SAASA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}