{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadColor = pendingJobs => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = job => {\n    setSelectedJob(job);\n    setShowChatModal(true);\n    // Load messages for this job\n    // TODO: Implement message loading from API\n    setMessages([]);\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      // TODO: Implement message sending API\n      const newMessage = {\n        id: Date.now(),\n        content: chatMessage,\n        sender: 'Student',\n        timestamp: new Date().toISOString(),\n        isFromStudent: true\n      };\n      setMessages(prev => [...prev, newMessage]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tachometer-alt me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), \"Student Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: \"auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => setShowUploadModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), \"Upload Files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: printJobs.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: [\"$\", printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-list me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), \"Recent Print Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Job #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"File Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Xerox Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: printJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 27\n                    }, this), job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(job.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.xeroxCenterName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: () => handleViewJob(job),\n                      title: \"View Details\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-eye\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 287,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: () => handleOpenChat(job),\n                      title: \"Chat\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 296,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      onClick: () => handleConfirmJob(job.id),\n                      title: \"Confirm Quote\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-check\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 305,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 25\n                  }, this)]\n                }, job.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this), \"No print jobs yet. Upload your first file to get started!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this), \"Available Centers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(Card, {\n              className: \"mb-3 border-0 bg-light\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: center.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getWorkloadColor(center.pendingJobs),\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-map-marker-alt me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 23\n                  }, this), center.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star text-warning me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"small\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 349,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 347,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this)\n            }, center.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this), \"Upload Files for Printing\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n              onChange: e => {\n                const files = e.target.files;\n                setSelectedFile(files ? files[0] : null);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Print Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.printType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    printType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Print\",\n                    children: \"Print\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 396,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Xerox\",\n                    children: \"Xerox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 397,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Binding\",\n                    children: \"Binding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 398,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Lamination\",\n                    children: \"Lamination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Number of Copies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  min: \"1\",\n                  value: uploadData.copies,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    copies: parseInt(e.target.value)\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Color Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.colorType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    colorType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BlackWhite\",\n                    children: \"Black & White\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Color\",\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Paper Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.paperSize,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    paperSize: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A4\",\n                    children: \"A4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 436,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A3\",\n                    children: \"A3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Letter\",\n                    children: \"Letter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Legal\",\n                    children: \"Legal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 439,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Preferred Xerox Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: uploadData.preferredXeroxCenterId,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                preferredXeroxCenterId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a center (optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: center.id,\n                children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              placeholder: \"Any special instructions or remarks...\",\n              value: uploadData.remarks,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                remarks: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowUploadModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          disabled: !selectedFile,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), \"Upload File\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showViewModal,\n      onHide: () => setShowViewModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-eye me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this), \"Job Details - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"File Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Print Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.printType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.colorType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Paper Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.paperSize]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 502,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Job Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 22\n                }, this), \" \", getStatusBadge(selectedJob.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Cost:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 507,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Xerox Center:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 508,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.xeroxCenterName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Created:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 22\n                }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 19\n              }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Estimated Completion:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 511,\n                  columnNumber: 24\n                }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 15\n          }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 517,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: selectedJob.remarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 17\n          }, this), selectedJob.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 19\n            }, this), \"This job has been quoted. Please confirm to proceed with printing.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [(selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.status) === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: () => {\n            handleConfirmJob(selectedJob.id);\n            setShowViewModal(false);\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-check me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this), \"Confirm Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 532,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowViewModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showChatModal,\n      onHide: () => setShowChatModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-comment me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this), \"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '400px',\n            overflowY: 'auto',\n            border: '1px solid #dee2e6',\n            borderRadius: '0.375rem',\n            padding: '1rem',\n            marginBottom: '1rem'\n          },\n          children: messages.length > 0 ? messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-3 ${message.isFromStudent ? 'text-end' : 'text-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `d-inline-block p-2 rounded ${message.isFromStudent ? 'bg-primary text-white' : 'bg-light'}`,\n              style: {\n                maxWidth: '70%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: `d-block mt-1 ${message.isFromStudent ? 'text-light' : 'text-muted'}`,\n                children: [message.sender, \" - \", new Date(message.timestamp).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comments fa-3x mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No messages yet. Start a conversation!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Type your message...\",\n            value: chatMessage,\n            onChange: e => setChatMessage(e.target.value),\n            onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSendMessage,\n            disabled: !chatMessage.trim(),\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-paper-plane\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 557,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowChatModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 550,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"8ErydrNvcotwx3lsAKqva+c5cB0=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadColor", "pendingJobs", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "handleConfirmJob", "jobId", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "newMessage", "id", "Date", "now", "content", "sender", "timestamp", "toISOString", "isFromStudent", "prev", "fluid", "username", "xs", "onClick", "md", "Body", "length", "filter", "includes", "reduce", "sum", "cost", "toFixed", "lg", "Header", "responsive", "hover", "map", "jobNumber", "slice", "xeroxCenterName", "size", "title", "center", "name", "location", "averageRating", "show", "onHide", "closeButton", "Title", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "Select", "value", "min", "parseInt", "as", "rows", "placeholder", "Footer", "disabled", "created", "toLocaleString", "estimatedCompletionTime", "style", "height", "overflowY", "border", "borderRadius", "padding", "marginBottom", "message", "max<PERSON><PERSON><PERSON>", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getWorkloadColor = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = (job: any) => {\n    setSelectedJob(job);\n    setShowChatModal(true);\n    // Load messages for this job\n    // TODO: Implement message loading from API\n    setMessages([]);\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      // TODO: Implement message sending API\n      const newMessage = {\n        id: Date.now(),\n        content: chatMessage,\n        sender: 'Student',\n        timestamp: new Date().toISOString(),\n        isFromStudent: true\n      };\n\n      setMessages(prev => [...prev, newMessage]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-tachometer-alt me-2\"></i>\n            Student Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n        <Col xs=\"auto\">\n          <Button variant=\"primary\" onClick={() => setShowUploadModal(true)}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files\n          </Button>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{printJobs.length}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-warning\">\n                {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-check-circle fa-2x text-success mb-2\"></i>\n              <h5>Completed</h5>\n              <h3 className=\"text-success\">\n                {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-info mb-2\"></i>\n              <h5>Total Spent</h5>\n              <h3 className=\"text-info\">\n                ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        {/* Recent Jobs */}\n        <Col lg={8}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-list me-2\"></i>\n                Recent Print Jobs\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {printJobs.length > 0 ? (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Job #</th>\n                      <th>File Name</th>\n                      <th>Status</th>\n                      <th>Cost</th>\n                      <th>Xerox Center</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {printJobs.map(job => (\n                      <tr key={job.id}>\n                        <td>\n                          <strong>{job.jobNumber}</strong>\n                        </td>\n                        <td>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          {job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName}\n                        </td>\n                        <td>{getStatusBadge(job.status)}</td>\n                        <td>\n                          {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                        </td>\n                        <td>{job.xeroxCenterName}</td>\n                        <td>\n                          <Button\n                            variant=\"outline-primary\"\n                            size=\"sm\"\n                            className=\"me-1\"\n                            onClick={() => handleViewJob(job)}\n                            title=\"View Details\"\n                          >\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                          <Button\n                            variant=\"outline-secondary\"\n                            size=\"sm\"\n                            className=\"me-1\"\n                            onClick={() => handleOpenChat(job)}\n                            title=\"Chat\"\n                          >\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n                          {job.status === 'Quoted' && (\n                            <Button\n                              variant=\"success\"\n                              size=\"sm\"\n                              onClick={() => handleConfirmJob(job.id)}\n                              title=\"Confirm Quote\"\n                            >\n                              <i className=\"fas fa-check\"></i>\n                            </Button>\n                          )}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              ) : (\n                <Alert variant=\"info\">\n                  <i className=\"fas fa-info-circle me-2\"></i>\n                  No print jobs yet. Upload your first file to get started!\n                </Alert>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n\n        {/* Xerox Centers */}\n        <Col lg={4}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-store me-2\"></i>\n                Available Centers\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {xeroxCenters.map(center => (\n                <Card key={center.id} className=\"mb-3 border-0 bg-light\">\n                  <Card.Body className=\"p-3\">\n                    <div className=\"d-flex justify-content-between align-items-start mb-2\">\n                      <h6 className=\"mb-1\">{center.name}</h6>\n                      <Badge bg={getWorkloadColor(center.pendingJobs)}>\n                        {center.pendingJobs} jobs\n                      </Badge>\n                    </div>\n                    <p className=\"text-muted small mb-2\">\n                      <i className=\"fas fa-map-marker-alt me-1\"></i>\n                      {center.location}\n                    </p>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      <div>\n                        <i className=\"fas fa-star text-warning me-1\"></i>\n                        <span className=\"small\">{center.averageRating.toFixed(1)}</span>\n                      </div>\n                      <Button variant=\"outline-primary\" size=\"sm\">\n                        Select\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              ))}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Upload Modal */}\n      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files for Printing\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select File</Form.Label>\n              <Form.Control\n                type=\"file\"\n                accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                onChange={(e) => {\n                  const files = (e.target as HTMLInputElement).files;\n                  setSelectedFile(files ? files[0] : null);\n                }}\n              />\n              <Form.Text className=\"text-muted\">\n                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n              </Form.Text>\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Print Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.printType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                  >\n                    <option value=\"Print\">Print</option>\n                    <option value=\"Xerox\">Xerox</option>\n                    <option value=\"Binding\">Binding</option>\n                    <option value=\"Lamination\">Lamination</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Number of Copies</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    min=\"1\"\n                    value={uploadData.copies}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Color Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.colorType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                  >\n                    <option value=\"BlackWhite\">Black & White</option>\n                    <option value=\"Color\">Color</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Paper Size</Form.Label>\n                  <Form.Select\n                    value={uploadData.paperSize}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"A3\">A3</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Preferred Xerox Center</Form.Label>\n              <Form.Select\n                value={uploadData.preferredXeroxCenterId}\n                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              >\n                <option value=\"\">Select a center (optional)</option>\n                {xeroxCenters.map(center => (\n                  <option key={center.id} value={center.id}>\n                    {center.name} - {center.pendingJobs} pending jobs\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Remarks</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                placeholder=\"Any special instructions or remarks...\"\n                value={uploadData.remarks}\n                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleFileUpload} disabled={!selectedFile}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload File\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* View Job Details Modal */}\n      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-eye me-2\"></i>\n            Job Details - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <div>\n              <Row>\n                <Col md={6}>\n                  <h6>File Information</h6>\n                  <hr />\n                  <p><strong>File Name:</strong> {selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName}</p>\n                  <p><strong>Print Type:</strong> {selectedJob.printType}</p>\n                  <p><strong>Copies:</strong> {selectedJob.copies}</p>\n                  <p><strong>Color Type:</strong> {selectedJob.colorType}</p>\n                  <p><strong>Paper Size:</strong> {selectedJob.paperSize}</p>\n                </Col>\n                <Col md={6}>\n                  <h6>Job Information</h6>\n                  <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>\n                  <p><strong>Cost:</strong> {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}</p>\n                  <p><strong>Xerox Center:</strong> {selectedJob.xeroxCenterName}</p>\n                  <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>\n                  {selectedJob.estimatedCompletionTime && (\n                    <p><strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}</p>\n                  )}\n                </Col>\n              </Row>\n              {selectedJob.remarks && (\n                <div className=\"mt-3\">\n                  <h6>Remarks</h6>\n                  <p className=\"text-muted\">{selectedJob.remarks}</p>\n                </div>\n              )}\n              {selectedJob.status === 'Quoted' && (\n                <Alert variant=\"warning\" className=\"mt-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  This job has been quoted. Please confirm to proceed with printing.\n                </Alert>\n              )}\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          {selectedJob?.status === 'Quoted' && (\n            <Button\n              variant=\"success\"\n              onClick={() => {\n                handleConfirmJob(selectedJob.id);\n                setShowViewModal(false);\n              }}\n            >\n              <i className=\"fas fa-check me-2\"></i>\n              Confirm Quote\n            </Button>\n          )}\n          <Button variant=\"secondary\" onClick={() => setShowViewModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Chat Modal */}\n      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-comment me-2\"></i>\n            Chat - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>\n            {messages.length > 0 ? (\n              messages.map((message) => (\n                <div key={message.id} className={`mb-3 ${message.isFromStudent ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromStudent ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>\n                    <div>{message.content}</div>\n                    <small className={`d-block mt-1 ${message.isFromStudent ? 'text-light' : 'text-muted'}`}>\n                      {message.sender} - {new Date(message.timestamp).toLocaleString()}\n                    </small>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center text-muted\">\n                <i className=\"fas fa-comments fa-3x mb-3\"></i>\n                <p>No messages yet. Start a conversation!</p>\n              </div>\n            )}\n          </div>\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={chatMessage}\n              onChange={(e) => setChatMessage(e.target.value)}\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n              className=\"me-2\"\n            />\n            <Button variant=\"primary\" onClick={handleSendMessage} disabled={!chatMessage.trim()}>\n              <i className=\"fas fa-paper-plane\"></i>\n            </Button>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowChatModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACrG,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB7E,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC+B,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACiC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC;IAC3CyC,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF7C,SAAS,CAAC,MAAM;IACd,MAAM8C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMnC,WAAW,CAACoC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;;QAEpC;QACA,MAAMC,oBAAoB,GAAG,MAAMrC,cAAc,CAACsC,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACE1C,OAAA,CAACT,KAAK;MAACqD,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxB7C,OAAA;QAAG8C,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CX,MAAM;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACrC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExC,YAAY,CAAC;MACrCsC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAElC,UAAU,CAACE,OAAO,CAAC;MAC9C8B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAElC,UAAU,CAACG,sBAAsB,CAAC;MAC5E6B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACI,SAAS,CAAC;MAClD4B,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAElC,UAAU,CAACK,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACM,SAAS,CAAC;MAClD0B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACO,SAAS,CAAC;MAElD,MAAM/B,aAAa,CAAC4D,UAAU,CAACJ,QAAQ,CAAC;;MAExC;MACA,MAAMvB,iBAAiB,GAAG,MAAMnC,WAAW,CAACoC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAMuB,aAAa,GAAIC,GAAQ,IAAK;IAClC7C,cAAc,CAAC6C,GAAG,CAAC;IACnBjD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkD,cAAc,GAAID,GAAQ,IAAK;IACnC7C,cAAc,CAAC6C,GAAG,CAAC;IACnB/C,gBAAgB,CAAC,IAAI,CAAC;IACtB;IACA;IACAQ,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EAED,MAAMyC,gBAAgB,GAAG,MAAOC,KAAa,IAAK;IAChD,IAAI;MACF,MAAMnE,WAAW,CAACoE,UAAU,CAACD,KAAK,CAAC;;MAEnC;MACA,MAAMhC,iBAAiB,GAAG,MAAMnC,WAAW,CAACoC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAM6B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC/C,WAAW,CAACgD,IAAI,CAAC,CAAC,IAAI,CAACpD,WAAW,EAAE;IAEzC,IAAI;MACF;MACA,MAAMqD,UAAU,GAAG;QACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,OAAO,EAAErD,WAAW;QACpBsD,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCC,aAAa,EAAE;MACjB,CAAC;MAEDtD,WAAW,CAACuD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAET,UAAU,CAAC,CAAC;MAC1ChD,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,oBACEpC,OAAA,CAACf,SAAS;IAAC4F,KAAK;IAAAhC,QAAA,gBACd7C,OAAA,CAACd,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB7C,OAAA,CAACb,GAAG;QAAA0D,QAAA,gBACF7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAG8C,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,qBAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlD,OAAA;UAAG8C,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,QAAQ,EAAC,GAAC;QAAA;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNlD,OAAA,CAACb,GAAG;QAAC4F,EAAE,EAAC,MAAM;QAAAlC,QAAA,eACZ7C,OAAA,CAACX,MAAM;UAACoD,OAAO,EAAC,SAAS;UAACuC,OAAO,EAAEA,CAAA,KAAMvE,kBAAkB,CAAC,IAAI,CAAE;UAAAoC,QAAA,gBAChE7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACd,GAAG;MAAC4D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB7C,OAAA,CAACb,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAApC,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAC0D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlD,OAAA;cAAA6C,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEzC,SAAS,CAAC+E;YAAM;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACb,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAApC,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAC0D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDlD,OAAA;cAAA6C,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBzC,SAAS,CAACgF,MAAM,CAACxB,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACyB,QAAQ,CAACzB,GAAG,CAACrB,MAAM,CAAC,CAAC,CAAC4C;YAAM;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACb,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAApC,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAC0D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DlD,OAAA;cAAA6C,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBzC,SAAS,CAACgF,MAAM,CAACxB,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACyB,QAAQ,CAACzB,GAAG,CAACrB,MAAM,CAAC,CAAC,CAAC4C;YAAM;cAAApC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACb,GAAG;QAAC8F,EAAE,EAAE,CAAE;QAAApC,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAC0D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlD,OAAA;cAAA6C,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlD,OAAA;cAAI8C,SAAS,EAAC,WAAW;cAAAD,QAAA,GAAC,GACvB,EAACzC,SAAS,CAACkF,MAAM,CAAC,CAACC,GAAG,EAAE3B,GAAG,KAAK2B,GAAG,IAAI3B,GAAG,CAAC4B,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlD,OAAA,CAACd,GAAG;MAAA2D,QAAA,gBAEF7C,OAAA,CAACb,GAAG;QAACuG,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAAyD,QAAA,gBACH7C,OAAA,CAACZ,IAAI,CAACuG,MAAM;YAAA9C,QAAA,eACV7C,OAAA;cAAI8C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdlD,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,EACPzC,SAAS,CAAC+E,MAAM,GAAG,CAAC,gBACnBnF,OAAA,CAACV,KAAK;cAACsG,UAAU;cAACC,KAAK;cAAAhD,QAAA,gBACrB7C,OAAA;gBAAA6C,QAAA,eACE7C,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAA6C,QAAA,EAAI;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACflD,OAAA;oBAAA6C,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACblD,OAAA;oBAAA6C,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRlD,OAAA;gBAAA6C,QAAA,EACGzC,SAAS,CAAC0F,GAAG,CAAClC,GAAG,iBAChB5D,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAA6C,QAAA,eACE7C,OAAA;sBAAA6C,QAAA,EAASe,GAAG,CAACmC;oBAAS;sBAAAhD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG8C,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDU,GAAG,CAACb,QAAQ,CAACoC,MAAM,GAAG,EAAE,GAAGvB,GAAG,CAACb,QAAQ,CAACiD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGpC,GAAG,CAACb,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,EAAKP,cAAc,CAACsB,GAAG,CAACrB,MAAM;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrClD,OAAA;oBAAA6C,QAAA,EACGe,GAAG,CAAC4B,IAAI,GAAG,IAAI5B,GAAG,CAAC4B,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;kBAAG;oBAAA1C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,EAAKe,GAAG,CAACqC;kBAAe;oBAAAlD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BlD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA,CAACX,MAAM;sBACLoD,OAAO,EAAC,iBAAiB;sBACzByD,IAAI,EAAC,IAAI;sBACTpD,SAAS,EAAC,MAAM;sBAChBkC,OAAO,EAAEA,CAAA,KAAMrB,aAAa,CAACC,GAAG,CAAE;sBAClCuC,KAAK,EAAC,cAAc;sBAAAtD,QAAA,eAEpB7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACTlD,OAAA,CAACX,MAAM;sBACLoD,OAAO,EAAC,mBAAmB;sBAC3ByD,IAAI,EAAC,IAAI;sBACTpD,SAAS,EAAC,MAAM;sBAChBkC,OAAO,EAAEA,CAAA,KAAMnB,cAAc,CAACD,GAAG,CAAE;sBACnCuC,KAAK,EAAC,MAAM;sBAAAtD,QAAA,eAEZ7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACRU,GAAG,CAACrB,MAAM,KAAK,QAAQ,iBACtBvC,OAAA,CAACX,MAAM;sBACLoD,OAAO,EAAC,SAAS;sBACjByD,IAAI,EAAC,IAAI;sBACTlB,OAAO,EAAEA,CAAA,KAAMlB,gBAAgB,CAACF,GAAG,CAACQ,EAAE,CAAE;sBACxC+B,KAAK,EAAC,eAAe;sBAAAtD,QAAA,eAErB7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GA1CEU,GAAG,CAACQ,EAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2CX,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAERlD,OAAA,CAACR,KAAK;cAACiD,OAAO,EAAC,MAAM;cAAAI,QAAA,gBACnB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6DAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlD,OAAA,CAACb,GAAG;QAACuG,EAAE,EAAE,CAAE;QAAA7C,QAAA,eACT7C,OAAA,CAACZ,IAAI;UAAAyD,QAAA,gBACH7C,OAAA,CAACZ,IAAI,CAACuG,MAAM;YAAA9C,QAAA,eACV7C,OAAA;cAAI8C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdlD,OAAA,CAACZ,IAAI,CAAC8F,IAAI;YAAArC,QAAA,EACPvC,YAAY,CAACwF,GAAG,CAACM,MAAM,iBACtBpG,OAAA,CAACZ,IAAI;cAAiB0D,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACtD7C,OAAA,CAACZ,IAAI,CAAC8F,IAAI;gBAACpC,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBACxB7C,OAAA;kBAAK8C,SAAS,EAAC,uDAAuD;kBAAAD,QAAA,gBACpE7C,OAAA;oBAAI8C,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEuD,MAAM,CAACC;kBAAI;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvClD,OAAA,CAACT,KAAK;oBAACqD,EAAE,EAAEO,gBAAgB,CAACiD,MAAM,CAAChD,WAAW,CAAE;oBAAAP,QAAA,GAC7CuD,MAAM,CAAChD,WAAW,EAAC,OACtB;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlD,OAAA;kBAAG8C,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAClC7C,OAAA;oBAAG8C,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC7CkD,MAAM,CAACE,QAAQ;gBAAA;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJlD,OAAA;kBAAK8C,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,gBAChE7C,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG8C,SAAS,EAAC;oBAA+B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDlD,OAAA;sBAAM8C,SAAS,EAAC,OAAO;sBAAAD,QAAA,EAAEuD,MAAM,CAACG,aAAa,CAACd,OAAO,CAAC,CAAC;oBAAC;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlD,OAAA,CAACX,MAAM;oBAACoD,OAAO,EAAC,iBAAiB;oBAACyD,IAAI,EAAC,IAAI;oBAAArD,QAAA,EAAC;kBAE5C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GArBHkD,MAAM,CAAChC,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBd,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACN,KAAK;MAAC8G,IAAI,EAAEhG,eAAgB;MAACiG,MAAM,EAAEA,CAAA,KAAMhG,kBAAkB,CAAC,KAAK,CAAE;MAACyF,IAAI,EAAC,IAAI;MAAArD,QAAA,gBAC9E7C,OAAA,CAACN,KAAK,CAACiG,MAAM;QAACe,WAAW;QAAA7D,QAAA,eACvB7C,OAAA,CAACN,KAAK,CAACiH,KAAK;UAAA9D,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,6BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACN,KAAK,CAACwF,IAAI;QAAArC,QAAA,eACT7C,OAAA,CAACP,IAAI;UAAAoD,QAAA,gBACH7C,OAAA,CAACP,IAAI,CAACmH,KAAK;YAAC9D,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;cAAAhE,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpClD,OAAA,CAACP,IAAI,CAACqH,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAGC,CAAC,IAAK;gBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;gBAClDlG,eAAe,CAACkG,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;cAC1C;YAAE;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFlD,OAAA,CAACP,IAAI,CAAC4H,IAAI;cAACvE,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEblD,OAAA,CAACd,GAAG;YAAA2D,QAAA,gBACF7C,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,eACT7C,OAAA,CAACP,IAAI,CAACmH,KAAK;gBAAC9D,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;kBAAAhE,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACP,IAAI,CAAC6H,MAAM;kBACVC,KAAK,EAAEjG,UAAU,CAACI,SAAU;kBAC5BuF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAElD,SAAS,EAAEwF,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA1E,QAAA,gBAEjF7C,OAAA;oBAAQuH,KAAK,EAAC,OAAO;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClD,OAAA;oBAAQuH,KAAK,EAAC,OAAO;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClD,OAAA;oBAAQuH,KAAK,EAAC,SAAS;oBAAA1E,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxClD,OAAA;oBAAQuH,KAAK,EAAC,YAAY;oBAAA1E,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlD,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,eACT7C,OAAA,CAACP,IAAI,CAACmH,KAAK;gBAAC9D,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;kBAAAhE,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzClD,OAAA,CAACP,IAAI,CAACqH,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbS,GAAG,EAAC,GAAG;kBACPD,KAAK,EAAEjG,UAAU,CAACK,MAAO;kBACzBsF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEjD,MAAM,EAAE8F,QAAQ,CAACP,CAAC,CAACE,MAAM,CAACG,KAAK;kBAAE,CAAC,CAAC;gBAAE;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA,CAACd,GAAG;YAAA2D,QAAA,gBACF7C,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,eACT7C,OAAA,CAACP,IAAI,CAACmH,KAAK;gBAAC9D,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;kBAAAhE,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACP,IAAI,CAAC6H,MAAM;kBACVC,KAAK,EAAEjG,UAAU,CAACM,SAAU;kBAC5BqF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhD,SAAS,EAAEsF,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA1E,QAAA,gBAEjF7C,OAAA;oBAAQuH,KAAK,EAAC,YAAY;oBAAA1E,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjDlD,OAAA;oBAAQuH,KAAK,EAAC,OAAO;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlD,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,eACT7C,OAAA,CAACP,IAAI,CAACmH,KAAK;gBAAC9D,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;kBAAAhE,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACP,IAAI,CAAC6H,MAAM;kBACVC,KAAK,EAAEjG,UAAU,CAACO,SAAU;kBAC5BoF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE/C,SAAS,EAAEqF,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAA1E,QAAA,gBAEjF7C,OAAA;oBAAQuH,KAAK,EAAC,IAAI;oBAAA1E,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BlD,OAAA;oBAAQuH,KAAK,EAAC,IAAI;oBAAA1E,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BlD,OAAA;oBAAQuH,KAAK,EAAC,QAAQ;oBAAA1E,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtClD,OAAA;oBAAQuH,KAAK,EAAC,OAAO;oBAAA1E,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA,CAACP,IAAI,CAACmH,KAAK;YAAC9D,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;cAAAhE,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ClD,OAAA,CAACP,IAAI,CAAC6H,MAAM;cACVC,KAAK,EAAEjG,UAAU,CAACG,sBAAuB;cACzCwF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEnD,sBAAsB,EAAEyF,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC,CAAE;cAAA1E,QAAA,gBAE9F7C,OAAA;gBAAQuH,KAAK,EAAC,EAAE;gBAAA1E,QAAA,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnD5C,YAAY,CAACwF,GAAG,CAACM,MAAM,iBACtBpG,OAAA;gBAAwBuH,KAAK,EAAEnB,MAAM,CAAChC,EAAG;gBAAAvB,QAAA,GACtCuD,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAAChD,WAAW,EAAC,eACtC;cAAA,GAFagD,MAAM,CAAChC,EAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEblD,OAAA,CAACP,IAAI,CAACmH,KAAK;YAAC9D,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACP,IAAI,CAACoH,KAAK;cAAAhE,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChClD,OAAA,CAACP,IAAI,CAACqH,OAAO;cACXY,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,wCAAwC;cACpDL,KAAK,EAAEjG,UAAU,CAACE,OAAQ;cAC1ByF,QAAQ,EAAGC,CAAC,IAAK3F,aAAa,CAACqD,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEpD,OAAO,EAAE0F,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC;YAAE;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACblD,OAAA,CAACN,KAAK,CAACmI,MAAM;QAAAhF,QAAA,gBACX7C,OAAA,CAACX,MAAM;UAACoD,OAAO,EAAC,WAAW;UAACuC,OAAO,EAAEA,CAAA,KAAMvE,kBAAkB,CAAC,KAAK,CAAE;UAAAoC,QAAA,EAAC;QAEtE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA,CAACX,MAAM;UAACoD,OAAO,EAAC,SAAS;UAACuC,OAAO,EAAE3B,gBAAiB;UAACyE,QAAQ,EAAE,CAAC9G,YAAa;UAAA6B,QAAA,gBAC3E7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRlD,OAAA,CAACN,KAAK;MAAC8G,IAAI,EAAE9F,aAAc;MAAC+F,MAAM,EAAEA,CAAA,KAAM9F,gBAAgB,CAAC,KAAK,CAAE;MAACuF,IAAI,EAAC,IAAI;MAAArD,QAAA,gBAC1E7C,OAAA,CAACN,KAAK,CAACiG,MAAM;QAACe,WAAW;QAAA7D,QAAA,eACvB7C,OAAA,CAACN,KAAK,CAACiH,KAAK;UAAA9D,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBACrB,EAACpC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiF,SAAS;QAAA;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACN,KAAK,CAACwF,IAAI;QAAArC,QAAA,EACR/B,WAAW,iBACVd,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACd,GAAG;YAAA2D,QAAA,gBACF7C,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACT7C,OAAA;gBAAA6C,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBlD,OAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACiC,QAAQ,CAACoC,MAAM,GAAG,EAAE,GAAGrE,WAAW,CAACiC,QAAQ,CAACiD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGlF,WAAW,CAACiC,QAAQ;cAAA;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxIlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACY,SAAS;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACa,MAAM;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACc,SAAS;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACe,SAAS;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNlD,OAAA,CAACb,GAAG;cAAC8F,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACT7C,OAAA;gBAAA6C,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACZ,cAAc,CAACxB,WAAW,CAACyB,MAAM,CAAC;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpElD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAAC0E,IAAI,GAAG,IAAI1E,WAAW,CAAC0E,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,gBAAgB;cAAA;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACmF,eAAe;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAImB,IAAI,CAACvD,WAAW,CAACiH,OAAO,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAChFpC,WAAW,CAACmH,uBAAuB,iBAClCjI,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAImB,IAAI,CAACvD,WAAW,CAACmH,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC;cAAA;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9G;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLpC,WAAW,CAACU,OAAO,iBAClBxB,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB7C,OAAA;cAAA6C,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBlD,OAAA;cAAG8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAE/B,WAAW,CAACU;YAAO;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN,EACApC,WAAW,CAACyB,MAAM,KAAK,QAAQ,iBAC9BvC,OAAA,CAACR,KAAK;YAACiD,OAAO,EAAC,SAAS;YAACK,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACvC7C,OAAA;cAAG8C,SAAS,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sEAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACblD,OAAA,CAACN,KAAK,CAACmI,MAAM;QAAAhF,QAAA,GACV,CAAA/B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,MAAM,MAAK,QAAQ,iBAC/BvC,OAAA,CAACX,MAAM;UACLoD,OAAO,EAAC,SAAS;UACjBuC,OAAO,EAAEA,CAAA,KAAM;YACblB,gBAAgB,CAAChD,WAAW,CAACsD,EAAE,CAAC;YAChCzD,gBAAgB,CAAC,KAAK,CAAC;UACzB,CAAE;UAAAkC,QAAA,gBAEF7C,OAAA;YAAG8C,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDlD,OAAA,CAACX,MAAM;UAACoD,OAAO,EAAC,WAAW;UAACuC,OAAO,EAAEA,CAAA,KAAMrE,gBAAgB,CAAC,KAAK,CAAE;UAAAkC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRlD,OAAA,CAACN,KAAK;MAAC8G,IAAI,EAAE5F,aAAc;MAAC6F,MAAM,EAAEA,CAAA,KAAM5F,gBAAgB,CAAC,KAAK,CAAE;MAACqF,IAAI,EAAC,IAAI;MAAArD,QAAA,gBAC1E7C,OAAA,CAACN,KAAK,CAACiG,MAAM;QAACe,WAAW;QAAA7D,QAAA,eACvB7C,OAAA,CAACN,KAAK,CAACiH,KAAK;UAAA9D,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WAChC,EAACpC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEiF,SAAS;QAAA;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACN,KAAK,CAACwF,IAAI;QAAArC,QAAA,gBACT7C,OAAA;UAAKkI,KAAK,EAAE;YAAEC,MAAM,EAAE,OAAO;YAAEC,SAAS,EAAE,MAAM;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE,UAAU;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAA3F,QAAA,EAC9IzB,QAAQ,CAAC+D,MAAM,GAAG,CAAC,GAClB/D,QAAQ,CAAC0E,GAAG,CAAE2C,OAAO,iBACnBzI,OAAA;YAAsB8C,SAAS,EAAE,QAAQ2F,OAAO,CAAC9D,aAAa,GAAG,UAAU,GAAG,YAAY,EAAG;YAAA9B,QAAA,eAC3F7C,OAAA;cAAK8C,SAAS,EAAE,8BAA8B2F,OAAO,CAAC9D,aAAa,GAAG,uBAAuB,GAAG,UAAU,EAAG;cAACuD,KAAK,EAAE;gBAAEQ,QAAQ,EAAE;cAAM,CAAE;cAAA7F,QAAA,gBACvI7C,OAAA;gBAAA6C,QAAA,EAAM4F,OAAO,CAAClE;cAAO;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BlD,OAAA;gBAAO8C,SAAS,EAAE,gBAAgB2F,OAAO,CAAC9D,aAAa,GAAG,YAAY,GAAG,YAAY,EAAG;gBAAA9B,QAAA,GACrF4F,OAAO,CAACjE,MAAM,EAAC,KAAG,EAAC,IAAIH,IAAI,CAACoE,OAAO,CAAChE,SAAS,CAAC,CAACuD,cAAc,CAAC,CAAC;cAAA;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GANEuF,OAAO,CAACrE,EAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACN,CAAC,gBAEFlD,OAAA;YAAK8C,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrC7C,OAAA;cAAG8C,SAAS,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ClD,OAAA;cAAA6C,QAAA,EAAG;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNlD,OAAA;UAAK8C,SAAS,EAAC,QAAQ;UAAAD,QAAA,gBACrB7C,OAAA,CAACP,IAAI,CAACqH,OAAO;YACXC,IAAI,EAAC,MAAM;YACXa,WAAW,EAAC,sBAAsB;YAClCL,KAAK,EAAErG,WAAY;YACnB+F,QAAQ,EAAGC,CAAC,IAAK/F,cAAc,CAAC+F,CAAC,CAACE,MAAM,CAACG,KAAK,CAAE;YAChDoB,SAAS,EAAGzB,CAAC,IAAKA,CAAC,CAAC0B,GAAG,KAAK,OAAO,IAAI3E,iBAAiB,CAAC,CAAE;YAC3DnB,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFlD,OAAA,CAACX,MAAM;YAACoD,OAAO,EAAC,SAAS;YAACuC,OAAO,EAAEf,iBAAkB;YAAC6D,QAAQ,EAAE,CAAC5G,WAAW,CAACgD,IAAI,CAAC,CAAE;YAAArB,QAAA,eAClF7C,OAAA;cAAG8C,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACblD,OAAA,CAACN,KAAK,CAACmI,MAAM;QAAAhF,QAAA,eACX7C,OAAA,CAACX,MAAM;UAACoD,OAAO,EAAC,WAAW;UAACuC,OAAO,EAAEA,CAAA,KAAMnE,gBAAgB,CAAC,KAAK,CAAE;UAAAgC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAChD,EAAA,CA7jBID,gBAA0B;EAAA,QACbN,OAAO;AAAA;AAAAkJ,EAAA,GADpB5I,gBAA0B;AA+jBhC,eAAeA,gBAAgB;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}