using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using XeroxModule.Core.Interfaces;
using XeroxModule.Core.Entities;

namespace XeroxModule.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class PrintJobController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public PrintJobController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        [HttpGet("student")]
        public async Task<IActionResult> GetStudentPrintJobs()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            // Get student by user ID
            var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
            if (student == null)
            {
                return BadRequest("Student not found");
            }

            var printJobs = await _unitOfWork.PrintJobs.GetAllAsync(
                pj => pj.FileUpload.StudentID == student.StudentID,
                includeProperties: "FileUpload,XeroxCenter,XeroxCenter.User"
            );

            var result = printJobs.Select(pj => new
            {
                id = pj.PrintJobID,
                jobNumber = pj.JobNumber,
                fileName = pj.FileUpload.OriginalFileName,
                status = pj.Status,
                cost = pj.Cost,
                estimatedCompletionTime = pj.EstimatedCompletionTime?.ToString("yyyy-MM-ddTHH:mm:ss"),
                xeroxCenterName = pj.XeroxCenter.XeroxCenterName,
                created = pj.Created.ToString("yyyy-MM-ddTHH:mm:ss"),
                printType = pj.FileUpload.PrintType,
                copies = pj.FileUpload.Copies,
                colorType = pj.FileUpload.ColorType,
                paperSize = pj.FileUpload.PaperSize,
                remarks = pj.FileUpload.Remarks
            });

            return Ok(result);
        }

        [HttpGet("xerox-center")]
        public async Task<IActionResult> GetXeroxCenterPrintJobs()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            // Get xerox center by user ID
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);
            if (xeroxCenter == null)
            {
                return BadRequest("Xerox center not found");
            }

            var printJobs = await _unitOfWork.PrintJobs.GetAllAsync(
                pj => pj.XeroxCenterID == xeroxCenter.XeroxCenterID,
                includeProperties: "FileUpload,FileUpload.Student,FileUpload.Student.User,XeroxCenter"
            );

            var result = printJobs.Select(pj => new
            {
                id = pj.PrintJobID,
                jobNumber = pj.JobNumber,
                fileName = pj.FileUpload.OriginalFileName,
                status = pj.Status,
                cost = pj.Cost,
                estimatedCompletionTime = pj.EstimatedCompletionTime?.ToString("yyyy-MM-ddTHH:mm:ss"),
                studentName = $"{pj.FileUpload.Student.FirstName} {pj.FileUpload.Student.LastName}",
                studentEmail = pj.FileUpload.Student.User.Email,
                printType = pj.FileUpload.PrintType,
                copies = pj.FileUpload.Copies,
                colorType = pj.FileUpload.ColorType,
                paperSize = pj.FileUpload.PaperSize,
                remarks = pj.FileUpload.Remarks,
                created = pj.Created.ToString("yyyy-MM-ddTHH:mm:ss")
            });

            return Ok(result);
        }

        [HttpPut("{id}/status")]
        public async Task<IActionResult> UpdatePrintJobStatus(int id, [FromBody] UpdateStatusRequest request)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

            var printJob = await _unitOfWork.PrintJobs.GetByIdAsync(id);
            if (printJob == null)
            {
                return NotFound("Print job not found");
            }

            // Verify the user has permission to update this job
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);
            if (xeroxCenter == null || printJob.XeroxCenterID != xeroxCenter.XeroxCenterID)
            {
                return StatusCode(403, new { message = "You don't have permission to update this job" });
            }

            printJob.Status = request.Status;
            printJob.Modified = DateTime.UtcNow;
            printJob.ModifiedUserID = userId;

            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "Status updated successfully" });
        }

        [HttpPut("{id}/confirm")]
        public async Task<IActionResult> ConfirmPrintJob(int id)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

            var printJob = await _unitOfWork.PrintJobs.GetFirstOrDefaultAsync(
                pj => pj.PrintJobID == id,
                includeProperties: "FileUpload"
            );

            if (printJob == null)
            {
                return NotFound("Print job not found");
            }

            // Verify the user is the student who owns this job
            var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
            if (student == null || printJob.FileUpload.StudentID != student.StudentID)
            {
                return StatusCode(403, new { message = "You don't have permission to confirm this job" });
            }

            // Check if job is in quoted status
            if (printJob.Status != "Quoted")
            {
                return BadRequest("Job must be in 'Quoted' status to confirm");
            }

            printJob.Status = "Confirmed";
            printJob.Modified = DateTime.UtcNow;
            printJob.ModifiedUserID = userId;

            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "Job confirmed successfully" });
        }

        [HttpPut("{id}/quote")]
        public async Task<IActionResult> SetPrintJobQuote(int id, [FromBody] SetQuoteRequest request)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var printJob = await _unitOfWork.PrintJobs.GetByIdAsync(id);
            if (printJob == null)
            {
                return NotFound("Print job not found");
            }

            // Verify the user has permission to update this job
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);
            if (xeroxCenter == null || printJob.XeroxCenterID != xeroxCenter.XeroxCenterID)
            {
                return Forbid("You don't have permission to update this job");
            }

            printJob.Cost = request.Cost;
            printJob.EstimatedCompletionTime = request.EstimatedCompletionTime;
            printJob.Status = "Quoted";
            printJob.Modified = DateTime.UtcNow;
            printJob.ModifiedUserID = userId;

            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "Quote set successfully" });
        }
    }

    public class UpdateStatusRequest
    {
        public string Status { get; set; } = string.Empty;
    }

    public class SetQuoteRequest
    {
        public decimal Cost { get; set; }
        public DateTime? EstimatedCompletionTime { get; set; }
        public string? Notes { get; set; }
    }
}
