using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class PrintJob
    {
        public int PrintJobID { get; set; }
        
        [Required]
        [StringLength(50)]
        public string JobNumber { get; set; } = string.Empty;
        
        public int XeroxCenterID { get; set; }
        
        public int FileUploadID { get; set; }
        
        public decimal? Cost { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Status { get; set; } = "Requested";
        
        public DateTime? EstimatedCompletionTime { get; set; }
        
        public DateTime? ActualCompletionTime { get; set; }
        
        public DateTime? DeliveredAt { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Priority { get; set; } = "Normal"; // 'Low', 'Normal', 'High', 'Urgent'
        
        public int CreatedUserID { get; set; }
        
        public int? ModifiedUserID { get; set; }
        
        public DateTime Created { get; set; } = DateTime.UtcNow;
        
        public DateTime? Modified { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        // Navigation properties
        public XeroxCenter XeroxCenter { get; set; } = null!;
        public FileUpload FileUpload { get; set; } = null!;
        public User CreatedUser { get; set; } = null!;
        public User? ModifiedUser { get; set; }
        public ICollection<Message> Messages { get; set; } = new List<Message>();
        public ICollection<Rating> Ratings { get; set; } = new List<Rating>();
        
        // Computed properties
        public bool IsCompleted => Status == "Completed" || Status == "Delivered";
        public bool IsInProgress => Status == "InProgress" || Status == "Confirmed";
        public bool IsPending => Status == "Requested" || Status == "UnderReview" || Status == "Quoted" || Status == "WaitingConfirmation";
        public bool IsOverdue => EstimatedCompletionTime.HasValue && EstimatedCompletionTime < DateTime.UtcNow && !IsCompleted;
        
        public TimeSpan? EstimatedDuration => EstimatedCompletionTime.HasValue ? EstimatedCompletionTime - Created : null;
        public TimeSpan? ActualDuration => ActualCompletionTime.HasValue ? ActualCompletionTime - Created : null;
        
        public string StatusDisplayName => Status switch
        {
            "Requested" => "Requested",
            "UnderReview" => "Under Review",
            "Quoted" => "Quoted",
            "WaitingConfirmation" => "Waiting for Confirmation",
            "Confirmed" => "Confirmed",
            "InProgress" => "In Progress",
            "Completed" => "Completed",
            "Delivered" => "Delivered",
            "Rejected" => "Rejected",
            "Cancelled" => "Cancelled",
            _ => Status
        };
    }
    
    public static class PrintJobStatus
    {
        public const string Requested = "Requested";
        public const string UnderReview = "UnderReview";
        public const string Quoted = "Quoted";
        public const string WaitingConfirmation = "WaitingConfirmation";
        public const string Confirmed = "Confirmed";
        public const string InProgress = "InProgress";
        public const string Completed = "Completed";
        public const string Delivered = "Delivered";
        public const string Rejected = "Rejected";
        public const string Cancelled = "Cancelled";
        
        public static readonly string[] AllStatuses = 
        {
            Requested, UnderReview, Quoted, WaitingConfirmation, 
            Confirmed, InProgress, Completed, Delivered, Rejected, Cancelled
        };
        
        public static readonly string[] ActiveStatuses = 
        {
            Requested, UnderReview, Quoted, WaitingConfirmation, Confirmed, InProgress
        };
        
        public static readonly string[] CompletedStatuses = 
        {
            Completed, Delivered
        };
        
        public static readonly string[] ClosedStatuses = 
        {
            Completed, Delivered, Rejected, Cancelled
        };
    }
    
    public static class PrintJobPriority
    {
        public const string Low = "Low";
        public const string Normal = "Normal";
        public const string High = "High";
        public const string Urgent = "Urgent";
        
        public static readonly string[] AllPriorities = { Low, Normal, High, Urgent };
    }
}
