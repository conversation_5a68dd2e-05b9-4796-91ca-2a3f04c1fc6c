using XeroxModule.Core.Entities;

namespace XeroxModule.Core.Interfaces
{
    public interface IPrintJobService
    {
        Task<PrintJob> CreatePrintJobAsync(int fileUploadId, int xeroxCenterId);
        Task<PrintJob?> GetPrintJobAsync(int printJobId);
        Task<IEnumerable<PrintJob>> GetStudentPrintJobsAsync(int studentId);
        Task<IEnumerable<PrintJob>> GetXeroxCenterPrintJobsAsync(int xeroxCenterId);
        Task<bool> UpdatePrintJobStatusAsync(int printJobId, string status, int userId);
        Task<bool> SetPrintJobCostAsync(int printJobId, decimal cost, DateTime? estimatedCompletion, int userId);
        Task<bool> ConfirmPrintJobAsync(int printJobId, int userId);
        Task<bool> RejectPrintJobAsync(int printJobId, string reason, int userId);
        Task<bool> CompletePrintJobAsync(int printJobId, int userId);
        Task<bool> DeliverPrintJobAsync(int printJobId, int userId);
        Task<PrintJobStatistics> GetPrintJobStatisticsAsync(int? xeroxCenterId = null, int? studentId = null);
        Task<IEnumerable<PrintJob>> GetOverduePrintJobsAsync();
        Task<string> GenerateJobNumberAsync();
    }
    
    public class PrintJobStatistics
    {
        public int TotalJobs { get; set; }
        public int PendingJobs { get; set; }
        public int InProgressJobs { get; set; }
        public int CompletedJobs { get; set; }
        public int RejectedJobs { get; set; }
        public int CancelledJobs { get; set; }
        public decimal TotalRevenue { get; set; }
        public double AverageCompletionTime { get; set; }
        public double AverageRating { get; set; }
        public int OverdueJobs { get; set; }
        
        public Dictionary<string, int> JobsByStatus { get; set; } = new();
        public Dictionary<string, int> JobsByPriority { get; set; } = new();
        public Dictionary<DateTime, int> JobsByDate { get; set; } = new();
    }
}
