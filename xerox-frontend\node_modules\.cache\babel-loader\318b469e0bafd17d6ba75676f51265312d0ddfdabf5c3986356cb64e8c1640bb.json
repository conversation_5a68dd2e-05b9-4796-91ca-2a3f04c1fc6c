{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityRegister.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Form, Button, Alert, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { User, Building, Mail, Lock, Eye, EyeOff, UserPlus, Sparkles } from 'lucide-react';\nimport { authApi } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AceternityRegister = () => {\n  _s();\n  const [userType, setUserType] = useState('Student');\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    name: '',\n    location: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n    setLoading(true);\n    try {\n      const registrationData = {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        userType,\n        ...(userType === 'XeroxCenter' ? {\n          name: formData.name,\n          location: formData.location\n        } : {})\n      };\n      if (userType === 'Student') {\n        await authApi.registerStudent(registrationData);\n      } else {\n        await authApi.registerXeroxCenter(registrationData);\n      }\n      navigate('/login', {\n        state: {\n          message: 'Registration successful! Please login with your credentials.',\n          email: formData.email\n        }\n      });\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  const floatingShapes = Array.from({\n    length: 6\n  }, (_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"absolute rounded-full opacity-20\",\n    style: {\n      background: `linear-gradient(135deg, ${i % 2 === 0 ? '#667eea' : '#764ba2'}, ${i % 2 === 0 ? '#764ba2' : '#667eea'})`,\n      width: `${Math.random() * 100 + 50}px`,\n      height: `${Math.random() * 100 + 50}px`,\n      left: `${Math.random() * 100}%`,\n      top: `${Math.random() * 100}%`\n    },\n    animate: {\n      x: [0, Math.random() * 100 - 50],\n      y: [0, Math.random() * 100 - 50],\n      rotate: [0, 360]\n    },\n    transition: {\n      duration: Math.random() * 10 + 10,\n      repeat: Infinity,\n      repeatType: \"reverse\"\n    }\n  }, i, false, {\n    fileName: _jsxFileName,\n    lineNumber: 98,\n    columnNumber: 5\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen position-relative overflow-hidden\",\n    style: {\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute w-100 h-100\",\n      children: [floatingShapes, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute w-100 h-100\",\n        style: {\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"min-h-screen d-flex align-items-center justify-content-center position-relative\",\n      style: {\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        className: \"w-100\",\n        style: {\n          maxWidth: '500px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          className: \"justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(Col, {\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              variants: itemVariants,\n              className: \"text-center mb-5\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  rotate: [0, 360],\n                  scale: [1, 1.1, 1]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"d-inline-flex align-items-center justify-content-center mb-4\",\n                style: {\n                  width: '80px',\n                  height: '80px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '20px',\n                  boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n                },\n                children: /*#__PURE__*/_jsxDEV(UserPlus, {\n                  className: \"text-white\",\n                  size: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-white fw-bold mb-2\",\n                style: {\n                  fontSize: '2.5rem'\n                },\n                children: \"Join Xerox Hub\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white-50 fs-5\",\n                children: \"Create your account and start printing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              variants: itemVariants,\n              className: \"p-4 rounded-4 position-relative\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)',\n                backdropFilter: 'blur(20px)',\n                border: '1px solid rgba(255, 255, 255, 0.2)',\n                boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                variants: itemVariants,\n                className: \"mb-4\",\n                children: /*#__PURE__*/_jsxDEV(Nav, {\n                  variant: \"pills\",\n                  className: \"justify-content-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: userType === 'Student',\n                        onClick: () => setUserType('Student'),\n                        className: `px-4 py-3 rounded-3 fw-semibold ${userType === 'Student' ? 'bg-white text-primary' : 'text-white-50'}`,\n                        style: {\n                          border: 'none',\n                          background: userType === 'Student' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                          transition: 'all 0.3s ease'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(User, {\n                          size: 18,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 208,\n                          columnNumber: 27\n                        }, this), \"Student\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 194,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n                    className: \"ms-2\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: userType === 'XeroxCenter',\n                        onClick: () => setUserType('XeroxCenter'),\n                        className: `px-4 py-3 rounded-3 fw-semibold ${userType === 'XeroxCenter' ? 'bg-white text-primary' : 'text-white-50'}`,\n                        style: {\n                          border: 'none',\n                          background: userType === 'XeroxCenter' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                          transition: 'all 0.3s ease'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Building, {\n                          size: 18,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 232,\n                          columnNumber: 27\n                        }, this), \"Xerox Center\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 218,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                mode: \"wait\",\n                children: error && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: -10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  exit: {\n                    opacity: 0,\n                    y: -10\n                  },\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"danger\",\n                    className: \"border-0 rounded-3\",\n                    style: {\n                      background: 'rgba(220, 53, 69, 0.1)',\n                      backdropFilter: 'blur(10px)',\n                      color: '#fff'\n                    },\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                onSubmit: handleSubmit,\n                children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: itemVariants,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(User, {\n                        className: \"position-absolute text-white-50\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"text\",\n                        name: \"username\",\n                        placeholder: \"Username\",\n                        value: formData.username,\n                        onChange: handleInputChange,\n                        required: true,\n                        className: \"ps-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: 'rgba(255, 255, 255, 0.1)',\n                          backdropFilter: 'blur(10px)',\n                          color: '#fff',\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: itemVariants,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(Mail, {\n                        className: \"position-absolute text-white-50\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        name: \"email\",\n                        placeholder: \"Email Address\",\n                        value: formData.email,\n                        onChange: handleInputChange,\n                        required: true,\n                        className: \"ps-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: 'rgba(255, 255, 255, 0.1)',\n                          backdropFilter: 'blur(10px)',\n                          color: '#fff',\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 297,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n                  mode: \"wait\",\n                  children: userType === 'XeroxCenter' && /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      height: 0\n                    },\n                    animate: {\n                      opacity: 1,\n                      height: 'auto'\n                    },\n                    exit: {\n                      opacity: 0,\n                      height: 0\n                    },\n                    transition: {\n                      duration: 0.3\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                      variants: itemVariants,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"position-relative\",\n                          children: [/*#__PURE__*/_jsxDEV(Building, {\n                            className: \"position-absolute text-white-50\",\n                            size: 20,\n                            style: {\n                              left: '15px',\n                              top: '50%',\n                              transform: 'translateY(-50%)',\n                              zIndex: 2\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 327,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            name: \"name\",\n                            placeholder: \"Center Name\",\n                            value: formData.name,\n                            onChange: handleInputChange,\n                            required: userType === 'XeroxCenter',\n                            className: \"ps-5 py-3 rounded-3 border-0\",\n                            style: {\n                              background: 'rgba(255, 255, 255, 0.1)',\n                              backdropFilter: 'blur(10px)',\n                              color: '#fff',\n                              fontSize: '16px'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 333,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 326,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 325,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                      variants: itemVariants,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"position-relative\",\n                          children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                            className: \"position-absolute text-white-50\",\n                            size: 20,\n                            style: {\n                              left: '15px',\n                              top: '50%',\n                              transform: 'translateY(-50%)',\n                              zIndex: 2\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 355,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                            type: \"text\",\n                            name: \"location\",\n                            placeholder: \"Location\",\n                            value: formData.location,\n                            onChange: handleInputChange,\n                            required: userType === 'XeroxCenter',\n                            className: \"ps-5 py-3 rounded-3 border-0\",\n                            style: {\n                              background: 'rgba(255, 255, 255, 0.1)',\n                              backdropFilter: 'blur(10px)',\n                              color: '#fff',\n                              fontSize: '16px'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 361,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 354,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: itemVariants,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(Lock, {\n                        className: \"position-absolute text-white-50\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: showPassword ? 'text' : 'password',\n                        name: \"password\",\n                        placeholder: \"Password\",\n                        value: formData.password,\n                        onChange: handleInputChange,\n                        required: true,\n                        className: \"ps-5 pe-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: 'rgba(255, 255, 255, 0.1)',\n                          backdropFilter: 'blur(10px)',\n                          color: '#fff',\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 392,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => setShowPassword(!showPassword),\n                        className: \"position-absolute border-0 bg-transparent text-white-50\",\n                        style: {\n                          right: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        },\n                        children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 418,\n                          columnNumber: 43\n                        }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 418,\n                          columnNumber: 66\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 407,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 384,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 383,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: itemVariants,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(Lock, {\n                        className: \"position-absolute text-white-50\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: showConfirmPassword ? 'text' : 'password',\n                        name: \"confirmPassword\",\n                        placeholder: \"Confirm Password\",\n                        value: formData.confirmPassword,\n                        onChange: handleInputChange,\n                        required: true,\n                        className: \"ps-5 pe-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: 'rgba(255, 255, 255, 0.1)',\n                          backdropFilter: 'blur(10px)',\n                          color: '#fff',\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                        className: \"position-absolute border-0 bg-transparent text-white-50\",\n                        style: {\n                          right: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        },\n                        children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 50\n                        }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 73\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  variants: itemVariants,\n                  children: /*#__PURE__*/_jsxDEV(motion.div, {\n                    whileHover: {\n                      scale: 1.02\n                    },\n                    whileTap: {\n                      scale: 0.98\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"submit\",\n                      disabled: loading,\n                      className: \"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\",\n                      style: {\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)',\n                        transition: 'all 0.3s ease'\n                      },\n                      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center justify-content-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"spinner-border spinner-border-sm me-2\",\n                          role: \"status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 482,\n                          columnNumber: 29\n                        }, this), \"Creating Account...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 481,\n                        columnNumber: 27\n                      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(UserPlus, {\n                          size: 20,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 29\n                        }, this), \"Create Account\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 470,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                variants: itemVariants,\n                className: \"text-center mt-4\",\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-white-50 mb-0\",\n                  children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                    to: \"/login\",\n                    className: \"text-white fw-semibold text-decoration-none\",\n                    style: {\n                      transition: 'all 0.3s ease'\n                    },\n                    onMouseEnter: e => e.target.style.color = '#764ba2',\n                    onMouseLeave: e => e.target.style.color = '#fff',\n                    children: \"Sign In\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 497,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(AceternityRegister, \"myv7UPXFsvMp57qHZ4BV8wjcsKw=\", false, function () {\n  return [useNavigate];\n});\n_c = AceternityRegister;\nexport default AceternityRegister;\nvar _c;\n$RefreshReg$(_c, \"AceternityRegister\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Nav", "Link", "useNavigate", "motion", "AnimatePresence", "User", "Building", "Mail", "Lock", "Eye", "Eye<PERSON>ff", "UserPlus", "<PERSON><PERSON><PERSON>", "authApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AceternityRegister", "_s", "userType", "setUserType", "formData", "setFormData", "username", "email", "password", "confirmPassword", "name", "location", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "error", "setError", "loading", "setLoading", "navigate", "handleInputChange", "e", "target", "value", "handleSubmit", "preventDefault", "length", "registrationData", "registerStudent", "registerXeroxCenter", "state", "message", "_error$response", "_error$response$data", "response", "data", "containerVariants", "hidden", "opacity", "visible", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "floatingShapes", "Array", "from", "_", "i", "div", "className", "style", "background", "width", "Math", "random", "height", "left", "top", "animate", "x", "rotate", "repeat", "Infinity", "repeatType", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "fluid", "zIndex", "variants", "initial", "max<PERSON><PERSON><PERSON>", "scale", "ease", "borderRadius", "boxShadow", "size", "fontSize", "<PERSON><PERSON>ilter", "border", "variant", "<PERSON><PERSON>", "whileHover", "whileTap", "active", "onClick", "mode", "exit", "color", "onSubmit", "Group", "transform", "Control", "type", "placeholder", "onChange", "required", "right", "disabled", "role", "to", "onMouseEnter", "onMouseLeave", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityRegister.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Container, Row, Col, Form, Button, Alert, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { User, Building, Mail, Lock, Eye, EyeOff, UserPlus, Sparkles } from 'lucide-react';\nimport { authApi } from '../../services/api';\n\nconst AceternityRegister: React.FC = () => {\n  const [userType, setUserType] = useState<'Student' | 'XeroxCenter'>('Student');\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    name: '',\n    location: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n\n    if (formData.password !== formData.confirmPassword) {\n      setError('Passwords do not match');\n      return;\n    }\n\n    if (formData.password.length < 6) {\n      setError('Password must be at least 6 characters long');\n      return;\n    }\n\n    setLoading(true);\n\n    try {\n      const registrationData = {\n        username: formData.username,\n        email: formData.email,\n        password: formData.password,\n        userType,\n        ...(userType === 'XeroxCenter' ? {\n          name: formData.name,\n          location: formData.location\n        } : {})\n      };\n\n      if (userType === 'Student') {\n        await authApi.registerStudent(registrationData);\n      } else {\n        await authApi.registerXeroxCenter(registrationData);\n      }\n      navigate('/login', { \n        state: { \n          message: 'Registration successful! Please login with your credentials.',\n          email: formData.email \n        } \n      });\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Registration failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const floatingShapes = Array.from({ length: 6 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"absolute rounded-full opacity-20\"\n      style={{\n        background: `linear-gradient(135deg, ${i % 2 === 0 ? '#667eea' : '#764ba2'}, ${i % 2 === 0 ? '#764ba2' : '#667eea'})`,\n        width: `${Math.random() * 100 + 50}px`,\n        height: `${Math.random() * 100 + 50}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n      }}\n      transition={{\n        duration: Math.random() * 10 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n      }}\n    />\n  ));\n\n  return (\n    <div className=\"min-h-screen position-relative overflow-hidden\" style={{\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n    }}>\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%)',\n        }} />\n      </div>\n\n      <Container fluid className=\"min-h-screen d-flex align-items-center justify-content-center position-relative\" style={{ zIndex: 1 }}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"w-100\"\n          style={{ maxWidth: '500px' }}\n        >\n          <Row className=\"justify-content-center\">\n            <Col>\n              <motion.div\n                variants={itemVariants}\n                className=\"text-center mb-5\"\n              >\n                <motion.div\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [1, 1.1, 1],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"d-inline-flex align-items-center justify-content-center mb-4\"\n                  style={{\n                    width: '80px',\n                    height: '80px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '20px',\n                    boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n                  }}\n                >\n                  <UserPlus className=\"text-white\" size={40} />\n                </motion.div>\n                <h1 className=\"text-white fw-bold mb-2\" style={{ fontSize: '2.5rem' }}>\n                  Join Xerox Hub\n                </h1>\n                <p className=\"text-white-50 fs-5\">\n                  Create your account and start printing\n                </p>\n              </motion.div>\n\n              <motion.div\n                variants={itemVariants}\n                className=\"p-4 rounded-4 position-relative\"\n                style={{\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  backdropFilter: 'blur(20px)',\n                  border: '1px solid rgba(255, 255, 255, 0.2)',\n                  boxShadow: '0 25px 50px rgba(0, 0, 0, 0.1)'\n                }}\n              >\n                {/* User Type Tabs */}\n                <motion.div variants={itemVariants} className=\"mb-4\">\n                  <Nav variant=\"pills\" className=\"justify-content-center\">\n                    <Nav.Item>\n                      <motion.div\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Nav.Link\n                          active={userType === 'Student'}\n                          onClick={() => setUserType('Student')}\n                          className={`px-4 py-3 rounded-3 fw-semibold ${\n                            userType === 'Student'\n                              ? 'bg-white text-primary'\n                              : 'text-white-50'\n                          }`}\n                          style={{\n                            border: 'none',\n                            background: userType === 'Student' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                            transition: 'all 0.3s ease'\n                          }}\n                        >\n                          <User size={18} className=\"me-2\" />\n                          Student\n                        </Nav.Link>\n                      </motion.div>\n                    </Nav.Item>\n                    <Nav.Item className=\"ms-2\">\n                      <motion.div\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <Nav.Link\n                          active={userType === 'XeroxCenter'}\n                          onClick={() => setUserType('XeroxCenter')}\n                          className={`px-4 py-3 rounded-3 fw-semibold ${\n                            userType === 'XeroxCenter'\n                              ? 'bg-white text-primary'\n                              : 'text-white-50'\n                          }`}\n                          style={{\n                            border: 'none',\n                            background: userType === 'XeroxCenter' ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                            transition: 'all 0.3s ease'\n                          }}\n                        >\n                          <Building size={18} className=\"me-2\" />\n                          Xerox Center\n                        </Nav.Link>\n                      </motion.div>\n                    </Nav.Item>\n                  </Nav>\n                </motion.div>\n\n                <AnimatePresence mode=\"wait\">\n                  {error && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                      className=\"mb-3\"\n                    >\n                      <Alert variant=\"danger\" className=\"border-0 rounded-3\" style={{\n                        background: 'rgba(220, 53, 69, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        color: '#fff'\n                      }}>\n                        {error}\n                      </Alert>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n\n                <Form onSubmit={handleSubmit}>\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <User className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type=\"text\"\n                          name=\"username\"\n                          placeholder=\"Username\"\n                          value={formData.username}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <Mail className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type=\"email\"\n                          name=\"email\"\n                          placeholder=\"Email Address\"\n                          value={formData.email}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <AnimatePresence mode=\"wait\">\n                    {userType === 'XeroxCenter' && (\n                      <motion.div\n                        initial={{ opacity: 0, height: 0 }}\n                        animate={{ opacity: 1, height: 'auto' }}\n                        exit={{ opacity: 0, height: 0 }}\n                        transition={{ duration: 0.3 }}\n                      >\n                        <motion.div variants={itemVariants}>\n                          <Form.Group className=\"mb-3\">\n                            <div className=\"position-relative\">\n                              <Building className=\"position-absolute text-white-50\" size={20} style={{\n                                left: '15px',\n                                top: '50%',\n                                transform: 'translateY(-50%)',\n                                zIndex: 2\n                              }} />\n                              <Form.Control\n                                type=\"text\"\n                                name=\"name\"\n                                placeholder=\"Center Name\"\n                                value={formData.name}\n                                onChange={handleInputChange}\n                                required={userType === 'XeroxCenter'}\n                                className=\"ps-5 py-3 rounded-3 border-0\"\n                                style={{\n                                  background: 'rgba(255, 255, 255, 0.1)',\n                                  backdropFilter: 'blur(10px)',\n                                  color: '#fff',\n                                  fontSize: '16px'\n                                }}\n                              />\n                            </div>\n                          </Form.Group>\n                        </motion.div>\n\n                        <motion.div variants={itemVariants}>\n                          <Form.Group className=\"mb-3\">\n                            <div className=\"position-relative\">\n                              <Sparkles className=\"position-absolute text-white-50\" size={20} style={{\n                                left: '15px',\n                                top: '50%',\n                                transform: 'translateY(-50%)',\n                                zIndex: 2\n                              }} />\n                              <Form.Control\n                                type=\"text\"\n                                name=\"location\"\n                                placeholder=\"Location\"\n                                value={formData.location}\n                                onChange={handleInputChange}\n                                required={userType === 'XeroxCenter'}\n                                className=\"ps-5 py-3 rounded-3 border-0\"\n                                style={{\n                                  background: 'rgba(255, 255, 255, 0.1)',\n                                  backdropFilter: 'blur(10px)',\n                                  color: '#fff',\n                                  fontSize: '16px'\n                                }}\n                              />\n                            </div>\n                          </Form.Group>\n                        </motion.div>\n                      </motion.div>\n                    )}\n                  </AnimatePresence>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <Lock className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type={showPassword ? 'text' : 'password'}\n                          name=\"password\"\n                          placeholder=\"Password\"\n                          value={formData.password}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 pe-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowPassword(!showPassword)}\n                          className=\"position-absolute border-0 bg-transparent text-white-50\"\n                          style={{\n                            right: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2\n                          }}\n                        >\n                          {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                        </button>\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <Form.Group className=\"mb-4\">\n                      <div className=\"position-relative\">\n                        <Lock className=\"position-absolute text-white-50\" size={20} style={{\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2\n                        }} />\n                        <Form.Control\n                          type={showConfirmPassword ? 'text' : 'password'}\n                          name=\"confirmPassword\"\n                          placeholder=\"Confirm Password\"\n                          value={formData.confirmPassword}\n                          onChange={handleInputChange}\n                          required\n                          className=\"ps-5 pe-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: 'rgba(255, 255, 255, 0.1)',\n                            backdropFilter: 'blur(10px)',\n                            color: '#fff',\n                            fontSize: '16px'\n                          }}\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                          className=\"position-absolute border-0 bg-transparent text-white-50\"\n                          style={{\n                            right: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2\n                          }}\n                        >\n                          {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                        </button>\n                      </div>\n                    </Form.Group>\n                  </motion.div>\n\n                  <motion.div variants={itemVariants}>\n                    <motion.div\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Button\n                        type=\"submit\"\n                        disabled={loading}\n                        className=\"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\"\n                        style={{\n                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                          boxShadow: '0 10px 30px rgba(102, 126, 234, 0.3)',\n                          transition: 'all 0.3s ease'\n                        }}\n                      >\n                        {loading ? (\n                          <div className=\"d-flex align-items-center justify-content-center\">\n                            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\" />\n                            Creating Account...\n                          </div>\n                        ) : (\n                          <>\n                            <UserPlus size={20} className=\"me-2\" />\n                            Create Account\n                          </>\n                        )}\n                      </Button>\n                    </motion.div>\n                  </motion.div>\n                </Form>\n\n                <motion.div variants={itemVariants} className=\"text-center mt-4\">\n                  <p className=\"text-white-50 mb-0\">\n                    Already have an account?{' '}\n                    <Link\n                      to=\"/login\"\n                      className=\"text-white fw-semibold text-decoration-none\"\n                      style={{\n                        transition: 'all 0.3s ease'\n                      }}\n                      onMouseEnter={(e) => (e.target as HTMLElement).style.color = '#764ba2'}\n                      onMouseLeave={(e) => (e.target as HTMLElement).style.color = '#fff'}\n                    >\n                      Sign In\n                    </Link>\n                  </p>\n                </motion.div>\n              </motion.div>\n            </Col>\n          </Row>\n        </motion.div>\n      </Container>\n    </div>\n  );\n};\n\nexport default AceternityRegister;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,iBAAiB;AAC/E,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAC1F,SAASC,OAAO,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7C,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAA4B,SAAS,CAAC;EAC9E,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC;IACvC+B,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM6C,QAAQ,GAAGpC,WAAW,CAAC,CAAC;EAE9B,MAAMqC,iBAAiB,GAAIC,CAAsC,IAAK;IACpEjB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACkB,CAAC,CAACC,MAAM,CAACb,IAAI,GAAGY,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAkB,IAAK;IACjDA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBT,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAIb,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MAClDQ,QAAQ,CAAC,wBAAwB,CAAC;MAClC;IACF;IAEA,IAAIb,QAAQ,CAACI,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;MAChCV,QAAQ,CAAC,6CAA6C,CAAC;MACvD;IACF;IAEAE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMS,gBAAgB,GAAG;QACvBtB,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;QAC3BC,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBC,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ;QAC3BN,QAAQ;QACR,IAAIA,QAAQ,KAAK,aAAa,GAAG;UAC/BQ,IAAI,EAAEN,QAAQ,CAACM,IAAI;UACnBC,QAAQ,EAAEP,QAAQ,CAACO;QACrB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;MAED,IAAIT,QAAQ,KAAK,SAAS,EAAE;QAC1B,MAAMP,OAAO,CAACkC,eAAe,CAACD,gBAAgB,CAAC;MACjD,CAAC,MAAM;QACL,MAAMjC,OAAO,CAACmC,mBAAmB,CAACF,gBAAgB,CAAC;MACrD;MACAR,QAAQ,CAAC,QAAQ,EAAE;QACjBW,KAAK,EAAE;UACLC,OAAO,EAAE,8DAA8D;UACvEzB,KAAK,EAAEH,QAAQ,CAACG;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOS,KAAU,EAAE;MAAA,IAAAiB,eAAA,EAAAC,oBAAA;MACnBjB,QAAQ,CAAC,EAAAgB,eAAA,GAAAjB,KAAK,CAACmB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,wCAAwC,CAAC;IACrF,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBN,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEM,CAAC,EAAE;IAAG,CAAC;IAC7BL,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVM,CAAC,EAAE,CAAC;MACJJ,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,MAAMI,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAErB,MAAM,EAAE;EAAE,CAAC,EAAE,CAACsB,CAAC,EAAEC,CAAC,kBACpDrD,OAAA,CAACZ,MAAM,CAACkE,GAAG;IAETC,SAAS,EAAC,kCAAkC;IAC5CC,KAAK,EAAE;MACLC,UAAU,EAAE,2BAA2BJ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,KAAKA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,GAAG;MACrHK,KAAK,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI;MACtCC,MAAM,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,IAAI;MACvCE,IAAI,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC/BG,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IAC7B,CAAE;IACFI,OAAO,EAAE;MACPC,CAAC,EAAE,CAAC,CAAC,EAAEN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCZ,CAAC,EAAE,CAAC,CAAC,EAAEW,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCM,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG;IACjB,CAAE;IACFtB,UAAU,EAAE;MACVC,QAAQ,EAAEc,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACjCO,MAAM,EAAEC,QAAQ;MAChBC,UAAU,EAAE;IACd;EAAE,GAlBGhB,CAAC;IAAAiB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAmBP,CACF,CAAC;EAEF,oBACEzE,OAAA;IAAKuD,SAAS,EAAC,gDAAgD;IAACC,KAAK,EAAE;MACrEC,UAAU,EAAE;IACd,CAAE;IAAAiB,QAAA,gBAEA1E,OAAA;MAAKuD,SAAS,EAAC,+BAA+B;MAAAmB,QAAA,GAC3CzB,cAAc,eACfjD,OAAA;QAAKuD,SAAS,EAAC,+BAA+B;QAACC,KAAK,EAAE;UACpDC,UAAU,EAAE;QACd;MAAE;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENzE,OAAA,CAACrB,SAAS;MAACgG,KAAK;MAACpB,SAAS,EAAC,iFAAiF;MAACC,KAAK,EAAE;QAAEoB,MAAM,EAAE;MAAE,CAAE;MAAAF,QAAA,eAChI1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;QACTuB,QAAQ,EAAErC,iBAAkB;QAC5BsC,OAAO,EAAC,QAAQ;QAChBd,OAAO,EAAC,SAAS;QACjBT,SAAS,EAAC,OAAO;QACjBC,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAQ,CAAE;QAAAL,QAAA,eAE7B1E,OAAA,CAACpB,GAAG;UAAC2E,SAAS,EAAC,wBAAwB;UAAAmB,QAAA,eACrC1E,OAAA,CAACnB,GAAG;YAAA6F,QAAA,gBACF1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;cACTuB,QAAQ,EAAE9B,YAAa;cACvBQ,SAAS,EAAC,kBAAkB;cAAAmB,QAAA,gBAE5B1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;gBACTU,OAAO,EAAE;kBACPE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;kBAChBc,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;gBACnB,CAAE;gBACFpC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXsB,MAAM,EAAEC,QAAQ;kBAChBa,IAAI,EAAE;gBACR,CAAE;gBACF1B,SAAS,EAAC,8DAA8D;gBACxEC,KAAK,EAAE;kBACLE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DyB,YAAY,EAAE,MAAM;kBACpBC,SAAS,EAAE;gBACb,CAAE;gBAAAT,QAAA,eAEF1E,OAAA,CAACJ,QAAQ;kBAAC2D,SAAS,EAAC,YAAY;kBAAC6B,IAAI,EAAE;gBAAG;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACbzE,OAAA;gBAAIuD,SAAS,EAAC,yBAAyB;gBAACC,KAAK,EAAE;kBAAE6B,QAAQ,EAAE;gBAAS,CAAE;gBAAAX,QAAA,EAAC;cAEvE;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzE,OAAA;gBAAGuD,SAAS,EAAC,oBAAoB;gBAAAmB,QAAA,EAAC;cAElC;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM,CAAC,eAEbzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;cACTuB,QAAQ,EAAE9B,YAAa;cACvBQ,SAAS,EAAC,iCAAiC;cAC3CC,KAAK,EAAE;gBACLC,UAAU,EAAE,0BAA0B;gBACtC6B,cAAc,EAAE,YAAY;gBAC5BC,MAAM,EAAE,oCAAoC;gBAC5CJ,SAAS,EAAE;cACb,CAAE;cAAAT,QAAA,gBAGF1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;gBAACuB,QAAQ,EAAE9B,YAAa;gBAACQ,SAAS,EAAC,MAAM;gBAAAmB,QAAA,eAClD1E,OAAA,CAACf,GAAG;kBAACuG,OAAO,EAAC,OAAO;kBAACjC,SAAS,EAAC,wBAAwB;kBAAAmB,QAAA,gBACrD1E,OAAA,CAACf,GAAG,CAACwG,IAAI;oBAAAf,QAAA,eACP1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;sBACToC,UAAU,EAAE;wBAAEV,KAAK,EAAE;sBAAK,CAAE;sBAC5BW,QAAQ,EAAE;wBAAEX,KAAK,EAAE;sBAAK,CAAE;sBAAAN,QAAA,eAE1B1E,OAAA,CAACf,GAAG,CAACC,IAAI;wBACP0G,MAAM,EAAEvF,QAAQ,KAAK,SAAU;wBAC/BwF,OAAO,EAAEA,CAAA,KAAMvF,WAAW,CAAC,SAAS,CAAE;wBACtCiD,SAAS,EAAE,mCACTlD,QAAQ,KAAK,SAAS,GAClB,uBAAuB,GACvB,eAAe,EAClB;wBACHmD,KAAK,EAAE;0BACL+B,MAAM,EAAE,MAAM;0BACd9B,UAAU,EAAEpD,QAAQ,KAAK,SAAS,GAAG,0BAA0B,GAAG,aAAa;0BAC/EuC,UAAU,EAAE;wBACd,CAAE;wBAAA8B,QAAA,gBAEF1E,OAAA,CAACV,IAAI;0BAAC8F,IAAI,EAAE,EAAG;0BAAC7B,SAAS,EAAC;wBAAM;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,WAErC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACXzE,OAAA,CAACf,GAAG,CAACwG,IAAI;oBAAClC,SAAS,EAAC,MAAM;oBAAAmB,QAAA,eACxB1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;sBACToC,UAAU,EAAE;wBAAEV,KAAK,EAAE;sBAAK,CAAE;sBAC5BW,QAAQ,EAAE;wBAAEX,KAAK,EAAE;sBAAK,CAAE;sBAAAN,QAAA,eAE1B1E,OAAA,CAACf,GAAG,CAACC,IAAI;wBACP0G,MAAM,EAAEvF,QAAQ,KAAK,aAAc;wBACnCwF,OAAO,EAAEA,CAAA,KAAMvF,WAAW,CAAC,aAAa,CAAE;wBAC1CiD,SAAS,EAAE,mCACTlD,QAAQ,KAAK,aAAa,GACtB,uBAAuB,GACvB,eAAe,EAClB;wBACHmD,KAAK,EAAE;0BACL+B,MAAM,EAAE,MAAM;0BACd9B,UAAU,EAAEpD,QAAQ,KAAK,aAAa,GAAG,0BAA0B,GAAG,aAAa;0BACnFuC,UAAU,EAAE;wBACd,CAAE;wBAAA8B,QAAA,gBAEF1E,OAAA,CAACT,QAAQ;0BAAC6F,IAAI,EAAE,EAAG;0BAAC7B,SAAS,EAAC;wBAAM;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEzC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAU;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAEbzE,OAAA,CAACX,eAAe;gBAACyG,IAAI,EAAC,MAAM;gBAAApB,QAAA,EACzBvD,KAAK,iBACJnB,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBACTwB,OAAO,EAAE;oBAAEpC,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCgB,OAAO,EAAE;oBAAEtB,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE;kBAAE,CAAE;kBAC9B+C,IAAI,EAAE;oBAAErD,OAAO,EAAE,CAAC;oBAAEM,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAC7BO,SAAS,EAAC,MAAM;kBAAAmB,QAAA,eAEhB1E,OAAA,CAAChB,KAAK;oBAACwG,OAAO,EAAC,QAAQ;oBAACjC,SAAS,EAAC,oBAAoB;oBAACC,KAAK,EAAE;sBAC5DC,UAAU,EAAE,wBAAwB;sBACpC6B,cAAc,EAAE,YAAY;sBAC5BU,KAAK,EAAE;oBACT,CAAE;oBAAAtB,QAAA,EACCvD;kBAAK;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cACb;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACc,CAAC,eAElBzE,OAAA,CAAClB,IAAI;gBAACmH,QAAQ,EAAErE,YAAa;gBAAA8C,QAAA,gBAC3B1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBAACuB,QAAQ,EAAE9B,YAAa;kBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAmB,QAAA,eAC1B1E,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAAmB,QAAA,gBAChC1E,OAAA,CAACV,IAAI;wBAACiE,SAAS,EAAC,iCAAiC;wBAAC6B,IAAI,EAAE,EAAG;wBAAC5B,KAAK,EAAE;0BACjEM,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV;sBAAE;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;wBACXC,IAAI,EAAC,MAAM;wBACXxF,IAAI,EAAC,UAAU;wBACfyF,WAAW,EAAC,UAAU;wBACtB3E,KAAK,EAAEpB,QAAQ,CAACE,QAAS;wBACzB8F,QAAQ,EAAE/E,iBAAkB;wBAC5BgF,QAAQ;wBACRjD,SAAS,EAAC,8BAA8B;wBACxCC,KAAK,EAAE;0BACLC,UAAU,EAAE,0BAA0B;0BACtC6B,cAAc,EAAE,YAAY;0BAC5BU,KAAK,EAAE,MAAM;0BACbX,QAAQ,EAAE;wBACZ;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEbzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBAACuB,QAAQ,EAAE9B,YAAa;kBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAmB,QAAA,eAC1B1E,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAAmB,QAAA,gBAChC1E,OAAA,CAACR,IAAI;wBAAC+D,SAAS,EAAC,iCAAiC;wBAAC6B,IAAI,EAAE,EAAG;wBAAC5B,KAAK,EAAE;0BACjEM,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV;sBAAE;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZxF,IAAI,EAAC,OAAO;wBACZyF,WAAW,EAAC,eAAe;wBAC3B3E,KAAK,EAAEpB,QAAQ,CAACG,KAAM;wBACtB6F,QAAQ,EAAE/E,iBAAkB;wBAC5BgF,QAAQ;wBACRjD,SAAS,EAAC,8BAA8B;wBACxCC,KAAK,EAAE;0BACLC,UAAU,EAAE,0BAA0B;0BACtC6B,cAAc,EAAE,YAAY;0BAC5BU,KAAK,EAAE,MAAM;0BACbX,QAAQ,EAAE;wBACZ;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEbzE,OAAA,CAACX,eAAe;kBAACyG,IAAI,EAAC,MAAM;kBAAApB,QAAA,EACzBrE,QAAQ,KAAK,aAAa,iBACzBL,OAAA,CAACZ,MAAM,CAACkE,GAAG;oBACTwB,OAAO,EAAE;sBAAEpC,OAAO,EAAE,CAAC;sBAAEmB,MAAM,EAAE;oBAAE,CAAE;oBACnCG,OAAO,EAAE;sBAAEtB,OAAO,EAAE,CAAC;sBAAEmB,MAAM,EAAE;oBAAO,CAAE;oBACxCkC,IAAI,EAAE;sBAAErD,OAAO,EAAE,CAAC;sBAAEmB,MAAM,EAAE;oBAAE,CAAE;oBAChCjB,UAAU,EAAE;sBAAEC,QAAQ,EAAE;oBAAI,CAAE;oBAAA6B,QAAA,gBAE9B1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;sBAACuB,QAAQ,EAAE9B,YAAa;sBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;wBAAC3C,SAAS,EAAC,MAAM;wBAAAmB,QAAA,eAC1B1E,OAAA;0BAAKuD,SAAS,EAAC,mBAAmB;0BAAAmB,QAAA,gBAChC1E,OAAA,CAACT,QAAQ;4BAACgE,SAAS,EAAC,iCAAiC;4BAAC6B,IAAI,EAAE,EAAG;4BAAC5B,KAAK,EAAE;8BACrEM,IAAI,EAAE,MAAM;8BACZC,GAAG,EAAE,KAAK;8BACVoC,SAAS,EAAE,kBAAkB;8BAC7BvB,MAAM,EAAE;4BACV;0BAAE;4BAAAN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXxF,IAAI,EAAC,MAAM;4BACXyF,WAAW,EAAC,aAAa;4BACzB3E,KAAK,EAAEpB,QAAQ,CAACM,IAAK;4BACrB0F,QAAQ,EAAE/E,iBAAkB;4BAC5BgF,QAAQ,EAAEnG,QAAQ,KAAK,aAAc;4BACrCkD,SAAS,EAAC,8BAA8B;4BACxCC,KAAK,EAAE;8BACLC,UAAU,EAAE,0BAA0B;8BACtC6B,cAAc,EAAE,YAAY;8BAC5BU,KAAK,EAAE,MAAM;8BACbX,QAAQ,EAAE;4BACZ;0BAAE;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEbzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;sBAACuB,QAAQ,EAAE9B,YAAa;sBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;wBAAC3C,SAAS,EAAC,MAAM;wBAAAmB,QAAA,eAC1B1E,OAAA;0BAAKuD,SAAS,EAAC,mBAAmB;0BAAAmB,QAAA,gBAChC1E,OAAA,CAACH,QAAQ;4BAAC0D,SAAS,EAAC,iCAAiC;4BAAC6B,IAAI,EAAE,EAAG;4BAAC5B,KAAK,EAAE;8BACrEM,IAAI,EAAE,MAAM;8BACZC,GAAG,EAAE,KAAK;8BACVoC,SAAS,EAAE,kBAAkB;8BAC7BvB,MAAM,EAAE;4BACV;0BAAE;4BAAAN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;4BACXC,IAAI,EAAC,MAAM;4BACXxF,IAAI,EAAC,UAAU;4BACfyF,WAAW,EAAC,UAAU;4BACtB3E,KAAK,EAAEpB,QAAQ,CAACO,QAAS;4BACzByF,QAAQ,EAAE/E,iBAAkB;4BAC5BgF,QAAQ,EAAEnG,QAAQ,KAAK,aAAc;4BACrCkD,SAAS,EAAC,8BAA8B;4BACxCC,KAAK,EAAE;8BACLC,UAAU,EAAE,0BAA0B;8BACtC6B,cAAc,EAAE,YAAY;8BAC5BU,KAAK,EAAE,MAAM;8BACbX,QAAQ,EAAE;4BACZ;0BAAE;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACc,CAAC,eAElBzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBAACuB,QAAQ,EAAE9B,YAAa;kBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAmB,QAAA,eAC1B1E,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAAmB,QAAA,gBAChC1E,OAAA,CAACP,IAAI;wBAAC8D,SAAS,EAAC,iCAAiC;wBAAC6B,IAAI,EAAE,EAAG;wBAAC5B,KAAK,EAAE;0BACjEM,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV;sBAAE;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;wBACXC,IAAI,EAAEtF,YAAY,GAAG,MAAM,GAAG,UAAW;wBACzCF,IAAI,EAAC,UAAU;wBACfyF,WAAW,EAAC,UAAU;wBACtB3E,KAAK,EAAEpB,QAAQ,CAACI,QAAS;wBACzB4F,QAAQ,EAAE/E,iBAAkB;wBAC5BgF,QAAQ;wBACRjD,SAAS,EAAC,mCAAmC;wBAC7CC,KAAK,EAAE;0BACLC,UAAU,EAAE,0BAA0B;0BACtC6B,cAAc,EAAE,YAAY;0BAC5BU,KAAK,EAAE,MAAM;0BACbX,QAAQ,EAAE;wBACZ;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFzE,OAAA;wBACEqG,IAAI,EAAC,QAAQ;wBACbR,OAAO,EAAEA,CAAA,KAAM7E,eAAe,CAAC,CAACD,YAAY,CAAE;wBAC9CwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACLiD,KAAK,EAAE,MAAM;0BACb1C,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV,CAAE;wBAAAF,QAAA,EAED3D,YAAY,gBAAGf,OAAA,CAACL,MAAM;0BAACyF,IAAI,EAAE;wBAAG;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGzE,OAAA,CAACN,GAAG;0BAAC0F,IAAI,EAAE;wBAAG;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEbzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBAACuB,QAAQ,EAAE9B,YAAa;kBAAA2B,QAAA,eACjC1E,OAAA,CAAClB,IAAI,CAACoH,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAmB,QAAA,eAC1B1E,OAAA;sBAAKuD,SAAS,EAAC,mBAAmB;sBAAAmB,QAAA,gBAChC1E,OAAA,CAACP,IAAI;wBAAC8D,SAAS,EAAC,iCAAiC;wBAAC6B,IAAI,EAAE,EAAG;wBAAC5B,KAAK,EAAE;0BACjEM,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV;sBAAE;wBAAAN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACLzE,OAAA,CAAClB,IAAI,CAACsH,OAAO;wBACXC,IAAI,EAAEpF,mBAAmB,GAAG,MAAM,GAAG,UAAW;wBAChDJ,IAAI,EAAC,iBAAiB;wBACtByF,WAAW,EAAC,kBAAkB;wBAC9B3E,KAAK,EAAEpB,QAAQ,CAACK,eAAgB;wBAChC2F,QAAQ,EAAE/E,iBAAkB;wBAC5BgF,QAAQ;wBACRjD,SAAS,EAAC,mCAAmC;wBAC7CC,KAAK,EAAE;0BACLC,UAAU,EAAE,0BAA0B;0BACtC6B,cAAc,EAAE,YAAY;0BAC5BU,KAAK,EAAE,MAAM;0BACbX,QAAQ,EAAE;wBACZ;sBAAE;wBAAAf,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFzE,OAAA;wBACEqG,IAAI,EAAC,QAAQ;wBACbR,OAAO,EAAEA,CAAA,KAAM3E,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;wBAC5DsC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACLiD,KAAK,EAAE,MAAM;0BACb1C,GAAG,EAAE,KAAK;0BACVoC,SAAS,EAAE,kBAAkB;0BAC7BvB,MAAM,EAAE;wBACV,CAAE;wBAAAF,QAAA,EAEDzD,mBAAmB,gBAAGjB,OAAA,CAACL,MAAM;0BAACyF,IAAI,EAAE;wBAAG;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGzE,OAAA,CAACN,GAAG;0BAAC0F,IAAI,EAAE;wBAAG;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEbzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;kBAACuB,QAAQ,EAAE9B,YAAa;kBAAA2B,QAAA,eACjC1E,OAAA,CAACZ,MAAM,CAACkE,GAAG;oBACToC,UAAU,EAAE;sBAAEV,KAAK,EAAE;oBAAK,CAAE;oBAC5BW,QAAQ,EAAE;sBAAEX,KAAK,EAAE;oBAAK,CAAE;oBAAAN,QAAA,eAE1B1E,OAAA,CAACjB,MAAM;sBACLsH,IAAI,EAAC,QAAQ;sBACbK,QAAQ,EAAErF,OAAQ;sBAClBkC,SAAS,EAAC,gDAAgD;sBAC1DC,KAAK,EAAE;wBACLC,UAAU,EAAE,mDAAmD;wBAC/D0B,SAAS,EAAE,sCAAsC;wBACjDvC,UAAU,EAAE;sBACd,CAAE;sBAAA8B,QAAA,EAEDrD,OAAO,gBACNrB,OAAA;wBAAKuD,SAAS,EAAC,kDAAkD;wBAAAmB,QAAA,gBAC/D1E,OAAA;0BAAKuD,SAAS,EAAC,uCAAuC;0BAACoD,IAAI,EAAC;wBAAQ;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,uBAEzE;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,gBAENzE,OAAA,CAAAE,SAAA;wBAAAwE,QAAA,gBACE1E,OAAA,CAACJ,QAAQ;0BAACwF,IAAI,EAAE,EAAG;0BAAC7B,SAAS,EAAC;wBAAM;0BAAAe,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,kBAEzC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAEPzE,OAAA,CAACZ,MAAM,CAACkE,GAAG;gBAACuB,QAAQ,EAAE9B,YAAa;gBAACQ,SAAS,EAAC,kBAAkB;gBAAAmB,QAAA,eAC9D1E,OAAA;kBAAGuD,SAAS,EAAC,oBAAoB;kBAAAmB,QAAA,GAAC,0BACR,EAAC,GAAG,eAC5B1E,OAAA,CAACd,IAAI;oBACH0H,EAAE,EAAC,QAAQ;oBACXrD,SAAS,EAAC,6CAA6C;oBACvDC,KAAK,EAAE;sBACLZ,UAAU,EAAE;oBACd,CAAE;oBACFiE,YAAY,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAiB8B,KAAK,CAACwC,KAAK,GAAG,SAAU;oBACvEc,YAAY,EAAGrF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAiB8B,KAAK,CAACwC,KAAK,GAAG,MAAO;oBAAAtB,QAAA,EACrE;kBAED;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACrE,EAAA,CA/fID,kBAA4B;EAAA,QAcfhB,WAAW;AAAA;AAAA4H,EAAA,GAdxB5G,kBAA4B;AAigBlC,eAAeA,kBAAkB;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}