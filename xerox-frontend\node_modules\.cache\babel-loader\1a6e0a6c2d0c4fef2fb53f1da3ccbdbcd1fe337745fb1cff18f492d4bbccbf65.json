{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Modal, Alert } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport '../styles/ProfessionalDashboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        className: 'requested',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        className: 'under-review',\n        icon: 'eye'\n      },\n      'Quoted': {\n        className: 'quoted',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        className: 'quoted',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        className: 'confirmed',\n        icon: 'check'\n      },\n      'InProgress': {\n        className: 'in-progress',\n        icon: 'cog'\n      },\n      'Completed': {\n        className: 'completed',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        className: 'delivered',\n        icon: 'truck'\n      },\n      'Rejected': {\n        className: 'rejected',\n        icon: 'times'\n      },\n      'Cancelled': {\n        className: 'cancelled',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      className: 'requested',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `status-badge ${config.className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon}`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadClass = pendingJobs => {\n    if (pendingJobs <= 5) return 'low';\n    if (pendingJobs <= 10) return 'medium';\n    return 'high';\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"professional-dashboard\",\n    children: [/*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"dashboard-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-between align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"dashboard-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-chart-line\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 17\n              }, this), \"Student Dashboard\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"dashboard-subtitle\",\n              children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"upload-btn\",\n            onClick: () => setShowUploadModal(true),\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cloud-upload-alt me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), \"Upload Files\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon primary\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"stat-title\",\n            children: \"Total Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"stat-value\",\n            children: printJobs.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card warning\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon warning\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"stat-title\",\n            children: \"In Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"stat-value\",\n            children: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card success\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon success\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"stat-title\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"stat-value\",\n            children: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-icon info\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"stat-title\",\n            children: \"Total Spent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"stat-value\",\n            children: [\"$\", printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"content-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pro-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pro-card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"pro-card-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-list\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this), \"Recent Print Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pro-card-body\",\n            children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"pro-table\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Job #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"File Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: printJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex align-items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-file-pdf me-2\",\n                        style: {\n                          color: 'var(--danger-color)'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 290,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 289,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(job.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.xeroxCenterName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"action-btn\",\n                        onClick: () => handleDownloadFile(job.id, job.fileName),\n                        title: \"Download File\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-download\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 306,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 301,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"action-btn primary\",\n                        onClick: () => handleViewJob(job),\n                        title: \"View Details\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-info\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 313,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 308,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"action-btn\",\n                        onClick: () => handleOpenChat(job),\n                        title: \"Chat\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-comment\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 320,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 29\n                      }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"action-btn success\",\n                        onClick: () => handleConfirmJob(job.id),\n                        title: \"Confirm Quote\",\n                        children: /*#__PURE__*/_jsxDEV(\"i\", {\n                          className: \"fas fa-check\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 328,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 323,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 25\n                  }, this)]\n                }, job.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"empty-state\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"empty-state-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-alt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"empty-state-title\",\n                children: \"No print jobs yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"empty-state-description\",\n                children: \"Upload your first file to get started!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pro-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pro-card-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"pro-card-title\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 353,\n                columnNumber: 17\n              }, this), \"Available Centers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pro-card-body\",\n            children: xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"center-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"center-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"center-name\",\n                  children: center.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `workload-badge ${getWorkloadClass(center.pendingJobs)}`,\n                  children: [center.pendingJobs, \" jobs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"center-location\",\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-map-marker-alt\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 367,\n                  columnNumber: 21\n                }, this), center.location]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"center-footer\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"rating\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-star\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: center.averageRating.toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"select-btn\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, center.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), \"Upload Files for Printing\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n              onChange: e => {\n                const files = e.target.files;\n                setSelectedFile(files ? files[0] : null);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Print Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.printType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    printType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Print\",\n                    children: \"Print\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Xerox\",\n                    children: \"Xerox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Binding\",\n                    children: \"Binding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 421,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Lamination\",\n                    children: \"Lamination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 422,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Number of Copies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 428,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  min: \"1\",\n                  value: uploadData.copies,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    copies: parseInt(e.target.value)\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Color Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.colorType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    colorType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BlackWhite\",\n                    children: \"Black & White\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Color\",\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Paper Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.paperSize,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    paperSize: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A4\",\n                    children: \"A4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 459,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A3\",\n                    children: \"A3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Letter\",\n                    children: \"Letter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Legal\",\n                    children: \"Legal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Preferred Xerox Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: uploadData.preferredXeroxCenterId,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                preferredXeroxCenterId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a center (optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: center.id,\n                children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              placeholder: \"Any special instructions or remarks...\",\n              value: uploadData.remarks,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                remarks: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowUploadModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          disabled: !selectedFile,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), \"Upload File\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showViewModal,\n      onHide: () => setShowViewModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-eye me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), \"Job Details - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 508,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"File Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Print Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.printType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 522,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 523,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.colorType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Paper Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.paperSize]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Job Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 22\n                }, this), \" \", getStatusBadge(selectedJob.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Cost:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Xerox Center:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.xeroxCenterName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Created:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 533,\n                  columnNumber: 22\n                }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Estimated Completion:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 24\n                }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 15\n          }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: selectedJob.remarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 17\n          }, this), selectedJob.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 19\n            }, this), \"This job has been quoted. Please confirm to proceed with printing.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [(selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.status) === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: () => {\n            handleConfirmJob(selectedJob.id);\n            setShowViewModal(false);\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-check me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 15\n          }, this), \"Confirm Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowViewModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 567,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 507,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showChatModal,\n      onHide: () => setShowChatModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-comment me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), \"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 575,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '400px',\n            overflowY: 'auto',\n            border: '1px solid #dee2e6',\n            borderRadius: '0.375rem',\n            padding: '1rem',\n            marginBottom: '1rem'\n          },\n          children: messages.length > 0 ? messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`,\n              style: {\n                maxWidth: '70%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: `d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`,\n                children: [message.senderName, \" - \", new Date(message.sentAt).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comments fa-3x mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No messages yet. Start a conversation!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Type your message...\",\n            value: chatMessage,\n            onChange: e => setChatMessage(e.target.value),\n            onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSendMessage,\n            disabled: !chatMessage.trim(),\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-paper-plane\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 581,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowChatModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"8ErydrNvcotwx3lsAKqva+c5cB0=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "<PERSON><PERSON>", "Form", "Modal", "<PERSON><PERSON>", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "className", "icon", "config", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadClass", "pendingJobs", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "fluid", "username", "onClick", "length", "filter", "includes", "reduce", "sum", "cost", "toFixed", "map", "jobNumber", "style", "color", "slice", "xeroxCenterName", "title", "center", "name", "location", "averageRating", "show", "onHide", "size", "Header", "closeButton", "Title", "Body", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "md", "Select", "value", "min", "parseInt", "as", "rows", "placeholder", "Footer", "variant", "disabled", "Date", "created", "toLocaleString", "estimatedCompletionTime", "height", "overflowY", "border", "borderRadius", "padding", "marginBottom", "message", "isFromCurrentUser", "max<PERSON><PERSON><PERSON>", "content", "sender<PERSON>ame", "sentAt", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Button, Form, Modal, Alert } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport '../styles/ProfessionalDashboard.css';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { className: 'requested', icon: 'clock' },\n      'UnderReview': { className: 'under-review', icon: 'eye' },\n      'Quoted': { className: 'quoted', icon: 'dollar-sign' },\n      'WaitingConfirmation': { className: 'quoted', icon: 'hourglass-half' },\n      'Confirmed': { className: 'confirmed', icon: 'check' },\n      'InProgress': { className: 'in-progress', icon: 'cog' },\n      'Completed': { className: 'completed', icon: 'check-circle' },\n      'Delivered': { className: 'delivered', icon: 'truck' },\n      'Rejected': { className: 'rejected', icon: 'times' },\n      'Cancelled': { className: 'cancelled', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { className: 'requested', icon: 'question' };\n\n    return (\n      <span className={`status-badge ${config.className}`}>\n        <i className={`fas fa-${config.icon}`}></i>\n        {status}\n      </span>\n    );\n  };\n\n  const getWorkloadClass = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'low';\n    if (pendingJobs <= 10) return 'medium';\n    return 'high';\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  return (\n    <div className=\"professional-dashboard\">\n      <Container fluid>\n        {/* Header Section */}\n        <div className=\"dashboard-header\">\n          <div className=\"d-flex justify-content-between align-items-center\">\n            <div>\n              <h1 className=\"dashboard-title\">\n                <i className=\"fas fa-chart-line\"></i>\n                Student Dashboard\n              </h1>\n              <p className=\"dashboard-subtitle\">Welcome back, {user?.username}!</p>\n            </div>\n            <button className=\"upload-btn\" onClick={() => setShowUploadModal(true)}>\n              <i className=\"fas fa-cloud-upload-alt me-2\"></i>\n              Upload Files\n            </button>\n          </div>\n        </div>\n\n        {/* Statistics Grid */}\n        <div className=\"stats-grid\">\n          <div className=\"stat-card\">\n            <div className=\"stat-icon primary\">\n              <i className=\"fas fa-file-alt\"></i>\n            </div>\n            <h3 className=\"stat-title\">Total Jobs</h3>\n            <p className=\"stat-value\">{printJobs.length}</p>\n          </div>\n\n          <div className=\"stat-card warning\">\n            <div className=\"stat-icon warning\">\n              <i className=\"fas fa-clock\"></i>\n            </div>\n            <h3 className=\"stat-title\">In Progress</h3>\n            <p className=\"stat-value\">\n              {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}\n            </p>\n          </div>\n\n          <div className=\"stat-card success\">\n            <div className=\"stat-icon success\">\n              <i className=\"fas fa-check-circle\"></i>\n            </div>\n            <h3 className=\"stat-title\">Completed</h3>\n            <p className=\"stat-value\">\n              {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}\n            </p>\n          </div>\n\n          <div className=\"stat-card info\">\n            <div className=\"stat-icon info\">\n              <i className=\"fas fa-dollar-sign\"></i>\n            </div>\n            <h3 className=\"stat-title\">Total Spent</h3>\n            <p className=\"stat-value\">\n              ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}\n            </p>\n          </div>\n        </div>\n\n        {/* Main Content Grid */}\n        <div className=\"content-grid\">\n          {/* Recent Jobs */}\n          <div className=\"pro-card\">\n            <div className=\"pro-card-header\">\n              <h2 className=\"pro-card-title\">\n                <i className=\"fas fa-list\"></i>\n                Recent Print Jobs\n              </h2>\n            </div>\n            <div className=\"pro-card-body\">\n              {printJobs.length > 0 ? (\n                <table className=\"pro-table\">\n                  <thead>\n                    <tr>\n                      <th>Job #</th>\n                      <th>File Name</th>\n                      <th>Status</th>\n                      <th>Cost</th>\n                      <th>Center</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {printJobs.map(job => (\n                      <tr key={job.id}>\n                        <td>\n                          <strong>{job.jobNumber}</strong>\n                        </td>\n                        <td>\n                          <div className=\"d-flex align-items-center\">\n                            <i className=\"fas fa-file-pdf me-2\" style={{color: 'var(--danger-color)'}}></i>\n                            <span>{job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName}</span>\n                          </div>\n                        </td>\n                        <td>{getStatusBadge(job.status)}</td>\n                        <td>\n                          <strong>{job.cost ? `$${job.cost.toFixed(2)}` : '-'}</strong>\n                        </td>\n                        <td>{job.xeroxCenterName}</td>\n                        <td>\n                          <div className=\"d-flex gap-2\">\n                            <button\n                              className=\"action-btn\"\n                              onClick={() => handleDownloadFile(job.id, job.fileName)}\n                              title=\"Download File\"\n                            >\n                              <i className=\"fas fa-download\"></i>\n                            </button>\n                            <button\n                              className=\"action-btn primary\"\n                              onClick={() => handleViewJob(job)}\n                              title=\"View Details\"\n                            >\n                              <i className=\"fas fa-info\"></i>\n                            </button>\n                            <button\n                              className=\"action-btn\"\n                              onClick={() => handleOpenChat(job)}\n                              title=\"Chat\"\n                            >\n                              <i className=\"fas fa-comment\"></i>\n                            </button>\n                            {job.status === 'Quoted' && (\n                              <button\n                                className=\"action-btn success\"\n                                onClick={() => handleConfirmJob(job.id)}\n                                title=\"Confirm Quote\"\n                              >\n                                <i className=\"fas fa-check\"></i>\n                              </button>\n                            )}\n                          </div>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </table>\n              ) : (\n                <div className=\"empty-state\">\n                  <div className=\"empty-state-icon\">\n                    <i className=\"fas fa-file-alt\"></i>\n                  </div>\n                  <h3 className=\"empty-state-title\">No print jobs yet</h3>\n                  <p className=\"empty-state-description\">Upload your first file to get started!</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Xerox Centers */}\n          <div className=\"pro-card\">\n            <div className=\"pro-card-header\">\n              <h2 className=\"pro-card-title\">\n                <i className=\"fas fa-store\"></i>\n                Available Centers\n              </h2>\n            </div>\n            <div className=\"pro-card-body\">\n              {xeroxCenters.map(center => (\n                <div key={center.id} className=\"center-card\">\n                  <div className=\"center-header\">\n                    <h3 className=\"center-name\">{center.name}</h3>\n                    <span className={`workload-badge ${getWorkloadClass(center.pendingJobs)}`}>\n                      {center.pendingJobs} jobs\n                    </span>\n                  </div>\n                  <p className=\"center-location\">\n                    <i className=\"fas fa-map-marker-alt\"></i>\n                    {center.location}\n                  </p>\n                  <div className=\"center-footer\">\n                    <div className=\"rating\">\n                      <i className=\"fas fa-star\"></i>\n                      <span>{center.averageRating.toFixed(1)}</span>\n                    </div>\n                    <button className=\"select-btn\">\n                      Select\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </Container>\n\n      {/* Upload Modal */}\n      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files for Printing\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select File</Form.Label>\n              <Form.Control\n                type=\"file\"\n                accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                onChange={(e) => {\n                  const files = (e.target as HTMLInputElement).files;\n                  setSelectedFile(files ? files[0] : null);\n                }}\n              />\n              <Form.Text className=\"text-muted\">\n                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n              </Form.Text>\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Print Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.printType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                  >\n                    <option value=\"Print\">Print</option>\n                    <option value=\"Xerox\">Xerox</option>\n                    <option value=\"Binding\">Binding</option>\n                    <option value=\"Lamination\">Lamination</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Number of Copies</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    min=\"1\"\n                    value={uploadData.copies}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Color Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.colorType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                  >\n                    <option value=\"BlackWhite\">Black & White</option>\n                    <option value=\"Color\">Color</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Paper Size</Form.Label>\n                  <Form.Select\n                    value={uploadData.paperSize}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"A3\">A3</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Preferred Xerox Center</Form.Label>\n              <Form.Select\n                value={uploadData.preferredXeroxCenterId}\n                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              >\n                <option value=\"\">Select a center (optional)</option>\n                {xeroxCenters.map(center => (\n                  <option key={center.id} value={center.id}>\n                    {center.name} - {center.pendingJobs} pending jobs\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Remarks</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                placeholder=\"Any special instructions or remarks...\"\n                value={uploadData.remarks}\n                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleFileUpload} disabled={!selectedFile}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload File\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* View Job Details Modal */}\n      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-eye me-2\"></i>\n            Job Details - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <div>\n              <Row>\n                <Col md={6}>\n                  <h6>File Information</h6>\n                  <hr />\n                  <p><strong>File Name:</strong> {selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName}</p>\n                  <p><strong>Print Type:</strong> {selectedJob.printType}</p>\n                  <p><strong>Copies:</strong> {selectedJob.copies}</p>\n                  <p><strong>Color Type:</strong> {selectedJob.colorType}</p>\n                  <p><strong>Paper Size:</strong> {selectedJob.paperSize}</p>\n                </Col>\n                <Col md={6}>\n                  <h6>Job Information</h6>\n                  <hr />\n                  <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>\n                  <p><strong>Cost:</strong> {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}</p>\n                  <p><strong>Xerox Center:</strong> {selectedJob.xeroxCenterName}</p>\n                  <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>\n                  {selectedJob.estimatedCompletionTime && (\n                    <p><strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}</p>\n                  )}\n                </Col>\n              </Row>\n              {selectedJob.remarks && (\n                <div className=\"mt-3\">\n                  <h6>Remarks</h6>\n                  <p className=\"text-muted\">{selectedJob.remarks}</p>\n                </div>\n              )}\n              {selectedJob.status === 'Quoted' && (\n                <Alert variant=\"warning\" className=\"mt-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  This job has been quoted. Please confirm to proceed with printing.\n                </Alert>\n              )}\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          {selectedJob?.status === 'Quoted' && (\n            <Button\n              variant=\"success\"\n              onClick={() => {\n                handleConfirmJob(selectedJob.id);\n                setShowViewModal(false);\n              }}\n            >\n              <i className=\"fas fa-check me-2\"></i>\n              Confirm Quote\n            </Button>\n          )}\n          <Button variant=\"secondary\" onClick={() => setShowViewModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Chat Modal */}\n      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-comment me-2\"></i>\n            Chat - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>\n            {messages.length > 0 ? (\n              messages.map((message) => (\n                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>\n                    <div>{message.content}</div>\n                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>\n                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}\n                    </small>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center text-muted\">\n                <i className=\"fas fa-comments fa-3x mb-3\"></i>\n                <p>No messages yet. Start a conversation!</p>\n              </div>\n            )}\n          </div>\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={chatMessage}\n              onChange={(e) => setChatMessage(e.target.value)}\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n              className=\"me-2\"\n            />\n            <Button variant=\"primary\" onClick={handleSendMessage} disabled={!chatMessage.trim()}>\n              <i className=\"fas fa-paper-plane\"></i>\n            </Button>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowChatModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </div>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AACjF,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AACxF,OAAO,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB7C,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyB,aAAa,EAAEC,gBAAgB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAC;IAC3CuC,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF3C,SAAS,CAAC,MAAM;IACd,MAAM4C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;;QAEpC;QACA,MAAMC,oBAAoB,GAAG,MAAMtC,cAAc,CAACuC,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,SAAS,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACtD,aAAa,EAAE;QAAED,SAAS,EAAE,cAAc;QAAEC,IAAI,EAAE;MAAM,CAAC;MACzD,QAAQ,EAAE;QAAED,SAAS,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAc,CAAC;MACtD,qBAAqB,EAAE;QAAED,SAAS,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACtE,WAAW,EAAE;QAAED,SAAS,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACtD,YAAY,EAAE;QAAED,SAAS,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAM,CAAC;MACvD,WAAW,EAAE;QAAED,SAAS,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAe,CAAC;MAC7D,WAAW,EAAE;QAAED,SAAS,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACtD,UAAU,EAAE;QAAED,SAAS,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,WAAW,EAAE;QAAED,SAAS,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAM;IACrD,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,SAAS,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAEhH,oBACE1C,OAAA;MAAMyC,SAAS,EAAE,gBAAgBE,MAAM,CAACF,SAAS,EAAG;MAAAG,QAAA,gBAClD5C,OAAA;QAAGyC,SAAS,EAAE,UAAUE,MAAM,CAACD,IAAI;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC1CT,MAAM;IAAA;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEX,CAAC;EAED,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,KAAK;IAClC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,QAAQ;IACtC,OAAO,MAAM;EACf,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACnC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMoC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtC,YAAY,CAAC;MACrCoC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEhC,UAAU,CAACE,OAAO,CAAC;MAC9C4B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAEhC,UAAU,CAACG,sBAAsB,CAAC;MAC5E2B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEhC,UAAU,CAACI,SAAS,CAAC;MAClD0B,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEhC,UAAU,CAACK,MAAM,CAAC4B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEhC,UAAU,CAACM,SAAS,CAAC;MAClDwB,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAEhC,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMhC,aAAa,CAAC2D,UAAU,CAACJ,QAAQ,CAAC;;MAExC;MACA,MAAMrB,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAMqB,aAAa,GAAIC,GAAQ,IAAK;IAClC3C,cAAc,CAAC2C,GAAG,CAAC;IACnB/C,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMgD,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACF3C,cAAc,CAAC2C,GAAG,CAAC;MACnB7C,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAM+C,QAAQ,GAAG,MAAM9D,UAAU,CAAC+D,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxDzC,WAAW,CAACuC,QAAQ,CAAC3B,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Cf,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAM0C,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEnB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAM/D,aAAa,CAACoE,YAAY,CAACD,KAAK,CAAC;;MAExD;MACA,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAAC3B,IAAI,CAAC,CAAC;MACtC,MAAMmC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG/B,QAAQ;MACxB4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAM8C,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAMrE,WAAW,CAACwF,UAAU,CAACnB,KAAK,CAAC;;MAEnC;MACA,MAAMjC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMgD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAClE,WAAW,CAACmE,IAAI,CAAC,CAAC,IAAI,CAACvE,WAAW,EAAE;IAEzC,IAAI;MACF,MAAM8C,QAAQ,GAAG,MAAM9D,UAAU,CAACwF,WAAW,CAACxE,WAAW,CAACgD,EAAE,EAAE5C,WAAW,CAACmE,IAAI,CAAC,CAAC,CAAC;;MAEjF;MACAhE,WAAW,CAACkE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAAC3B,IAAI,CAAC,CAAC;MAC7Cd,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,oBACEpC,OAAA;IAAKyC,SAAS,EAAC,wBAAwB;IAAAG,QAAA,gBACrC5C,OAAA,CAACb,SAAS;MAACqG,KAAK;MAAA5C,QAAA,gBAEd5C,OAAA;QAAKyC,SAAS,EAAC,kBAAkB;QAAAG,QAAA,eAC/B5C,OAAA;UAAKyC,SAAS,EAAC,mDAAmD;UAAAG,QAAA,gBAChE5C,OAAA;YAAA4C,QAAA,gBACE5C,OAAA;cAAIyC,SAAS,EAAC,iBAAiB;cAAAG,QAAA,gBAC7B5C,OAAA;gBAAGyC,SAAS,EAAC;cAAmB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLhD,OAAA;cAAGyC,SAAS,EAAC,oBAAoB;cAAAG,QAAA,GAAC,gBAAc,EAACzC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsF,QAAQ,EAAC,GAAC;YAAA;cAAA5C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eACNhD,OAAA;YAAQyC,SAAS,EAAC,YAAY;YAACiD,OAAO,EAAEA,CAAA,KAAMjF,kBAAkB,CAAC,IAAI,CAAE;YAAAmC,QAAA,gBACrE5C,OAAA;cAAGyC,SAAS,EAAC;YAA8B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gBAElD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAKyC,SAAS,EAAC,YAAY;QAAAG,QAAA,gBACzB5C,OAAA;UAAKyC,SAAS,EAAC,WAAW;UAAAG,QAAA,gBACxB5C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAG,QAAA,eAChC5C,OAAA;cAAGyC,SAAS,EAAC;YAAiB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eACNhD,OAAA;YAAIyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1ChD,OAAA;YAAGyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAExC,SAAS,CAACuF;UAAM;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAENhD,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAChC5C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAG,QAAA,eAChC5C,OAAA;cAAGyC,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC,eACNhD,OAAA;YAAIyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ChD,OAAA;YAAGyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EACtBxC,SAAS,CAACwF,MAAM,CAAClC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACmC,QAAQ,CAACnC,GAAG,CAACnB,MAAM,CAAC,CAAC,CAACoD;UAAM;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhD,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAChC5C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAG,QAAA,eAChC5C,OAAA;cAAGyC,SAAS,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNhD,OAAA;YAAIyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzChD,OAAA;YAAGyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EACtBxC,SAAS,CAACwF,MAAM,CAAClC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACmC,QAAQ,CAACnC,GAAG,CAACnB,MAAM,CAAC,CAAC,CAACoD;UAAM;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENhD,OAAA;UAAKyC,SAAS,EAAC,gBAAgB;UAAAG,QAAA,gBAC7B5C,OAAA;YAAKyC,SAAS,EAAC,gBAAgB;YAAAG,QAAA,eAC7B5C,OAAA;cAAGyC,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACNhD,OAAA;YAAIyC,SAAS,EAAC,YAAY;YAAAG,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3ChD,OAAA;YAAGyC,SAAS,EAAC,YAAY;YAAAG,QAAA,GAAC,GACvB,EAACxC,SAAS,CAAC0F,MAAM,CAAC,CAACC,GAAG,EAAErC,GAAG,KAAKqC,GAAG,IAAIrC,GAAG,CAACsC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhD,OAAA;QAAKyC,SAAS,EAAC,cAAc;QAAAG,QAAA,gBAE3B5C,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAAAG,QAAA,gBACvB5C,OAAA;YAAKyC,SAAS,EAAC,iBAAiB;YAAAG,QAAA,eAC9B5C,OAAA;cAAIyC,SAAS,EAAC,gBAAgB;cAAAG,QAAA,gBAC5B5C,OAAA;gBAAGyC,SAAS,EAAC;cAAa;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhD,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAG,QAAA,EAC3BxC,SAAS,CAACuF,MAAM,GAAG,CAAC,gBACnB3F,OAAA;cAAOyC,SAAS,EAAC,WAAW;cAAAG,QAAA,gBAC1B5C,OAAA;gBAAA4C,QAAA,eACE5C,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAA4C,QAAA,EAAI;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdhD,OAAA;oBAAA4C,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBhD,OAAA;oBAAA4C,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfhD,OAAA;oBAAA4C,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbhD,OAAA;oBAAA4C,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfhD,OAAA;oBAAA4C,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRhD,OAAA;gBAAA4C,QAAA,EACGxC,SAAS,CAAC8F,GAAG,CAACxC,GAAG,iBAChB1D,OAAA;kBAAA4C,QAAA,gBACE5C,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAA4C,QAAA,EAASc,GAAG,CAACyC;oBAAS;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLhD,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAKyC,SAAS,EAAC,2BAA2B;sBAAAG,QAAA,gBACxC5C,OAAA;wBAAGyC,SAAS,EAAC,sBAAsB;wBAAC2D,KAAK,EAAE;0BAACC,KAAK,EAAE;wBAAqB;sBAAE;wBAAAxD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/EhD,OAAA;wBAAA4C,QAAA,EAAOc,GAAG,CAACb,QAAQ,CAAC8C,MAAM,GAAG,EAAE,GAAGjC,GAAG,CAACb,QAAQ,CAACyD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG5C,GAAG,CAACb;sBAAQ;wBAAAA,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACLhD,OAAA;oBAAA4C,QAAA,EAAKN,cAAc,CAACoB,GAAG,CAACnB,MAAM;kBAAC;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrChD,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAA4C,QAAA,EAASc,GAAG,CAACsC,IAAI,GAAG,IAAItC,GAAG,CAACsC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;oBAAG;sBAAApD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eACLhD,OAAA;oBAAA4C,QAAA,EAAKc,GAAG,CAAC6C;kBAAe;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BhD,OAAA;oBAAA4C,QAAA,eACE5C,OAAA;sBAAKyC,SAAS,EAAC,cAAc;sBAAAG,QAAA,gBAC3B5C,OAAA;wBACEyC,SAAS,EAAC,YAAY;wBACtBiD,OAAO,EAAEA,CAAA,KAAM3B,kBAAkB,CAACL,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAACb,QAAQ,CAAE;wBACxD2D,KAAK,EAAC,eAAe;wBAAA5D,QAAA,eAErB5C,OAAA;0BAAGyC,SAAS,EAAC;wBAAiB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,eACThD,OAAA;wBACEyC,SAAS,EAAC,oBAAoB;wBAC9BiD,OAAO,EAAEA,CAAA,KAAMjC,aAAa,CAACC,GAAG,CAAE;wBAClC8C,KAAK,EAAC,cAAc;wBAAA5D,QAAA,eAEpB5C,OAAA;0BAAGyC,SAAS,EAAC;wBAAa;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACThD,OAAA;wBACEyC,SAAS,EAAC,YAAY;wBACtBiD,OAAO,EAAEA,CAAA,KAAM/B,cAAc,CAACD,GAAG,CAAE;wBACnC8C,KAAK,EAAC,MAAM;wBAAA5D,QAAA,eAEZ5C,OAAA;0BAAGyC,SAAS,EAAC;wBAAgB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5B,CAAC,EACRU,GAAG,CAACnB,MAAM,KAAK,QAAQ,iBACtBvC,OAAA;wBACEyC,SAAS,EAAC,oBAAoB;wBAC9BiD,OAAO,EAAEA,CAAA,KAAMR,gBAAgB,CAACxB,GAAG,CAACI,EAAE,CAAE;wBACxC0C,KAAK,EAAC,eAAe;wBAAA5D,QAAA,eAErB5C,OAAA;0BAAGyC,SAAS,EAAC;wBAAc;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CACT;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAhDEU,GAAG,CAACI,EAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiDX,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAERhD,OAAA;cAAKyC,SAAS,EAAC,aAAa;cAAAG,QAAA,gBAC1B5C,OAAA;gBAAKyC,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,eAC/B5C,OAAA;kBAAGyC,SAAS,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNhD,OAAA;gBAAIyC,SAAS,EAAC,mBAAmB;gBAAAG,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDhD,OAAA;gBAAGyC,SAAS,EAAC,yBAAyB;gBAAAG,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhD,OAAA;UAAKyC,SAAS,EAAC,UAAU;UAAAG,QAAA,gBACvB5C,OAAA;YAAKyC,SAAS,EAAC,iBAAiB;YAAAG,QAAA,eAC9B5C,OAAA;cAAIyC,SAAS,EAAC,gBAAgB;cAAAG,QAAA,gBAC5B5C,OAAA;gBAAGyC,SAAS,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAElC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNhD,OAAA;YAAKyC,SAAS,EAAC,eAAe;YAAAG,QAAA,EAC3BtC,YAAY,CAAC4F,GAAG,CAACO,MAAM,iBACtBzG,OAAA;cAAqByC,SAAS,EAAC,aAAa;cAAAG,QAAA,gBAC1C5C,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAG,QAAA,gBAC5B5C,OAAA;kBAAIyC,SAAS,EAAC,aAAa;kBAAAG,QAAA,EAAE6D,MAAM,CAACC;gBAAI;kBAAA7D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9ChD,OAAA;kBAAMyC,SAAS,EAAE,kBAAkBQ,gBAAgB,CAACwD,MAAM,CAACvD,WAAW,CAAC,EAAG;kBAAAN,QAAA,GACvE6D,MAAM,CAACvD,WAAW,EAAC,OACtB;gBAAA;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNhD,OAAA;gBAAGyC,SAAS,EAAC,iBAAiB;gBAAAG,QAAA,gBAC5B5C,OAAA;kBAAGyC,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACxCyD,MAAM,CAACE,QAAQ;cAAA;gBAAA9D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eACJhD,OAAA;gBAAKyC,SAAS,EAAC,eAAe;gBAAAG,QAAA,gBAC5B5C,OAAA;kBAAKyC,SAAS,EAAC,QAAQ;kBAAAG,QAAA,gBACrB5C,OAAA;oBAAGyC,SAAS,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/BhD,OAAA;oBAAA4C,QAAA,EAAO6D,MAAM,CAACG,aAAa,CAACX,OAAO,CAAC,CAAC;kBAAC;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACNhD,OAAA;kBAAQyC,SAAS,EAAC,YAAY;kBAAAG,QAAA,EAAC;gBAE/B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAnBEyD,MAAM,CAAC3C,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZhD,OAAA,CAACR,KAAK;MAACqH,IAAI,EAAErG,eAAgB;MAACsG,MAAM,EAAEA,CAAA,KAAMrG,kBAAkB,CAAC,KAAK,CAAE;MAACsG,IAAI,EAAC,IAAI;MAAAnE,QAAA,gBAC9E5C,OAAA,CAACR,KAAK,CAACwH,MAAM;QAACC,WAAW;QAAArE,QAAA,eACvB5C,OAAA,CAACR,KAAK,CAAC0H,KAAK;UAAAtE,QAAA,gBACV5C,OAAA;YAAGyC,SAAS,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,6BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfhD,OAAA,CAACR,KAAK,CAAC2H,IAAI;QAAAvE,QAAA,eACT5C,OAAA,CAACT,IAAI;UAAAqD,QAAA,gBACH5C,OAAA,CAACT,IAAI,CAAC6H,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;cAAAzE,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpChD,OAAA,CAACT,IAAI,CAAC+H,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAGC,CAAC,IAAK;gBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;gBAClD1G,eAAe,CAAC0G,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;cAC1C;YAAE;cAAA9E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFhD,OAAA,CAACT,IAAI,CAACsI,IAAI;cAACpF,SAAS,EAAC,YAAY;cAAAG,QAAA,EAAC;YAElC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEbhD,OAAA,CAACZ,GAAG;YAAAwD,QAAA,gBACF5C,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACT5C,OAAA,CAACT,IAAI,CAAC6H,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;kBAAAzE,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnChD,OAAA,CAACT,IAAI,CAACwI,MAAM;kBACVC,KAAK,EAAE1G,UAAU,CAACI,SAAU;kBAC5B+F,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7D,SAAS,EAAEgG,CAAC,CAACE,MAAM,CAACI;kBAAM,CAAC,CAAC,CAAE;kBAAApF,QAAA,gBAEjF5C,OAAA;oBAAQgI,KAAK,EAAC,OAAO;oBAAApF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpChD,OAAA;oBAAQgI,KAAK,EAAC,OAAO;oBAAApF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpChD,OAAA;oBAAQgI,KAAK,EAAC,SAAS;oBAAApF,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxChD,OAAA;oBAAQgI,KAAK,EAAC,YAAY;oBAAApF,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhD,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACT5C,OAAA,CAACT,IAAI,CAAC6H,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;kBAAAzE,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzChD,OAAA,CAACT,IAAI,CAAC+H,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbU,GAAG,EAAC,GAAG;kBACPD,KAAK,EAAE1G,UAAU,CAACK,MAAO;kBACzB8F,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5D,MAAM,EAAEuG,QAAQ,CAACR,CAAC,CAACE,MAAM,CAACI,KAAK;kBAAE,CAAC,CAAC;gBAAE;kBAAAnF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA,CAACZ,GAAG;YAAAwD,QAAA,gBACF5C,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACT5C,OAAA,CAACT,IAAI,CAAC6H,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;kBAAAzE,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnChD,OAAA,CAACT,IAAI,CAACwI,MAAM;kBACVC,KAAK,EAAE1G,UAAU,CAACM,SAAU;kBAC5B6F,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE3D,SAAS,EAAE8F,CAAC,CAACE,MAAM,CAACI;kBAAM,CAAC,CAAC,CAAE;kBAAApF,QAAA,gBAEjF5C,OAAA;oBAAQgI,KAAK,EAAC,YAAY;oBAAApF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjDhD,OAAA;oBAAQgI,KAAK,EAAC,OAAO;oBAAApF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNhD,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,eACT5C,OAAA,CAACT,IAAI,CAAC6H,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;kBAAAzE,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnChD,OAAA,CAACT,IAAI,CAACwI,MAAM;kBACVC,KAAK,EAAE1G,UAAU,CAACO,SAAU;kBAC5B4F,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1D,SAAS,EAAE6F,CAAC,CAACE,MAAM,CAACI;kBAAM,CAAC,CAAC,CAAE;kBAAApF,QAAA,gBAEjF5C,OAAA;oBAAQgI,KAAK,EAAC,IAAI;oBAAApF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BhD,OAAA;oBAAQgI,KAAK,EAAC,IAAI;oBAAApF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BhD,OAAA;oBAAQgI,KAAK,EAAC,QAAQ;oBAAApF,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtChD,OAAA;oBAAQgI,KAAK,EAAC,OAAO;oBAAApF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENhD,OAAA,CAACT,IAAI,CAAC6H,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;cAAAzE,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ChD,OAAA,CAACT,IAAI,CAACwI,MAAM;cACVC,KAAK,EAAE1G,UAAU,CAACG,sBAAuB;cACzCgG,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9D,sBAAsB,EAAEiG,CAAC,CAACE,MAAM,CAACI;cAAM,CAAC,CAAC,CAAE;cAAApF,QAAA,gBAE9F5C,OAAA;gBAAQgI,KAAK,EAAC,EAAE;gBAAApF,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnD1C,YAAY,CAAC4F,GAAG,CAACO,MAAM,iBACtBzG,OAAA;gBAAwBgI,KAAK,EAAEvB,MAAM,CAAC3C,EAAG;gBAAAlB,QAAA,GACtC6D,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAACvD,WAAW,EAAC,eACtC;cAAA,GAFauD,MAAM,CAAC3C,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbhD,OAAA,CAACT,IAAI,CAAC6H,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAG,QAAA,gBAC1B5C,OAAA,CAACT,IAAI,CAAC8H,KAAK;cAAAzE,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChChD,OAAA,CAACT,IAAI,CAAC+H,OAAO;cACXa,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,wCAAwC;cACpDL,KAAK,EAAE1G,UAAU,CAACE,OAAQ;cAC1BiG,QAAQ,EAAGC,CAAC,IAAKnG,aAAa,CAACgE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE/D,OAAO,EAAEkG,CAAC,CAACE,MAAM,CAACI;cAAM,CAAC,CAAC;YAAE;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbhD,OAAA,CAACR,KAAK,CAAC8I,MAAM;QAAA1F,QAAA,gBACX5C,OAAA,CAACV,MAAM;UAACiJ,OAAO,EAAC,WAAW;UAAC7C,OAAO,EAAEA,CAAA,KAAMjF,kBAAkB,CAAC,KAAK,CAAE;UAAAmC,QAAA,EAAC;QAEtE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThD,OAAA,CAACV,MAAM;UAACiJ,OAAO,EAAC,SAAS;UAAC7C,OAAO,EAAEvC,gBAAiB;UAACqF,QAAQ,EAAE,CAACxH,YAAa;UAAA4B,QAAA,gBAC3E5C,OAAA;YAAGyC,SAAS,EAAC;UAAoB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRhD,OAAA,CAACR,KAAK;MAACqH,IAAI,EAAEnG,aAAc;MAACoG,MAAM,EAAEA,CAAA,KAAMnG,gBAAgB,CAAC,KAAK,CAAE;MAACoG,IAAI,EAAC,IAAI;MAAAnE,QAAA,gBAC1E5C,OAAA,CAACR,KAAK,CAACwH,MAAM;QAACC,WAAW;QAAArE,QAAA,eACvB5C,OAAA,CAACR,KAAK,CAAC0H,KAAK;UAAAtE,QAAA,gBACV5C,OAAA;YAAGyC,SAAS,EAAC;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBACrB,EAAClC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,SAAS;QAAA;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfhD,OAAA,CAACR,KAAK,CAAC2H,IAAI;QAAAvE,QAAA,EACR9B,WAAW,iBACVd,OAAA;UAAA4C,QAAA,gBACE5C,OAAA,CAACZ,GAAG;YAAAwD,QAAA,gBACF5C,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,gBACT5C,OAAA;gBAAA4C,QAAA,EAAI;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAAC+B,QAAQ,CAAC8C,MAAM,GAAG,EAAE,GAAG7E,WAAW,CAAC+B,QAAQ,CAACyD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGxF,WAAW,CAAC+B,QAAQ;cAAA;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxIhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACY,SAAS;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACa,MAAM;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACc,SAAS;cAAA;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACe,SAAS;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNhD,OAAA,CAACX,GAAG;cAACyI,EAAE,EAAE,CAAE;cAAAlF,QAAA,gBACT5C,OAAA;gBAAA4C,QAAA,EAAI;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACV,cAAc,CAACxB,WAAW,CAACyB,MAAM,CAAC;cAAA;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACkF,IAAI,GAAG,IAAIlF,WAAW,CAACkF,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,gBAAgB;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACyF,eAAe;cAAA;gBAAA1D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnEhD,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIyF,IAAI,CAAC3H,WAAW,CAAC4H,OAAO,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAChFlC,WAAW,CAAC8H,uBAAuB,iBAClC5I,OAAA;gBAAA4C,QAAA,gBAAG5C,OAAA;kBAAA4C,QAAA,EAAQ;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAIyF,IAAI,CAAC3H,WAAW,CAAC8H,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC;cAAA;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9G;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLlC,WAAW,CAACU,OAAO,iBAClBxB,OAAA;YAAKyC,SAAS,EAAC,MAAM;YAAAG,QAAA,gBACnB5C,OAAA;cAAA4C,QAAA,EAAI;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBhD,OAAA;cAAGyC,SAAS,EAAC,YAAY;cAAAG,QAAA,EAAE9B,WAAW,CAACU;YAAO;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN,EACAlC,WAAW,CAACyB,MAAM,KAAK,QAAQ,iBAC9BvC,OAAA,CAACP,KAAK;YAAC8I,OAAO,EAAC,SAAS;YAAC9F,SAAS,EAAC,MAAM;YAAAG,QAAA,gBACvC5C,OAAA;cAAGyC,SAAS,EAAC;YAAkC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sEAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbhD,OAAA,CAACR,KAAK,CAAC8I,MAAM;QAAA1F,QAAA,GACV,CAAA9B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,MAAM,MAAK,QAAQ,iBAC/BvC,OAAA,CAACV,MAAM;UACLiJ,OAAO,EAAC,SAAS;UACjB7C,OAAO,EAAEA,CAAA,KAAM;YACbR,gBAAgB,CAACpE,WAAW,CAACgD,EAAE,CAAC;YAChCnD,gBAAgB,CAAC,KAAK,CAAC;UACzB,CAAE;UAAAiC,QAAA,gBAEF5C,OAAA;YAAGyC,SAAS,EAAC;UAAmB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDhD,OAAA,CAACV,MAAM;UAACiJ,OAAO,EAAC,WAAW;UAAC7C,OAAO,EAAEA,CAAA,KAAM/E,gBAAgB,CAAC,KAAK,CAAE;UAAAiC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRhD,OAAA,CAACR,KAAK;MAACqH,IAAI,EAAEjG,aAAc;MAACkG,MAAM,EAAEA,CAAA,KAAMjG,gBAAgB,CAAC,KAAK,CAAE;MAACkG,IAAI,EAAC,IAAI;MAAAnE,QAAA,gBAC1E5C,OAAA,CAACR,KAAK,CAACwH,MAAM;QAACC,WAAW;QAAArE,QAAA,eACvB5C,OAAA,CAACR,KAAK,CAAC0H,KAAK;UAAAtE,QAAA,gBACV5C,OAAA;YAAGyC,SAAS,EAAC;UAAqB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WAChC,EAAClC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqF,SAAS;QAAA;UAAAtD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfhD,OAAA,CAACR,KAAK,CAAC2H,IAAI;QAAAvE,QAAA,gBACT5C,OAAA;UAAKoG,KAAK,EAAE;YAAEyC,MAAM,EAAE,OAAO;YAAEC,SAAS,EAAE,MAAM;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE,UAAU;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAtG,QAAA,EAC9IxB,QAAQ,CAACuE,MAAM,GAAG,CAAC,GAClBvE,QAAQ,CAAC8E,GAAG,CAAEiD,OAAO,iBACnBnJ,OAAA;YAAsByC,SAAS,EAAE,QAAQ0G,OAAO,CAACC,iBAAiB,GAAG,UAAU,GAAG,YAAY,EAAG;YAAAxG,QAAA,eAC/F5C,OAAA;cAAKyC,SAAS,EAAE,8BAA8B0G,OAAO,CAACC,iBAAiB,GAAG,uBAAuB,GAAG,UAAU,EAAG;cAAChD,KAAK,EAAE;gBAAEiD,QAAQ,EAAE;cAAM,CAAE;cAAAzG,QAAA,gBAC3I5C,OAAA;gBAAA4C,QAAA,EAAMuG,OAAO,CAACG;cAAO;gBAAAzG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BhD,OAAA;gBAAOyC,SAAS,EAAE,gBAAgB0G,OAAO,CAACC,iBAAiB,GAAG,YAAY,GAAG,YAAY,EAAG;gBAAAxG,QAAA,GACzFuG,OAAO,CAACI,UAAU,EAAC,KAAG,EAAC,IAAId,IAAI,CAACU,OAAO,CAACK,MAAM,CAAC,CAACb,cAAc,CAAC,CAAC;cAAA;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GANEmG,OAAO,CAACrF,EAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACN,CAAC,gBAEFhD,OAAA;YAAKyC,SAAS,EAAC,wBAAwB;YAAAG,QAAA,gBACrC5C,OAAA;cAAGyC,SAAS,EAAC;YAA4B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ChD,OAAA;cAAA4C,QAAA,EAAG;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNhD,OAAA;UAAKyC,SAAS,EAAC,QAAQ;UAAAG,QAAA,gBACrB5C,OAAA,CAACT,IAAI,CAAC+H,OAAO;YACXC,IAAI,EAAC,MAAM;YACXc,WAAW,EAAC,sBAAsB;YAClCL,KAAK,EAAE9G,WAAY;YACnBuG,QAAQ,EAAGC,CAAC,IAAKvG,cAAc,CAACuG,CAAC,CAACE,MAAM,CAACI,KAAK,CAAE;YAChDyB,SAAS,EAAG/B,CAAC,IAAKA,CAAC,CAACgC,GAAG,KAAK,OAAO,IAAItE,iBAAiB,CAAC,CAAE;YAC3D3C,SAAS,EAAC;UAAM;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFhD,OAAA,CAACV,MAAM;YAACiJ,OAAO,EAAC,SAAS;YAAC7C,OAAO,EAAEN,iBAAkB;YAACoD,QAAQ,EAAE,CAACtH,WAAW,CAACmE,IAAI,CAAC,CAAE;YAAAzC,QAAA,eAClF5C,OAAA;cAAGyC,SAAS,EAAC;YAAoB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACbhD,OAAA,CAACR,KAAK,CAAC8I,MAAM;QAAA1F,QAAA,eACX5C,OAAA,CAACV,MAAM;UAACiJ,OAAO,EAAC,WAAW;UAAC7C,OAAO,EAAEA,CAAA,KAAM7E,gBAAgB,CAAC,KAAK,CAAE;UAAA+B,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAplBID,gBAA0B;EAAA,QACbP,OAAO;AAAA;AAAAiK,EAAA,GADpB1J,gBAA0B;AAslBhC,eAAeA,gBAAgB;AAAC,IAAA0J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}