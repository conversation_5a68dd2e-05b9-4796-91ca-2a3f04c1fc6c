using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using XeroxModule.Core.Interfaces;
using XeroxModule.Core.Entities;

namespace XeroxModule.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class XeroxCenterController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public XeroxCenterController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllXeroxCenters()
        {
            var xeroxCenters = await _unitOfWork.XeroxCenters.GetAllAsync(
                xc => xc.IsActive,
                includeProperties: "PrintJobs"
            );

            var result = xeroxCenters.Select(xc => new
            {
                id = xc.XeroxCenterID,
                name = xc.XeroxCenterName,
                location = xc.Location,
                contactPerson = xc.ContactPerson,
                phoneNumber = xc.PhoneNumber,
                description = xc.Description,
                pendingJobs = xc.PrintJobs.Count(pj => new[] { "Requested", "UnderReview", "Quoted", "WaitingConfirmation", "Confirmed", "InProgress" }.Contains(pj.Status)),
                totalJobs = xc.TotalJobs,
                completedJobs = xc.CompletedJobs,
                averageRating = xc.AverageRating ?? 0,
                totalRatings = xc.TotalRatings,
                isActive = xc.IsActive
            });

            return Ok(result);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetXeroxCenter(int id)
        {
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(
                xc => xc.XeroxCenterID == id && xc.IsActive,
                includeProperties: "PrintJobs,User"
            );

            if (xeroxCenter == null)
            {
                return NotFound("Xerox center not found");
            }

            var result = new
            {
                id = xeroxCenter.XeroxCenterID,
                name = xeroxCenter.XeroxCenterName,
                location = xeroxCenter.Location,
                contactPerson = xeroxCenter.ContactPerson,
                email = xeroxCenter.Email,
                phoneNumber = xeroxCenter.PhoneNumber,
                description = xeroxCenter.Description,
                pendingJobs = xeroxCenter.PrintJobs.Count(pj => new[] { "Requested", "UnderReview", "Quoted", "WaitingConfirmation", "Confirmed", "InProgress" }.Contains(pj.Status)),
                totalJobs = xeroxCenter.TotalJobs,
                completedJobs = xeroxCenter.CompletedJobs,
                averageRating = xeroxCenter.AverageRating.GetValueOrDefault(),
                totalRatings = xeroxCenter.TotalRatings,
                isActive = xeroxCenter.IsActive,
                createdAt = xeroxCenter.Created
            };

            return Ok(result);
        }

        [HttpGet("dashboard-stats")]
        [Authorize]
        public async Task<IActionResult> GetDashboardStats()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(
                xc => xc.UserID == userId,
                includeProperties: "PrintJobs"
            );

            if (xeroxCenter == null)
            {
                return BadRequest("Xerox center not found");
            }

            var printJobs = xeroxCenter.PrintJobs.ToList();
            
            var stats = new
            {
                total = printJobs.Count,
                pending = printJobs.Count(pj => new[] { "Requested", "UnderReview", "Quoted", "WaitingConfirmation" }.Contains(pj.Status)),
                inProgress = printJobs.Count(pj => new[] { "Confirmed", "InProgress" }.Contains(pj.Status)),
                completed = printJobs.Count(pj => new[] { "Completed", "Delivered" }.Contains(pj.Status)),
                revenue = printJobs.Where(pj => pj.Cost.HasValue).Sum(pj => pj.Cost.Value),
                todayJobs = printJobs.Count(pj => pj.Created.Date == DateTime.Today),
                weekJobs = printJobs.Count(pj => pj.Created >= DateTime.Today.AddDays(-7)),
                monthJobs = printJobs.Count(pj => pj.Created >= DateTime.Today.AddDays(-30))
            };

            return Ok(stats);
        }

        [HttpPut("profile")]
        [Authorize]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateXeroxCenterRequest request)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);
            if (xeroxCenter == null)
            {
                return BadRequest("Xerox center not found");
            }

            xeroxCenter.XeroxCenterName = request.XeroxCenterName;
            xeroxCenter.Location = request.Location;
            xeroxCenter.ContactPerson = request.ContactPerson;
            xeroxCenter.PhoneNumber = request.PhoneNumber;
            xeroxCenter.Description = request.Description;
            xeroxCenter.Modified = DateTime.UtcNow;
            xeroxCenter.ModifiedUserID = userId;

            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "Profile updated successfully" });
        }
    }

    public class UpdateXeroxCenterRequest
    {
        public string XeroxCenterName { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string? ContactPerson { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string? Description { get; set; }
    }
}
