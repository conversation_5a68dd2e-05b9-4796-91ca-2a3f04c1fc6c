{"ast": null, "code": "import useMergeState from './useMergeState';\nexport default function useMergeStateFromProps(props, gDSFP, initialState) {\n  const [state, setState] = useMergeState(initialState);\n  const nextState = gDSFP(props, state);\n  if (nextState !== null) setState(nextState);\n  return [state, setState];\n}", "map": {"version": 3, "names": ["useMergeState", "useMergeStateFromProps", "props", "gDSFP", "initialState", "state", "setState", "nextState"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useMergeStateFromProps.js"], "sourcesContent": ["import useMergeState from './useMergeState';\nexport default function useMergeStateFromProps(props, gDSFP, initialState) {\n  const [state, setState] = useMergeState(initialState);\n  const nextState = gDSFP(props, state);\n  if (nextState !== null) setState(nextState);\n  return [state, setState];\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,eAAe,SAASC,sBAAsBA,CAACC,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAE;EACzE,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,aAAa,CAACI,YAAY,CAAC;EACrD,MAAMG,SAAS,GAAGJ,KAAK,CAACD,KAAK,EAAEG,KAAK,CAAC;EACrC,IAAIE,SAAS,KAAK,IAAI,EAAED,QAAQ,CAACC,SAAS,CAAC;EAC3C,OAAO,CAACF,KAAK,EAAEC,QAAQ,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}