{"ast": null, "code": "import { warning, secondsToMilliseconds, clamp, millisecondsToSeconds } from 'motion-utils';\nimport { springDefaults } from './defaults.mjs';\nconst safeMin = 0.001;\nfunction findSpring(_ref) {\n  let {\n    duration = springDefaults.duration,\n    bounce = springDefaults.bounce,\n    velocity = springDefaults.velocity,\n    mass = springDefaults.mass\n  } = _ref;\n  let envelope;\n  let derivative;\n  warning(duration <= secondsToMilliseconds(springDefaults.maxDuration), \"Spring duration must be 10 seconds or less\");\n  let dampingRatio = 1 - bounce;\n  /**\n   * Restrict dampingRatio and duration to within acceptable ranges.\n   */\n  dampingRatio = clamp(springDefaults.minDamping, springDefaults.maxDamping, dampingRatio);\n  duration = clamp(springDefaults.minDuration, springDefaults.maxDuration, millisecondsToSeconds(duration));\n  if (dampingRatio < 1) {\n    /**\n     * Underdamped spring\n     */\n    envelope = undampedFreq => {\n      const exponentialDecay = undampedFreq * dampingRatio;\n      const delta = exponentialDecay * duration;\n      const a = exponentialDecay - velocity;\n      const b = calcAngularFreq(undampedFreq, dampingRatio);\n      const c = Math.exp(-delta);\n      return safeMin - a / b * c;\n    };\n    derivative = undampedFreq => {\n      const exponentialDecay = undampedFreq * dampingRatio;\n      const delta = exponentialDecay * duration;\n      const d = delta * velocity + velocity;\n      const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n      const f = Math.exp(-delta);\n      const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n      const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n      return factor * ((d - e) * f) / g;\n    };\n  } else {\n    /**\n     * Critically-damped spring\n     */\n    envelope = undampedFreq => {\n      const a = Math.exp(-undampedFreq * duration);\n      const b = (undampedFreq - velocity) * duration + 1;\n      return -safeMin + a * b;\n    };\n    derivative = undampedFreq => {\n      const a = Math.exp(-undampedFreq * duration);\n      const b = (velocity - undampedFreq) * (duration * duration);\n      return a * b;\n    };\n  }\n  const initialGuess = 5 / duration;\n  const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n  duration = secondsToMilliseconds(duration);\n  if (isNaN(undampedFreq)) {\n    return {\n      stiffness: springDefaults.stiffness,\n      damping: springDefaults.damping,\n      duration\n    };\n  } else {\n    const stiffness = Math.pow(undampedFreq, 2) * mass;\n    return {\n      stiffness,\n      damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n      duration\n    };\n  }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n  let result = initialGuess;\n  for (let i = 1; i < rootIterations; i++) {\n    result = result - envelope(result) / derivative(result);\n  }\n  return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n  return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\nexport { calcAngularFreq, findSpring };", "map": {"version": 3, "names": ["warning", "secondsToMilliseconds", "clamp", "millisecondsToSeconds", "springDefaults", "safeMin", "findSpring", "_ref", "duration", "bounce", "velocity", "mass", "envelope", "derivative", "maxDuration", "dampingRatio", "minDamping", "maxDamping", "minDuration", "undampedFreq", "exponentialDecay", "delta", "a", "b", "calcAngularFreq", "c", "Math", "exp", "d", "e", "pow", "f", "g", "factor", "initialGuess", "approximateRoot", "isNaN", "stiffness", "damping", "sqrt", "rootIterations", "result", "i"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/animation/generators/spring/find.mjs"], "sourcesContent": ["import { warning, secondsToMilliseconds, clamp, millisecondsToSeconds } from 'motion-utils';\nimport { springDefaults } from './defaults.mjs';\n\nconst safeMin = 0.001;\nfunction findSpring({ duration = springDefaults.duration, bounce = springDefaults.bounce, velocity = springDefaults.velocity, mass = springDefaults.mass, }) {\n    let envelope;\n    let derivative;\n    warning(duration <= secondsToMilliseconds(springDefaults.maxDuration), \"Spring duration must be 10 seconds or less\");\n    let dampingRatio = 1 - bounce;\n    /**\n     * Restrict dampingRatio and duration to within acceptable ranges.\n     */\n    dampingRatio = clamp(springDefaults.minDamping, springDefaults.maxDamping, dampingRatio);\n    duration = clamp(springDefaults.minDuration, springDefaults.maxDuration, millisecondsToSeconds(duration));\n    if (dampingRatio < 1) {\n        /**\n         * Underdamped spring\n         */\n        envelope = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const a = exponentialDecay - velocity;\n            const b = calcAngularFreq(undampedFreq, dampingRatio);\n            const c = Math.exp(-delta);\n            return safeMin - (a / b) * c;\n        };\n        derivative = (undampedFreq) => {\n            const exponentialDecay = undampedFreq * dampingRatio;\n            const delta = exponentialDecay * duration;\n            const d = delta * velocity + velocity;\n            const e = Math.pow(dampingRatio, 2) * Math.pow(undampedFreq, 2) * duration;\n            const f = Math.exp(-delta);\n            const g = calcAngularFreq(Math.pow(undampedFreq, 2), dampingRatio);\n            const factor = -envelope(undampedFreq) + safeMin > 0 ? -1 : 1;\n            return (factor * ((d - e) * f)) / g;\n        };\n    }\n    else {\n        /**\n         * Critically-damped spring\n         */\n        envelope = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (undampedFreq - velocity) * duration + 1;\n            return -safeMin + a * b;\n        };\n        derivative = (undampedFreq) => {\n            const a = Math.exp(-undampedFreq * duration);\n            const b = (velocity - undampedFreq) * (duration * duration);\n            return a * b;\n        };\n    }\n    const initialGuess = 5 / duration;\n    const undampedFreq = approximateRoot(envelope, derivative, initialGuess);\n    duration = secondsToMilliseconds(duration);\n    if (isNaN(undampedFreq)) {\n        return {\n            stiffness: springDefaults.stiffness,\n            damping: springDefaults.damping,\n            duration,\n        };\n    }\n    else {\n        const stiffness = Math.pow(undampedFreq, 2) * mass;\n        return {\n            stiffness,\n            damping: dampingRatio * 2 * Math.sqrt(mass * stiffness),\n            duration,\n        };\n    }\n}\nconst rootIterations = 12;\nfunction approximateRoot(envelope, derivative, initialGuess) {\n    let result = initialGuess;\n    for (let i = 1; i < rootIterations; i++) {\n        result = result - envelope(result) / derivative(result);\n    }\n    return result;\n}\nfunction calcAngularFreq(undampedFreq, dampingRatio) {\n    return undampedFreq * Math.sqrt(1 - dampingRatio * dampingRatio);\n}\n\nexport { calcAngularFreq, findSpring };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,qBAAqB,EAAEC,KAAK,EAAEC,qBAAqB,QAAQ,cAAc;AAC3F,SAASC,cAAc,QAAQ,gBAAgB;AAE/C,MAAMC,OAAO,GAAG,KAAK;AACrB,SAASC,UAAUA,CAAAC,IAAA,EAA0I;EAAA,IAAzI;IAAEC,QAAQ,GAAGJ,cAAc,CAACI,QAAQ;IAAEC,MAAM,GAAGL,cAAc,CAACK,MAAM;IAAEC,QAAQ,GAAGN,cAAc,CAACM,QAAQ;IAAEC,IAAI,GAAGP,cAAc,CAACO;EAAM,CAAC,GAAAJ,IAAA;EACvJ,IAAIK,QAAQ;EACZ,IAAIC,UAAU;EACdb,OAAO,CAACQ,QAAQ,IAAIP,qBAAqB,CAACG,cAAc,CAACU,WAAW,CAAC,EAAE,4CAA4C,CAAC;EACpH,IAAIC,YAAY,GAAG,CAAC,GAAGN,MAAM;EAC7B;AACJ;AACA;EACIM,YAAY,GAAGb,KAAK,CAACE,cAAc,CAACY,UAAU,EAAEZ,cAAc,CAACa,UAAU,EAAEF,YAAY,CAAC;EACxFP,QAAQ,GAAGN,KAAK,CAACE,cAAc,CAACc,WAAW,EAAEd,cAAc,CAACU,WAAW,EAAEX,qBAAqB,CAACK,QAAQ,CAAC,CAAC;EACzG,IAAIO,YAAY,GAAG,CAAC,EAAE;IAClB;AACR;AACA;IACQH,QAAQ,GAAIO,YAAY,IAAK;MACzB,MAAMC,gBAAgB,GAAGD,YAAY,GAAGJ,YAAY;MACpD,MAAMM,KAAK,GAAGD,gBAAgB,GAAGZ,QAAQ;MACzC,MAAMc,CAAC,GAAGF,gBAAgB,GAAGV,QAAQ;MACrC,MAAMa,CAAC,GAAGC,eAAe,CAACL,YAAY,EAAEJ,YAAY,CAAC;MACrD,MAAMU,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAACN,KAAK,CAAC;MAC1B,OAAOhB,OAAO,GAAIiB,CAAC,GAAGC,CAAC,GAAIE,CAAC;IAChC,CAAC;IACDZ,UAAU,GAAIM,YAAY,IAAK;MAC3B,MAAMC,gBAAgB,GAAGD,YAAY,GAAGJ,YAAY;MACpD,MAAMM,KAAK,GAAGD,gBAAgB,GAAGZ,QAAQ;MACzC,MAAMoB,CAAC,GAAGP,KAAK,GAAGX,QAAQ,GAAGA,QAAQ;MACrC,MAAMmB,CAAC,GAAGH,IAAI,CAACI,GAAG,CAACf,YAAY,EAAE,CAAC,CAAC,GAAGW,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,GAAGX,QAAQ;MAC1E,MAAMuB,CAAC,GAAGL,IAAI,CAACC,GAAG,CAAC,CAACN,KAAK,CAAC;MAC1B,MAAMW,CAAC,GAAGR,eAAe,CAACE,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,EAAEJ,YAAY,CAAC;MAClE,MAAMkB,MAAM,GAAG,CAACrB,QAAQ,CAACO,YAAY,CAAC,GAAGd,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC7D,OAAQ4B,MAAM,IAAI,CAACL,CAAC,GAAGC,CAAC,IAAIE,CAAC,CAAC,GAAIC,CAAC;IACvC,CAAC;EACL,CAAC,MACI;IACD;AACR;AACA;IACQpB,QAAQ,GAAIO,YAAY,IAAK;MACzB,MAAMG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACR,YAAY,GAAGX,QAAQ,CAAC;MAC5C,MAAMe,CAAC,GAAG,CAACJ,YAAY,GAAGT,QAAQ,IAAIF,QAAQ,GAAG,CAAC;MAClD,OAAO,CAACH,OAAO,GAAGiB,CAAC,GAAGC,CAAC;IAC3B,CAAC;IACDV,UAAU,GAAIM,YAAY,IAAK;MAC3B,MAAMG,CAAC,GAAGI,IAAI,CAACC,GAAG,CAAC,CAACR,YAAY,GAAGX,QAAQ,CAAC;MAC5C,MAAMe,CAAC,GAAG,CAACb,QAAQ,GAAGS,YAAY,KAAKX,QAAQ,GAAGA,QAAQ,CAAC;MAC3D,OAAOc,CAAC,GAAGC,CAAC;IAChB,CAAC;EACL;EACA,MAAMW,YAAY,GAAG,CAAC,GAAG1B,QAAQ;EACjC,MAAMW,YAAY,GAAGgB,eAAe,CAACvB,QAAQ,EAAEC,UAAU,EAAEqB,YAAY,CAAC;EACxE1B,QAAQ,GAAGP,qBAAqB,CAACO,QAAQ,CAAC;EAC1C,IAAI4B,KAAK,CAACjB,YAAY,CAAC,EAAE;IACrB,OAAO;MACHkB,SAAS,EAAEjC,cAAc,CAACiC,SAAS;MACnCC,OAAO,EAAElC,cAAc,CAACkC,OAAO;MAC/B9B;IACJ,CAAC;EACL,CAAC,MACI;IACD,MAAM6B,SAAS,GAAGX,IAAI,CAACI,GAAG,CAACX,YAAY,EAAE,CAAC,CAAC,GAAGR,IAAI;IAClD,OAAO;MACH0B,SAAS;MACTC,OAAO,EAAEvB,YAAY,GAAG,CAAC,GAAGW,IAAI,CAACa,IAAI,CAAC5B,IAAI,GAAG0B,SAAS,CAAC;MACvD7B;IACJ,CAAC;EACL;AACJ;AACA,MAAMgC,cAAc,GAAG,EAAE;AACzB,SAASL,eAAeA,CAACvB,QAAQ,EAAEC,UAAU,EAAEqB,YAAY,EAAE;EACzD,IAAIO,MAAM,GAAGP,YAAY;EACzB,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,cAAc,EAAEE,CAAC,EAAE,EAAE;IACrCD,MAAM,GAAGA,MAAM,GAAG7B,QAAQ,CAAC6B,MAAM,CAAC,GAAG5B,UAAU,CAAC4B,MAAM,CAAC;EAC3D;EACA,OAAOA,MAAM;AACjB;AACA,SAASjB,eAAeA,CAACL,YAAY,EAAEJ,YAAY,EAAE;EACjD,OAAOI,YAAY,GAAGO,IAAI,CAACa,IAAI,CAAC,CAAC,GAAGxB,YAAY,GAAGA,YAAY,CAAC;AACpE;AAEA,SAASS,eAAe,EAAElB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}