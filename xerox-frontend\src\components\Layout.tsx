import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import ProfessionalNavbar from './ProfessionalNavbar';
import { useTheme } from '../contexts/ThemeContext';
import { useAuth } from '../contexts/AuthContext';
import '../styles/Layout.css';

interface LayoutProps {
  children: ReactNode;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({ children, className = '' }) => {
  const { isDarkMode, toggleTheme } = useTheme();
  const { user } = useAuth();
  const location = useLocation();



  const backgroundVariants = {
    light: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      transition: { duration: 0.5 }
    },
    dark: {
      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className={`layout-wrapper ${isDarkMode ? 'dark-mode' : 'light-mode'}`}>
      {/* Professional Navbar */}
      {user && (
        <ProfessionalNavbar 
          onThemeToggle={toggleTheme} 
          isDarkMode={isDarkMode} 
        />
      )}

      {/* Main Content Area */}
      <motion.main
        className={`main-content ${className}`}
        variants={backgroundVariants}
        animate={isDarkMode ? 'dark' : 'light'}
        style={{
          paddingTop: user ? '100px' : '0', // Account for fixed navbar
          minHeight: '100vh'
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 20, scale: 0.98 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.98 }}
            transition={{ duration: 0.4 }}
            className="page-content"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </motion.main>

      {/* Background Decorations */}
      <div className="background-decorations">
        <div className="decoration decoration-1"></div>
        <div className="decoration decoration-2"></div>
        <div className="decoration decoration-3"></div>
      </div>

      {/* Floating Action Button (Optional) */}
      {user && (
        <motion.div
          className="floating-action-btn"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1, duration: 0.3 }}
        >
          <button
            className="fab-btn"
            onClick={() => {
              // Quick action based on user type
              if (user.userType === 'Student') {
                window.location.href = '/upload';
              } else {
                window.location.href = '/job-queue';
              }
            }}
            title={user.userType === 'Student' ? 'Quick Upload' : 'View Jobs'}
          >
            {user.userType === 'Student' ? '📄' : '📋'}
          </button>
        </motion.div>
      )}

      {/* Theme Toggle Indicator */}
      {/* <AnimatePresence>
        {isDarkMode !== undefined && (
          <motion.div
            className="theme-indicator"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.3 }}
          >
            {isDarkMode ? '🌙' : '☀️'}
          </motion.div>
        )}
      </AnimatePresence> */}
    </div>
  );
};

export default Layout;
