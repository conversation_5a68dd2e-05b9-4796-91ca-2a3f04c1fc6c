using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class Rating
    {
        public int RatingID { get; set; }
        
        public int PrintJobID { get; set; }
        
        public int StudentID { get; set; }
        
        public int XeroxCenterID { get; set; }
        
        [Range(1, 5)]
        public int RatingValue { get; set; }
        
        [StringLength(1000)]
        public string? Review { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation properties
        public PrintJob PrintJob { get; set; } = null!;
        public Student Student { get; set; } = null!;
        public XeroxCenter XeroxCenter { get; set; } = null!;
        
        // Computed properties
        public string RatingStars
        {
            get
            {
                var stars = "";
                for (int i = 1; i <= 5; i++)
                {
                    stars += i <= RatingValue ? "★" : "☆";
                }
                return stars;
            }
        }
        
        public string RatingDescription => RatingValue switch
        {
            1 => "Poor",
            2 => "Fair",
            3 => "Good",
            4 => "Very Good",
            5 => "Excellent",
            _ => "Unknown"
        };
    }
}
