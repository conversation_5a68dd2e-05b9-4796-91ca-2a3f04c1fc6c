using XeroxModule.Core.Entities;

namespace XeroxModule.Core.Interfaces
{
    public interface INotificationService
    {
        Task<Notification> CreateNotificationAsync(int userId, string title, string message, string type, int? relatedEntityId = null, string? relatedEntityType = null);
        Task<IEnumerable<Notification>> GetUserNotificationsAsync(int userId, bool unreadOnly = false);
        Task<bool> MarkAsReadAsync(int notificationId);
        Task<bool> MarkAllAsReadAsync(int userId);
        Task<bool> DeleteNotificationAsync(int notificationId);
        Task<int> GetUnreadCountAsync(int userId);
        
        // Specific notification types
        Task NotifyJobStatusChangeAsync(int printJobId, string newStatus);
        Task NotifyNewMessageAsync(int messageId);
        Task NotifyJobAssignedAsync(int printJobId);
        Task NotifyJobCompletedAsync(int printJobId);
        Task NotifyPaymentRequiredAsync(int printJobId);
        
        // Real-time notifications
        Task SendRealTimeNotificationAsync(int userId, string title, string message, string type);
        Task SendRealTimeNotificationToGroupAsync(IEnumerable<int> userIds, string title, string message, string type);
    }
}
