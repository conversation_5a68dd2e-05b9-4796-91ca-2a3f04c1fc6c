{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nfunction createUseRender() {\n  let forwardMotionProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n  const useRender = (Component, props, ref, _ref, isStatic) => {\n    let {\n      latestValues\n    } = _ref;\n    const useVisualProps = isSVGComponent(Component) ? useSVGProps : useHTMLProps;\n    const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n    const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n    const elementProps = Component !== Fragment ? _objectSpread(_objectSpread(_objectSpread({}, filteredProps), visualProps), {}, {\n      ref\n    }) : {};\n    /**\n     * If component has been handed a motion value as its child,\n     * memoise its initial value and render that. Subsequent updates\n     * will be handled by the onChange handler\n     */\n    const {\n      children\n    } = props;\n    const renderedChildren = useMemo(() => isMotionValue(children) ? children.get() : children, [children]);\n    return createElement(Component, _objectSpread(_objectSpread({}, elementProps), {}, {\n      children: renderedChildren\n    }));\n  };\n  return useRender;\n}\nexport { createUseRender };", "map": {"version": 3, "names": ["isMotionValue", "Fragment", "useMemo", "createElement", "useHTMLProps", "useSVGProps", "filterProps", "isSVGComponent", "createUseRender", "forwardMotionProps", "arguments", "length", "undefined", "useRender", "Component", "props", "ref", "_ref", "isStatic", "latestValues", "useVisualProps", "visualProps", "filteredProps", "elementProps", "_objectSpread", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "get"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/render/dom/use-render.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { Fragment, useMemo, createElement } from 'react';\nimport { useHTMLProps } from '../html/use-props.mjs';\nimport { useSVGProps } from '../svg/use-props.mjs';\nimport { filterProps } from './utils/filter-props.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nfunction createUseRender(forwardMotionProps = false) {\n    const useRender = (Component, props, ref, { latestValues }, isStatic) => {\n        const useVisualProps = isSVGComponent(Component)\n            ? useSVGProps\n            : useHTMLProps;\n        const visualProps = useVisualProps(props, latestValues, isStatic, Component);\n        const filteredProps = filterProps(props, typeof Component === \"string\", forwardMotionProps);\n        const elementProps = Component !== Fragment\n            ? { ...filteredProps, ...visualProps, ref }\n            : {};\n        /**\n         * If component has been handed a motion value as its child,\n         * memoise its initial value and render that. Subsequent updates\n         * will be handled by the onChange handler\n         */\n        const { children } = props;\n        const renderedChildren = useMemo(() => (isMotionValue(children) ? children.get() : children), [children]);\n        return createElement(Component, {\n            ...elementProps,\n            children: renderedChildren,\n        });\n    };\n    return useRender;\n}\n\nexport { createUseRender };\n"], "mappings": ";AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,QAAQ,OAAO;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,SAASC,eAAeA,CAAA,EAA6B;EAAA,IAA5BC,kBAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC/C,MAAMG,SAAS,GAAGA,CAACC,SAAS,EAAEC,KAAK,EAAEC,GAAG,EAAAC,IAAA,EAAoBC,QAAQ,KAAK;IAAA,IAA/B;MAAEC;IAAa,CAAC,GAAAF,IAAA;IACtD,MAAMG,cAAc,GAAGb,cAAc,CAACO,SAAS,CAAC,GAC1CT,WAAW,GACXD,YAAY;IAClB,MAAMiB,WAAW,GAAGD,cAAc,CAACL,KAAK,EAAEI,YAAY,EAAED,QAAQ,EAAEJ,SAAS,CAAC;IAC5E,MAAMQ,aAAa,GAAGhB,WAAW,CAACS,KAAK,EAAE,OAAOD,SAAS,KAAK,QAAQ,EAAEL,kBAAkB,CAAC;IAC3F,MAAMc,YAAY,GAAGT,SAAS,KAAKb,QAAQ,GAAAuB,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAChCF,aAAa,GAAKD,WAAW;MAAEL;IAAG,KACvC,CAAC,CAAC;IACR;AACR;AACA;AACA;AACA;IACQ,MAAM;MAAES;IAAS,CAAC,GAAGV,KAAK;IAC1B,MAAMW,gBAAgB,GAAGxB,OAAO,CAAC,MAAOF,aAAa,CAACyB,QAAQ,CAAC,GAAGA,QAAQ,CAACE,GAAG,CAAC,CAAC,GAAGF,QAAS,EAAE,CAACA,QAAQ,CAAC,CAAC;IACzG,OAAOtB,aAAa,CAACW,SAAS,EAAAU,aAAA,CAAAA,aAAA,KACvBD,YAAY;MACfE,QAAQ,EAAEC;IAAgB,EAC7B,CAAC;EACN,CAAC;EACD,OAAOb,SAAS;AACpB;AAEA,SAASL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}