{"ast": null, "code": "import { isMotionValue } from 'motion-dom';\nimport { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nfunction copyRawValuesOnly(target, source, props) {\n  for (const key in source) {\n    if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n      target[key] = source[key];\n    }\n  }\n}\nfunction useInitialMotionValues(_ref, visualState) {\n  let {\n    transformTemplate\n  } = _ref;\n  return useMemo(() => {\n    const state = createHtmlRenderState();\n    buildHTMLStyles(state, visualState, transformTemplate);\n    return Object.assign({}, state.vars, state.style);\n  }, [visualState]);\n}\nfunction useStyle(props, visualState) {\n  const styleProp = props.style || {};\n  const style = {};\n  /**\n   * Copy non-Motion Values straight into style\n   */\n  copyRawValuesOnly(style, styleProp, props);\n  Object.assign(style, useInitialMotionValues(props, visualState));\n  return style;\n}\nfunction useHTMLProps(props, visualState) {\n  // The `any` isn't ideal but it is the type of createElement props argument\n  const htmlProps = {};\n  const style = useStyle(props, visualState);\n  if (props.drag && props.dragListener !== false) {\n    // Disable the ghost element when a user drags\n    htmlProps.draggable = false;\n    // Disable text selection\n    style.userSelect = style.WebkitUserSelect = style.WebkitTouchCallout = \"none\";\n    // Disable scrolling on the draggable direction\n    style.touchAction = props.drag === true ? \"none\" : \"pan-\".concat(props.drag === \"x\" ? \"y\" : \"x\");\n  }\n  if (props.tabIndex === undefined && (props.onTap || props.onTapStart || props.whileTap)) {\n    htmlProps.tabIndex = 0;\n  }\n  htmlProps.style = style;\n  return htmlProps;\n}\nexport { copyRawValuesOnly, useHTMLProps };", "map": {"version": 3, "names": ["isMotionValue", "useMemo", "isForcedMotionValue", "buildHTMLStyles", "createHtmlRenderState", "copyRawValuesOnly", "target", "source", "props", "key", "useInitialMotionValues", "_ref", "visualState", "transformTemplate", "state", "Object", "assign", "vars", "style", "useStyle", "styleProp", "useHTMLProps", "htmlProps", "drag", "dragListener", "draggable", "userSelect", "WebkitUserSelect", "WebkitTouchCallout", "touchAction", "concat", "tabIndex", "undefined", "onTap", "onTapStart", "whileTap"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/render/html/use-props.mjs"], "sourcesContent": ["import { isMotionValue } from 'motion-dom';\nimport { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState) {\n    return useMemo(() => {\n        const state = createHtmlRenderState();\n        buildHTMLStyles(state, visualState, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState));\n    return style;\n}\nfunction useHTMLProps(props, visualState) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\nexport { copyRawValuesOnly, useHTMLProps };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAY;AAC1C,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,qBAAqB,QAAQ,iCAAiC;AAEvE,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC9C,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACtB,IAAI,CAACP,aAAa,CAACO,MAAM,CAACE,GAAG,CAAC,CAAC,IAAI,CAACP,mBAAmB,CAACO,GAAG,EAAED,KAAK,CAAC,EAAE;MACjEF,MAAM,CAACG,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC7B;EACJ;AACJ;AACA,SAASC,sBAAsBA,CAAAC,IAAA,EAAwBC,WAAW,EAAE;EAAA,IAApC;IAAEC;EAAkB,CAAC,GAAAF,IAAA;EACjD,OAAOV,OAAO,CAAC,MAAM;IACjB,MAAMa,KAAK,GAAGV,qBAAqB,CAAC,CAAC;IACrCD,eAAe,CAACW,KAAK,EAAEF,WAAW,EAAEC,iBAAiB,CAAC;IACtD,OAAOE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACG,IAAI,EAAEH,KAAK,CAACI,KAAK,CAAC;EACrD,CAAC,EAAE,CAACN,WAAW,CAAC,CAAC;AACrB;AACA,SAASO,QAAQA,CAACX,KAAK,EAAEI,WAAW,EAAE;EAClC,MAAMQ,SAAS,GAAGZ,KAAK,CAACU,KAAK,IAAI,CAAC,CAAC;EACnC,MAAMA,KAAK,GAAG,CAAC,CAAC;EAChB;AACJ;AACA;EACIb,iBAAiB,CAACa,KAAK,EAAEE,SAAS,EAAEZ,KAAK,CAAC;EAC1CO,MAAM,CAACC,MAAM,CAACE,KAAK,EAAER,sBAAsB,CAACF,KAAK,EAAEI,WAAW,CAAC,CAAC;EAChE,OAAOM,KAAK;AAChB;AACA,SAASG,YAAYA,CAACb,KAAK,EAAEI,WAAW,EAAE;EACtC;EACA,MAAMU,SAAS,GAAG,CAAC,CAAC;EACpB,MAAMJ,KAAK,GAAGC,QAAQ,CAACX,KAAK,EAAEI,WAAW,CAAC;EAC1C,IAAIJ,KAAK,CAACe,IAAI,IAAIf,KAAK,CAACgB,YAAY,KAAK,KAAK,EAAE;IAC5C;IACAF,SAAS,CAACG,SAAS,GAAG,KAAK;IAC3B;IACAP,KAAK,CAACQ,UAAU,GACZR,KAAK,CAACS,gBAAgB,GAClBT,KAAK,CAACU,kBAAkB,GACpB,MAAM;IAClB;IACAV,KAAK,CAACW,WAAW,GACbrB,KAAK,CAACe,IAAI,KAAK,IAAI,GACb,MAAM,UAAAO,MAAA,CACCtB,KAAK,CAACe,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE;EACrD;EACA,IAAIf,KAAK,CAACuB,QAAQ,KAAKC,SAAS,KAC3BxB,KAAK,CAACyB,KAAK,IAAIzB,KAAK,CAAC0B,UAAU,IAAI1B,KAAK,CAAC2B,QAAQ,CAAC,EAAE;IACrDb,SAAS,CAACS,QAAQ,GAAG,CAAC;EAC1B;EACAT,SAAS,CAACJ,KAAK,GAAGA,KAAK;EACvB,OAAOI,SAAS;AACpB;AAEA,SAASjB,iBAAiB,EAAEgB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}