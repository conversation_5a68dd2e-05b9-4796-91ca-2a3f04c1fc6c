{"ast": null, "code": "import { mixNumber, percent, px } from 'motion-dom';\nimport { progress, circOut, noop } from 'motion-utils';\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = value => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = value => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n  if (shouldCrossfadeOpacity) {\n    var _lead$opacity, _follow$opacity;\n    target.opacity = mixNumber(0, (_lead$opacity = lead.opacity) !== null && _lead$opacity !== void 0 ? _lead$opacity : 1, easeCrossfadeIn(progress));\n    target.opacityExit = mixNumber((_follow$opacity = follow.opacity) !== null && _follow$opacity !== void 0 ? _follow$opacity : 1, 0, easeCrossfadeOut(progress));\n  } else if (isOnlyMember) {\n    var _follow$opacity2, _lead$opacity2;\n    target.opacity = mixNumber((_follow$opacity2 = follow.opacity) !== null && _follow$opacity2 !== void 0 ? _follow$opacity2 : 1, (_lead$opacity2 = lead.opacity) !== null && _lead$opacity2 !== void 0 ? _lead$opacity2 : 1, progress);\n  }\n  /**\n   * Mix border radius\n   */\n  for (let i = 0; i < numBorders; i++) {\n    const borderLabel = \"border\".concat(borders[i], \"Radius\");\n    let followRadius = getRadius(follow, borderLabel);\n    let leadRadius = getRadius(lead, borderLabel);\n    if (followRadius === undefined && leadRadius === undefined) continue;\n    followRadius || (followRadius = 0);\n    leadRadius || (leadRadius = 0);\n    const canMix = followRadius === 0 || leadRadius === 0 || isPx(followRadius) === isPx(leadRadius);\n    if (canMix) {\n      target[borderLabel] = Math.max(mixNumber(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n      if (percent.test(leadRadius) || percent.test(followRadius)) {\n        target[borderLabel] += \"%\";\n      }\n    } else {\n      target[borderLabel] = leadRadius;\n    }\n  }\n  /**\n   * Mix rotation\n   */\n  if (follow.rotate || lead.rotate) {\n    target.rotate = mixNumber(follow.rotate || 0, lead.rotate || 0, progress);\n  }\n}\nfunction getRadius(values, radiusName) {\n  return values[radiusName] !== undefined ? values[radiusName] : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/compress(0, 0.5, circOut);\nconst easeCrossfadeOut = /*@__PURE__*/compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n  return p => {\n    // Could replace ifs with clamp\n    if (p < min) return 0;\n    if (p > max) return 1;\n    return easing(progress(min, max, p));\n  };\n}\nexport { mixValues };", "map": {"version": 3, "names": ["mixNumber", "percent", "px", "progress", "circOut", "noop", "borders", "numBorders", "length", "asNumber", "value", "parseFloat", "isPx", "test", "mixValues", "target", "follow", "lead", "shouldCrossfadeOpacity", "isOnlyMember", "_lead$opacity", "_follow$opacity", "opacity", "easeCrossfadeIn", "opacityExit", "easeCrossfadeOut", "_follow$opacity2", "_lead$opacity2", "i", "borderLabel", "concat", "followRadius", "getRadius", "leadRadius", "undefined", "canMix", "Math", "max", "rotate", "values", "radiusName", "borderRadius", "compress", "min", "easing", "p"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs"], "sourcesContent": ["import { mixNumber, percent, px } from 'motion-dom';\nimport { progress, circOut, noop } from 'motion-utils';\n\nconst borders = [\"TopLeft\", \"TopRight\", \"BottomLeft\", \"BottomRight\"];\nconst numBorders = borders.length;\nconst asNumber = (value) => typeof value === \"string\" ? parseFloat(value) : value;\nconst isPx = (value) => typeof value === \"number\" || px.test(value);\nfunction mixValues(target, follow, lead, progress, shouldCrossfadeOpacity, isOnlyMember) {\n    if (shouldCrossfadeOpacity) {\n        target.opacity = mixNumber(0, lead.opacity ?? 1, easeCrossfadeIn(progress));\n        target.opacityExit = mixNumber(follow.opacity ?? 1, 0, easeCrossfadeOut(progress));\n    }\n    else if (isOnlyMember) {\n        target.opacity = mixNumber(follow.opacity ?? 1, lead.opacity ?? 1, progress);\n    }\n    /**\n     * Mix border radius\n     */\n    for (let i = 0; i < numBorders; i++) {\n        const borderLabel = `border${borders[i]}Radius`;\n        let followRadius = getRadius(follow, borderLabel);\n        let leadRadius = getRadius(lead, borderLabel);\n        if (followRadius === undefined && leadRadius === undefined)\n            continue;\n        followRadius || (followRadius = 0);\n        leadRadius || (leadRadius = 0);\n        const canMix = followRadius === 0 ||\n            leadRadius === 0 ||\n            isPx(followRadius) === isPx(leadRadius);\n        if (canMix) {\n            target[borderLabel] = Math.max(mixNumber(asNumber(followRadius), asNumber(leadRadius), progress), 0);\n            if (percent.test(leadRadius) || percent.test(followRadius)) {\n                target[borderLabel] += \"%\";\n            }\n        }\n        else {\n            target[borderLabel] = leadRadius;\n        }\n    }\n    /**\n     * Mix rotation\n     */\n    if (follow.rotate || lead.rotate) {\n        target.rotate = mixNumber(follow.rotate || 0, lead.rotate || 0, progress);\n    }\n}\nfunction getRadius(values, radiusName) {\n    return values[radiusName] !== undefined\n        ? values[radiusName]\n        : values.borderRadius;\n}\n// /**\n//  * We only want to mix the background color if there's a follow element\n//  * that we're not crossfading opacity between. For instance with switch\n//  * AnimateSharedLayout animations, this helps the illusion of a continuous\n//  * element being animated but also cuts down on the number of paints triggered\n//  * for elements where opacity is doing that work for us.\n//  */\n// if (\n//     !hasFollowElement &&\n//     latestLeadValues.backgroundColor &&\n//     latestFollowValues.backgroundColor\n// ) {\n//     /**\n//      * This isn't ideal performance-wise as mixColor is creating a new function every frame.\n//      * We could probably create a mixer that runs at the start of the animation but\n//      * the idea behind the crossfader is that it runs dynamically between two potentially\n//      * changing targets (ie opacity or borderRadius may be animating independently via variants)\n//      */\n//     leadState.backgroundColor = followState.backgroundColor = mixColor(\n//         latestFollowValues.backgroundColor as string,\n//         latestLeadValues.backgroundColor as string\n//     )(p)\n// }\nconst easeCrossfadeIn = /*@__PURE__*/ compress(0, 0.5, circOut);\nconst easeCrossfadeOut = /*@__PURE__*/ compress(0.5, 0.95, noop);\nfunction compress(min, max, easing) {\n    return (p) => {\n        // Could replace ifs with clamp\n        if (p < min)\n            return 0;\n        if (p > max)\n            return 1;\n        return easing(progress(min, max, p));\n    };\n}\n\nexport { mixValues };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,OAAO,EAAEC,EAAE,QAAQ,YAAY;AACnD,SAASC,QAAQ,EAAEC,OAAO,EAAEC,IAAI,QAAQ,cAAc;AAEtD,MAAMC,OAAO,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,CAAC;AACpE,MAAMC,UAAU,GAAGD,OAAO,CAACE,MAAM;AACjC,MAAMC,QAAQ,GAAIC,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,GAAGC,UAAU,CAACD,KAAK,CAAC,GAAGA,KAAK;AACjF,MAAME,IAAI,GAAIF,KAAK,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIR,EAAE,CAACW,IAAI,CAACH,KAAK,CAAC;AACnE,SAASI,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEd,QAAQ,EAAEe,sBAAsB,EAAEC,YAAY,EAAE;EACrF,IAAID,sBAAsB,EAAE;IAAA,IAAAE,aAAA,EAAAC,eAAA;IACxBN,MAAM,CAACO,OAAO,GAAGtB,SAAS,CAAC,CAAC,GAAAoB,aAAA,GAAEH,IAAI,CAACK,OAAO,cAAAF,aAAA,cAAAA,aAAA,GAAI,CAAC,EAAEG,eAAe,CAACpB,QAAQ,CAAC,CAAC;IAC3EY,MAAM,CAACS,WAAW,GAAGxB,SAAS,EAAAqB,eAAA,GAACL,MAAM,CAACM,OAAO,cAAAD,eAAA,cAAAA,eAAA,GAAI,CAAC,EAAE,CAAC,EAAEI,gBAAgB,CAACtB,QAAQ,CAAC,CAAC;EACtF,CAAC,MACI,IAAIgB,YAAY,EAAE;IAAA,IAAAO,gBAAA,EAAAC,cAAA;IACnBZ,MAAM,CAACO,OAAO,GAAGtB,SAAS,EAAA0B,gBAAA,GAACV,MAAM,CAACM,OAAO,cAAAI,gBAAA,cAAAA,gBAAA,GAAI,CAAC,GAAAC,cAAA,GAAEV,IAAI,CAACK,OAAO,cAAAK,cAAA,cAAAA,cAAA,GAAI,CAAC,EAAExB,QAAQ,CAAC;EAChF;EACA;AACJ;AACA;EACI,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,UAAU,EAAEqB,CAAC,EAAE,EAAE;IACjC,MAAMC,WAAW,YAAAC,MAAA,CAAYxB,OAAO,CAACsB,CAAC,CAAC,WAAQ;IAC/C,IAAIG,YAAY,GAAGC,SAAS,CAAChB,MAAM,EAAEa,WAAW,CAAC;IACjD,IAAII,UAAU,GAAGD,SAAS,CAACf,IAAI,EAAEY,WAAW,CAAC;IAC7C,IAAIE,YAAY,KAAKG,SAAS,IAAID,UAAU,KAAKC,SAAS,EACtD;IACJH,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC;IAClCE,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC;IAC9B,MAAME,MAAM,GAAGJ,YAAY,KAAK,CAAC,IAC7BE,UAAU,KAAK,CAAC,IAChBrB,IAAI,CAACmB,YAAY,CAAC,KAAKnB,IAAI,CAACqB,UAAU,CAAC;IAC3C,IAAIE,MAAM,EAAE;MACRpB,MAAM,CAACc,WAAW,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACrC,SAAS,CAACS,QAAQ,CAACsB,YAAY,CAAC,EAAEtB,QAAQ,CAACwB,UAAU,CAAC,EAAE9B,QAAQ,CAAC,EAAE,CAAC,CAAC;MACpG,IAAIF,OAAO,CAACY,IAAI,CAACoB,UAAU,CAAC,IAAIhC,OAAO,CAACY,IAAI,CAACkB,YAAY,CAAC,EAAE;QACxDhB,MAAM,CAACc,WAAW,CAAC,IAAI,GAAG;MAC9B;IACJ,CAAC,MACI;MACDd,MAAM,CAACc,WAAW,CAAC,GAAGI,UAAU;IACpC;EACJ;EACA;AACJ;AACA;EACI,IAAIjB,MAAM,CAACsB,MAAM,IAAIrB,IAAI,CAACqB,MAAM,EAAE;IAC9BvB,MAAM,CAACuB,MAAM,GAAGtC,SAAS,CAACgB,MAAM,CAACsB,MAAM,IAAI,CAAC,EAAErB,IAAI,CAACqB,MAAM,IAAI,CAAC,EAAEnC,QAAQ,CAAC;EAC7E;AACJ;AACA,SAAS6B,SAASA,CAACO,MAAM,EAAEC,UAAU,EAAE;EACnC,OAAOD,MAAM,CAACC,UAAU,CAAC,KAAKN,SAAS,GACjCK,MAAM,CAACC,UAAU,CAAC,GAClBD,MAAM,CAACE,YAAY;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMlB,eAAe,GAAG,aAAcmB,QAAQ,CAAC,CAAC,EAAE,GAAG,EAAEtC,OAAO,CAAC;AAC/D,MAAMqB,gBAAgB,GAAG,aAAciB,QAAQ,CAAC,GAAG,EAAE,IAAI,EAAErC,IAAI,CAAC;AAChE,SAASqC,QAAQA,CAACC,GAAG,EAAEN,GAAG,EAAEO,MAAM,EAAE;EAChC,OAAQC,CAAC,IAAK;IACV;IACA,IAAIA,CAAC,GAAGF,GAAG,EACP,OAAO,CAAC;IACZ,IAAIE,CAAC,GAAGR,GAAG,EACP,OAAO,CAAC;IACZ,OAAOO,MAAM,CAACzC,QAAQ,CAACwC,GAAG,EAAEN,GAAG,EAAEQ,CAAC,CAAC,CAAC;EACxC,CAAC;AACL;AAEA,SAAS/B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}