{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadColor = pendingJobs => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tachometer-alt me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), \"Student Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: \"auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => setShowUploadModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), \"Upload Files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: printJobs.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: [\"$\", printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-list me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 17\n              }, this), \"Recent Print Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Job #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"File Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 208,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Xerox Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 211,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: printJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 27\n                    }, this), job.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(job.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.xeroxCenterName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-eye\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 234,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this)]\n                }, job.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this), \"No print jobs yet. Upload your first file to get started!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 17\n              }, this), \"Available Centers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(Card, {\n              className: \"mb-3 border-0 bg-light\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: center.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getWorkloadColor(center.pendingJobs),\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-map-marker-alt me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this), center.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star text-warning me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 276,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"small\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this)\n            }, center.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), \"Upload Files for Printing\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n              onChange: e => {\n                const files = e.target.files;\n                setSelectedFile(files ? files[0] : null);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Print Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.printType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    printType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Print\",\n                    children: \"Print\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Xerox\",\n                    children: \"Xerox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 325,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Binding\",\n                    children: \"Binding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 326,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Lamination\",\n                    children: \"Lamination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 327,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Number of Copies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  min: \"1\",\n                  value: uploadData.copies,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    copies: parseInt(e.target.value)\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Color Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.colorType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    colorType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BlackWhite\",\n                    children: \"Black & White\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Color\",\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Paper Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.paperSize,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    paperSize: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A4\",\n                    children: \"A4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 364,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A3\",\n                    children: \"A3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Letter\",\n                    children: \"Letter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Legal\",\n                    children: \"Legal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Preferred Xerox Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: uploadData.preferredXeroxCenterId,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                preferredXeroxCenterId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a center (optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: center.id,\n                children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              placeholder: \"Any special instructions or remarks...\",\n              value: uploadData.remarks,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                remarks: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowUploadModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          disabled: !selectedFile,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 13\n          }, this), \"Upload File\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"7ojXxjqihYdoyrPmwIm9juuCuWc=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "selectedFile", "setSelectedFile", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadColor", "pendingJobs", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "fluid", "username", "xs", "onClick", "md", "Body", "length", "filter", "job", "includes", "reduce", "sum", "cost", "toFixed", "lg", "Header", "responsive", "hover", "map", "jobNumber", "xeroxCenterName", "size", "id", "center", "name", "location", "averageRating", "show", "onHide", "closeButton", "Title", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "Select", "value", "prev", "min", "parseInt", "as", "rows", "placeholder", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Contain<PERSON>, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getWorkloadColor = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-tachometer-alt me-2\"></i>\n            Student Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n        <Col xs=\"auto\">\n          <Button variant=\"primary\" onClick={() => setShowUploadModal(true)}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files\n          </Button>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{printJobs.length}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-warning\">\n                {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-check-circle fa-2x text-success mb-2\"></i>\n              <h5>Completed</h5>\n              <h3 className=\"text-success\">\n                {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-info mb-2\"></i>\n              <h5>Total Spent</h5>\n              <h3 className=\"text-info\">\n                ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        {/* Recent Jobs */}\n        <Col lg={8}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-list me-2\"></i>\n                Recent Print Jobs\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {printJobs.length > 0 ? (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Job #</th>\n                      <th>File Name</th>\n                      <th>Status</th>\n                      <th>Cost</th>\n                      <th>Xerox Center</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {printJobs.map(job => (\n                      <tr key={job.id}>\n                        <td>\n                          <strong>{job.jobNumber}</strong>\n                        </td>\n                        <td>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          {job.fileName}\n                        </td>\n                        <td>{getStatusBadge(job.status)}</td>\n                        <td>\n                          {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                        </td>\n                        <td>{job.xeroxCenterName}</td>\n                        <td>\n                          <Button variant=\"outline-primary\" size=\"sm\" className=\"me-1\">\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                          <Button variant=\"outline-secondary\" size=\"sm\">\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              ) : (\n                <Alert variant=\"info\">\n                  <i className=\"fas fa-info-circle me-2\"></i>\n                  No print jobs yet. Upload your first file to get started!\n                </Alert>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n\n        {/* Xerox Centers */}\n        <Col lg={4}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-store me-2\"></i>\n                Available Centers\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {xeroxCenters.map(center => (\n                <Card key={center.id} className=\"mb-3 border-0 bg-light\">\n                  <Card.Body className=\"p-3\">\n                    <div className=\"d-flex justify-content-between align-items-start mb-2\">\n                      <h6 className=\"mb-1\">{center.name}</h6>\n                      <Badge bg={getWorkloadColor(center.pendingJobs)}>\n                        {center.pendingJobs} jobs\n                      </Badge>\n                    </div>\n                    <p className=\"text-muted small mb-2\">\n                      <i className=\"fas fa-map-marker-alt me-1\"></i>\n                      {center.location}\n                    </p>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      <div>\n                        <i className=\"fas fa-star text-warning me-1\"></i>\n                        <span className=\"small\">{center.averageRating.toFixed(1)}</span>\n                      </div>\n                      <Button variant=\"outline-primary\" size=\"sm\">\n                        Select\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              ))}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Upload Modal */}\n      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files for Printing\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select File</Form.Label>\n              <Form.Control\n                type=\"file\"\n                accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                onChange={(e) => {\n                  const files = (e.target as HTMLInputElement).files;\n                  setSelectedFile(files ? files[0] : null);\n                }}\n              />\n              <Form.Text className=\"text-muted\">\n                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n              </Form.Text>\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Print Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.printType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                  >\n                    <option value=\"Print\">Print</option>\n                    <option value=\"Xerox\">Xerox</option>\n                    <option value=\"Binding\">Binding</option>\n                    <option value=\"Lamination\">Lamination</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Number of Copies</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    min=\"1\"\n                    value={uploadData.copies}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Color Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.colorType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                  >\n                    <option value=\"BlackWhite\">Black & White</option>\n                    <option value=\"Color\">Color</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Paper Size</Form.Label>\n                  <Form.Select\n                    value={uploadData.paperSize}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"A3\">A3</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Preferred Xerox Center</Form.Label>\n              <Form.Select\n                value={uploadData.preferredXeroxCenterId}\n                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              >\n                <option value=\"\">Select a center (optional)</option>\n                {xeroxCenters.map(center => (\n                  <option key={center.id} value={center.id}>\n                    {center.name} - {center.pendingJobs} pending jobs\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Remarks</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                placeholder=\"Any special instructions or remarks...\"\n                value={uploadData.remarks}\n                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleFileUpload} disabled={!selectedFile}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload File\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACrG,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB7E,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACyB,eAAe,EAAEC,kBAAkB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAG9B,QAAQ,CAAC;IAC3C+B,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEFnC,SAAS,CAAC,MAAM;IACd,MAAMoC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMzB,WAAW,CAAC0B,cAAc,CAAC,CAAC;QAC5DjB,YAAY,CAACgB,iBAAiB,CAACE,IAAI,CAAC;;QAEpC;QACA,MAAMC,oBAAoB,GAAG,MAAM3B,cAAc,CAAC4B,MAAM,CAAC,CAAC;QAC1DlB,eAAe,CAACiB,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACArB,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDa,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACEhC,OAAA,CAACT,KAAK;MAAC2C,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxBnC,OAAA;QAAGoC,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CX,MAAM;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACjC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMkC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEpC,YAAY,CAAC;MACrCkC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAElC,UAAU,CAACE,OAAO,CAAC;MAC9C8B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAElC,UAAU,CAACG,sBAAsB,CAAC;MAC5E6B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACI,SAAS,CAAC;MAClD4B,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAElC,UAAU,CAACK,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACM,SAAS,CAAC;MAClD0B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMrB,aAAa,CAACkD,UAAU,CAACJ,QAAQ,CAAC;;MAExC;MACA,MAAMvB,iBAAiB,GAAG,MAAMzB,WAAW,CAAC0B,cAAc,CAAC,CAAC;MAC5DjB,YAAY,CAACgB,iBAAiB,CAACE,IAAI,CAAC;MAEpCd,kBAAkB,CAAC,KAAK,CAAC;MACzBE,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF;EACF,CAAC;EAED,oBACE1B,OAAA,CAACf,SAAS;IAACgE,KAAK;IAAAd,QAAA,gBACdnC,OAAA,CAACd,GAAG;MAACkD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBnC,OAAA,CAACb,GAAG;QAAAgD,QAAA,gBACFnC,OAAA;UAAAmC,QAAA,gBACEnC,OAAA;YAAGoC,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,qBAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLxC,OAAA;UAAGoC,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAAChC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,QAAQ,EAAC,GAAC;QAAA;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNxC,OAAA,CAACb,GAAG;QAACgE,EAAE,EAAC,MAAM;QAAAhB,QAAA,eACZnC,OAAA,CAACX,MAAM;UAAC0C,OAAO,EAAC,SAAS;UAACqB,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,IAAI,CAAE;UAAA0B,QAAA,gBAChEnC,OAAA;YAAGoC,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxC,OAAA,CAACd,GAAG;MAACkD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBnC,OAAA,CAACb,GAAG;QAACkE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAACgD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BnC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,gBACRnC,OAAA;cAAGoC,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DxC,OAAA;cAAAmC,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBxC,OAAA;cAAIoC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAE/B,SAAS,CAACmD;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxC,OAAA,CAACb,GAAG;QAACkE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAACgD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BnC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,gBACRnC,OAAA;cAAGoC,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDxC,OAAA;cAAAmC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBxC,OAAA;cAAIoC,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzB/B,SAAS,CAACoD,MAAM,CAACC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAAC0B;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxC,OAAA,CAACb,GAAG;QAACkE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAACgD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BnC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,gBACRnC,OAAA;cAAGoC,SAAS,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DxC,OAAA;cAAAmC,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBxC,OAAA;cAAIoC,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzB/B,SAAS,CAACoD,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAAC0B;YAAM;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNxC,OAAA,CAACb,GAAG;QAACkE,EAAE,EAAE,CAAE;QAAAlB,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAACgD,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3BnC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,gBACRnC,OAAA;cAAGoC,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DxC,OAAA;cAAAmC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBxC,OAAA;cAAIoC,SAAS,EAAC,WAAW;cAAAD,QAAA,GAAC,GACvB,EAAC/B,SAAS,CAACuD,MAAM,CAAC,CAACC,GAAG,EAAEH,GAAG,KAAKG,GAAG,IAAIH,GAAG,CAACI,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxC,OAAA,CAACd,GAAG;MAAAiD,QAAA,gBAEFnC,OAAA,CAACb,GAAG;QAAC4E,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAAA+C,QAAA,gBACHnC,OAAA,CAACZ,IAAI,CAAC4E,MAAM;YAAA7B,QAAA,eACVnC,OAAA;cAAIoC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClBnC,OAAA;gBAAGoC,SAAS,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,EACP/B,SAAS,CAACmD,MAAM,GAAG,CAAC,gBACnBvD,OAAA,CAACV,KAAK;cAAC2E,UAAU;cAACC,KAAK;cAAA/B,QAAA,gBACrBnC,OAAA;gBAAAmC,QAAA,eACEnC,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAAmC,QAAA,EAAI;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdxC,OAAA;oBAAAmC,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBxC,OAAA;oBAAAmC,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACfxC,OAAA;oBAAAmC,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbxC,OAAA;oBAAAmC,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBxC,OAAA;oBAAAmC,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRxC,OAAA;gBAAAmC,QAAA,EACG/B,SAAS,CAAC+D,GAAG,CAACV,GAAG,iBAChBzD,OAAA;kBAAAmC,QAAA,gBACEnC,OAAA;oBAAAmC,QAAA,eACEnC,OAAA;sBAAAmC,QAAA,EAASsB,GAAG,CAACW;oBAAS;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLxC,OAAA;oBAAAmC,QAAA,gBACEnC,OAAA;sBAAGoC,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDiB,GAAG,CAACpB,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLxC,OAAA;oBAAAmC,QAAA,EAAKP,cAAc,CAAC6B,GAAG,CAAC5B,MAAM;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrCxC,OAAA;oBAAAmC,QAAA,EACGsB,GAAG,CAACI,IAAI,GAAG,IAAIJ,GAAG,CAACI,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;kBAAG;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACLxC,OAAA;oBAAAmC,QAAA,EAAKsB,GAAG,CAACY;kBAAe;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BxC,OAAA;oBAAAmC,QAAA,gBACEnC,OAAA,CAACX,MAAM;sBAAC0C,OAAO,EAAC,iBAAiB;sBAACuC,IAAI,EAAC,IAAI;sBAAClC,SAAS,EAAC,MAAM;sBAAAD,QAAA,eAC1DnC,OAAA;wBAAGoC,SAAS,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACTxC,OAAA,CAACX,MAAM;sBAAC0C,OAAO,EAAC,mBAAmB;sBAACuC,IAAI,EAAC,IAAI;sBAAAnC,QAAA,eAC3CnC,OAAA;wBAAGoC,SAAS,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GApBEiB,GAAG,CAACc,EAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBX,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAERxC,OAAA,CAACR,KAAK;cAACuC,OAAO,EAAC,MAAM;cAAAI,QAAA,gBACnBnC,OAAA;gBAAGoC,SAAS,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6DAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNxC,OAAA,CAACb,GAAG;QAAC4E,EAAE,EAAE,CAAE;QAAA5B,QAAA,eACTnC,OAAA,CAACZ,IAAI;UAAA+C,QAAA,gBACHnC,OAAA,CAACZ,IAAI,CAAC4E,MAAM;YAAA7B,QAAA,eACVnC,OAAA;cAAIoC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClBnC,OAAA;gBAAGoC,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdxC,OAAA,CAACZ,IAAI,CAACkE,IAAI;YAAAnB,QAAA,EACP7B,YAAY,CAAC6D,GAAG,CAACK,MAAM,iBACtBxE,OAAA,CAACZ,IAAI;cAAiBgD,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACtDnC,OAAA,CAACZ,IAAI,CAACkE,IAAI;gBAAClB,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBACxBnC,OAAA;kBAAKoC,SAAS,EAAC,uDAAuD;kBAAAD,QAAA,gBACpEnC,OAAA;oBAAIoC,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEqC,MAAM,CAACC;kBAAI;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvCxC,OAAA,CAACT,KAAK;oBAAC2C,EAAE,EAAEO,gBAAgB,CAAC+B,MAAM,CAAC9B,WAAW,CAAE;oBAAAP,QAAA,GAC7CqC,MAAM,CAAC9B,WAAW,EAAC,OACtB;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNxC,OAAA;kBAAGoC,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAClCnC,OAAA;oBAAGoC,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC7CgC,MAAM,CAACE,QAAQ;gBAAA;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJxC,OAAA;kBAAKoC,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,gBAChEnC,OAAA;oBAAAmC,QAAA,gBACEnC,OAAA;sBAAGoC,SAAS,EAAC;oBAA+B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDxC,OAAA;sBAAMoC,SAAS,EAAC,OAAO;sBAAAD,QAAA,EAAEqC,MAAM,CAACG,aAAa,CAACb,OAAO,CAAC,CAAC;oBAAC;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNxC,OAAA,CAACX,MAAM;oBAAC0C,OAAO,EAAC,iBAAiB;oBAACuC,IAAI,EAAC,IAAI;oBAAAnC,QAAA,EAAC;kBAE5C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GArBHgC,MAAM,CAACD,EAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBd,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxC,OAAA,CAACN,KAAK;MAACkF,IAAI,EAAEpE,eAAgB;MAACqE,MAAM,EAAEA,CAAA,KAAMpE,kBAAkB,CAAC,KAAK,CAAE;MAAC6D,IAAI,EAAC,IAAI;MAAAnC,QAAA,gBAC9EnC,OAAA,CAACN,KAAK,CAACsE,MAAM;QAACc,WAAW;QAAA3C,QAAA,eACvBnC,OAAA,CAACN,KAAK,CAACqF,KAAK;UAAA5C,QAAA,gBACVnC,OAAA;YAAGoC,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,6BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfxC,OAAA,CAACN,KAAK,CAAC4D,IAAI;QAAAnB,QAAA,eACTnC,OAAA,CAACP,IAAI;UAAA0C,QAAA,gBACHnC,OAAA,CAACP,IAAI,CAACuF,KAAK;YAAC5C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;cAAA9C,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpCxC,OAAA,CAACP,IAAI,CAACyF,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAGC,CAAC,IAAK;gBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;gBAClD5E,eAAe,CAAC4E,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;cAC1C;YAAE;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFxC,OAAA,CAACP,IAAI,CAACgG,IAAI;cAACrD,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEbxC,OAAA,CAACd,GAAG;YAAAiD,QAAA,gBACFnC,OAAA,CAACb,GAAG;cAACkE,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTnC,OAAA,CAACP,IAAI,CAACuF,KAAK;gBAAC5C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;kBAAA9C,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCxC,OAAA,CAACP,IAAI,CAACiG,MAAM;kBACVC,KAAK,EAAE/E,UAAU,CAACI,SAAU;kBAC5BqE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5E,SAAS,EAAEsE,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAxD,QAAA,gBAEjFnC,OAAA;oBAAQ2F,KAAK,EAAC,OAAO;oBAAAxD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCxC,OAAA;oBAAQ2F,KAAK,EAAC,OAAO;oBAAAxD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCxC,OAAA;oBAAQ2F,KAAK,EAAC,SAAS;oBAAAxD,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxCxC,OAAA;oBAAQ2F,KAAK,EAAC,YAAY;oBAAAxD,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxC,OAAA,CAACb,GAAG;cAACkE,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTnC,OAAA,CAACP,IAAI,CAACuF,KAAK;gBAAC5C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;kBAAA9C,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzCxC,OAAA,CAACP,IAAI,CAACyF,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbU,GAAG,EAAC,GAAG;kBACPF,KAAK,EAAE/E,UAAU,CAACK,MAAO;kBACzBoE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE3E,MAAM,EAAE6E,QAAQ,CAACR,CAAC,CAACE,MAAM,CAACG,KAAK;kBAAE,CAAC,CAAC;gBAAE;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA,CAACd,GAAG;YAAAiD,QAAA,gBACFnC,OAAA,CAACb,GAAG;cAACkE,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTnC,OAAA,CAACP,IAAI,CAACuF,KAAK;gBAAC5C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;kBAAA9C,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCxC,OAAA,CAACP,IAAI,CAACiG,MAAM;kBACVC,KAAK,EAAE/E,UAAU,CAACM,SAAU;kBAC5BmE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE1E,SAAS,EAAEoE,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAxD,QAAA,gBAEjFnC,OAAA;oBAAQ2F,KAAK,EAAC,YAAY;oBAAAxD,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjDxC,OAAA;oBAAQ2F,KAAK,EAAC,OAAO;oBAAAxD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNxC,OAAA,CAACb,GAAG;cAACkE,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTnC,OAAA,CAACP,IAAI,CAACuF,KAAK;gBAAC5C,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;kBAAA9C,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnCxC,OAAA,CAACP,IAAI,CAACiG,MAAM;kBACVC,KAAK,EAAE/E,UAAU,CAACO,SAAU;kBAC5BkE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEzE,SAAS,EAAEmE,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAxD,QAAA,gBAEjFnC,OAAA;oBAAQ2F,KAAK,EAAC,IAAI;oBAAAxD,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxC,OAAA;oBAAQ2F,KAAK,EAAC,IAAI;oBAAAxD,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BxC,OAAA;oBAAQ2F,KAAK,EAAC,QAAQ;oBAAAxD,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtCxC,OAAA;oBAAQ2F,KAAK,EAAC,OAAO;oBAAAxD,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA,CAACP,IAAI,CAACuF,KAAK;YAAC5C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;cAAA9C,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/CxC,OAAA,CAACP,IAAI,CAACiG,MAAM;cACVC,KAAK,EAAE/E,UAAU,CAACG,sBAAuB;cACzCsE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE7E,sBAAsB,EAAEuE,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC,CAAE;cAAAxD,QAAA,gBAE9FnC,OAAA;gBAAQ2F,KAAK,EAAC,EAAE;gBAAAxD,QAAA,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDlC,YAAY,CAAC6D,GAAG,CAACK,MAAM,iBACtBxE,OAAA;gBAAwB2F,KAAK,EAAEnB,MAAM,CAACD,EAAG;gBAAApC,QAAA,GACtCqC,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAAC9B,WAAW,EAAC,eACtC;cAAA,GAFa8B,MAAM,CAACD,EAAE;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEbxC,OAAA,CAACP,IAAI,CAACuF,KAAK;YAAC5C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1BnC,OAAA,CAACP,IAAI,CAACwF,KAAK;cAAA9C,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChCxC,OAAA,CAACP,IAAI,CAACyF,OAAO;cACXa,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,wCAAwC;cACpDN,KAAK,EAAE/E,UAAU,CAACE,OAAQ;cAC1BuE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAAC+E,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAE9E,OAAO,EAAEwE,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC;YAAE;cAAAtD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACbxC,OAAA,CAACN,KAAK,CAACwG,MAAM;QAAA/D,QAAA,gBACXnC,OAAA,CAACX,MAAM;UAAC0C,OAAO,EAAC,WAAW;UAACqB,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAAC,KAAK,CAAE;UAAA0B,QAAA,EAAC;QAEtE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxC,OAAA,CAACX,MAAM;UAAC0C,OAAO,EAAC,SAAS;UAACqB,OAAO,EAAET,gBAAiB;UAACwD,QAAQ,EAAE,CAACzF,YAAa;UAAAyB,QAAA,gBAC3EnC,OAAA;YAAGoC,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACtC,EAAA,CAlYID,gBAA0B;EAAA,QACbN,OAAO;AAAA;AAAAyG,EAAA,GADpBnG,gBAA0B;AAoYhC,eAAeA,gBAAgB;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}