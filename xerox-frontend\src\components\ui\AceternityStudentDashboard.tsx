import React, { useState, useEffect } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Badge, Form, Modal, InputGroup, Nav, Tab, Table, ProgressBar } from 'react-bootstrap';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Upload,
  FileText,
  Clock,
  CheckCircle,
  DollarSign,
  Download,
  MessageCircle,
  Eye,
  Plus,
  Activity,
  Printer,
  MapPin,
  Star,
  History,
  Settings,
  User,
  CreditCard,
  Bell,
  Search,
  Filter,
  Calendar,
  TrendingUp,
  BarChart3,
  PieChart,
  Zap,
  Target,
  Award,
  Bookmark,
  Heart,
  Share2,
  RefreshCw,
  AlertCircle,
  Info,
  CheckCircle2,
  XCircle,
  Trash2,
  Edit3,
  Send,
  Phone,
  Mail,
  Globe,
  MapPin as Location
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  estimatedCompletionTime?: string;
  xeroxCenterName: string;
  created: string;
  printType: string;
  copies: number;
  colorType: string;
  paperSize: string;
  priority: string;
}

interface XeroxCenter {
  id: number;
  name: string;
  location: string;
  pendingJobs: number;
  averageRating: number;
  isActive: boolean;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  services?: string[];
  priceRange?: string;
  workingHours?: string;
}

const AceternityStudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);
  const [favoritesCenters, setFavoritesCenters] = useState<number[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [selectedCenter, setSelectedCenter] = useState<XeroxCenter | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [chatMessage, setChatMessage] = useState('');
  const [messages, setMessages] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [sortBy, setSortBy] = useState('created');
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [uploadData, setUploadData] = useState({
    remarks: '',
    preferredXeroxCenterId: '',
    printType: 'Print',
    copies: 1,
    colorType: 'BlackWhite',
    paperSize: 'A4',
    priority: 'Normal'
  });

  const [userSettings, setUserSettings] = useState({
    notifications: {
      email: true,
      push: true,
      sms: false
    },
    preferences: {
      defaultPrintType: 'Print',
      defaultColorType: 'BlackWhite',
      defaultPaperSize: 'A4',
      autoConfirmQuotes: false
    },
    profile: {
      phone: '',
      address: '',
      university: '',
      studentId: ''
    }
  });

  useEffect(() => {
    fetchData();
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async () => {
    setIsRefreshing(true);
    try {
      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([
        printJobApi.getStudentJobs(),
        xeroxCenterApi.getAll()
      ]);

      setPrintJobs(printJobsResponse.data || []);
      setXeroxCenters(xeroxCentersResponse.data || []);
    } catch (error) {
      console.error('Error fetching data:', error);
      setPrintJobs([]);
      setXeroxCenters([]);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Requested': { bg: 'secondary', icon: Clock },
      'UnderReview': { bg: 'info', icon: Eye },
      'Quoted': { bg: 'warning', icon: DollarSign },
      'WaitingConfirmation': { bg: 'warning', icon: AlertCircle },
      'Confirmed': { bg: 'info', icon: CheckCircle },
      'InProgress': { bg: 'primary', icon: Activity },
      'Completed': { bg: 'success', icon: CheckCircle2 },
      'Delivered': { bg: 'success', icon: CheckCircle2 },
      'Rejected': { bg: 'danger', icon: XCircle },
      'Cancelled': { bg: 'secondary', icon: XCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { bg: 'secondary', icon: Clock };
    const IconComponent = config.icon;

    return (
      <Badge bg={config.bg} className="d-flex align-items-center gap-1 px-3 py-2">
        <IconComponent size={14} />
        {status}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'Low': { bg: 'success', icon: '🟢' },
      'Normal': { bg: 'secondary', icon: '🔵' },
      'High': { bg: 'warning', icon: '🟡' },
      'Urgent': { bg: 'danger', icon: '🔴' }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || { bg: 'secondary', icon: '🔵' };

    return (
      <Badge bg={config.bg} className="px-2 py-1">
        {config.icon} {priority}
      </Badge>
    );
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      Object.entries(uploadData).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });

      await fileUploadApi.uploadFile(formData);
      await fetchData();
      setShowUploadModal(false);
      setSelectedFile(null);
      setUploadData({
        remarks: '',
        preferredXeroxCenterId: '',
        printType: 'Print',
        copies: 1,
        colorType: 'BlackWhite',
        paperSize: 'A4',
        priority: 'Normal'
      });
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  };

  const handleViewJob = (job: any) => {
    setSelectedJob(job);
    setShowViewModal(true);
  };

  const handleOpenChat = async (job: any) => {
    try {
      setSelectedJob(job);
      setShowChatModal(true);
      const response = await messageApi.getJobMessages(job.id);
      setMessages(response.data || []);
    } catch (error) {
      console.error('Error loading messages:', error);
      setMessages([]);
    }
  };

  const handleDownloadFile = async (jobId: number, fileName: string) => {
    try {
      const response = await fileUploadApi.downloadFile(jobId);
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
    }
  };

  const handleConfirmJob = async (jobId: number) => {
    try {
      await printJobApi.confirmJob(jobId);
      await fetchData();
    } catch (error) {
      console.error('Error confirming job:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!chatMessage.trim() || !selectedJob) return;

    try {
      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());
      setMessages(prev => [...prev, response.data]);
      setChatMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const toggleFavorite = (centerId: number) => {
    setFavoritesCenters(prev =>
      prev.includes(centerId)
        ? prev.filter(id => id !== centerId)
        : [...prev, centerId]
    );
  };

  const filteredJobs = printJobs.filter(job => {
    const matchesSearch = searchTerm === '' ||
      job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.xeroxCenterName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = filterStatus === 'All' || job.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);
  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;
  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;
  const pendingJobs = printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted'].includes(job.status)).length;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const floatingShapes = Array.from({ length: 8 }, (_, i) => (
    <motion.div
      key={i}
      className="position-absolute rounded-circle"
      style={{
        background: `linear-gradient(135deg, ${i % 3 === 0 ? '#667eea' : i % 3 === 1 ? '#764ba2' : '#f093fb'}, ${i % 3 === 0 ? '#764ba2' : i % 3 === 1 ? '#f093fb' : '#f5576c'})`,
        width: `${Math.random() * 60 + 20}px`,
        height: `${Math.random() * 60 + 20}px`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        opacity: 0.1,
        zIndex: 0
      }}
      animate={{
        x: [0, Math.random() * 100 - 50],
        y: [0, Math.random() * 100 - 50],
        rotate: [0, 360],
        scale: [1, 1.2, 1],
      }}
      transition={{
        duration: Math.random() * 20 + 10,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
    />
  ));

  return (
    <div className="min-h-screen position-relative" style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh'
    }}>
      {/* Animated Background */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {floatingShapes}
        <div className="position-absolute w-100 h-100" style={{
          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)',
        }} />
      </div>

      <Container fluid className="position-relative py-4" style={{ zIndex: 1 }}>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-5">
            <motion.div
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="d-inline-flex align-items-center justify-content-center mb-3"
              style={{
                width: '80px',
                height: '80px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '20px',
                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'
              }}
            >
              <User className="text-white" size={40} />
            </motion.div>
            <h1 className="text-white fw-bold mb-2" style={{ fontSize: '2.5rem' }}>
              Welcome back, {user?.username}!
            </h1>
            <p className="text-white-50 fs-5 mb-4">
              Manage your printing jobs and explore xerox centers
            </p>

            <div className="d-flex align-items-center justify-content-center gap-3 flex-wrap">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  size="lg"
                  onClick={() => setShowUploadModal(true)}
                  className="px-5 py-3 rounded-4 border-0 fw-semibold"
                  style={{
                    background: 'rgba(255, 255, 255, 0.2)',
                    backdropFilter: 'blur(20px)',
                    color: '#fff',
                    boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'
                  }}
                >
                  <Upload className="me-2" size={20} />
                  Upload Files
                </Button>
              </motion.div>

              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant="outline-light"
                  size="lg"
                  onClick={fetchData}
                  disabled={isRefreshing}
                  className="px-4 py-3 rounded-4 fw-semibold"
                >
                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />
                  Refresh
                </Button>
              </motion.div>
            </div>
          </motion.div>

          {/* Navigation Tabs */}
          <motion.div variants={itemVariants} className="mb-4">
            <Card className="border-0 shadow-lg" style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px'
            }}>
              <Card.Body className="p-2">
                <Nav variant="pills" className="justify-content-center flex-wrap">
                  {[
                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                    { key: 'jobs', label: 'My Jobs', icon: FileText },
                    { key: 'centers', label: 'Xerox Centers', icon: Printer },
                    { key: 'history', label: 'History', icon: History },
                    { key: 'favorites', label: 'Favorites', icon: Heart },
                    { key: 'analytics', label: 'Analytics', icon: PieChart },
                    { key: 'settings', label: 'Settings', icon: Settings }
                  ].map(tab => {
                    const IconComponent = tab.icon;
                    return (
                      <Nav.Item key={tab.key} className="m-1">
                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                          <Nav.Link
                            active={activeTab === tab.key}
                            onClick={() => setActiveTab(tab.key)}
                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${
                              activeTab === tab.key
                                ? 'text-primary'
                                : 'text-white-50'
                            }`}
                            style={{
                              background: activeTab === tab.key
                                ? 'rgba(255, 255, 255, 0.9)'
                                : 'transparent',
                              border: 'none',
                              transition: 'all 0.3s ease',
                              boxShadow: activeTab === tab.key
                                ? '0 10px 30px rgba(255, 255, 255, 0.2)'
                                : 'none'
                            }}
                          >
                            <IconComponent size={18} className="me-2" />
                            <span className="d-none d-md-inline">{tab.label}</span>
                          </Nav.Link>
                        </motion.div>
                      </Nav.Item>
                    );
                  })}
                </Nav>
              </Card.Body>
            </Card>
          </motion.div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {activeTab === 'dashboard' && (
                <DashboardTab
                  printJobs={printJobs}
                  xeroxCenters={xeroxCenters}
                  totalSpent={totalSpent}
                  inProgressJobs={inProgressJobs}
                  completedJobs={completedJobs}
                  pendingJobs={pendingJobs}
                  onViewJob={handleViewJob}
                  onDownloadFile={handleDownloadFile}
                  onOpenChat={handleOpenChat}
                  onConfirmJob={handleConfirmJob}
                  getStatusBadge={getStatusBadge}
                  getPriorityBadge={getPriorityBadge}
                />
              )}

              {activeTab === 'jobs' && (
                <JobsTab
                  jobs={filteredJobs}
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  filterStatus={filterStatus}
                  setFilterStatus={setFilterStatus}
                  sortBy={sortBy}
                  setSortBy={setSortBy}
                  onViewJob={handleViewJob}
                  onDownloadFile={handleDownloadFile}
                  onOpenChat={handleOpenChat}
                  onConfirmJob={handleConfirmJob}
                  getStatusBadge={getStatusBadge}
                  getPriorityBadge={getPriorityBadge}
                />
              )}

              {activeTab === 'centers' && (
                <CentersTab
                  centers={xeroxCenters}
                  favoritesCenters={favoritesCenters}
                  onToggleFavorite={toggleFavorite}
                  onSelectCenter={setSelectedCenter}
                />
              )}

              {activeTab === 'history' && (
                <HistoryTab
                  jobs={printJobs.filter(job => ['Completed', 'Delivered', 'Cancelled', 'Rejected'].includes(job.status))}
                  onViewJob={handleViewJob}
                  getStatusBadge={getStatusBadge}
                />
              )}

              {activeTab === 'favorites' && (
                <FavoritesTab
                  centers={xeroxCenters.filter(center => favoritesCenters.includes(center.id))}
                  onToggleFavorite={toggleFavorite}
                  onSelectCenter={setSelectedCenter}
                />
              )}

              {activeTab === 'analytics' && (
                <AnalyticsTab
                  jobs={printJobs}
                  totalSpent={totalSpent}
                />
              )}

              {activeTab === 'settings' && (
                <SettingsTab
                  userSettings={userSettings}
                  setUserSettings={setUserSettings}
                />
              )}
            </motion.div>
          </AnimatePresence>
        </motion.div>
      </Container>

      {/* Modals */}
      {showUploadModal && (
        <UploadModal
          show={showUploadModal}
          onHide={() => setShowUploadModal(false)}
          uploadData={uploadData}
          setUploadData={setUploadData}
          selectedFile={selectedFile}
          setSelectedFile={setSelectedFile}
          xeroxCenters={xeroxCenters}
          onUpload={handleFileUpload}
        />
      )}

      {showViewModal && (
        <ViewJobModal
          show={showViewModal}
          onHide={() => setShowViewModal(false)}
          job={selectedJob}
          getStatusBadge={getStatusBadge}
          getPriorityBadge={getPriorityBadge}
        />
      )}

      {showChatModal && (
        <ChatModal
          show={showChatModal}
          onHide={() => setShowChatModal(false)}
          job={selectedJob}
          messages={messages}
          chatMessage={chatMessage}
          setChatMessage={setChatMessage}
          onSendMessage={handleSendMessage}
        />
      )}
    </div>
  );
};

// Dashboard Tab Component
const DashboardTab: React.FC<any> = ({
  printJobs, xeroxCenters, totalSpent, inProgressJobs, completedJobs, pendingJobs,
  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge
}) => {
  return (
    <Row className="g-4">
      {/* Statistics Cards */}
      <Col md={3}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          whileHover={{ y: -5, scale: 1.02 }}
        >
          <Card className="h-100 border-0 shadow-lg" style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px'
          }}>
            <Card.Body className="text-center p-4">
              <div className="mb-3">
                <div className="d-inline-flex align-items-center justify-content-center" style={{
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  borderRadius: '15px'
                }}>
                  <FileText className="text-white" size={24} />
                </div>
              </div>
              <h6 className="text-white-50 mb-1">Total Jobs</h6>
              <h2 className="fw-bold text-white">{printJobs.length}</h2>
              <small className="text-success">
                <TrendingUp size={12} className="me-1" />
                +12% vs last month
              </small>
            </Card.Body>
          </Card>
        </motion.div>
      </Col>

      <Col md={3}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          whileHover={{ y: -5, scale: 1.02 }}
        >
          <Card className="h-100 border-0 shadow-lg" style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px'
          }}>
            <Card.Body className="text-center p-4">
              <div className="mb-3">
                <div className="d-inline-flex align-items-center justify-content-center" style={{
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                  borderRadius: '15px'
                }}>
                  <Clock className="text-white" size={24} />
                </div>
              </div>
              <h6 className="text-white-50 mb-1">In Progress</h6>
              <h2 className="fw-bold text-white">{inProgressJobs}</h2>
              <small className="text-warning">
                <Activity size={12} className="me-1" />
                Active jobs
              </small>
            </Card.Body>
          </Card>
        </motion.div>
      </Col>

      <Col md={3}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          whileHover={{ y: -5, scale: 1.02 }}
        >
          <Card className="h-100 border-0 shadow-lg" style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px'
          }}>
            <Card.Body className="text-center p-4">
              <div className="mb-3">
                <div className="d-inline-flex align-items-center justify-content-center" style={{
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                  borderRadius: '15px'
                }}>
                  <CheckCircle className="text-white" size={24} />
                </div>
              </div>
              <h6 className="text-white-50 mb-1">Completed</h6>
              <h2 className="fw-bold text-white">{completedJobs}</h2>
              <small className="text-success">
                <Award size={12} className="me-1" />
                Finished jobs
              </small>
            </Card.Body>
          </Card>
        </motion.div>
      </Col>

      <Col md={3}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          whileHover={{ y: -5, scale: 1.02 }}
        >
          <Card className="h-100 border-0 shadow-lg" style={{
            background: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(20px)',
            borderRadius: '20px'
          }}>
            <Card.Body className="text-center p-4">
              <div className="mb-3">
                <div className="d-inline-flex align-items-center justify-content-center" style={{
                  width: '60px',
                  height: '60px',
                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                  borderRadius: '15px'
                }}>
                  <DollarSign className="text-white" size={24} />
                </div>
              </div>
              <h6 className="text-white-50 mb-1">Total Spent</h6>
              <h2 className="fw-bold text-white">${totalSpent.toFixed(2)}</h2>
              <small className="text-info">
                <CreditCard size={12} className="me-1" />
                This month
              </small>
            </Card.Body>
          </Card>
        </motion.div>
      </Col>

      {/* Recent Jobs */}
      <Col lg={8}>
        <Card className="border-0 shadow-lg h-100" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <div className="d-flex align-items-center justify-content-between">
              <h4 className="fw-bold mb-0 text-white">
                <Activity className="me-2" size={20} />
                Recent Jobs
              </h4>
              <Badge bg="light" text="dark" className="px-3 py-2">
                {printJobs.length} total
              </Badge>
            </div>
          </Card.Header>
          <Card.Body>
            {printJobs.length > 0 ? (
              <div className="space-y-3">
                {printJobs.slice(0, 5).map((job: PrintJob, index: number) => (
                  <motion.div
                    key={job.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="p-3 rounded-3"
                    style={{
                      background: 'rgba(255, 255, 255, 0.1)',
                      border: '1px solid rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <div className="d-flex align-items-center justify-content-between">
                      <div className="d-flex align-items-center">
                        <div className="me-3">
                          <div className="d-inline-flex align-items-center justify-content-center" style={{
                            width: '50px',
                            height: '50px',
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            borderRadius: '12px'
                          }}>
                            <FileText className="text-white" size={20} />
                          </div>
                        </div>
                        <div>
                          <h6 className="fw-semibold mb-1 text-white">{job.jobNumber}</h6>
                          <p className="text-white-50 small mb-1">
                            {job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName}
                          </p>
                          <small className="text-white-50">{job.xeroxCenterName}</small>
                        </div>
                      </div>

                      <div className="d-flex align-items-center">
                        <div className="text-end me-3">
                          {getStatusBadge(job.status)}
                          {job.cost && (
                            <div className="fw-semibold text-success mt-1">
                              ${job.cost.toFixed(2)}
                            </div>
                          )}
                        </div>

                        <div className="d-flex gap-1">
                          <Button
                            variant="outline-light"
                            size="sm"
                            onClick={() => onDownloadFile(job.id, job.fileName)}
                            style={{ borderRadius: '8px' }}
                          >
                            <Download size={14} />
                          </Button>

                          <Button
                            variant="outline-light"
                            size="sm"
                            onClick={() => onViewJob(job)}
                            style={{ borderRadius: '8px' }}
                          >
                            <Eye size={14} />
                          </Button>

                          <Button
                            variant="outline-light"
                            size="sm"
                            onClick={() => onOpenChat(job)}
                            style={{ borderRadius: '8px' }}
                          >
                            <MessageCircle size={14} />
                          </Button>

                          {job.status === 'Quoted' && (
                            <Button
                              variant="success"
                              size="sm"
                              onClick={() => onConfirmJob(job.id)}
                              style={{ borderRadius: '8px' }}
                            >
                              <CheckCircle size={14} />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-5"
              >
                <div className="mb-4">
                  <div className="d-inline-flex align-items-center justify-content-center" style={{
                    width: '80px',
                    height: '80px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '20px'
                  }}>
                    <Upload className="text-white" size={40} />
                  </div>
                </div>
                <h5 className="fw-semibold mb-2 text-white">No jobs yet</h5>
                <p className="text-white-50 mb-4">Upload your first file to get started!</p>
              </motion.div>
            )}
          </Card.Body>
        </Card>
      </Col>

      {/* Quick Stats */}
      <Col lg={4}>
        <Card className="border-0 shadow-lg h-100" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <h4 className="fw-bold mb-0 text-white">
              <Target className="me-2" size={20} />
              Quick Stats
            </h4>
          </Card.Header>
          <Card.Body>
            <div className="space-y-4">
              <div className="d-flex align-items-center justify-content-between p-3 rounded-3" style={{
                background: 'rgba(255, 255, 255, 0.1)'
              }}>
                <div className="d-flex align-items-center">
                  <div className="me-3" style={{
                    width: '40px',
                    height: '40px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    borderRadius: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Clock className="text-white" size={20} />
                  </div>
                  <div>
                    <h6 className="mb-0 text-white">Pending</h6>
                    <small className="text-white-50">Awaiting review</small>
                  </div>
                </div>
                <h4 className="mb-0 text-warning">{pendingJobs}</h4>
              </div>

              <div className="d-flex align-items-center justify-content-between p-3 rounded-3" style={{
                background: 'rgba(255, 255, 255, 0.1)'
              }}>
                <div className="d-flex align-items-center">
                  <div className="me-3" style={{
                    width: '40px',
                    height: '40px',
                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                    borderRadius: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Printer className="text-white" size={20} />
                  </div>
                  <div>
                    <h6 className="mb-0 text-white">Centers</h6>
                    <small className="text-white-50">Available now</small>
                  </div>
                </div>
                <h4 className="mb-0 text-info">{xeroxCenters.filter((c: XeroxCenter) => c.isActive).length}</h4>
              </div>

              <div className="d-flex align-items-center justify-content-between p-3 rounded-3" style={{
                background: 'rgba(255, 255, 255, 0.1)'
              }}>
                <div className="d-flex align-items-center">
                  <div className="me-3" style={{
                    width: '40px',
                    height: '40px',
                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                    borderRadius: '10px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Star className="text-white" size={20} />
                  </div>
                  <div>
                    <h6 className="mb-0 text-white">Avg Rating</h6>
                    <small className="text-white-50">Your experience</small>
                  </div>
                </div>
                <h4 className="mb-0 text-success">4.8</h4>
              </div>
            </div>
          </Card.Body>
        </Card>
      </Col>
    </Row>
  );
};

// Jobs Tab Component
const JobsTab: React.FC<any> = ({
  jobs, searchTerm, setSearchTerm, filterStatus, setFilterStatus, sortBy, setSortBy,
  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge
}) => {
  return (
    <Card className="border-0 shadow-lg" style={{
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      borderRadius: '20px'
    }}>
      <Card.Header className="border-0 bg-transparent">
        <div className="d-flex align-items-center justify-content-between mb-3">
          <h4 className="fw-bold mb-0 text-white">
            <FileText className="me-2" size={20} />
            All Jobs
          </h4>
          <Badge bg="light" text="dark" className="px-3 py-2">
            {jobs.length} jobs
          </Badge>
        </div>

        {/* Search and Filter Controls */}
        <Row className="g-3">
          <Col md={4}>
            <InputGroup>
              <InputGroup.Text style={{ background: 'rgba(255, 255, 255, 0.1)', border: 'none' }}>
                <Search className="text-white-50" size={16} />
              </InputGroup.Text>
              <Form.Control
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: 'none',
                  color: '#fff'
                }}
              />
            </InputGroup>
          </Col>

          <Col md={3}>
            <Form.Select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                border: 'none',
                color: '#fff'
              }}
            >
              <option value="All">All Status</option>
              <option value="Requested">Requested</option>
              <option value="UnderReview">Under Review</option>
              <option value="Quoted">Quoted</option>
              <option value="Confirmed">Confirmed</option>
              <option value="InProgress">In Progress</option>
              <option value="Completed">Completed</option>
              <option value="Delivered">Delivered</option>
            </Form.Select>
          </Col>

          <Col md={3}>
            <Form.Select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              style={{
                background: 'rgba(255, 255, 255, 0.1)',
                border: 'none',
                color: '#fff'
              }}
            >
              <option value="created">Sort by Date</option>
              <option value="status">Sort by Status</option>
              <option value="cost">Sort by Cost</option>
              <option value="priority">Sort by Priority</option>
            </Form.Select>
          </Col>
        </Row>
      </Card.Header>

      <Card.Body>
        {jobs.length > 0 ? (
          <Row className="g-4">
            {jobs.map((job: any, index: number) => (
              <Col key={job.id} md={6} lg={4}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <Card className="h-100 border-0" style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    borderRadius: '15px'
                  }}>
                    <Card.Header className="border-0 bg-transparent">
                      <div className="d-flex align-items-start justify-content-between">
                        <div>
                          <h6 className="fw-bold mb-1 text-white">{job.jobNumber}</h6>
                          {getPriorityBadge(job.priority)}
                        </div>
                        {getStatusBadge(job.status)}
                      </div>
                    </Card.Header>

                    <Card.Body className="pt-0">
                      <div className="mb-3">
                        <div className="d-flex align-items-center text-white-50 small mb-2">
                          <FileText size={14} className="me-1" />
                          {job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}
                        </div>
                        <div className="small text-white-50">
                          <div><strong>Type:</strong> {job.printType}</div>
                          <div><strong>Copies:</strong> {job.copies}</div>
                          <div><strong>Color:</strong> {job.colorType}</div>
                          <div><strong>Size:</strong> {job.paperSize}</div>
                        </div>
                      </div>

                      {job.cost && (
                        <div className="mb-3">
                          <span className="h5 fw-bold text-success">
                            ${job.cost.toFixed(2)}
                          </span>
                        </div>
                      )}

                      <div className="small text-white-50 mb-3">
                        {new Date(job.created).toLocaleDateString()}
                      </div>

                      <div className="d-flex flex-wrap gap-1">
                        <Button
                          variant="outline-light"
                          size="sm"
                          onClick={() => onDownloadFile(job.id, job.fileName)}
                          style={{ borderRadius: '8px' }}
                        >
                          <Download size={12} />
                        </Button>

                        <Button
                          variant="outline-light"
                          size="sm"
                          onClick={() => onViewJob(job)}
                          style={{ borderRadius: '8px' }}
                        >
                          <Eye size={12} />
                        </Button>

                        <Button
                          variant="outline-light"
                          size="sm"
                          onClick={() => onOpenChat(job)}
                          style={{ borderRadius: '8px' }}
                        >
                          <MessageCircle size={12} />
                        </Button>

                        {job.status === 'Quoted' && (
                          <Button
                            variant="success"
                            size="sm"
                            onClick={() => onConfirmJob(job.id)}
                            style={{ borderRadius: '8px' }}
                          >
                            <CheckCircle size={12} />
                          </Button>
                        )}
                      </div>
                    </Card.Body>
                  </Card>
                </motion.div>
              </Col>
            ))}
          </Row>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-5"
          >
            <div className="mb-4">
              <div className="d-inline-flex align-items-center justify-content-center" style={{
                width: '80px',
                height: '80px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '20px'
              }}>
                <FileText className="text-white" size={40} />
              </div>
            </div>
            <h5 className="fw-semibold mb-2 text-white">No jobs found</h5>
            <p className="text-white-50">No jobs match your current filter criteria.</p>
          </motion.div>
        )}
      </Card.Body>
    </Card>
  );
};

// Centers Tab Component
const CentersTab: React.FC<any> = ({ centers, favoritesCenters, onToggleFavorite, onSelectCenter }) => {
  return (
    <Row className="g-4">
      {centers.map((center: any, index: number) => (
        <Col key={center.id} md={6} lg={4}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            whileHover={{ y: -5, scale: 1.02 }}
          >
            <Card className="h-100 border-0 shadow-lg" style={{
              background: 'rgba(255, 255, 255, 0.1)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px'
            }}>
              <Card.Header className="border-0 bg-transparent">
                <div className="d-flex align-items-start justify-content-between">
                  <div>
                    <h5 className="fw-bold mb-1 text-white">{center.name}</h5>
                    <div className="d-flex align-items-center text-white-50 small">
                      <Location size={12} className="me-1" />
                      {center.location}
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => onToggleFavorite(center.id)}
                    className="btn btn-link p-0 border-0"
                  >
                    <Heart
                      size={20}
                      className={favoritesCenters.includes(center.id) ? 'text-danger' : 'text-white-50'}
                      fill={favoritesCenters.includes(center.id) ? 'currentColor' : 'none'}
                    />
                  </motion.button>
                </div>
              </Card.Header>

              <Card.Body>
                <div className="mb-3">
                  <div className="d-flex align-items-center justify-content-between mb-2">
                    <div className="d-flex align-items-center">
                      <Star className="text-warning me-1" size={16} />
                      <span className="fw-semibold text-white">
                        {center.averageRating.toFixed(1)}
                      </span>
                    </div>
                    <Badge
                      bg={center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'danger'}
                      className="px-2 py-1"
                    >
                      {center.pendingJobs} jobs
                    </Badge>
                  </div>

                  {center.services && (
                    <div className="mb-3">
                      <div className="d-flex flex-wrap gap-1">
                        {center.services.slice(0, 3).map((service: string, idx: number) => (
                          <Badge key={idx} bg="light" text="dark" className="px-2 py-1">
                            {service}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {center.priceRange && (
                    <div className="text-white-50 small mb-2">
                      <DollarSign size={12} className="me-1" />
                      {center.priceRange}
                    </div>
                  )}

                  {center.workingHours && (
                    <div className="text-white-50 small mb-3">
                      <Clock size={12} className="me-1" />
                      {center.workingHours}
                    </div>
                  )}
                </div>

                <div className="d-flex gap-2">
                  <Button
                    variant="outline-light"
                    size="sm"
                    onClick={() => onSelectCenter(center)}
                    className="flex-grow-1"
                    style={{ borderRadius: '10px' }}
                  >
                    <Eye size={14} className="me-1" />
                    View Details
                  </Button>

                  {center.phone && (
                    <Button
                      variant="outline-light"
                      size="sm"
                      href={`tel:${center.phone}`}
                      style={{ borderRadius: '10px' }}
                    >
                      <Phone size={14} />
                    </Button>
                  )}
                </div>
              </Card.Body>
            </Card>
          </motion.div>
        </Col>
      ))}
    </Row>
  );
};

// History Tab Component
const HistoryTab: React.FC<any> = ({ jobs, onViewJob, getStatusBadge }) => {
  return (
    <Card className="border-0 shadow-lg" style={{
      background: 'rgba(255, 255, 255, 0.1)',
      backdropFilter: 'blur(20px)',
      borderRadius: '20px'
    }}>
      <Card.Header className="border-0 bg-transparent">
        <h4 className="fw-bold mb-0 text-white">
          <History className="me-2" size={20} />
          Job History
        </h4>
      </Card.Header>
      <Card.Body>
        {jobs.length > 0 ? (
          <div className="table-responsive">
            <Table className="table-dark table-hover">
              <thead>
                <tr>
                  <th>Job Number</th>
                  <th>File Name</th>
                  <th>Status</th>
                  <th>Cost</th>
                  <th>Date</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobs.map((job: any) => (
                  <tr key={job.id}>
                    <td className="fw-semibold">{job.jobNumber}</td>
                    <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>
                    <td>{getStatusBadge(job.status)}</td>
                    <td className="text-success fw-semibold">
                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                    </td>
                    <td>{new Date(job.created).toLocaleDateString()}</td>
                    <td>
                      <Button
                        variant="outline-light"
                        size="sm"
                        onClick={() => onViewJob(job)}
                      >
                        <Eye size={14} />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        ) : (
          <div className="text-center py-5">
            <History className="text-white-50 mb-3" size={60} />
            <h5 className="text-white">No history yet</h5>
            <p className="text-white-50">Your completed jobs will appear here.</p>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

// Favorites Tab Component
const FavoritesTab: React.FC<any> = ({ centers, onToggleFavorite, onSelectCenter }) => {
  return (
    <div>
      {centers.length > 0 ? (
        <CentersTab
          centers={centers}
          favoritesCenters={centers.map((c: any) => c.id)}
          onToggleFavorite={onToggleFavorite}
          onSelectCenter={onSelectCenter}
        />
      ) : (
        <Card className="border-0 shadow-lg" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Body className="text-center py-5">
            <Heart className="text-white-50 mb-3" size={60} />
            <h5 className="text-white">No favorites yet</h5>
            <p className="text-white-50">Add xerox centers to your favorites for quick access.</p>
          </Card.Body>
        </Card>
      )}
    </div>
  );
};

// Analytics Tab Component
const AnalyticsTab: React.FC<any> = ({ jobs, totalSpent }) => {
  const monthlySpending = jobs.reduce((acc: any, job: any) => {
    const month = new Date(job.created).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    acc[month] = (acc[month] || 0) + (job.cost || 0);
    return acc;
  }, {});

  const statusCounts = jobs.reduce((acc: any, job: any) => {
    acc[job.status] = (acc[job.status] || 0) + 1;
    return acc;
  }, {});

  return (
    <Row className="g-4">
      <Col md={6}>
        <Card className="border-0 shadow-lg h-100" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <h5 className="fw-bold mb-0 text-white">
              <BarChart3 className="me-2" size={20} />
              Spending Overview
            </h5>
          </Card.Header>
          <Card.Body>
            <div className="mb-4">
              <h3 className="text-success fw-bold">${totalSpent.toFixed(2)}</h3>
              <p className="text-white-50">Total spent this year</p>
            </div>

            <div className="space-y-3">
              {Object.entries(monthlySpending).slice(-6).map(([month, amount]: [string, any]) => (
                <div key={month} className="d-flex align-items-center justify-content-between">
                  <span className="text-white">{month}</span>
                  <div className="d-flex align-items-center">
                    <div className="me-3" style={{ width: '100px' }}>
                      <ProgressBar
                        now={(amount / Math.max(...Object.values(monthlySpending).map(v => Number(v)))) * 100}
                        style={{ height: '8px' }}
                        variant="success"
                      />
                    </div>
                    <span className="text-success fw-semibold">${amount.toFixed(2)}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      </Col>

      <Col md={6}>
        <Card className="border-0 shadow-lg h-100" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <h5 className="fw-bold mb-0 text-white">
              <PieChart className="me-2" size={20} />
              Job Status Distribution
            </h5>
          </Card.Header>
          <Card.Body>
            <div className="space-y-3">
              {Object.entries(statusCounts).map(([status, count]: [string, any]) => (
                <div key={status} className="d-flex align-items-center justify-content-between">
                  <span className="text-white">{status}</span>
                  <div className="d-flex align-items-center">
                    <div className="me-3" style={{ width: '100px' }}>
                      <ProgressBar
                        now={(count / jobs.length) * 100}
                        style={{ height: '8px' }}
                        variant="info"
                      />
                    </div>
                    <span className="text-info fw-semibold">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </Card.Body>
        </Card>
      </Col>
    </Row>
  );
};

// Settings Tab Component
const SettingsTab: React.FC<any> = ({ userSettings, setUserSettings }) => {
  return (
    <Row className="g-4">
      <Col md={6}>
        <Card className="border-0 shadow-lg" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <h5 className="fw-bold mb-0 text-white">
              <Bell className="me-2" size={20} />
              Notification Settings
            </h5>
          </Card.Header>
          <Card.Body>
            <Form>
              <Form.Check
                type="switch"
                id="email-notifications"
                label="Email Notifications"
                checked={userSettings.notifications.email}
                onChange={(e) => setUserSettings({
                  ...userSettings,
                  notifications: { ...userSettings.notifications, email: e.target.checked }
                })}
                className="text-white mb-3"
              />
              <Form.Check
                type="switch"
                id="push-notifications"
                label="Push Notifications"
                checked={userSettings.notifications.push}
                onChange={(e) => setUserSettings({
                  ...userSettings,
                  notifications: { ...userSettings.notifications, push: e.target.checked }
                })}
                className="text-white mb-3"
              />
              <Form.Check
                type="switch"
                id="sms-notifications"
                label="SMS Notifications"
                checked={userSettings.notifications.sms}
                onChange={(e) => setUserSettings({
                  ...userSettings,
                  notifications: { ...userSettings.notifications, sms: e.target.checked }
                })}
                className="text-white"
              />
            </Form>
          </Card.Body>
        </Card>
      </Col>

      <Col md={6}>
        <Card className="border-0 shadow-lg" style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px'
        }}>
          <Card.Header className="border-0 bg-transparent">
            <h5 className="fw-bold mb-0 text-white">
              <Settings className="me-2" size={20} />
              Print Preferences
            </h5>
          </Card.Header>
          <Card.Body>
            <Form>
              <Form.Group className="mb-3">
                <Form.Label className="text-white">Default Print Type</Form.Label>
                <Form.Select
                  value={userSettings.preferences.defaultPrintType}
                  onChange={(e) => setUserSettings({
                    ...userSettings,
                    preferences: { ...userSettings.preferences, defaultPrintType: e.target.value }
                  })}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: 'none',
                    color: '#fff'
                  }}
                >
                  <option value="Print">Print</option>
                  <option value="Xerox">Xerox</option>
                  <option value="Binding">Binding</option>
                  <option value="Lamination">Lamination</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label className="text-white">Default Color Type</Form.Label>
                <Form.Select
                  value={userSettings.preferences.defaultColorType}
                  onChange={(e) => setUserSettings({
                    ...userSettings,
                    preferences: { ...userSettings.preferences, defaultColorType: e.target.value }
                  })}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: 'none',
                    color: '#fff'
                  }}
                >
                  <option value="BlackWhite">Black & White</option>
                  <option value="Color">Color</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label className="text-white">Default Paper Size</Form.Label>
                <Form.Select
                  value={userSettings.preferences.defaultPaperSize}
                  onChange={(e) => setUserSettings({
                    ...userSettings,
                    preferences: { ...userSettings.preferences, defaultPaperSize: e.target.value }
                  })}
                  style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: 'none',
                    color: '#fff'
                  }}
                >
                  <option value="A4">A4</option>
                  <option value="A3">A3</option>
                  <option value="Letter">Letter</option>
                  <option value="Legal">Legal</option>
                </Form.Select>
              </Form.Group>

              <Form.Check
                type="switch"
                id="auto-confirm"
                label="Auto-confirm quotes under $10"
                checked={userSettings.preferences.autoConfirmQuotes}
                onChange={(e) => setUserSettings({
                  ...userSettings,
                  preferences: { ...userSettings.preferences, autoConfirmQuotes: e.target.checked }
                })}
                className="text-white"
              />
            </Form>
          </Card.Body>
        </Card>
      </Col>
    </Row>
  );
};

// Upload Modal Component
const UploadModal: React.FC<any> = ({
  show, onHide, uploadData, setUploadData, selectedFile, setSelectedFile, xeroxCenters, onUpload
}) => {
  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>
        <Modal.Title className="text-white">
          <Upload className="me-2" size={20} />
          Upload Files for Printing
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>
        <Form>
          <Form.Group className="mb-3">
            <Form.Label>Select File</Form.Label>
            <Form.Control
              type="file"
              accept=".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png"
              onChange={(e) => {
                const files = (e.target as HTMLInputElement).files;
                setSelectedFile(files ? files[0] : null);
              }}
              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
            />
            <Form.Text className="text-muted">
              Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)
            </Form.Text>
          </Form.Group>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Print Type</Form.Label>
                <Form.Select
                  value={uploadData.printType}
                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, printType: e.target.value }))}
                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
                >
                  <option value="Print">Print</option>
                  <option value="Xerox">Xerox</option>
                  <option value="Binding">Binding</option>
                  <option value="Lamination">Lamination</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Number of Copies</Form.Label>
                <Form.Control
                  type="number"
                  min="1"
                  value={uploadData.copies}
                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, copies: parseInt(e.target.value) }))}
                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Color Type</Form.Label>
                <Form.Select
                  value={uploadData.colorType}
                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, colorType: e.target.value }))}
                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
                >
                  <option value="BlackWhite">Black & White</option>
                  <option value="Color">Color</option>
                </Form.Select>
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Paper Size</Form.Label>
                <Form.Select
                  value={uploadData.paperSize}
                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, paperSize: e.target.value }))}
                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
                >
                  <option value="A4">A4</option>
                  <option value="A3">A3</option>
                  <option value="Letter">Letter</option>
                  <option value="Legal">Legal</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Priority</Form.Label>
            <Form.Select
              value={uploadData.priority}
              onChange={(e) => setUploadData((prev: any) => ({ ...prev, priority: e.target.value }))}
              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
            >
              <option value="Low">Low</option>
              <option value="Normal">Normal</option>
              <option value="High">High</option>
              <option value="Urgent">Urgent</option>
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Preferred Xerox Center</Form.Label>
            <Form.Select
              value={uploadData.preferredXeroxCenterId}
              onChange={(e) => setUploadData((prev: any) => ({ ...prev, preferredXeroxCenterId: e.target.value }))}
              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
            >
              <option value="">Select a center (optional)</option>
              {xeroxCenters.map((center: any) => (
                <option key={center.id} value={center.id}>
                  {center.name} - {center.pendingJobs} pending jobs
                </option>
              ))}
            </Form.Select>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Remarks</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              placeholder="Any special instructions or remarks..."
              value={uploadData.remarks}
              onChange={(e) => setUploadData((prev: any) => ({ ...prev, remarks: e.target.value }))}
              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>
        <Button variant="secondary" onClick={onHide}>
          Cancel
        </Button>
        <Button
          onClick={onUpload}
          disabled={!selectedFile}
          style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}
        >
          <Upload className="me-2" size={16} />
          Upload File
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

// View Job Modal Component
const ViewJobModal: React.FC<any> = ({ show, onHide, job, getStatusBadge, getPriorityBadge }) => {
  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>
        <Modal.Title className="text-white">
          <Eye className="me-2" size={20} />
          Job Details
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>
        {job && (
          <Row>
            <Col md={6}>
              <h6 className="text-primary">Job Information</h6>
              <p><strong>Job Number:</strong> {job.jobNumber}</p>
              <p><strong>Status:</strong> {getStatusBadge(job.status)}</p>
              <p><strong>Priority:</strong> {getPriorityBadge(job.priority)}</p>
              <p><strong>File Name:</strong> {job.fileName}</p>
              <p><strong>Xerox Center:</strong> {job.xeroxCenterName}</p>
              {job.cost && <p><strong>Cost:</strong> <span className="text-success">${job.cost.toFixed(2)}</span></p>}
              <p><strong>Created:</strong> {new Date(job.created).toLocaleString()}</p>
              {job.estimatedCompletionTime && (
                <p><strong>Estimated Completion:</strong> {new Date(job.estimatedCompletionTime).toLocaleString()}</p>
              )}
            </Col>
            <Col md={6}>
              <h6 className="text-primary">Print Specifications</h6>
              <p><strong>Type:</strong> {job.printType}</p>
              <p><strong>Copies:</strong> {job.copies}</p>
              <p><strong>Color:</strong> {job.colorType}</p>
              <p><strong>Paper Size:</strong> {job.paperSize}</p>
              {job.remarks && (
                <>
                  <h6 className="text-primary mt-3">Remarks</h6>
                  <p className="text-muted">{job.remarks}</p>
                </>
              )}
            </Col>
          </Row>
        )}
      </Modal.Body>
      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

// Chat Modal Component
const ChatModal: React.FC<any> = ({
  show, onHide, job, messages, chatMessage, setChatMessage, onSendMessage
}) => {
  return (
    <Modal show={show} onHide={onHide} size="lg" centered>
      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>
        <Modal.Title className="text-white">
          <MessageCircle className="me-2" size={20} />
          Chat - {job?.jobNumber}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ background: '#1a1a1a', color: '#fff', height: '400px', overflowY: 'auto' }}>
        <div className="mb-3">
          {messages.map((message: any, index: number) => (
            <div key={index} className={`mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`}>
              <div className={`d-inline-block p-2 rounded ${
                message.isFromStudent
                  ? 'text-white'
                  : 'bg-secondary text-white'
              }`} style={{
                background: message.isFromStudent
                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                  : '#6c757d'
              }}>
                {message.content}
              </div>
              <div className="small text-muted">
                {new Date(message.timestamp).toLocaleString()}
              </div>
            </div>
          ))}
        </div>
      </Modal.Body>
      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>
        <InputGroup>
          <Form.Control
            type="text"
            placeholder="Type your message..."
            value={chatMessage}
            onChange={(e) => setChatMessage(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && onSendMessage()}
            style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}
          />
          <Button
            onClick={onSendMessage}
            style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}
          >
            <Send size={16} />
          </Button>
        </InputGroup>
      </Modal.Footer>
    </Modal>
  );
};

export default AceternityStudentDashboard;