{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\n/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = unit => ({\n  test: v => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n  parse: parseFloat,\n  transform: v => \"\".concat(v).concat(unit)\n});\nconst degrees = /*@__PURE__*/createUnitType(\"deg\");\nconst percent = /*@__PURE__*/createUnitType(\"%\");\nconst px = /*@__PURE__*/createUnitType(\"px\");\nconst vh = /*@__PURE__*/createUnitType(\"vh\");\nconst vw = /*@__PURE__*/createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/(() => _objectSpread(_objectSpread({}, percent), {}, {\n  parse: v => percent.parse(v) / 100,\n  transform: v => percent.transform(v * 100)\n}))();\nexport { degrees, percent, progressPercentage, px, vh, vw };", "map": {"version": 3, "names": ["createUnitType", "unit", "test", "v", "endsWith", "split", "length", "parse", "parseFloat", "transform", "concat", "degrees", "percent", "px", "vh", "vw", "progressPercentage", "_objectSpread"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst createUnitType = (unit) => ({\n    test: (v) => typeof v === \"string\" && v.endsWith(unit) && v.split(\" \").length === 1,\n    parse: parseFloat,\n    transform: (v) => `${v}${unit}`,\n});\nconst degrees = /*@__PURE__*/ createUnitType(\"deg\");\nconst percent = /*@__PURE__*/ createUnitType(\"%\");\nconst px = /*@__PURE__*/ createUnitType(\"px\");\nconst vh = /*@__PURE__*/ createUnitType(\"vh\");\nconst vw = /*@__PURE__*/ createUnitType(\"vw\");\nconst progressPercentage = /*@__PURE__*/ (() => ({\n    ...percent,\n    parse: (v) => percent.parse(v) / 100,\n    transform: (v) => percent.transform(v * 100),\n}))();\n\nexport { degrees, percent, progressPercentage, px, vh, vw };\n"], "mappings": ";AAAA;AACA,MAAMA,cAAc,GAAIC,IAAI,KAAM;EAC9BC,IAAI,EAAGC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,CAACC,QAAQ,CAACH,IAAI,CAAC,IAAIE,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC;EACnFC,KAAK,EAAEC,UAAU;EACjBC,SAAS,EAAGN,CAAC,OAAAO,MAAA,CAAQP,CAAC,EAAAO,MAAA,CAAGT,IAAI;AACjC,CAAC,CAAC;AACF,MAAMU,OAAO,GAAG,aAAcX,cAAc,CAAC,KAAK,CAAC;AACnD,MAAMY,OAAO,GAAG,aAAcZ,cAAc,CAAC,GAAG,CAAC;AACjD,MAAMa,EAAE,GAAG,aAAcb,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMc,EAAE,GAAG,aAAcd,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMe,EAAE,GAAG,aAAcf,cAAc,CAAC,IAAI,CAAC;AAC7C,MAAMgB,kBAAkB,GAAG,aAAc,CAAC,MAAAC,aAAA,CAAAA,aAAA,KACnCL,OAAO;EACVL,KAAK,EAAGJ,CAAC,IAAKS,OAAO,CAACL,KAAK,CAACJ,CAAC,CAAC,GAAG,GAAG;EACpCM,SAAS,EAAGN,CAAC,IAAKS,OAAO,CAACH,SAAS,CAACN,CAAC,GAAG,GAAG;AAAC,EAC9C,EAAE,CAAC;AAEL,SAASQ,OAAO,EAAEC,OAAO,EAAEI,kBAAkB,EAAEH,EAAE,EAAEC,EAAE,EAAEC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}