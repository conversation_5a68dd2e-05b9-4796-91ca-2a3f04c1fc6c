using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class XeroxCenter
    {
        public int XeroxCenterID { get; set; }
        
        public int UserID { get; set; }
        
        [Required]
        [StringLength(100)]
        public string XeroxCenterName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string Location { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string? ContactPerson { get; set; }
        
        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }
        
        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;
        
        public int TotalJobs { get; set; } = 0;
        
        public int PendingJobs { get; set; } = 0;
        
        public int CompletedJobs { get; set; } = 0;
        
        public decimal? AverageRating { get; set; }
        
        public int TotalRatings { get; set; } = 0;
        
        public bool IsActive { get; set; } = true;
        
        public int CreatedUserID { get; set; }
        
        public int? ModifiedUserID { get; set; }
        
        public DateTime Created { get; set; } = DateTime.UtcNow;
        
        public DateTime? Modified { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        // Navigation properties
        public User User { get; set; } = null!;
        public User CreatedUser { get; set; } = null!;
        public User? ModifiedUser { get; set; }
        public ICollection<PrintJob> PrintJobs { get; set; } = new List<PrintJob>();
        public ICollection<FileUpload> PreferredFileUploads { get; set; } = new List<FileUpload>();
        public ICollection<Rating> Ratings { get; set; } = new List<Rating>();
        
        // Computed properties
        public double WorkloadPercentage => TotalJobs > 0 ? (double)PendingJobs / TotalJobs * 100 : 0;
        public bool IsBusy => PendingJobs > 10; // Configurable threshold
    }
}
