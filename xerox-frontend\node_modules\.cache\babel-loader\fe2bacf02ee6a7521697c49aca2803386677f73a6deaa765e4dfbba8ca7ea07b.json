{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ProfessionalNavbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Navbar, Nav, NavDropdown, Container, Badge, Button, Offcanvas } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { Bell, User, Settings, LogOut, Menu, X, Home, FileText, Upload, MessageCircle, BarChart3, Store, Search, Moon, Sun, Printer, DollarSign } from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/ProfessionalNavbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfessionalNavbar = ({\n  onThemeToggle,\n  isDarkMode = false\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const [notifications, setNotifications] = useState(3); // Mock notification count\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState(null);\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const handleLogout = () => {\n    logout();\n  };\n  const getNavItems = () => {\n    if ((user === null || user === void 0 ? void 0 : user.userType) === 'Student') {\n      return [{\n        icon: Home,\n        label: 'Dashboard',\n        href: '/student-dashboard',\n        active: true\n      }, {\n        icon: Upload,\n        label: 'Upload File',\n        href: '/upload'\n      }, {\n        icon: FileText,\n        label: 'My Jobs',\n        href: '/my-jobs'\n      }, {\n        icon: MessageCircle,\n        label: 'Messages',\n        href: '/messages',\n        badge: 2\n      }, {\n        icon: Store,\n        label: 'Xerox Centers',\n        href: '/xerox-centers'\n      }];\n    } else if ((user === null || user === void 0 ? void 0 : user.userType) === 'XeroxCenter') {\n      return [{\n        icon: BarChart3,\n        label: 'Dashboard',\n        href: '/xerox-dashboard',\n        active: true\n      }, {\n        icon: FileText,\n        label: 'Job Queue',\n        href: '/job-queue'\n      }, {\n        icon: Printer,\n        label: 'Active Jobs',\n        href: '/active-jobs',\n        badge: 5\n      }, {\n        icon: MessageCircle,\n        label: 'Messages',\n        href: '/messages',\n        badge: 3\n      }, {\n        icon: DollarSign,\n        label: 'Revenue',\n        href: '/revenue'\n      }, {\n        icon: Settings,\n        label: 'Settings',\n        href: '/settings'\n      }];\n    }\n    return [];\n  };\n  const navItems = getNavItems();\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(motion.div, {\n      initial: {\n        y: -100,\n        opacity: 0\n      },\n      animate: {\n        y: 0,\n        opacity: 1\n      },\n      transition: {\n        duration: 0.6\n      },\n      className: `professional-navbar-wrapper ${isScrolled ? 'scrolled' : ''}`,\n      children: /*#__PURE__*/_jsxDEV(Navbar, {\n        expand: \"lg\",\n        className: `professional-navbar ${isDarkMode ? 'dark-mode' : ''}`,\n        fixed: \"top\",\n        children: /*#__PURE__*/_jsxDEV(Container, {\n          fluid: true,\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            children: /*#__PURE__*/_jsxDEV(Navbar.Brand, {\n              href: \"/\",\n              className: \"brand-logo my-10\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"logo-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"logo-icon\",\n                  children: /*#__PURE__*/_jsxDEV(Printer, {\n                    size: 28\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"logo-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"brand-name\",\n                    children: \"XeroxHub\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"brand-tagline\",\n                    children: \"Print Solutions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"logo-text\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \" \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"brand-tagline\",\n                    children: \" \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-lg-none\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline-light\",\n              className: \"mobile-menu-btn\",\n              onClick: () => setShowOffcanvas(true),\n              children: /*#__PURE__*/_jsxDEV(Menu, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n            id: \"navbar-nav\",\n            className: \"d-none d-lg-flex\",\n            children: [/*#__PURE__*/_jsxDEV(Nav, {\n              className: \"nav-items me-auto\",\n              children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  y: -2\n                },\n                whileTap: {\n                  y: 0\n                },\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                transition: {\n                  delay: index * 0.1\n                },\n                children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  href: item.href,\n                  className: `nav-item ${item.active ? 'active' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(item.icon, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 23\n                  }, this), item.badge && /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"danger\",\n                    className: \"nav-badge\",\n                    children: item.badge\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this)\n              }, item.label, false, {\n                fileName: _jsxFileName,\n                lineNumber: 130,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Nav, {\n              className: \"nav-actions\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                className: \"search-container\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"action-btn search-btn\",\n                  children: /*#__PURE__*/_jsxDEV(Search, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 162,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"action-btn theme-btn\",\n                  onClick: onThemeToggle,\n                  children: isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 35\n                  }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 55\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                whileHover: {\n                  scale: 1.05\n                },\n                className: \"notification-container\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  className: \"action-btn notification-btn\",\n                  children: [/*#__PURE__*/_jsxDEV(Bell, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 186,\n                    columnNumber: 21\n                  }, this), notifications > 0 && /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"danger\",\n                    className: \"notification-badge\",\n                    children: notifications\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(NavDropdown, {\n                title: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-profile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"user-avatar\",\n                    children: /*#__PURE__*/_jsxDEV(User, {\n                      size: 18\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"user-info d-none d-xl-block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-name\",\n                      children: user === null || user === void 0 ? void 0 : user.username\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 203,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"user-role\",\n                      children: user === null || user === void 0 ? void 0 : user.userType\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this),\n                id: \"user-dropdown\",\n                align: \"end\",\n                className: \"user-dropdown\",\n                children: [/*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  href: \"/profile\",\n                  children: [/*#__PURE__*/_jsxDEV(User, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this), \"Profile\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  href: \"/settings\",\n                  children: [/*#__PURE__*/_jsxDEV(Settings, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 21\n                  }, this), \"Settings\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Divider, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(NavDropdown.Item, {\n                  onClick: handleLogout,\n                  className: \"logout-item\",\n                  children: [/*#__PURE__*/_jsxDEV(LogOut, {\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this), \"Logout\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Offcanvas, {\n      show: showOffcanvas,\n      onHide: () => setShowOffcanvas(false),\n      placement: \"end\",\n      className: `mobile-offcanvas ${isDarkMode ? 'dark-mode' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(Offcanvas.Header, {\n        children: [/*#__PURE__*/_jsxDEV(Offcanvas.Title, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mobile-brand\",\n            children: [/*#__PURE__*/_jsxDEV(Printer, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"XeroxHub\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline-secondary\",\n          onClick: () => setShowOffcanvas(false),\n          className: \"close-btn\",\n          children: /*#__PURE__*/_jsxDEV(X, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Offcanvas.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-nav-items\",\n          children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n            href: item.href,\n            className: `mobile-nav-item ${item.active ? 'active' : ''}`,\n            initial: {\n              opacity: 0,\n              x: 50\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            onClick: () => setShowOffcanvas(false),\n            children: [/*#__PURE__*/_jsxDEV(item.icon, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), item.badge && /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"danger\",\n              className: \"mobile-nav-badge\",\n              children: item.badge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 19\n            }, this)]\n          }, item.label, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mobile-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-primary\",\n            className: \"mobile-action-btn\",\n            onClick: onThemeToggle,\n            children: [isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 29\n            }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 49\n            }, this), isDarkMode ? 'Light Mode' : 'Dark Mode']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-danger\",\n            className: \"mobile-action-btn\",\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(ProfessionalNavbar, \"1yFTNXQBgCUfPtRYXMY8dizH1fo=\", false, function () {\n  return [useAuth];\n});\n_c = ProfessionalNavbar;\nexport default ProfessionalNavbar;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalNavbar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "Nav", "NavDropdown", "Container", "Badge", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "motion", "Bell", "User", "Settings", "LogOut", "<PERSON><PERSON>", "X", "Home", "FileText", "Upload", "MessageCircle", "BarChart3", "Store", "Search", "Moon", "Sun", "Printer", "DollarSign", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfessionalNavbar", "onThemeToggle", "isDarkMode", "_s", "user", "logout", "showOffcanvas", "setShowOffcanvas", "notifications", "setNotifications", "isScrolled", "setIsScrolled", "activeDropdown", "setActiveDropdown", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "handleLogout", "getNavItems", "userType", "icon", "label", "href", "active", "badge", "navItems", "children", "div", "initial", "y", "opacity", "animate", "transition", "duration", "className", "expand", "fixed", "fluid", "whileHover", "scale", "whileTap", "Brand", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "Collapse", "id", "map", "item", "index", "delay", "Link", "bg", "title", "username", "align", "<PERSON><PERSON>", "Divider", "show", "onHide", "placement", "Header", "Title", "Body", "a", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ProfessionalNavbar.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Navbar, Nav, NavDropdown, Container, Badge, Button, Offcanvas } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Bell, \n  User, \n  Settings, \n  LogOut, \n  Menu, \n  X, \n  Home,\n  FileText,\n  Upload,\n  MessageCircle,\n  BarChart3,\n  Store,\n  Search,\n  Moon,\n  Sun,\n  Printer,\n  Download,\n  Clock,\n  DollarSign\n} from 'lucide-react';\nimport { useAuth } from '../contexts/AuthContext';\nimport '../styles/ProfessionalNavbar.css';\n\ninterface NavbarProps {\n  onThemeToggle?: () => void;\n  isDarkMode?: boolean;\n}\n\nconst ProfessionalNavbar: React.FC<NavbarProps> = ({ onThemeToggle, isDarkMode = false }) => {\n  const { user, logout } = useAuth();\n  const [showOffcanvas, setShowOffcanvas] = useState(false);\n  const [notifications, setNotifications] = useState(3); // Mock notification count\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const handleLogout = () => {\n    logout();\n  };\n\n\n\n  const getNavItems = () => {\n    if (user?.userType === 'Student') {\n      return [\n        { icon: Home, label: 'Dashboard', href: '/student-dashboard', active: true },\n        { icon: Upload, label: 'Upload File', href: '/upload' },\n        { icon: FileText, label: 'My Jobs', href: '/my-jobs' },\n        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 2 },\n        { icon: Store, label: 'Xerox Centers', href: '/xerox-centers' }\n      ];\n    } else if (user?.userType === 'XeroxCenter') {\n      return [\n        { icon: BarChart3, label: 'Dashboard', href: '/xerox-dashboard', active: true },\n        { icon: FileText, label: 'Job Queue', href: '/job-queue' },\n        { icon: Printer, label: 'Active Jobs', href: '/active-jobs', badge: 5 },\n        { icon: MessageCircle, label: 'Messages', href: '/messages', badge: 3 },\n        { icon: DollarSign, label: 'Revenue', href: '/revenue' },\n        { icon: Settings, label: 'Settings', href: '/settings' }\n      ];\n    }\n    return [];\n  };\n\n  const navItems = getNavItems();\n\n  return (\n    <>\n      <motion.div\n        initial={{ y: -100, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        transition={{ duration: 0.6 }}\n        className={`professional-navbar-wrapper ${isScrolled ? 'scrolled' : ''}`}\n      >\n        <Navbar \n          expand=\"lg\" \n          className={`professional-navbar ${isDarkMode ? 'dark-mode' : ''}`}\n          fixed=\"top\"\n        >\n          <Container fluid>\n            {/* Brand Logo */}\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Navbar.Brand href=\"/\" className=\"brand-logo my-10\">\n                <div className=\"logo-container\">\n                  <div className=\"logo-icon\">\n                    <Printer size={28} />\n                  </div>\n                  <div className=\"logo-text\">\n                    <span className=\"brand-name\">XeroxHub</span>\n                    <span className=\"brand-tagline\">Print Solutions</span>\n                  </div>\n                  <div className=\"logo-text\">\n                    <span > </span>\n                    <span className=\"brand-tagline\"> </span>\n                  </div>\n                </div>\n              </Navbar.Brand>\n            </motion.div>\n\n            {/* Mobile Menu Toggle */}\n            <div className=\"d-lg-none\">\n              <Button\n                variant=\"outline-light\"\n                className=\"mobile-menu-btn\"\n                onClick={() => setShowOffcanvas(true)}\n              >\n                <Menu size={20} />\n              </Button>\n            </div>\n\n            {/* Desktop Navigation */}\n            <Navbar.Collapse id=\"navbar-nav\" className=\"d-none d-lg-flex\">\n              <Nav className=\"nav-items me-auto\">\n                {navItems.map((item, index) => (\n                  <motion.div\n                    key={item.label}\n                    whileHover={{ y: -2 }}\n                    whileTap={{ y: 0 }}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Nav.Link \n                      href={item.href}\n                      className={`nav-item ${item.active ? 'active' : ''}`}\n                    >\n                      <item.icon size={18} />\n                      <span>{item.label}</span>\n                      {item.badge && (\n                        <Badge bg=\"danger\" className=\"nav-badge\">\n                          {item.badge}\n                        </Badge>\n                      )}\n                    </Nav.Link>\n                  </motion.div>\n                ))}\n              </Nav>\n\n              {/* Right Side Actions */}\n              <Nav className=\"nav-actions\">\n                {/* Search */}\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  className=\"search-container\"\n                >\n                  <Button variant=\"outline-light\" className=\"action-btn search-btn\">\n                    <Search size={18} />\n                  </Button>\n                </motion.div>\n\n                {/* Theme Toggle */}\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Button\n                    variant=\"outline-light\"\n                    className=\"action-btn theme-btn\"\n                    onClick={onThemeToggle}\n                  >\n                    {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}\n                  </Button>\n                </motion.div>\n\n                {/* Notifications */}\n                <motion.div\n                  whileHover={{ scale: 1.05 }}\n                  className=\"notification-container\"\n                >\n                  <Button variant=\"outline-light\" className=\"action-btn notification-btn\">\n                    <Bell size={18} />\n                    {notifications > 0 && (\n                      <Badge bg=\"danger\" className=\"notification-badge\">\n                        {notifications}\n                      </Badge>\n                    )}\n                  </Button>\n                </motion.div>\n\n                {/* User Profile Dropdown */}\n                <NavDropdown\n                  title={\n                    <div className=\"user-profile\">\n                      <div className=\"user-avatar\">\n                        <User size={18} />\n                      </div>\n                      <div className=\"user-info d-none d-xl-block\">\n                        <span className=\"user-name\">{user?.username}</span>\n                        <span className=\"user-role\">{user?.userType}</span>\n                      </div>\n                    </div>\n                  }\n                  id=\"user-dropdown\"\n                  align=\"end\"\n                  className=\"user-dropdown\"\n                >\n                  <NavDropdown.Item href=\"/profile\">\n                    <User size={16} />\n                    Profile\n                  </NavDropdown.Item>\n                  <NavDropdown.Item href=\"/settings\">\n                    <Settings size={16} />\n                    Settings\n                  </NavDropdown.Item>\n                  <NavDropdown.Divider />\n                  <NavDropdown.Item onClick={handleLogout} className=\"logout-item\">\n                    <LogOut size={16} />\n                    Logout\n                  </NavDropdown.Item>\n                </NavDropdown>\n              </Nav>\n            </Navbar.Collapse>\n          </Container>\n        </Navbar>\n      </motion.div>\n\n      {/* Mobile Offcanvas Menu */}\n      <Offcanvas\n        show={showOffcanvas}\n        onHide={() => setShowOffcanvas(false)}\n        placement=\"end\"\n        className={`mobile-offcanvas ${isDarkMode ? 'dark-mode' : ''}`}\n      >\n        <Offcanvas.Header>\n          <Offcanvas.Title>\n            <div className=\"mobile-brand\">\n              <Printer size={24} />\n              <span>XeroxHub</span>\n            </div>\n          </Offcanvas.Title>\n          <Button\n            variant=\"outline-secondary\"\n            onClick={() => setShowOffcanvas(false)}\n            className=\"close-btn\"\n          >\n            <X size={20} />\n          </Button>\n        </Offcanvas.Header>\n        <Offcanvas.Body>\n          <div className=\"mobile-nav-items\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.label}\n                href={item.href}\n                className={`mobile-nav-item ${item.active ? 'active' : ''}`}\n                initial={{ opacity: 0, x: 50 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.1 }}\n                onClick={() => setShowOffcanvas(false)}\n              >\n                <item.icon size={20} />\n                <span>{item.label}</span>\n                {item.badge && (\n                  <Badge bg=\"danger\" className=\"mobile-nav-badge\">\n                    {item.badge}\n                  </Badge>\n                )}\n              </motion.a>\n            ))}\n          </div>\n\n          <div className=\"mobile-actions\">\n            <Button\n              variant=\"outline-primary\"\n              className=\"mobile-action-btn\"\n              onClick={onThemeToggle}\n            >\n              {isDarkMode ? <Sun size={18} /> : <Moon size={18} />}\n              {isDarkMode ? 'Light Mode' : 'Dark Mode'}\n            </Button>\n            \n            <Button\n              variant=\"outline-danger\"\n              className=\"mobile-action-btn\"\n              onClick={handleLogout}\n            >\n              <LogOut size={18} />\n              Logout\n            </Button>\n          </div>\n        </Offcanvas.Body>\n      </Offcanvas>\n    </>\n  );\n};\n\nexport default ProfessionalNavbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,GAAG,EAAEC,WAAW,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,iBAAiB;AAC/F,SAASC,MAAM,QAAyB,eAAe;AACvD,SACEC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,IAAI,EACJC,CAAC,EACDC,IAAI,EACJC,QAAQ,EACRC,MAAM,EACNC,aAAa,EACbC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,OAAO,EAGPC,UAAU,QACL,cAAc;AACrB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,OAAO,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAO1C,MAAMC,kBAAyC,GAAGA,CAAC;EAAEC,aAAa;EAAEC,UAAU,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC3F,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGV,OAAO,CAAC,CAAC;EAClC,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAgB,IAAI,CAAC;EAEzEC,SAAS,CAAC,MAAM;IACd,MAAM6C,YAAY,GAAGA,CAAA,KAAM;MACzBH,aAAa,CAACI,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IAEDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBd,MAAM,CAAC,CAAC;EACV,CAAC;EAID,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ,MAAK,SAAS,EAAE;MAChC,OAAO,CACL;QAAEC,IAAI,EAAEtC,IAAI;QAAEuC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE,oBAAoB;QAAEC,MAAM,EAAE;MAAK,CAAC,EAC5E;QAAEH,IAAI,EAAEpC,MAAM;QAAEqC,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE;MAAU,CAAC,EACvD;QAAEF,IAAI,EAAErC,QAAQ;QAAEsC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC,EACtD;QAAEF,IAAI,EAAEnC,aAAa;QAAEoC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEE,KAAK,EAAE;MAAE,CAAC,EACvE;QAAEJ,IAAI,EAAEjC,KAAK;QAAEkC,KAAK,EAAE,eAAe;QAAEC,IAAI,EAAE;MAAiB,CAAC,CAChE;IACH,CAAC,MAAM,IAAI,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,QAAQ,MAAK,aAAa,EAAE;MAC3C,OAAO,CACL;QAAEC,IAAI,EAAElC,SAAS;QAAEmC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE,kBAAkB;QAAEC,MAAM,EAAE;MAAK,CAAC,EAC/E;QAAEH,IAAI,EAAErC,QAAQ;QAAEsC,KAAK,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAa,CAAC,EAC1D;QAAEF,IAAI,EAAE7B,OAAO;QAAE8B,KAAK,EAAE,aAAa;QAAEC,IAAI,EAAE,cAAc;QAAEE,KAAK,EAAE;MAAE,CAAC,EACvE;QAAEJ,IAAI,EAAEnC,aAAa;QAAEoC,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEE,KAAK,EAAE;MAAE,CAAC,EACvE;QAAEJ,IAAI,EAAE5B,UAAU;QAAE6B,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAW,CAAC,EACxD;QAAEF,IAAI,EAAE1C,QAAQ;QAAE2C,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAY,CAAC,CACzD;IACH;IACA,OAAO,EAAE;EACX,CAAC;EAED,MAAMG,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAE9B,oBACEvB,OAAA,CAAAE,SAAA;IAAA6B,QAAA,gBACE/B,OAAA,CAACpB,MAAM,CAACoD,GAAG;MACTC,OAAO,EAAE;QAAEC,CAAC,EAAE,CAAC,GAAG;QAAEC,OAAO,EAAE;MAAE,CAAE;MACjCC,OAAO,EAAE;QAAEF,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE,CAAE;MAC9BE,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI,CAAE;MAC9BC,SAAS,EAAE,+BAA+B1B,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;MAAAkB,QAAA,eAEzE/B,OAAA,CAAC3B,MAAM;QACLmE,MAAM,EAAC,IAAI;QACXD,SAAS,EAAE,uBAAuBlC,UAAU,GAAG,WAAW,GAAG,EAAE,EAAG;QAClEoC,KAAK,EAAC,KAAK;QAAAV,QAAA,eAEX/B,OAAA,CAACxB,SAAS;UAACkE,KAAK;UAAAX,QAAA,gBAEd/B,OAAA,CAACpB,MAAM,CAACoD,GAAG;YACTW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAAAb,QAAA,eAE1B/B,OAAA,CAAC3B,MAAM,CAACyE,KAAK;cAACnB,IAAI,EAAC,GAAG;cAACY,SAAS,EAAC,kBAAkB;cAAAR,QAAA,eACjD/B,OAAA;gBAAKuC,SAAS,EAAC,gBAAgB;gBAAAR,QAAA,gBAC7B/B,OAAA;kBAAKuC,SAAS,EAAC,WAAW;kBAAAR,QAAA,eACxB/B,OAAA,CAACJ,OAAO;oBAACmD,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACNnD,OAAA;kBAAKuC,SAAS,EAAC,WAAW;kBAAAR,QAAA,gBACxB/B,OAAA;oBAAMuC,SAAS,EAAC,YAAY;oBAAAR,QAAA,EAAC;kBAAQ;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC5CnD,OAAA;oBAAMuC,SAAS,EAAC,eAAe;oBAAAR,QAAA,EAAC;kBAAe;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACNnD,OAAA;kBAAKuC,SAAS,EAAC,WAAW;kBAAAR,QAAA,gBACxB/B,OAAA;oBAAA+B,QAAA,EAAO;kBAAC;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACfnD,OAAA;oBAAMuC,SAAS,EAAC,eAAe;oBAAAR,QAAA,EAAC;kBAAC;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGbnD,OAAA;YAAKuC,SAAS,EAAC,WAAW;YAAAR,QAAA,eACxB/B,OAAA,CAACtB,MAAM;cACL0E,OAAO,EAAC,eAAe;cACvBb,SAAS,EAAC,iBAAiB;cAC3Bc,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAAC,IAAI,CAAE;cAAAqB,QAAA,eAEtC/B,OAAA,CAACf,IAAI;gBAAC8D,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNnD,OAAA,CAAC3B,MAAM,CAACiF,QAAQ;YAACC,EAAE,EAAC,YAAY;YAAChB,SAAS,EAAC,kBAAkB;YAAAR,QAAA,gBAC3D/B,OAAA,CAAC1B,GAAG;cAACiE,SAAS,EAAC,mBAAmB;cAAAR,QAAA,EAC/BD,QAAQ,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxB1D,OAAA,CAACpB,MAAM,CAACoD,GAAG;gBAETW,UAAU,EAAE;kBAAET,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACtBW,QAAQ,EAAE;kBAAEX,CAAC,EAAE;gBAAE,CAAE;gBACnBD,OAAO,EAAE;kBAAEE,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAG,CAAE;gBAC/BE,OAAO,EAAE;kBAAED,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAE,CAAE;gBAC9BG,UAAU,EAAE;kBAAEsB,KAAK,EAAED,KAAK,GAAG;gBAAI,CAAE;gBAAA3B,QAAA,eAEnC/B,OAAA,CAAC1B,GAAG,CAACsF,IAAI;kBACPjC,IAAI,EAAE8B,IAAI,CAAC9B,IAAK;kBAChBY,SAAS,EAAE,YAAYkB,IAAI,CAAC7B,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;kBAAAG,QAAA,gBAErD/B,OAAA,CAACyD,IAAI,CAAChC,IAAI;oBAACsB,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACvBnD,OAAA;oBAAA+B,QAAA,EAAO0B,IAAI,CAAC/B;kBAAK;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,EACxBM,IAAI,CAAC5B,KAAK,iBACT7B,OAAA,CAACvB,KAAK;oBAACoF,EAAE,EAAC,QAAQ;oBAACtB,SAAS,EAAC,WAAW;oBAAAR,QAAA,EACrC0B,IAAI,CAAC5B;kBAAK;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cAAC,GAlBNM,IAAI,CAAC/B,KAAK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmBL,CACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNnD,OAAA,CAAC1B,GAAG;cAACiE,SAAS,EAAC,aAAa;cAAAR,QAAA,gBAE1B/B,OAAA,CAACpB,MAAM,CAACoD,GAAG;gBACTW,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BL,SAAS,EAAC,kBAAkB;gBAAAR,QAAA,eAE5B/B,OAAA,CAACtB,MAAM;kBAAC0E,OAAO,EAAC,eAAe;kBAACb,SAAS,EAAC,uBAAuB;kBAAAR,QAAA,eAC/D/B,OAAA,CAACP,MAAM;oBAACsD,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGbnD,OAAA,CAACpB,MAAM,CAACoD,GAAG;gBACTW,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BC,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAK,CAAE;gBAAAb,QAAA,eAE1B/B,OAAA,CAACtB,MAAM;kBACL0E,OAAO,EAAC,eAAe;kBACvBb,SAAS,EAAC,sBAAsB;kBAChCc,OAAO,EAAEjD,aAAc;kBAAA2B,QAAA,EAEtB1B,UAAU,gBAAGL,OAAA,CAACL,GAAG;oBAACoD,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACN,IAAI;oBAACqD,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGbnD,OAAA,CAACpB,MAAM,CAACoD,GAAG;gBACTW,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAK,CAAE;gBAC5BL,SAAS,EAAC,wBAAwB;gBAAAR,QAAA,eAElC/B,OAAA,CAACtB,MAAM;kBAAC0E,OAAO,EAAC,eAAe;kBAACb,SAAS,EAAC,6BAA6B;kBAAAR,QAAA,gBACrE/B,OAAA,CAACnB,IAAI;oBAACkE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACjBxC,aAAa,GAAG,CAAC,iBAChBX,OAAA,CAACvB,KAAK;oBAACoF,EAAE,EAAC,QAAQ;oBAACtB,SAAS,EAAC,oBAAoB;oBAAAR,QAAA,EAC9CpB;kBAAa;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CACR;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGbnD,OAAA,CAACzB,WAAW;gBACVuF,KAAK,eACH9D,OAAA;kBAAKuC,SAAS,EAAC,cAAc;kBAAAR,QAAA,gBAC3B/B,OAAA;oBAAKuC,SAAS,EAAC,aAAa;oBAAAR,QAAA,eAC1B/B,OAAA,CAAClB,IAAI;sBAACiE,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,eACNnD,OAAA;oBAAKuC,SAAS,EAAC,6BAA6B;oBAAAR,QAAA,gBAC1C/B,OAAA;sBAAMuC,SAAS,EAAC,WAAW;sBAAAR,QAAA,EAAExB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD;oBAAQ;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACnDnD,OAAA;sBAAMuC,SAAS,EAAC,WAAW;sBAAAR,QAAA,EAAExB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;oBAAQ;sBAAAwB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;gBACDI,EAAE,EAAC,eAAe;gBAClBS,KAAK,EAAC,KAAK;gBACXzB,SAAS,EAAC,eAAe;gBAAAR,QAAA,gBAEzB/B,OAAA,CAACzB,WAAW,CAAC0F,IAAI;kBAACtC,IAAI,EAAC,UAAU;kBAAAI,QAAA,gBAC/B/B,OAAA,CAAClB,IAAI;oBAACiE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAEpB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB,CAAC,eACnBnD,OAAA,CAACzB,WAAW,CAAC0F,IAAI;kBAACtC,IAAI,EAAC,WAAW;kBAAAI,QAAA,gBAChC/B,OAAA,CAACjB,QAAQ;oBAACgE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,YAExB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB,CAAC,eACnBnD,OAAA,CAACzB,WAAW,CAAC2F,OAAO;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvBnD,OAAA,CAACzB,WAAW,CAAC0F,IAAI;kBAACZ,OAAO,EAAE/B,YAAa;kBAACiB,SAAS,EAAC,aAAa;kBAAAR,QAAA,gBAC9D/B,OAAA,CAAChB,MAAM;oBAAC+D,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEtB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAkB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGbnD,OAAA,CAACrB,SAAS;MACRwF,IAAI,EAAE1D,aAAc;MACpB2D,MAAM,EAAEA,CAAA,KAAM1D,gBAAgB,CAAC,KAAK,CAAE;MACtC2D,SAAS,EAAC,KAAK;MACf9B,SAAS,EAAE,oBAAoBlC,UAAU,GAAG,WAAW,GAAG,EAAE,EAAG;MAAA0B,QAAA,gBAE/D/B,OAAA,CAACrB,SAAS,CAAC2F,MAAM;QAAAvC,QAAA,gBACf/B,OAAA,CAACrB,SAAS,CAAC4F,KAAK;UAAAxC,QAAA,eACd/B,OAAA;YAAKuC,SAAS,EAAC,cAAc;YAAAR,QAAA,gBAC3B/B,OAAA,CAACJ,OAAO;cAACmD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrBnD,OAAA;cAAA+B,QAAA,EAAM;YAAQ;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAClBnD,OAAA,CAACtB,MAAM;UACL0E,OAAO,EAAC,mBAAmB;UAC3BC,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAAC,KAAK,CAAE;UACvC6B,SAAS,EAAC,WAAW;UAAAR,QAAA,eAErB/B,OAAA,CAACd,CAAC;YAAC6D,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eACnBnD,OAAA,CAACrB,SAAS,CAAC6F,IAAI;QAAAzC,QAAA,gBACb/B,OAAA;UAAKuC,SAAS,EAAC,kBAAkB;UAAAR,QAAA,EAC9BD,QAAQ,CAAC0B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxB1D,OAAA,CAACpB,MAAM,CAAC6F,CAAC;YAEP9C,IAAI,EAAE8B,IAAI,CAAC9B,IAAK;YAChBY,SAAS,EAAE,mBAAmBkB,IAAI,CAAC7B,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC5DK,OAAO,EAAE;cAAEE,OAAO,EAAE,CAAC;cAAEuC,CAAC,EAAE;YAAG,CAAE;YAC/BtC,OAAO,EAAE;cAAED,OAAO,EAAE,CAAC;cAAEuC,CAAC,EAAE;YAAE,CAAE;YAC9BrC,UAAU,EAAE;cAAEsB,KAAK,EAAED,KAAK,GAAG;YAAI,CAAE;YACnCL,OAAO,EAAEA,CAAA,KAAM3C,gBAAgB,CAAC,KAAK,CAAE;YAAAqB,QAAA,gBAEvC/B,OAAA,CAACyD,IAAI,CAAChC,IAAI;cAACsB,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvBnD,OAAA;cAAA+B,QAAA,EAAO0B,IAAI,CAAC/B;YAAK;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxBM,IAAI,CAAC5B,KAAK,iBACT7B,OAAA,CAACvB,KAAK;cAACoF,EAAE,EAAC,QAAQ;cAACtB,SAAS,EAAC,kBAAkB;cAAAR,QAAA,EAC5C0B,IAAI,CAAC5B;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACR;UAAA,GAdIM,IAAI,CAAC/B,KAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeP,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENnD,OAAA;UAAKuC,SAAS,EAAC,gBAAgB;UAAAR,QAAA,gBAC7B/B,OAAA,CAACtB,MAAM;YACL0E,OAAO,EAAC,iBAAiB;YACzBb,SAAS,EAAC,mBAAmB;YAC7Bc,OAAO,EAAEjD,aAAc;YAAA2B,QAAA,GAEtB1B,UAAU,gBAAGL,OAAA,CAACL,GAAG;cAACoD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnD,OAAA,CAACN,IAAI;cAACqD,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACnD9C,UAAU,GAAG,YAAY,GAAG,WAAW;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAETnD,OAAA,CAACtB,MAAM;YACL0E,OAAO,EAAC,gBAAgB;YACxBb,SAAS,EAAC,mBAAmB;YAC7Bc,OAAO,EAAE/B,YAAa;YAAAS,QAAA,gBAEtB/B,OAAA,CAAChB,MAAM;cAAC+D,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA,eACZ,CAAC;AAEP,CAAC;AAAC7C,EAAA,CA3QIH,kBAAyC;EAAA,QACpBL,OAAO;AAAA;AAAA6E,EAAA,GAD5BxE,kBAAyC;AA6Q/C,eAAeA,kBAAkB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}