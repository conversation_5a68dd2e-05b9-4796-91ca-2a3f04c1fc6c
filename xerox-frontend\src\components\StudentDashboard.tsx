import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';
import { useAuth } from '../contexts/AuthContext';
import { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';

interface PrintJob {
  id: number;
  jobNumber: string;
  fileName: string;
  status: string;
  cost?: number;
  estimatedCompletionTime?: string;
  xeroxCenterName: string;
  created: string;
}

interface XeroxCenter {
  id: number;
  name: string;
  location: string;
  pendingJobs: number;
  averageRating: number;
  isActive: boolean;
}

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);
  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showChatModal, setShowChatModal] = useState(false);
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [chatMessage, setChatMessage] = useState('');
  const [messages, setMessages] = useState<any[]>([]);
  const [uploadData, setUploadData] = useState({
    remarks: '',
    preferredXeroxCenterId: '',
    printType: 'Print',
    copies: 1,
    colorType: 'BlackWhite',
    paperSize: 'A4'
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch print jobs
        const printJobsResponse = await printJobApi.getStudentJobs();
        setPrintJobs(printJobsResponse.data);

        // Fetch xerox centers
        const xeroxCentersResponse = await xeroxCenterApi.getAll();
        setXeroxCenters(xeroxCentersResponse.data);
      } catch (error) {
        console.error('Error fetching data:', error);
        // Set empty arrays on error
        setPrintJobs([]);
        setXeroxCenters([]);
      }
    };

    fetchData();
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Requested': { variant: 'secondary', icon: 'clock' },
      'UnderReview': { variant: 'info', icon: 'eye' },
      'Quoted': { variant: 'warning', icon: 'dollar-sign' },
      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },
      'Confirmed': { variant: 'primary', icon: 'check' },
      'InProgress': { variant: 'info', icon: 'cog' },
      'Completed': { variant: 'success', icon: 'check-circle' },
      'Delivered': { variant: 'success', icon: 'truck' },
      'Rejected': { variant: 'danger', icon: 'times' },
      'Cancelled': { variant: 'dark', icon: 'ban' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };
    
    return (
      <Badge bg={config.variant}>
        <i className={`fas fa-${config.icon} me-1`}></i>
        {status}
      </Badge>
    );
  };

  const getWorkloadColor = (pendingJobs: number) => {
    if (pendingJobs <= 5) return 'success';
    if (pendingJobs <= 10) return 'warning';
    return 'danger';
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('remarks', uploadData.remarks);
      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);
      formData.append('printType', uploadData.printType);
      formData.append('copies', uploadData.copies.toString());
      formData.append('colorType', uploadData.colorType);
      formData.append('paperSize', uploadData.paperSize);

      await fileUploadApi.uploadFile(formData);

      // Refresh print jobs after upload
      const printJobsResponse = await printJobApi.getStudentJobs();
      setPrintJobs(printJobsResponse.data);

      setShowUploadModal(false);
      setSelectedFile(null);
      setUploadData({
        remarks: '',
        preferredXeroxCenterId: '',
        printType: 'Print',
        copies: 1,
        colorType: 'BlackWhite',
        paperSize: 'A4'
      });
    } catch (error) {
      console.error('Error uploading file:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleViewJob = (job: any) => {
    setSelectedJob(job);
    setShowViewModal(true);
  };

  const handleOpenChat = async (job: any) => {
    try {
      setSelectedJob(job);
      setShowChatModal(true);

      // Load messages for this job
      const response = await messageApi.getJobMessages(job.id);
      setMessages(response.data);
    } catch (error) {
      console.error('Error loading messages:', error);
      setMessages([]);
    }
  };

  const handleDownloadFile = async (jobId: number, fileName: string) => {
    try {
      const response = await fileUploadApi.downloadFile(jobId);

      // Create blob URL and trigger download
      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error downloading file:', error);
      // You might want to show an error message to the user here
    }
  };

  const handleConfirmJob = async (jobId: number) => {
    try {
      await printJobApi.confirmJob(jobId);

      // Refresh print jobs after confirmation
      const printJobsResponse = await printJobApi.getStudentJobs();
      setPrintJobs(printJobsResponse.data);
    } catch (error) {
      console.error('Error confirming job:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!chatMessage.trim() || !selectedJob) return;

    try {
      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());

      // Add the new message to the list
      setMessages(prev => [...prev, response.data]);
      setChatMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h2>
            <i className="fas fa-tachometer-alt me-2"></i>
            Student Dashboard
          </h2>
          <p className="text-muted">Welcome back, {user?.username}!</p>
        </Col>
        <Col xs="auto">
          <Button variant="primary" onClick={() => setShowUploadModal(true)}>
            <i className="fas fa-upload me-2"></i>
            Upload Files
          </Button>
        </Col>
      </Row>

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <i className="fas fa-file-alt fa-2x text-primary mb-2"></i>
              <h5>Total Jobs</h5>
              <h3 className="text-primary">{printJobs.length}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <i className="fas fa-clock fa-2x text-warning mb-2"></i>
              <h5>In Progress</h5>
              <h3 className="text-warning">
                {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}
              </h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <i className="fas fa-check-circle fa-2x text-success mb-2"></i>
              <h5>Completed</h5>
              <h3 className="text-success">
                {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}
              </h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <i className="fas fa-dollar-sign fa-2x text-info mb-2"></i>
              <h5>Total Spent</h5>
              <h3 className="text-info">
                ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}
              </h3>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        {/* Recent Jobs */}
        <Col lg={8}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-list me-2"></i>
                Recent Print Jobs
              </h5>
            </Card.Header>
            <Card.Body>
              {printJobs.length > 0 ? (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Job #</th>
                      <th>File Name</th>
                      <th>Status</th>
                      <th>Cost</th>
                      <th>Xerox Center</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {printJobs.map(job => (
                      <tr key={job.id}>
                        <td>
                          <strong>{job.jobNumber}</strong>
                        </td>
                        <td>
                          <i className="fas fa-file-pdf me-2 text-danger"></i>
                          {job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName}
                        </td>
                        <td>{getStatusBadge(job.status)}</td>
                        <td>
                          {job.cost ? `$${job.cost.toFixed(2)}` : '-'}
                        </td>
                        <td>{job.xeroxCenterName}</td>
                        <td>
                          <Button
                            variant="outline-info"
                            size="sm"
                            onClick={() => handleDownloadFile(job.id, job.fileName)}
                            title="Download File"
                          >
                            <i className="fas fa-download me-1"></i>
                            Download
                          </Button>
                          <Button
                            variant="outline-primary"
                            size="sm"
                            className="me-1"
                            onClick={() => handleViewJob(job)}
                            title="View Details"
                          >
                            <i className="fas fa-eye"></i>
                          </Button>
                          <Button
                            variant="outline-secondary"
                            size="sm"
                            className="me-1"
                            onClick={() => handleOpenChat(job)}
                            title="Chat"
                          >
                            <i className="fas fa-comment"></i>
                          </Button>
                          {job.status === 'Quoted' && (
                            <Button
                              variant="success"
                              size="sm"
                              onClick={() => handleConfirmJob(job.id)}
                              title="Confirm Quote"
                            >
                              <i className="fas fa-check"></i>
                            </Button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              ) : (
                <Alert variant="info">
                  <i className="fas fa-info-circle me-2"></i>
                  No print jobs yet. Upload your first file to get started!
                </Alert>
              )}
            </Card.Body>
          </Card>
        </Col>

        {/* Xerox Centers */}
        <Col lg={4}>
          <Card>
            <Card.Header>
              <h5 className="mb-0">
                <i className="fas fa-store me-2"></i>
                Available Centers
              </h5>
            </Card.Header>
            <Card.Body>
              {xeroxCenters.map(center => (
                <Card key={center.id} className="mb-3 border-0 bg-light">
                  <Card.Body className="p-3">
                    <div className="d-flex justify-content-between align-items-start mb-2">
                      <h6 className="mb-1">{center.name}</h6>
                      <Badge bg={getWorkloadColor(center.pendingJobs)}>
                        {center.pendingJobs} jobs
                      </Badge>
                    </div>
                    <p className="text-muted small mb-2">
                      <i className="fas fa-map-marker-alt me-1"></i>
                      {center.location}
                    </p>
                    <div className="d-flex justify-content-between align-items-center">
                      <div>
                        <i className="fas fa-star text-warning me-1"></i>
                        <span className="small">{center.averageRating.toFixed(1)}</span>
                      </div>
                      <Button variant="outline-primary" size="sm">
                        Select
                      </Button>
                    </div>
                  </Card.Body>
                </Card>
              ))}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Upload Modal */}
      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-upload me-2"></i>
            Upload Files for Printing
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Select File</Form.Label>
              <Form.Control
                type="file"
                accept=".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png"
                onChange={(e) => {
                  const files = (e.target as HTMLInputElement).files;
                  setSelectedFile(files ? files[0] : null);
                }}
              />
              <Form.Text className="text-muted">
                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)
              </Form.Text>
            </Form.Group>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Print Type</Form.Label>
                  <Form.Select
                    value={uploadData.printType}
                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}
                  >
                    <option value="Print">Print</option>
                    <option value="Xerox">Xerox</option>
                    <option value="Binding">Binding</option>
                    <option value="Lamination">Lamination</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Number of Copies</Form.Label>
                  <Form.Control
                    type="number"
                    min="1"
                    value={uploadData.copies}
                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Color Type</Form.Label>
                  <Form.Select
                    value={uploadData.colorType}
                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}
                  >
                    <option value="BlackWhite">Black & White</option>
                    <option value="Color">Color</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Paper Size</Form.Label>
                  <Form.Select
                    value={uploadData.paperSize}
                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}
                  >
                    <option value="A4">A4</option>
                    <option value="A3">A3</option>
                    <option value="Letter">Letter</option>
                    <option value="Legal">Legal</option>
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Form.Group className="mb-3">
              <Form.Label>Preferred Xerox Center</Form.Label>
              <Form.Select
                value={uploadData.preferredXeroxCenterId}
                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}
              >
                <option value="">Select a center (optional)</option>
                {xeroxCenters.map(center => (
                  <option key={center.id} value={center.id}>
                    {center.name} - {center.pendingJobs} pending jobs
                  </option>
                ))}
              </Form.Select>
            </Form.Group>

            <Form.Group className="mb-3">
              <Form.Label>Remarks</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                placeholder="Any special instructions or remarks..."
                value={uploadData.remarks}
                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowUploadModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handleFileUpload} disabled={!selectedFile}>
            <i className="fas fa-upload me-2"></i>
            Upload File
          </Button>
        </Modal.Footer>
      </Modal>

      {/* View Job Details Modal */}
      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-eye me-2"></i>
            Job Details - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedJob && (
            <div>
              <Row>
                <Col md={6}>
                  <h6>File Information</h6>
                  <hr />
                  <p><strong>File Name:</strong> {selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName}</p>
                  <p><strong>Print Type:</strong> {selectedJob.printType}</p>
                  <p><strong>Copies:</strong> {selectedJob.copies}</p>
                  <p><strong>Color Type:</strong> {selectedJob.colorType}</p>
                  <p><strong>Paper Size:</strong> {selectedJob.paperSize}</p>
                </Col>
                <Col md={6}>
                  <h6>Job Information</h6>
                  <hr />
                  <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>
                  <p><strong>Cost:</strong> {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}</p>
                  <p><strong>Xerox Center:</strong> {selectedJob.xeroxCenterName}</p>
                  <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>
                  {selectedJob.estimatedCompletionTime && (
                    <p><strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}</p>
                  )}
                </Col>
              </Row>
              {selectedJob.remarks && (
                <div className="mt-3">
                  <h6>Remarks</h6>
                  <p className="text-muted">{selectedJob.remarks}</p>
                </div>
              )}
              {selectedJob.status === 'Quoted' && (
                <Alert variant="warning" className="mt-3">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  This job has been quoted. Please confirm to proceed with printing.
                </Alert>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          {selectedJob?.status === 'Quoted' && (
            <Button
              variant="success"
              onClick={() => {
                handleConfirmJob(selectedJob.id);
                setShowViewModal(false);
              }}
            >
              <i className="fas fa-check me-2"></i>
              Confirm Quote
            </Button>
          )}
          <Button variant="secondary" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Chat Modal */}
      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            <i className="fas fa-comment me-2"></i>
            Chat - {selectedJob?.jobNumber}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>
            {messages.length > 0 ? (
              messages.map((message) => (
                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>
                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>
                    <div>{message.content}</div>
                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>
                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}
                    </small>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-muted">
                <i className="fas fa-comments fa-3x mb-3"></i>
                <p>No messages yet. Start a conversation!</p>
              </div>
            )}
          </div>
          <div className="d-flex">
            <Form.Control
              type="text"
              placeholder="Type your message..."
              value={chatMessage}
              onChange={(e) => setChatMessage(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              className="me-2"
            />
            <Button variant="primary" onClick={handleSendMessage} disabled={!chatMessage.trim()}>
              <i className="fas fa-paper-plane"></i>
            </Button>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowChatModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default StudentDashboard;
