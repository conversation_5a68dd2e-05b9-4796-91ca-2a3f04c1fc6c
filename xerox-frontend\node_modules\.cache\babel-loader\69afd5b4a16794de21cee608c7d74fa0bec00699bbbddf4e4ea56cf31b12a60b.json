{"ast": null, "code": "function camelToDash(str) {\n  return str.replace(/([A-Z])/g, match => \"-\".concat(match.toLowerCase()));\n}\nexport { camelToDash };", "map": {"version": 3, "names": ["camelToDash", "str", "replace", "match", "concat", "toLowerCase"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/render/dom/utils/camel-to-dash.mjs"], "sourcesContent": ["function camelToDash(str) {\n    return str.replace(/([A-Z])/g, (match) => `-${match.toLowerCase()}`);\n}\n\nexport { camelToDash };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,GAAG,EAAE;EACtB,OAAOA,GAAG,CAACC,OAAO,CAAC,UAAU,EAAGC,KAAK,QAAAC,MAAA,CAASD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAE,CAAC;AACxE;AAEA,SAASL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}