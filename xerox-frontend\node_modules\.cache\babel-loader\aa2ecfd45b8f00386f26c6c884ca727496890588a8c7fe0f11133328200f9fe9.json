{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\Register.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('student');\n  const [studentData, setStudentData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n  const {\n    registerStudent,\n    registerXeroxCenter,\n    isLoading,\n    error,\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n  const handleStudentSubmit = async e => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleXeroxSubmit = async e => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n  const handleStudentChange = (field, value) => {\n    setStudentData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleXeroxChange = (field, value) => {\n    setXeroxData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    className: \"mt-5\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"justify-content-center\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-user-plus fa-3x text-primary mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"h4 text-gray-900 mb-4\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Nav, {\n              variant: \"tabs\",\n              className: \"mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(Nav.Item, {\n                children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  active: activeTab === 'student',\n                  onClick: () => setActiveTab('student'),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-graduation-cap me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 80,\n                    columnNumber: 21\n                  }, this), \"Student\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Nav.Item, {\n                children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                  active: activeTab === 'xerox',\n                  onClick: () => setActiveTab('xerox'),\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-store me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 89,\n                    columnNumber: 21\n                  }, this), \"Xerox Center\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-exclamation-triangle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), error]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), activeTab === 'student' ? /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleStudentSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Enter username\",\n                      value: studentData.username,\n                      onChange: e => handleStudentChange('username', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Student Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 120,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Enter student number\",\n                      value: studentData.studentNumber,\n                      onChange: e => handleStudentChange('studentNumber', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 121,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  placeholder: \"Enter email\",\n                  value: studentData.email,\n                  onChange: e => handleStudentChange('email', e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  placeholder: \"Password\",\n                  value: studentData.password,\n                  onChange: e => handleStudentChange('password', e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"First Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"First name\",\n                      value: studentData.firstName,\n                      onChange: e => handleStudentChange('firstName', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 173,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Last name\",\n                      value: studentData.lastName,\n                      onChange: e => handleStudentChange('lastName', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Department\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Department (optional)\",\n                      value: studentData.department,\n                      onChange: e => handleStudentChange('department', e.target.value),\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 190,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Year\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      placeholder: \"Year (optional)\",\n                      value: studentData.year || '',\n                      onChange: e => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined),\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 202,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Phone Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"tel\",\n                  placeholder: \"Phone number (optional)\",\n                  value: studentData.phoneNumber,\n                  onChange: e => handleStudentChange('phoneNumber', e.target.value),\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: isLoading,\n                children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), \"Creating Account...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-user-plus me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 25\n                  }, this), \"Register as Student\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleXeroxSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 255,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Enter username\",\n                      value: xeroxData.username,\n                      onChange: e => handleXeroxChange('username', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 256,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Xerox Center Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Center name\",\n                      value: xeroxData.xeroxCenterName,\n                      onChange: e => handleXeroxChange('xeroxCenterName', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Email Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"email\",\n                  placeholder: \"Enter email\",\n                  value: xeroxData.email,\n                  onChange: e => handleXeroxChange('email', e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  placeholder: \"Password\",\n                  value: xeroxData.password,\n                  onChange: e => handleXeroxChange('password', e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Location\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  placeholder: \"Center location\",\n                  value: xeroxData.location,\n                  onChange: e => handleXeroxChange('location', e.target.value),\n                  required: true,\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Contact Person\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      placeholder: \"Contact person (optional)\",\n                      value: xeroxData.contactPerson,\n                      onChange: e => handleXeroxChange('contactPerson', e.target.value),\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      children: \"Phone Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"tel\",\n                      placeholder: \"Phone number\",\n                      value: xeroxData.phoneNumber,\n                      onChange: e => handleXeroxChange('phoneNumber', e.target.value),\n                      required: true,\n                      disabled: isLoading\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  as: \"textarea\",\n                  rows: 3,\n                  placeholder: \"Center description (optional)\",\n                  value: xeroxData.description,\n                  onChange: e => handleXeroxChange('description', e.target.value),\n                  disabled: isLoading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 347,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: isLoading,\n                children: isLoading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 365,\n                    columnNumber: 25\n                  }, this), \"Creating Account...\"]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-store me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 25\n                  }, this), \"Register as Xerox Center\"]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/login\",\n                className: \"small\",\n                children: \"Already have an account? Login!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 64,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"++4JwURHb4HC74M+qE5ViJ9FgsY=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "Nav", "Link", "useNavigate", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "activeTab", "setActiveTab", "studentData", "setStudentData", "username", "email", "password", "studentNumber", "firstName", "lastName", "phoneNumber", "department", "year", "undefined", "xeroxData", "setXeroxData", "xeroxCenterName", "location", "<PERSON><PERSON><PERSON>", "description", "registerStudent", "registerXeroxCenter", "isLoading", "error", "user", "navigate", "handleStudentSubmit", "e", "preventDefault", "success", "handleXeroxSubmit", "handleStudentChange", "field", "value", "prev", "handleXeroxChange", "className", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "<PERSON><PERSON>", "active", "onClick", "onSubmit", "Group", "Label", "Control", "type", "placeholder", "onChange", "target", "required", "disabled", "parseInt", "as", "animation", "size", "role", "rows", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/Register.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner, Nav } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../contexts/AuthContext';\n\nconst Register: React.FC = () => {\n  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');\n  const [studentData, setStudentData] = useState<StudentRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    studentNumber: '',\n    firstName: '',\n    lastName: '',\n    phoneNumber: '',\n    department: '',\n    year: undefined\n  });\n  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({\n    username: '',\n    email: '',\n    password: '',\n    xeroxCenterName: '',\n    location: '',\n    contactPerson: '',\n    phoneNumber: '',\n    description: ''\n  });\n\n  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    if (user) {\n      navigate('/dashboard');\n    }\n  }, [user, navigate]);\n\n  const handleStudentSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerStudent(studentData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleXeroxSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    const success = await registerXeroxCenter(xeroxData);\n    if (success) {\n      navigate('/dashboard');\n    }\n  };\n\n  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number) => {\n    setStudentData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {\n    setXeroxData(prev => ({ ...prev, [field]: value }));\n  };\n\n  return (\n    <Container className=\"mt-5\">\n      <Row className=\"justify-content-center\">\n        <Col md={8} lg={6}>\n          <Card className=\"shadow\">\n            <Card.Body className=\"p-4\">\n              <div className=\"text-center mb-4\">\n                <i className=\"fas fa-user-plus fa-3x text-primary mb-3\"></i>\n                <h2 className=\"h4 text-gray-900 mb-4\">Create Account</h2>\n              </div>\n\n              <Nav variant=\"tabs\" className=\"mb-4\">\n                <Nav.Item>\n                  <Nav.Link \n                    active={activeTab === 'student'} \n                    onClick={() => setActiveTab('student')}\n                  >\n                    <i className=\"fas fa-graduation-cap me-2\"></i>\n                    Student\n                  </Nav.Link>\n                </Nav.Item>\n                <Nav.Item>\n                  <Nav.Link \n                    active={activeTab === 'xerox'} \n                    onClick={() => setActiveTab('xerox')}\n                  >\n                    <i className=\"fas fa-store me-2\"></i>\n                    Xerox Center\n                  </Nav.Link>\n                </Nav.Item>\n              </Nav>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  {error}\n                </Alert>\n              )}\n\n              {activeTab === 'student' ? (\n                <Form onSubmit={handleStudentSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={studentData.username}\n                          onChange={(e) => handleStudentChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Student Number</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter student number\"\n                          value={studentData.studentNumber}\n                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={studentData.email}\n                      onChange={(e) => handleStudentChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={studentData.password}\n                      onChange={(e) => handleStudentChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>First Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"First name\"\n                          value={studentData.firstName}\n                          onChange={(e) => handleStudentChange('firstName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Last Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Last name\"\n                          value={studentData.lastName}\n                          onChange={(e) => handleStudentChange('lastName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Department</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Department (optional)\"\n                          value={studentData.department}\n                          onChange={(e) => handleStudentChange('department', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Year</Form.Label>\n                        <Form.Control\n                          type=\"number\"\n                          placeholder=\"Year (optional)\"\n                          value={studentData.year || ''}\n                          onChange={(e) => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Phone Number</Form.Label>\n                    <Form.Control\n                      type=\"tel\"\n                      placeholder=\"Phone number (optional)\"\n                      value={studentData.phoneNumber}\n                      onChange={(e) => handleStudentChange('phoneNumber', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-user-plus me-2\"></i>\n                        Register as Student\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              ) : (\n                <Form onSubmit={handleXeroxSubmit}>\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Username</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Enter username\"\n                          value={xeroxData.username}\n                          onChange={(e) => handleXeroxChange('username', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Xerox Center Name</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Center name\"\n                          value={xeroxData.xeroxCenterName}\n                          onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Email Address</Form.Label>\n                    <Form.Control\n                      type=\"email\"\n                      placeholder=\"Enter email\"\n                      value={xeroxData.email}\n                      onChange={(e) => handleXeroxChange('email', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Password</Form.Label>\n                    <Form.Control\n                      type=\"password\"\n                      placeholder=\"Password\"\n                      value={xeroxData.password}\n                      onChange={(e) => handleXeroxChange('password', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Location</Form.Label>\n                    <Form.Control\n                      type=\"text\"\n                      placeholder=\"Center location\"\n                      value={xeroxData.location}\n                      onChange={(e) => handleXeroxChange('location', e.target.value)}\n                      required\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Row>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Contact Person</Form.Label>\n                        <Form.Control\n                          type=\"text\"\n                          placeholder=\"Contact person (optional)\"\n                          value={xeroxData.contactPerson}\n                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                    <Col md={6}>\n                      <Form.Group className=\"mb-3\">\n                        <Form.Label>Phone Number</Form.Label>\n                        <Form.Control\n                          type=\"tel\"\n                          placeholder=\"Phone number\"\n                          value={xeroxData.phoneNumber}\n                          onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}\n                          required\n                          disabled={isLoading}\n                        />\n                      </Form.Group>\n                    </Col>\n                  </Row>\n\n                  <Form.Group className=\"mb-3\">\n                    <Form.Label>Description</Form.Label>\n                    <Form.Control\n                      as=\"textarea\"\n                      rows={3}\n                      placeholder=\"Center description (optional)\"\n                      value={xeroxData.description}\n                      onChange={(e) => handleXeroxChange('description', e.target.value)}\n                      disabled={isLoading}\n                    />\n                  </Form.Group>\n\n                  <Button \n                    variant=\"primary\" \n                    type=\"submit\" \n                    className=\"w-100 mb-3\"\n                    disabled={isLoading}\n                  >\n                    {isLoading ? (\n                      <>\n                        <Spinner\n                          as=\"span\"\n                          animation=\"border\"\n                          size=\"sm\"\n                          role=\"status\"\n                          aria-hidden=\"true\"\n                          className=\"me-2\"\n                        />\n                        Creating Account...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"fas fa-store me-2\"></i>\n                        Register as Xerox Center\n                      </>\n                    )}\n                  </Button>\n                </Form>\n              )}\n\n              <hr />\n\n              <div className=\"text-center\">\n                <Link to=\"/login\" className=\"small\">\n                  Already have an account? Login!\n                </Link>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,GAAG,QAAQ,iBAAiB;AAC9F,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAA8D,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExG,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAsB,SAAS,CAAC;EAC1E,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAA0B;IACtEwB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,EAAE;IACfC,UAAU,EAAE,EAAE;IACdC,IAAI,EAAEC;EACR,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAA8B;IACtEwB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZU,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBR,WAAW,EAAE,EAAE;IACfS,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,eAAe;IAAEC,mBAAmB;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAK,CAAC,GAAG/B,OAAO,CAAC,CAAC;EAClF,MAAMgC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAE9BX,SAAS,CAAC,MAAM;IACd,IAAI2C,IAAI,EAAE;MACRC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACD,IAAI,EAAEC,QAAQ,CAAC,CAAC;EAEpB,MAAMC,mBAAmB,GAAG,MAAOC,CAAkB,IAAK;IACxDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMT,eAAe,CAAClB,WAAW,CAAC;IAClD,IAAI2B,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAOH,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMC,OAAO,GAAG,MAAMR,mBAAmB,CAACP,SAAS,CAAC;IACpD,IAAIe,OAAO,EAAE;MACXJ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAMM,mBAAmB,GAAGA,CAACC,KAAoC,EAAEC,KAAsB,KAAK;IAC5F9B,cAAc,CAAC+B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACvD,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACH,KAAwC,EAAEC,KAAa,KAAK;IACrFlB,YAAY,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;EACrD,CAAC;EAED,oBACEtC,OAAA,CAACb,SAAS;IAACsD,SAAS,EAAC,MAAM;IAAAC,QAAA,eACzB1C,OAAA,CAACZ,GAAG;MAACqD,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC1C,OAAA,CAACX,GAAG;QAACsD,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAF,QAAA,eAChB1C,OAAA,CAACV,IAAI;UAACmD,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACtB1C,OAAA,CAACV,IAAI,CAACuD,IAAI;YAACJ,SAAS,EAAC,KAAK;YAAAC,QAAA,gBACxB1C,OAAA;cAAKyC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B1C,OAAA;gBAAGyC,SAAS,EAAC;cAA0C;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5DjD,OAAA;gBAAIyC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAc;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eAENjD,OAAA,CAACL,GAAG;cAACuD,OAAO,EAAC,MAAM;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAClC1C,OAAA,CAACL,GAAG,CAACwD,IAAI;gBAAAT,QAAA,eACP1C,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPwD,MAAM,EAAE/C,SAAS,KAAK,SAAU;kBAChCgD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC,SAAS,CAAE;kBAAAoC,QAAA,gBAEvC1C,OAAA;oBAAGyC,SAAS,EAAC;kBAA4B;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,WAEhD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACXjD,OAAA,CAACL,GAAG,CAACwD,IAAI;gBAAAT,QAAA,eACP1C,OAAA,CAACL,GAAG,CAACC,IAAI;kBACPwD,MAAM,EAAE/C,SAAS,KAAK,OAAQ;kBAC9BgD,OAAO,EAAEA,CAAA,KAAM/C,YAAY,CAAC,OAAO,CAAE;kBAAAoC,QAAA,gBAErC1C,OAAA;oBAAGyC,SAAS,EAAC;kBAAmB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,gBAEvC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAELrB,KAAK,iBACJ5B,OAAA,CAACP,KAAK;cAACyD,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACtC1C,OAAA;gBAAGyC,SAAS,EAAC;cAAkC;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EACnDrB,KAAK;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,EAEA5C,SAAS,KAAK,SAAS,gBACtBL,OAAA,CAACT,IAAI;cAAC+D,QAAQ,EAAEvB,mBAAoB;cAAAW,QAAA,gBAClC1C,OAAA,CAACZ,GAAG;gBAAAsD,QAAA,gBACF1C,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,gBAAgB;sBAC5BrB,KAAK,EAAE/B,WAAW,CAACE,QAAS;sBAC5BmD,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACjEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNjD,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,sBAAsB;sBAClCrB,KAAK,EAAE/B,WAAW,CAACK,aAAc;sBACjCgD,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,eAAe,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACtEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,aAAa;kBACzBrB,KAAK,EAAE/B,WAAW,CAACG,KAAM;kBACzBkD,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,OAAO,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBAC9DwB,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,UAAU;kBACtBrB,KAAK,EAAE/B,WAAW,CAACI,QAAS;kBAC5BiD,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBACjEwB,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACZ,GAAG;gBAAAsD,QAAA,gBACF1C,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACnCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,YAAY;sBACxBrB,KAAK,EAAE/B,WAAW,CAACM,SAAU;sBAC7B+C,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,WAAW,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBAClEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNjD,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAS;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,WAAW;sBACvBrB,KAAK,EAAE/B,WAAW,CAACO,QAAS;sBAC5B8C,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,UAAU,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACjEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjD,OAAA,CAACZ,GAAG;gBAAAsD,QAAA,gBACF1C,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAU;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACnCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,uBAAuB;sBACnCrB,KAAK,EAAE/B,WAAW,CAACS,UAAW;sBAC9B4C,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,YAAY,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACnEyB,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNjD,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAI;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7BjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbC,WAAW,EAAC,iBAAiB;sBAC7BrB,KAAK,EAAE/B,WAAW,CAACU,IAAI,IAAI,EAAG;sBAC9B2C,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,MAAM,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,GAAG0B,QAAQ,CAAChC,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAC,GAAGpB,SAAS,CAAE;sBACpG6C,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,KAAK;kBACVC,WAAW,EAAC,yBAAyB;kBACrCrB,KAAK,EAAE/B,WAAW,CAACQ,WAAY;kBAC/B6C,QAAQ,EAAG5B,CAAC,IAAKI,mBAAmB,CAAC,aAAa,EAAEJ,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBACpEyB,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACR,MAAM;gBACL0D,OAAO,EAAC,SAAS;gBACjBQ,IAAI,EAAC,QAAQ;gBACbjB,SAAS,EAAC,YAAY;gBACtBsB,QAAQ,EAAEpC,SAAU;gBAAAe,QAAA,EAEnBf,SAAS,gBACR3B,OAAA,CAAAE,SAAA;kBAAAwC,QAAA,gBACE1C,OAAA,CAACN,OAAO;oBACNuE,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClB3B,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,uBAEJ;gBAAA,eAAE,CAAC,gBAEHjD,OAAA,CAAAE,SAAA;kBAAAwC,QAAA,gBACE1C,OAAA;oBAAGyC,SAAS,EAAC;kBAAuB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,uBAE3C;gBAAA,eAAE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAEPjD,OAAA,CAACT,IAAI;cAAC+D,QAAQ,EAAEnB,iBAAkB;cAAAO,QAAA,gBAChC1C,OAAA,CAACZ,GAAG;gBAAAsD,QAAA,gBACF1C,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAQ;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,gBAAgB;sBAC5BrB,KAAK,EAAEnB,SAAS,CAACV,QAAS;sBAC1BmD,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBAC/DwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNjD,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAiB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC1CjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,aAAa;sBACzBrB,KAAK,EAAEnB,SAAS,CAACE,eAAgB;sBACjCuC,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,iBAAiB,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACtEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,aAAa;kBACzBrB,KAAK,EAAEnB,SAAS,CAACT,KAAM;kBACvBkD,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,OAAO,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBAC5DwB,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACfC,WAAW,EAAC,UAAU;kBACtBrB,KAAK,EAAEnB,SAAS,CAACR,QAAS;kBAC1BiD,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBAC/DwB,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,iBAAiB;kBAC7BrB,KAAK,EAAEnB,SAAS,CAACG,QAAS;kBAC1BsC,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,UAAU,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBAC/DwB,QAAQ;kBACRC,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACZ,GAAG;gBAAAsD,QAAA,gBACF1C,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAc;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,2BAA2B;sBACvCrB,KAAK,EAAEnB,SAAS,CAACI,aAAc;sBAC/BqC,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,eAAe,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBACpEyB,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACNjD,OAAA,CAACX,GAAG;kBAACsD,EAAE,EAAE,CAAE;kBAAAD,QAAA,eACT1C,OAAA,CAACT,IAAI,CAACgE,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;sBAAAd,QAAA,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACrCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;sBACXC,IAAI,EAAC,KAAK;sBACVC,WAAW,EAAC,cAAc;sBAC1BrB,KAAK,EAAEnB,SAAS,CAACJ,WAAY;sBAC7B6C,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;sBAClEwB,QAAQ;sBACRC,QAAQ,EAAEpC;oBAAU;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENjD,OAAA,CAACT,IAAI,CAACgE,KAAK;gBAACd,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1C,OAAA,CAACT,IAAI,CAACiE,KAAK;kBAAAd,QAAA,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCjD,OAAA,CAACT,IAAI,CAACkE,OAAO;kBACXQ,EAAE,EAAC,UAAU;kBACbI,IAAI,EAAE,CAAE;kBACRV,WAAW,EAAC,+BAA+B;kBAC3CrB,KAAK,EAAEnB,SAAS,CAACK,WAAY;kBAC7BoC,QAAQ,EAAG5B,CAAC,IAAKQ,iBAAiB,CAAC,aAAa,EAAER,CAAC,CAAC6B,MAAM,CAACvB,KAAK,CAAE;kBAClEyB,QAAQ,EAAEpC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjD,OAAA,CAACR,MAAM;gBACL0D,OAAO,EAAC,SAAS;gBACjBQ,IAAI,EAAC,QAAQ;gBACbjB,SAAS,EAAC,YAAY;gBACtBsB,QAAQ,EAAEpC,SAAU;gBAAAe,QAAA,EAEnBf,SAAS,gBACR3B,OAAA,CAAAE,SAAA;kBAAAwC,QAAA,gBACE1C,OAAA,CAACN,OAAO;oBACNuE,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBC,IAAI,EAAC,IAAI;oBACTC,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClB3B,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,uBAEJ;gBAAA,eAAE,CAAC,gBAEHjD,OAAA,CAAAE,SAAA;kBAAAwC,QAAA,gBACE1C,OAAA;oBAAGyC,SAAS,EAAC;kBAAmB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,4BAEvC;gBAAA,eAAE;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACP,eAEDjD,OAAA;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENjD,OAAA;cAAKyC,SAAS,EAAC,aAAa;cAAAC,QAAA,eAC1B1C,OAAA,CAACJ,IAAI;gBAAC0E,EAAE,EAAC,QAAQ;gBAAC7B,SAAS,EAAC,OAAO;gBAAAC,QAAA,EAAC;cAEpC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAAC7C,EAAA,CAxYID,QAAkB;EAAA,QAwBmDL,OAAO,EAC/DD,WAAW;AAAA;AAAA0E,EAAA,GAzBxBpE,QAAkB;AA0YxB,eAAeA,QAAQ;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}