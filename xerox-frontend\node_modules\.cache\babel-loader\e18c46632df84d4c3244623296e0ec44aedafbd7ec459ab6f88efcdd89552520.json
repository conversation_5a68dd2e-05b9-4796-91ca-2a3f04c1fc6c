{"ast": null, "code": "const isNotNull = value => value !== null;\nfunction getFinalKeyframe(keyframes, _ref, finalKeyframe) {\n  let {\n    repeat,\n    repeatType = \"loop\"\n  } = _ref;\n  const resolvedKeyframes = keyframes.filter(isNotNull);\n  const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1 ? 0 : resolvedKeyframes.length - 1;\n  return !index || finalKeyframe === undefined ? resolvedKeyframes[index] : finalKeyframe;\n}\nexport { getFinalKeyframe };", "map": {"version": 3, "names": ["isNotNull", "value", "getFinalKeyframe", "keyframes", "_ref", "finalKeyframe", "repeat", "repeatType", "resolvedKeyframes", "filter", "index", "length", "undefined"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs"], "sourcesContent": ["const isNotNull = (value) => value !== null;\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }, finalKeyframe) {\n    const resolvedKeyframes = keyframes.filter(isNotNull);\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : resolvedKeyframes.length - 1;\n    return !index || finalKeyframe === undefined\n        ? resolvedKeyframes[index]\n        : finalKeyframe;\n}\n\nexport { getFinalKeyframe };\n"], "mappings": "AAAA,MAAMA,SAAS,GAAIC,KAAK,IAAKA,KAAK,KAAK,IAAI;AAC3C,SAASC,gBAAgBA,CAACC,SAAS,EAAAC,IAAA,EAAmCC,aAAa,EAAE;EAAA,IAAhD;IAAEC,MAAM;IAAEC,UAAU,GAAG;EAAO,CAAC,GAAAH,IAAA;EAChE,MAAMI,iBAAiB,GAAGL,SAAS,CAACM,MAAM,CAACT,SAAS,CAAC;EACrD,MAAMU,KAAK,GAAGJ,MAAM,IAAIC,UAAU,KAAK,MAAM,IAAID,MAAM,GAAG,CAAC,KAAK,CAAC,GAC3D,CAAC,GACDE,iBAAiB,CAACG,MAAM,GAAG,CAAC;EAClC,OAAO,CAACD,KAAK,IAAIL,aAAa,KAAKO,SAAS,GACtCJ,iBAAiB,CAACE,KAAK,CAAC,GACxBL,aAAa;AACvB;AAEA,SAASH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}