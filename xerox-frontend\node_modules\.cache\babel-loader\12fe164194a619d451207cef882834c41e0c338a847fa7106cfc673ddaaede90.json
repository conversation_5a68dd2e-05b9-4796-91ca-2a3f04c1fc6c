{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\XeroxCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi } from '../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XeroxCenterDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this);\n  };\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n        await printJobApi.setJobQuote(selectedJob.id, parseFloat(quoteData.cost), estimatedCompletion.toISOString(), quoteData.notes);\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({\n      cost: '',\n      estimatedHours: '',\n      notes: ''\n    });\n  };\n  const handleStatusUpdate = (jobId, newStatus) => {\n    // Mock API call to update job status\n    console.log('Updating job status:', {\n      jobId,\n      newStatus\n    });\n    setPrintJobs(prev => prev.map(job => job.id === jobId ? {\n      ...job,\n      status: newStatus\n    } : job));\n  };\n  const filteredJobs = filterStatus === 'all' ? printJobs : printJobs.filter(job => job.status === filterStatus);\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-store me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this), \"Xerox Center Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-primary\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: stats.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-warning\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: stats.pending\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-info\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cog fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: stats.inProgress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-success\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: [\"$\", stats.revenue.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tasks me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), \"Job Queue\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n          style: {\n            width: 'auto'\n          },\n          value: filterStatus,\n          onChange: e => setFilterStatus(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Requested\",\n            children: \"Requested\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"UnderReview\",\n            children: \"Under Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Quoted\",\n            children: \"Quoted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"WaitingConfirmation\",\n            children: \"Waiting Confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Confirmed\",\n            children: \"Confirmed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"InProgress\",\n            children: \"In Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Completed\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: filteredJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n          responsive: true,\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job #\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: job.jobNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: new Date(job.created).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: job.studentName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: job.studentEmail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-pdf me-2 text-danger\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 23\n                }, this), job.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 30\n                    }, this), \" \", job.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 30\n                    }, this), \" \", job.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 30\n                    }, this), \" \", job.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 234,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 30\n                    }, this), \" \", job.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this), job.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Remarks:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 32\n                    }, this), \" \", job.remarks]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStatusBadge(job.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"btn-group-vertical\",\n                  role: \"group\",\n                  children: [job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: () => {\n                        setSelectedJob(job);\n                        setShowQuoteModal(true);\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-dollar-sign me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 257,\n                        columnNumber: 31\n                      }, this), \"Quote\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 249,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-danger\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-times me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 31\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 260,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-info\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-play me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 29\n                    }, this), \"Start\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 27\n                  }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 29\n                    }, this), \"Complete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 27\n                  }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-truck me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 29\n                    }, this), \"Deliver\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 15\n          }, this), \"No jobs found for the selected filter.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showQuoteModal,\n      onHide: () => setShowQuoteModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-dollar-sign me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), \"Provide Quote - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 324,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 p-3 bg-light rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Job Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.fileName, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 66\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.printType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.colorType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 68\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.paperSize, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Student:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.studentName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Remarks:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 349,\n                columnNumber: 21\n              }, this), \" \", selectedJob.remarks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Cost ($)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 358,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                    children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                      children: \"$\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      step: \"0.01\",\n                      placeholder: \"0.00\",\n                      value: quoteData.cost,\n                      onChange: e => setQuoteData(prev => ({\n                        ...prev,\n                        cost: e.target.value\n                      })),\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 356,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Estimated Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"number\",\n                    min: \"1\",\n                    placeholder: \"Hours to complete\",\n                    value: quoteData.estimatedHours,\n                    onChange: e => setQuoteData(prev => ({\n                      ...prev,\n                      estimatedHours: e.target.value\n                    })),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Notes (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                placeholder: \"Any additional notes for the student...\",\n                value: quoteData.notes,\n                onChange: e => setQuoteData(prev => ({\n                  ...prev,\n                  notes: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowQuoteModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleQuoteSubmit,\n          disabled: !quoteData.cost || !quoteData.estimatedHours,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-paper-plane me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 410,\n            columnNumber: 13\n          }, this), \"Send Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(XeroxCenterDashboard, \"VJHAFSPjU/jS1C7Ma9QlRu7N0Bo=\", false, function () {\n  return [useAuth];\n});\n_c = XeroxCenterDashboard;\nexport default XeroxCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"XeroxCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "InputGroup", "useAuth", "printJobApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XeroxCenterDashboard", "_s", "user", "printJobs", "setPrintJobs", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "showQuoteModal", "setShowQuoteModal", "quoteData", "setQuoteData", "cost", "estimatedHours", "notes", "filterStatus", "setFilterStatus", "fetchData", "printJobsResponse", "getXeroxCenterJobs", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleQuoteSubmit", "estimatedCompletion", "Date", "setHours", "getHours", "parseInt", "setJobQuote", "id", "parseFloat", "toISOString", "handleStatusUpdate", "jobId", "newStatus", "log", "prev", "map", "job", "filteredJobs", "filter", "stats", "total", "length", "pending", "includes", "inProgress", "completed", "revenue", "reduce", "sum", "fluid", "username", "md", "Body", "toFixed", "Header", "Select", "style", "width", "value", "onChange", "e", "target", "responsive", "hover", "jobNumber", "created", "toLocaleDateString", "studentName", "studentEmail", "printType", "copies", "colorType", "paperSize", "remarks", "role", "size", "onClick", "show", "onHide", "closeButton", "Title", "Group", "Label", "Text", "Control", "type", "step", "placeholder", "required", "min", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/XeroxCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n}\n\nconst XeroxCenterDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs for this xerox center\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error fetching print jobs:', error);\n        setPrintJobs([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const handleQuoteSubmit = async () => {\n    if (selectedJob) {\n      try {\n        const estimatedCompletion = new Date();\n        estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n\n        await printJobApi.setJobQuote(\n          selectedJob.id,\n          parseFloat(quoteData.cost),\n          estimatedCompletion.toISOString(),\n          quoteData.notes\n        );\n\n        // Refresh print jobs after update\n        const printJobsResponse = await printJobApi.getXeroxCenterJobs();\n        setPrintJobs(printJobsResponse.data);\n      } catch (error) {\n        console.error('Error submitting quote:', error);\n      }\n    }\n\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({ cost: '', estimatedHours: '', notes: '' });\n  };\n\n  const handleStatusUpdate = (jobId: number, newStatus: string) => {\n    // Mock API call to update job status\n    console.log('Updating job status:', { jobId, newStatus });\n    \n    setPrintJobs(prev => prev.map(job => \n      job.id === jobId ? { ...job, status: newStatus } : job\n    ));\n  };\n\n  const filteredJobs = filterStatus === 'all' \n    ? printJobs \n    : printJobs.filter(job => job.status === filterStatus);\n\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-store me-2\"></i>\n            Xerox Center Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center border-left-primary\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{stats.total}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-warning\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>Pending</h5>\n              <h3 className=\"text-warning\">{stats.pending}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-info\">\n            <Card.Body>\n              <i className=\"fas fa-cog fa-2x text-info mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-info\">{stats.inProgress}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-success\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-success mb-2\"></i>\n              <h5>Revenue</h5>\n              <h3 className=\"text-success\">${stats.revenue.toFixed(2)}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Job Queue */}\n      <Card>\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-tasks me-2\"></i>\n            Job Queue\n          </h5>\n          <Form.Select \n            style={{ width: 'auto' }}\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n          >\n            <option value=\"all\">All Jobs</option>\n            <option value=\"Requested\">Requested</option>\n            <option value=\"UnderReview\">Under Review</option>\n            <option value=\"Quoted\">Quoted</option>\n            <option value=\"WaitingConfirmation\">Waiting Confirmation</option>\n            <option value=\"Confirmed\">Confirmed</option>\n            <option value=\"InProgress\">In Progress</option>\n            <option value=\"Completed\">Completed</option>\n          </Form.Select>\n        </Card.Header>\n        <Card.Body>\n          {filteredJobs.length > 0 ? (\n            <Table responsive hover>\n              <thead>\n                <tr>\n                  <th>Job #</th>\n                  <th>Student</th>\n                  <th>File</th>\n                  <th>Details</th>\n                  <th>Status</th>\n                  <th>Cost</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredJobs.map(job => (\n                  <tr key={job.id}>\n                    <td>\n                      <strong>{job.jobNumber}</strong>\n                      <br />\n                      <small className=\"text-muted\">\n                        {new Date(job.created).toLocaleDateString()}\n                      </small>\n                    </td>\n                    <td>\n                      <div>\n                        <strong>{job.studentName}</strong>\n                        <br />\n                        <small className=\"text-muted\">{job.studentEmail}</small>\n                      </div>\n                    </td>\n                    <td>\n                      <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                      {job.fileName}\n                    </td>\n                    <td>\n                      <div className=\"small\">\n                        <div><strong>Type:</strong> {job.printType}</div>\n                        <div><strong>Copies:</strong> {job.copies}</div>\n                        <div><strong>Color:</strong> {job.colorType}</div>\n                        <div><strong>Size:</strong> {job.paperSize}</div>\n                        {job.remarks && (\n                          <div><strong>Remarks:</strong> {job.remarks}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td>{getStatusBadge(job.status)}</td>\n                    <td>\n                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                    </td>\n                    <td>\n                      <div className=\"btn-group-vertical\" role=\"group\">\n                        {job.status === 'Requested' && (\n                          <>\n                            <Button \n                              variant=\"outline-primary\" \n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                            >\n                              <i className=\"fas fa-dollar-sign me-1\"></i>\n                              Quote\n                            </Button>\n                            <Button \n                              variant=\"outline-danger\" \n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                            >\n                              <i className=\"fas fa-times me-1\"></i>\n                              Reject\n                            </Button>\n                          </>\n                        )}\n                        \n                        {job.status === 'Confirmed' && (\n                          <Button \n                            variant=\"outline-info\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                          >\n                            <i className=\"fas fa-play me-1\"></i>\n                            Start\n                          </Button>\n                        )}\n                        \n                        {job.status === 'InProgress' && (\n                          <Button \n                            variant=\"outline-success\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                          >\n                            <i className=\"fas fa-check me-1\"></i>\n                            Complete\n                          </Button>\n                        )}\n                        \n                        {job.status === 'Completed' && (\n                          <Button \n                            variant=\"outline-success\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                          >\n                            <i className=\"fas fa-truck me-1\"></i>\n                            Deliver\n                          </Button>\n                        )}\n                        \n                        <Button variant=\"outline-secondary\" size=\"sm\">\n                          <i className=\"fas fa-comment\"></i>\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </Table>\n          ) : (\n            <Alert variant=\"info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              No jobs found for the selected filter.\n            </Alert>\n          )}\n        </Card.Body>\n      </Card>\n\n      {/* Quote Modal */}\n      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-dollar-sign me-2\"></i>\n            Provide Quote - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <>\n              <div className=\"mb-3 p-3 bg-light rounded\">\n                <h6>Job Details:</h6>\n                <div className=\"row\">\n                  <div className=\"col-6\">\n                    <strong>File:</strong> {selectedJob.fileName}<br />\n                    <strong>Type:</strong> {selectedJob.printType}<br />\n                    <strong>Copies:</strong> {selectedJob.copies}\n                  </div>\n                  <div className=\"col-6\">\n                    <strong>Color:</strong> {selectedJob.colorType}<br />\n                    <strong>Size:</strong> {selectedJob.paperSize}<br />\n                    <strong>Student:</strong> {selectedJob.studentName}\n                  </div>\n                </div>\n                {selectedJob.remarks && (\n                  <div className=\"mt-2\">\n                    <strong>Remarks:</strong> {selectedJob.remarks}\n                  </div>\n                )}\n              </div>\n\n              <Form>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Cost ($)</Form.Label>\n                      <InputGroup>\n                        <InputGroup.Text>$</InputGroup.Text>\n                        <Form.Control\n                          type=\"number\"\n                          step=\"0.01\"\n                          placeholder=\"0.00\"\n                          value={quoteData.cost}\n                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}\n                          required\n                        />\n                      </InputGroup>\n                    </Form.Group>\n                  </Col>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Estimated Hours</Form.Label>\n                      <Form.Control\n                        type=\"number\"\n                        min=\"1\"\n                        placeholder=\"Hours to complete\"\n                        value={quoteData.estimatedHours}\n                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}\n                        required\n                      />\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Notes (Optional)</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={3}\n                    placeholder=\"Any additional notes for the student...\"\n                    value={quoteData.notes}\n                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}\n                  />\n                </Form.Group>\n              </Form>\n            </>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"primary\" \n            onClick={handleQuoteSubmit}\n            disabled={!quoteData.cost || !quoteData.estimatedHours}\n          >\n            <i className=\"fas fa-paper-plane me-2\"></i>\n            Send Quote\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default XeroxCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AACjH,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAwB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAmB9D,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC;IACzC8B,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAMkC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMtB,WAAW,CAACuB,kBAAkB,CAAC,CAAC;QAChEd,YAAY,CAACa,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDhB,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC;IAEDY,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACE7B,OAAA,CAACR,KAAK;MAACuC,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxBhC,OAAA;QAAGiC,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CX,MAAM;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI9B,WAAW,EAAE;MACf,IAAI;QACF,MAAM+B,mBAAmB,GAAG,IAAIC,IAAI,CAAC,CAAC;QACtCD,mBAAmB,CAACE,QAAQ,CAACF,mBAAmB,CAACG,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAAC/B,SAAS,CAACG,cAAc,CAAC,CAAC;QAEjG,MAAMjB,WAAW,CAAC8C,WAAW,CAC3BpC,WAAW,CAACqC,EAAE,EACdC,UAAU,CAAClC,SAAS,CAACE,IAAI,CAAC,EAC1ByB,mBAAmB,CAACQ,WAAW,CAAC,CAAC,EACjCnC,SAAS,CAACI,KACZ,CAAC;;QAED;QACA,MAAMI,iBAAiB,GAAG,MAAMtB,WAAW,CAACuB,kBAAkB,CAAC,CAAC;QAChEd,YAAY,CAACa,iBAAiB,CAACE,IAAI,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD;IACF;IAEAZ,iBAAiB,CAAC,KAAK,CAAC;IACxBF,cAAc,CAAC,IAAI,CAAC;IACpBI,YAAY,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,cAAc,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMgC,kBAAkB,GAAGA,CAACC,KAAa,EAAEC,SAAiB,KAAK;IAC/D;IACA1B,OAAO,CAAC2B,GAAG,CAAC,sBAAsB,EAAE;MAAEF,KAAK;MAAEC;IAAU,CAAC,CAAC;IAEzD3C,YAAY,CAAC6C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,GAAG,IAC/BA,GAAG,CAACT,EAAE,KAAKI,KAAK,GAAG;MAAE,GAAGK,GAAG;MAAE5B,MAAM,EAAEwB;IAAU,CAAC,GAAGI,GACrD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGtC,YAAY,KAAK,KAAK,GACvCX,SAAS,GACTA,SAAS,CAACkD,MAAM,CAACF,GAAG,IAAIA,GAAG,CAAC5B,MAAM,KAAKT,YAAY,CAAC;EAExD,MAAMwC,KAAK,GAAG;IACZC,KAAK,EAAEpD,SAAS,CAACqD,MAAM;IACvBC,OAAO,EAAEtD,SAAS,CAACkD,MAAM,CAACF,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAACO,QAAQ,CAACP,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAACiC,MAAM;IAC3HG,UAAU,EAAExD,SAAS,CAACkD,MAAM,CAACF,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACO,QAAQ,CAACP,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAACiC,MAAM;IAC5FI,SAAS,EAAEzD,SAAS,CAACkD,MAAM,CAACF,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACO,QAAQ,CAACP,GAAG,CAAC5B,MAAM,CAAC,CAAC,CAACiC,MAAM;IAC1FK,OAAO,EAAE1D,SAAS,CAAC2D,MAAM,CAAC,CAACC,GAAG,EAAEZ,GAAG,KAAKY,GAAG,IAAIZ,GAAG,CAACxC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EAED,oBACEd,OAAA,CAACd,SAAS;IAACiF,KAAK;IAAAnC,QAAA,gBACdhC,OAAA,CAACb,GAAG;MAAC8C,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBhC,OAAA,CAACZ,GAAG;QAAA4C,QAAA,gBACFhC,OAAA;UAAAgC,QAAA,gBACEhC,OAAA;YAAGiC,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0BAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA;UAAGiC,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAAC3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,QAAQ,EAAC,GAAC;QAAA;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA,CAACb,GAAG;MAAC8C,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBhC,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAArC,QAAA,eACThC,OAAA,CAACX,IAAI;UAAC4C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACX,IAAI,CAACiF,IAAI;YAAAtC,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DrC,OAAA;cAAAgC,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEyB,KAAK,CAACC;YAAK;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAArC,QAAA,eACThC,OAAA,CAACX,IAAI;UAAC4C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACX,IAAI,CAACiF,IAAI;YAAAtC,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDrC,OAAA;cAAAgC,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEyB,KAAK,CAACG;YAAO;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAArC,QAAA,eACThC,OAAA,CAACX,IAAI;UAAC4C,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eAC5ChC,OAAA,CAACX,IAAI,CAACiF,IAAI;YAAAtC,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnDrC,OAAA;cAAAgC,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBrC,OAAA;cAAIiC,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAEyB,KAAK,CAACK;YAAU;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrC,OAAA,CAACZ,GAAG;QAACiF,EAAE,EAAE,CAAE;QAAArC,QAAA,eACThC,OAAA,CAACX,IAAI;UAAC4C,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/ChC,OAAA,CAACX,IAAI,CAACiF,IAAI;YAAAtC,QAAA,gBACRhC,OAAA;cAAGiC,SAAS,EAAC;YAA4C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DrC,OAAA;cAAAgC,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBrC,OAAA;cAAIiC,SAAS,EAAC,cAAc;cAAAD,QAAA,GAAC,GAAC,EAACyB,KAAK,CAACO,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC;YAAA;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrC,OAAA,CAACX,IAAI;MAAA2C,QAAA,gBACHhC,OAAA,CAACX,IAAI,CAACmF,MAAM;QAACvC,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBACxEhC,OAAA;UAAIiC,SAAS,EAAC,MAAM;UAAAD,QAAA,gBAClBhC,OAAA;YAAGiC,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,aAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrC,OAAA,CAACN,IAAI,CAAC+E,MAAM;UACVC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UACzBC,KAAK,EAAE3D,YAAa;UACpB4D,QAAQ,EAAGC,CAAC,IAAK5D,eAAe,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAA5C,QAAA,gBAEjDhC,OAAA;YAAQ4E,KAAK,EAAC,KAAK;YAAA5C,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCrC,OAAA;YAAQ4E,KAAK,EAAC,WAAW;YAAA5C,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CrC,OAAA;YAAQ4E,KAAK,EAAC,aAAa;YAAA5C,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjDrC,OAAA;YAAQ4E,KAAK,EAAC,QAAQ;YAAA5C,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCrC,OAAA;YAAQ4E,KAAK,EAAC,qBAAqB;YAAA5C,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjErC,OAAA;YAAQ4E,KAAK,EAAC,WAAW;YAAA5C,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CrC,OAAA;YAAQ4E,KAAK,EAAC,YAAY;YAAA5C,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/CrC,OAAA;YAAQ4E,KAAK,EAAC,WAAW;YAAA5C,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACdrC,OAAA,CAACX,IAAI,CAACiF,IAAI;QAAAtC,QAAA,EACPuB,YAAY,CAACI,MAAM,GAAG,CAAC,gBACtB3D,OAAA,CAACT,KAAK;UAACyF,UAAU;UAACC,KAAK;UAAAjD,QAAA,gBACrBhC,OAAA;YAAAgC,QAAA,eACEhC,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAAgC,QAAA,EAAI;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACdrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA;gBAAAgC,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChBrC,OAAA;gBAAAgC,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACfrC,OAAA;gBAAAgC,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACbrC,OAAA;gBAAAgC,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRrC,OAAA;YAAAgC,QAAA,EACGuB,YAAY,CAACF,GAAG,CAACC,GAAG,iBACnBtD,OAAA;cAAAgC,QAAA,gBACEhC,OAAA;gBAAAgC,QAAA,gBACEhC,OAAA;kBAAAgC,QAAA,EAASsB,GAAG,CAAC4B;gBAAS;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAChCrC,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNrC,OAAA;kBAAOiC,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAC1B,IAAIQ,IAAI,CAACc,GAAG,CAAC6B,OAAO,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAAgC,QAAA,gBACEhC,OAAA;oBAAAgC,QAAA,EAASsB,GAAG,CAAC+B;kBAAW;oBAAAnD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAClCrC,OAAA;oBAAAkC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrC,OAAA;oBAAOiC,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAEsB,GAAG,CAACgC;kBAAY;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,gBACEhC,OAAA;kBAAGiC,SAAS,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACnDiB,GAAG,CAACpB,QAAQ;cAAA;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAKiC,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBhC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiB,GAAG,CAACiC,SAAS;kBAAA;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiB,GAAG,CAACkC,MAAM;kBAAA;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiB,GAAG,CAACmC,SAAS;kBAAA;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClDrC,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAK;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiB,GAAG,CAACoC,SAAS;kBAAA;oBAAAxD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAChDiB,GAAG,CAACqC,OAAO,iBACV3F,OAAA;oBAAAgC,QAAA,gBAAKhC,OAAA;sBAAAgC,QAAA,EAAQ;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACiB,GAAG,CAACqC,OAAO;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,EAAKP,cAAc,CAAC6B,GAAG,CAAC5B,MAAM;cAAC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrCrC,OAAA;gBAAAgC,QAAA,EACGsB,GAAG,CAACxC,IAAI,GAAG,IAAIwC,GAAG,CAACxC,IAAI,CAACyD,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;cAAG;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACLrC,OAAA;gBAAAgC,QAAA,eACEhC,OAAA;kBAAKiC,SAAS,EAAC,oBAAoB;kBAAC2D,IAAI,EAAC,OAAO;kBAAA5D,QAAA,GAC7CsB,GAAG,CAAC5B,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAAAE,SAAA;oBAAA8B,QAAA,gBACEhC,OAAA,CAACV,MAAM;sBACLsC,OAAO,EAAC,iBAAiB;sBACzBiE,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAM;wBACbrF,cAAc,CAAC6C,GAAG,CAAC;wBACnB3C,iBAAiB,CAAC,IAAI,CAAC;sBACzB,CAAE;sBAAAqB,QAAA,gBAEFhC,OAAA;wBAAGiC,SAAS,EAAC;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SAE7C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTrC,OAAA,CAACV,MAAM;sBACLsC,OAAO,EAAC,gBAAgB;sBACxBiE,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACM,GAAG,CAACT,EAAE,EAAE,UAAU,CAAE;sBAAAb,QAAA,gBAEtDhC,OAAA;wBAAGiC,SAAS,EAAC;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,UAEvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH,EAEAiB,GAAG,CAAC5B,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAACV,MAAM;oBACLsC,OAAO,EAAC,cAAc;oBACtBiE,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACM,GAAG,CAACT,EAAE,EAAE,YAAY,CAAE;oBAAAb,QAAA,gBAExDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAkB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,SAEtC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAiB,GAAG,CAAC5B,MAAM,KAAK,YAAY,iBAC1B1B,OAAA,CAACV,MAAM;oBACLsC,OAAO,EAAC,iBAAiB;oBACzBiE,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACM,GAAG,CAACT,EAAE,EAAE,WAAW,CAAE;oBAAAb,QAAA,gBAEvDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAiB,GAAG,CAAC5B,MAAM,KAAK,WAAW,iBACzB1B,OAAA,CAACV,MAAM;oBACLsC,OAAO,EAAC,iBAAiB;oBACzBiE,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAACM,GAAG,CAACT,EAAE,EAAE,WAAW,CAAE;oBAAAb,QAAA,gBAEvDhC,OAAA;sBAAGiC,SAAS,EAAC;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAEvC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eAEDrC,OAAA,CAACV,MAAM;oBAACsC,OAAO,EAAC,mBAAmB;oBAACiE,IAAI,EAAC,IAAI;oBAAA7D,QAAA,eAC3ChC,OAAA;sBAAGiC,SAAS,EAAC;oBAAgB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAjGEiB,GAAG,CAACT,EAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkGX,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAERrC,OAAA,CAACP,KAAK;UAACmC,OAAO,EAAC,MAAM;UAAAI,QAAA,gBACnBhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0CAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGPrC,OAAA,CAACL,KAAK;MAACoG,IAAI,EAAErF,cAAe;MAACsF,MAAM,EAAEA,CAAA,KAAMrF,iBAAiB,CAAC,KAAK,CAAE;MAAAqB,QAAA,gBAClEhC,OAAA,CAACL,KAAK,CAAC6E,MAAM;QAACyB,WAAW;QAAAjE,QAAA,eACvBhC,OAAA,CAACL,KAAK,CAACuG,KAAK;UAAAlE,QAAA,gBACVhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3B,EAAC7B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE0E,SAAS;QAAA;UAAAhD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACfrC,OAAA,CAACL,KAAK,CAAC2E,IAAI;QAAAtC,QAAA,EACRxB,WAAW,iBACVR,OAAA,CAAAE,SAAA;UAAA8B,QAAA,gBACEhC,OAAA;YAAKiC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxChC,OAAA;cAAAgC,QAAA,EAAI;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrBrC,OAAA;cAAKiC,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClBhC,OAAA;gBAAKiC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBhC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAAC0B,QAAQ,eAAClC,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAAC+E,SAAS,eAACvF,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACgF,MAAM;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACNrC,OAAA;gBAAKiC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBhC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACiF,SAAS,eAACzF,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACkF,SAAS,eAAC1F,OAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpDrC,OAAA;kBAAAgC,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAAC6E,WAAW;cAAA;gBAAAnD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACL7B,WAAW,CAACmF,OAAO,iBAClB3F,OAAA;cAAKiC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBhC,OAAA;gBAAAgC,QAAA,EAAQ;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAC7B,WAAW,CAACmF,OAAO;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrC,OAAA,CAACN,IAAI;YAAAsC,QAAA,gBACHhC,OAAA,CAACb,GAAG;cAAA6C,QAAA,gBACFhC,OAAA,CAACZ,GAAG;gBAACiF,EAAE,EAAE,CAAE;gBAAArC,QAAA,eACThC,OAAA,CAACN,IAAI,CAACyG,KAAK;kBAAClE,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhC,OAAA,CAACN,IAAI,CAAC0G,KAAK;oBAAApE,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjCrC,OAAA,CAACJ,UAAU;oBAAAoC,QAAA,gBACThC,OAAA,CAACJ,UAAU,CAACyG,IAAI;sBAAArE,QAAA,EAAC;oBAAC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eACpCrC,OAAA,CAACN,IAAI,CAAC4G,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,MAAM;sBAClB7B,KAAK,EAAEhE,SAAS,CAACE,IAAK;sBACtB+D,QAAQ,EAAGC,CAAC,IAAKjE,YAAY,CAACuC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEtC,IAAI,EAAEgE,CAAC,CAACC,MAAM,CAACH;sBAAM,CAAC,CAAC,CAAE;sBAC3E8B,QAAQ;oBAAA;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNrC,OAAA,CAACZ,GAAG;gBAACiF,EAAE,EAAE,CAAE;gBAAArC,QAAA,eACThC,OAAA,CAACN,IAAI,CAACyG,KAAK;kBAAClE,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BhC,OAAA,CAACN,IAAI,CAAC0G,KAAK;oBAAApE,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxCrC,OAAA,CAACN,IAAI,CAAC4G,OAAO;oBACXC,IAAI,EAAC,QAAQ;oBACbI,GAAG,EAAC,GAAG;oBACPF,WAAW,EAAC,mBAAmB;oBAC/B7B,KAAK,EAAEhE,SAAS,CAACG,cAAe;oBAChC8D,QAAQ,EAAGC,CAAC,IAAKjE,YAAY,CAACuC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAErC,cAAc,EAAE+D,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAC,CAAE;oBACrF8B,QAAQ;kBAAA;oBAAAxE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENrC,OAAA,CAACN,IAAI,CAACyG,KAAK;cAAClE,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BhC,OAAA,CAACN,IAAI,CAAC0G,KAAK;gBAAApE,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzCrC,OAAA,CAACN,IAAI,CAAC4G,OAAO;gBACXM,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRJ,WAAW,EAAC,yCAAyC;gBACrD7B,KAAK,EAAEhE,SAAS,CAACI,KAAM;gBACvB6D,QAAQ,EAAGC,CAAC,IAAKjE,YAAY,CAACuC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEpC,KAAK,EAAE8D,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAC;cAAE;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACP;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACbrC,OAAA,CAACL,KAAK,CAACmH,MAAM;QAAA9E,QAAA,gBACXhC,OAAA,CAACV,MAAM;UAACsC,OAAO,EAAC,WAAW;UAACkE,OAAO,EAAEA,CAAA,KAAMnF,iBAAiB,CAAC,KAAK,CAAE;UAAAqB,QAAA,EAAC;QAErE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrC,OAAA,CAACV,MAAM;UACLsC,OAAO,EAAC,SAAS;UACjBkE,OAAO,EAAExD,iBAAkB;UAC3ByE,QAAQ,EAAE,CAACnG,SAAS,CAACE,IAAI,IAAI,CAACF,SAAS,CAACG,cAAe;UAAAiB,QAAA,gBAEvDhC,OAAA;YAAGiC,SAAS,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,cAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACjC,EAAA,CA1YID,oBAA8B;EAAA,QACjBN,OAAO;AAAA;AAAAmH,EAAA,GADpB7G,oBAA8B;AA4YpC,eAAeA,oBAAoB;AAAC,IAAA6G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}