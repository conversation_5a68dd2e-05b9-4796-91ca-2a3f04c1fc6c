{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { clamp } from 'motion-utils';\nconst number = {\n  test: v => typeof v === \"number\",\n  parse: parseFloat,\n  transform: v => v\n};\nconst alpha = _objectSpread(_objectSpread({}, number), {}, {\n  transform: v => clamp(0, 1, v)\n});\nconst scale = _objectSpread(_objectSpread({}, number), {}, {\n  default: 1\n});\nexport { alpha, number, scale };", "map": {"version": 3, "names": ["clamp", "number", "test", "v", "parse", "parseFloat", "transform", "alpha", "_objectSpread", "scale", "default"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs"], "sourcesContent": ["import { clamp } from 'motion-utils';\n\nconst number = {\n    test: (v) => typeof v === \"number\",\n    parse: parseFloat,\n    transform: (v) => v,\n};\nconst alpha = {\n    ...number,\n    transform: (v) => clamp(0, 1, v),\n};\nconst scale = {\n    ...number,\n    default: 1,\n};\n\nexport { alpha, number, scale };\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,cAAc;AAEpC,MAAMC,MAAM,GAAG;EACXC,IAAI,EAAGC,CAAC,IAAK,OAAOA,CAAC,KAAK,QAAQ;EAClCC,KAAK,EAAEC,UAAU;EACjBC,SAAS,EAAGH,CAAC,IAAKA;AACtB,CAAC;AACD,MAAMI,KAAK,GAAAC,aAAA,CAAAA,aAAA,KACJP,MAAM;EACTK,SAAS,EAAGH,CAAC,IAAKH,KAAK,CAAC,CAAC,EAAE,CAAC,EAAEG,CAAC;AAAC,EACnC;AACD,MAAMM,KAAK,GAAAD,aAAA,CAAAA,aAAA,KACJP,MAAM;EACTS,OAAO,EAAE;AAAC,EACb;AAED,SAASH,KAAK,EAAEN,MAAM,EAAEQ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}