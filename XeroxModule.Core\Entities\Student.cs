using System.ComponentModel.DataAnnotations;

namespace XeroxModule.Core.Entities
{
    public class Student
    {
        public int StudentID { get; set; }
        
        public int UserID { get; set; }
        
        [Required]
        [StringLength(20)]
        public string StudentNumber { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string? PhoneNumber { get; set; }
        
        [StringLength(100)]
        public string? Department { get; set; }
        
        public int? Year { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? ModifiedAt { get; set; }
        
        // Navigation properties
        public User User { get; set; } = null!;
        public ICollection<FileUpload> FileUploads { get; set; } = new List<FileUpload>();
        public ICollection<Rating> Ratings { get; set; } = new List<Rating>();
        
        // Computed property
        public string FullName => $"{FirstName} {LastName}";
    }
}
