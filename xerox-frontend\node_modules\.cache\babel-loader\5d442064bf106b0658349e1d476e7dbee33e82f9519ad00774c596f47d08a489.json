{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\StudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n    fetchData();\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this);\n  };\n  const getWorkloadColor = pendingJobs => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tachometer-alt me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), \"Student Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: \"auto\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: () => setShowUploadModal(true),\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), \"Upload Files\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: printJobs.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-check-circle fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: [\"$\", printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        lg: 8,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-list me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), \"Recent Print Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n              responsive: true,\n              hover: true,\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Job #\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"File Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 277,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Cost\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Xerox Center\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Actions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: printJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-file-pdf me-2 text-danger\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 27\n                    }, this), job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: getStatusBadge(job.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: job.xeroxCenterName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-info\",\n                      size: \"sm\",\n                      onClick: () => handleDownloadFile(job.id, job.fileName),\n                      title: \"Download File\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-download me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 53\n                      }, this), \"Download\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 51\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: () => handleViewJob(job),\n                      title: \"View Details\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-eye\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-secondary\",\n                      size: \"sm\",\n                      className: \"me-1\",\n                      onClick: () => handleOpenChat(job),\n                      title: \"Chat\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-comment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 27\n                    }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      onClick: () => handleConfirmJob(job.id),\n                      title: \"Confirm Quote\",\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-check\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 329,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 25\n                  }, this)]\n                }, job.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"info\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-info-circle me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 19\n              }, this), \"No print jobs yet. Upload your first file to get started!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        lg: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            children: /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"mb-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"fas fa-store me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 17\n              }, this), \"Available Centers\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(Card, {\n              className: \"mb-3 border-0 bg-light\",\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-start mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: center.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: getWorkloadColor(center.pendingJobs),\n                    children: [center.pendingJobs, \" jobs\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 368,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-muted small mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                    className: \"fas fa-map-marker-alt me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 23\n                  }, this), center.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex justify-content-between align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-star text-warning me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"small\",\n                      children: center.averageRating.toFixed(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-primary\",\n                    size: \"sm\",\n                    children: \"Select\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 19\n              }, this)\n            }, center.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this), \"Upload Files for Printing\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: /*#__PURE__*/_jsxDEV(Form, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Select File\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              type: \"file\",\n              accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n              onChange: e => {\n                const files = e.target.files;\n                setSelectedFile(files ? files[0] : null);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n              className: \"text-muted\",\n              children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Print Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.printType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    printType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Print\",\n                    children: \"Print\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 426,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Xerox\",\n                    children: \"Xerox\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Binding\",\n                    children: \"Binding\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 428,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Lamination\",\n                    children: \"Lamination\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 429,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Number of Copies\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  min: \"1\",\n                  value: uploadData.copies,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    copies: parseInt(e.target.value)\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 434,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 433,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Color Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.colorType,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    colorType: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"BlackWhite\",\n                    children: \"Black & White\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Color\",\n                    children: \"Color\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Paper Size\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  value: uploadData.paperSize,\n                  onChange: e => setUploadData(prev => ({\n                    ...prev,\n                    paperSize: e.target.value\n                  })),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A4\",\n                    children: \"A4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"A3\",\n                    children: \"A3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 467,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Letter\",\n                    children: \"Letter\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Legal\",\n                    children: \"Legal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Preferred Xerox Center\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n              value: uploadData.preferredXeroxCenterId,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                preferredXeroxCenterId: e.target.value\n              })),\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select a center (optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: center.id,\n                children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n              }, center.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 475,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n            className: \"mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              as: \"textarea\",\n              rows: 3,\n              placeholder: \"Any special instructions or remarks...\",\n              value: uploadData.remarks,\n              onChange: e => setUploadData(prev => ({\n                ...prev,\n                remarks: e.target.value\n              }))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowUploadModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleFileUpload,\n          disabled: !selectedFile,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-upload me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 507,\n            columnNumber: 13\n          }, this), \"Upload File\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 502,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showViewModal,\n      onHide: () => setShowViewModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-eye me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this), \"Job Details - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 516,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"File Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 526,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Print Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.printType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.colorType]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 531,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Paper Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 532,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.paperSize]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                children: \"Job Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 22\n                }, this), \" \", getStatusBadge(selectedJob.status)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Cost:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Xerox Center:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 22\n                }, this), \" \", selectedJob.xeroxCenterName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Created:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 22\n                }, this), \" \", new Date(selectedJob.created).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 540,\n                columnNumber: 19\n              }, this), selectedJob.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Estimated Completion:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 24\n                }, this), \" \", new Date(selectedJob.estimatedCompletionTime).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 15\n          }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: selectedJob.remarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this), selectedJob.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Alert, {\n            variant: \"warning\",\n            className: \"mt-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-exclamation-triangle me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 19\n            }, this), \"This job has been quoted. Please confirm to proceed with printing.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [(selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.status) === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          onClick: () => {\n            handleConfirmJob(selectedJob.id);\n            setShowViewModal(false);\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-check me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this), \"Confirm Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowViewModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 561,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showChatModal,\n      onHide: () => setShowChatModal(false),\n      size: \"lg\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-comment me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), \"Chat - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '400px',\n            overflowY: 'auto',\n            border: '1px solid #dee2e6',\n            borderRadius: '0.375rem',\n            padding: '1rem',\n            marginBottom: '1rem'\n          },\n          children: messages.length > 0 ? messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`,\n              style: {\n                maxWidth: '70%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: `d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`,\n                children: [message.senderName, \" - \", new Date(message.sentAt).toLocaleString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-muted\",\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-comments fa-3x mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No messages yet. Start a conversation!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"text\",\n            placeholder: \"Type your message...\",\n            value: chatMessage,\n            onChange: e => setChatMessage(e.target.value),\n            onKeyDown: e => e.key === 'Enter' && handleSendMessage(),\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"primary\",\n            onClick: handleSendMessage,\n            disabled: !chatMessage.trim(),\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-paper-plane\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowChatModal(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 581,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentDashboard, \"8ErydrNvcotwx3lsAKqva+c5cB0=\", false, function () {\n  return [useAuth];\n});\n_c = StudentDashboard;\nexport default StudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"StudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "jsxDEV", "_jsxDEV", "StudentDashboard", "_s", "user", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "fetchData", "printJobsResponse", "getStudentJobs", "data", "xeroxCentersResponse", "getAll", "error", "console", "getStatusBadge", "status", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getWorkloadColor", "pendingJobs", "handleFileUpload", "formData", "FormData", "append", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "fluid", "username", "xs", "onClick", "md", "Body", "length", "filter", "includes", "reduce", "sum", "cost", "toFixed", "lg", "Header", "responsive", "hover", "map", "jobNumber", "slice", "xeroxCenterName", "size", "title", "center", "name", "location", "averageRating", "show", "onHide", "closeButton", "Title", "Group", "Label", "Control", "type", "accept", "onChange", "e", "files", "target", "Text", "Select", "value", "min", "parseInt", "as", "rows", "placeholder", "Footer", "disabled", "Date", "created", "toLocaleString", "estimatedCompletionTime", "style", "height", "overflowY", "border", "borderRadius", "padding", "marginBottom", "message", "isFromCurrentUser", "max<PERSON><PERSON><PERSON>", "content", "sender<PERSON>ame", "sentAt", "onKeyDown", "key", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/StudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst StudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4'\n  });\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch print jobs\n        const printJobsResponse = await printJobApi.getStudentJobs();\n        setPrintJobs(printJobsResponse.data);\n\n        // Fetch xerox centers\n        const xeroxCentersResponse = await xeroxCenterApi.getAll();\n        setXeroxCenters(xeroxCentersResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        // Set empty arrays on error\n        setPrintJobs([]);\n        setXeroxCenters([]);\n      }\n    };\n\n    fetchData();\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const getWorkloadColor = (pendingJobs: number) => {\n    if (pendingJobs <= 5) return 'success';\n    if (pendingJobs <= 10) return 'warning';\n    return 'danger';\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      formData.append('remarks', uploadData.remarks);\n      formData.append('preferredXeroxCenterId', uploadData.preferredXeroxCenterId);\n      formData.append('printType', uploadData.printType);\n      formData.append('copies', uploadData.copies.toString());\n      formData.append('colorType', uploadData.colorType);\n      formData.append('paperSize', uploadData.paperSize);\n\n      await fileUploadApi.uploadFile(formData);\n\n      // Refresh print jobs after upload\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n\n      // Load messages for this job\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n\n      // Create blob URL and trigger download\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n      // You might want to show an error message to the user here\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n\n      // Refresh print jobs after confirmation\n      const printJobsResponse = await printJobApi.getStudentJobs();\n      setPrintJobs(printJobsResponse.data);\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n\n      // Add the new message to the list\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-tachometer-alt me-2\"></i>\n            Student Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n        <Col xs=\"auto\">\n          <Button variant=\"primary\" onClick={() => setShowUploadModal(true)}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files\n          </Button>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{printJobs.length}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-warning\">\n                {printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-check-circle fa-2x text-success mb-2\"></i>\n              <h5>Completed</h5>\n              <h3 className=\"text-success\">\n                {printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-info mb-2\"></i>\n              <h5>Total Spent</h5>\n              <h3 className=\"text-info\">\n                ${printJobs.reduce((sum, job) => sum + (job.cost || 0), 0).toFixed(2)}\n              </h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      <Row>\n        {/* Recent Jobs */}\n        <Col lg={8}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-list me-2\"></i>\n                Recent Print Jobs\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {printJobs.length > 0 ? (\n                <Table responsive hover>\n                  <thead>\n                    <tr>\n                      <th>Job #</th>\n                      <th>File Name</th>\n                      <th>Status</th>\n                      <th>Cost</th>\n                      <th>Xerox Center</th>\n                      <th>Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    {printJobs.map(job => (\n                      <tr key={job.id}>\n                        <td>\n                          <strong>{job.jobNumber}</strong>\n                        </td>\n                        <td>\n                          <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                          {job.fileName.length > 35 ? job.fileName.slice(0, 35) + '...' : job.fileName}\n                        </td>\n                        <td>{getStatusBadge(job.status)}</td>\n                        <td>\n                          {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                        </td>\n                        <td>{job.xeroxCenterName}</td>\n                        <td>\n                          {/* Download button - always available */}\n                                                  <Button\n                                                    variant=\"outline-info\"\n                                                    size=\"sm\"\n                                                    onClick={() => handleDownloadFile(job.id, job.fileName)}\n                                                    title=\"Download File\"\n                                                  >\n                                                    <i className=\"fas fa-download me-1\"></i>\n                                                    Download\n                                                  </Button>\n                          <Button\n                            variant=\"outline-primary\"\n                            size=\"sm\"\n                            className=\"me-1\"\n                            onClick={() => handleViewJob(job)}\n                            title=\"View Details\"\n                          >\n                            <i className=\"fas fa-eye\"></i>\n                          </Button>\n                          <Button\n                            variant=\"outline-secondary\"\n                            size=\"sm\"\n                            className=\"me-1\"\n                            onClick={() => handleOpenChat(job)}\n                            title=\"Chat\"\n                          >\n                            <i className=\"fas fa-comment\"></i>\n                          </Button>\n                          {job.status === 'Quoted' && (\n                            <Button\n                              variant=\"success\"\n                              size=\"sm\"\n                              onClick={() => handleConfirmJob(job.id)}\n                              title=\"Confirm Quote\"\n                            >\n                              <i className=\"fas fa-check\"></i>\n                            </Button>\n                          )}\n                        </td>\n                      </tr>\n                    ))}\n                  </tbody>\n                </Table>\n              ) : (\n                <Alert variant=\"info\">\n                  <i className=\"fas fa-info-circle me-2\"></i>\n                  No print jobs yet. Upload your first file to get started!\n                </Alert>\n              )}\n            </Card.Body>\n          </Card>\n        </Col>\n\n        {/* Xerox Centers */}\n        <Col lg={4}>\n          <Card>\n            <Card.Header>\n              <h5 className=\"mb-0\">\n                <i className=\"fas fa-store me-2\"></i>\n                Available Centers\n              </h5>\n            </Card.Header>\n            <Card.Body>\n              {xeroxCenters.map(center => (\n                <Card key={center.id} className=\"mb-3 border-0 bg-light\">\n                  <Card.Body className=\"p-3\">\n                    <div className=\"d-flex justify-content-between align-items-start mb-2\">\n                      <h6 className=\"mb-1\">{center.name}</h6>\n                      <Badge bg={getWorkloadColor(center.pendingJobs)}>\n                        {center.pendingJobs} jobs\n                      </Badge>\n                    </div>\n                    <p className=\"text-muted small mb-2\">\n                      <i className=\"fas fa-map-marker-alt me-1\"></i>\n                      {center.location}\n                    </p>\n                    <div className=\"d-flex justify-content-between align-items-center\">\n                      <div>\n                        <i className=\"fas fa-star text-warning me-1\"></i>\n                        <span className=\"small\">{center.averageRating.toFixed(1)}</span>\n                      </div>\n                      <Button variant=\"outline-primary\" size=\"sm\">\n                        Select\n                      </Button>\n                    </div>\n                  </Card.Body>\n                </Card>\n              ))}\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Upload Modal */}\n      <Modal show={showUploadModal} onHide={() => setShowUploadModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload Files for Printing\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <Form>\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Select File</Form.Label>\n              <Form.Control\n                type=\"file\"\n                accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n                onChange={(e) => {\n                  const files = (e.target as HTMLInputElement).files;\n                  setSelectedFile(files ? files[0] : null);\n                }}\n              />\n              <Form.Text className=\"text-muted\">\n                Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n              </Form.Text>\n            </Form.Group>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Print Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.printType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, printType: e.target.value }))}\n                  >\n                    <option value=\"Print\">Print</option>\n                    <option value=\"Xerox\">Xerox</option>\n                    <option value=\"Binding\">Binding</option>\n                    <option value=\"Lamination\">Lamination</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Number of Copies</Form.Label>\n                  <Form.Control\n                    type=\"number\"\n                    min=\"1\"\n                    value={uploadData.copies}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  />\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Row>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Color Type</Form.Label>\n                  <Form.Select\n                    value={uploadData.colorType}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, colorType: e.target.value }))}\n                  >\n                    <option value=\"BlackWhite\">Black & White</option>\n                    <option value=\"Color\">Color</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n              <Col md={6}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Paper Size</Form.Label>\n                  <Form.Select\n                    value={uploadData.paperSize}\n                    onChange={(e) => setUploadData(prev => ({ ...prev, paperSize: e.target.value }))}\n                  >\n                    <option value=\"A4\">A4</option>\n                    <option value=\"A3\">A3</option>\n                    <option value=\"Letter\">Letter</option>\n                    <option value=\"Legal\">Legal</option>\n                  </Form.Select>\n                </Form.Group>\n              </Col>\n            </Row>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Preferred Xerox Center</Form.Label>\n              <Form.Select\n                value={uploadData.preferredXeroxCenterId}\n                onChange={(e) => setUploadData(prev => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              >\n                <option value=\"\">Select a center (optional)</option>\n                {xeroxCenters.map(center => (\n                  <option key={center.id} value={center.id}>\n                    {center.name} - {center.pendingJobs} pending jobs\n                  </option>\n                ))}\n              </Form.Select>\n            </Form.Group>\n\n            <Form.Group className=\"mb-3\">\n              <Form.Label>Remarks</Form.Label>\n              <Form.Control\n                as=\"textarea\"\n                rows={3}\n                placeholder=\"Any special instructions or remarks...\"\n                value={uploadData.remarks}\n                onChange={(e) => setUploadData(prev => ({ ...prev, remarks: e.target.value }))}\n              />\n            </Form.Group>\n          </Form>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowUploadModal(false)}>\n            Cancel\n          </Button>\n          <Button variant=\"primary\" onClick={handleFileUpload} disabled={!selectedFile}>\n            <i className=\"fas fa-upload me-2\"></i>\n            Upload File\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* View Job Details Modal */}\n      <Modal show={showViewModal} onHide={() => setShowViewModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-eye me-2\"></i>\n            Job Details - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <div>\n              <Row>\n                <Col md={6}>\n                  <h6>File Information</h6>\n                  <hr />\n                  <p><strong>File Name:</strong> {selectedJob.fileName.length > 35 ? selectedJob.fileName.slice(0, 35) + '...' : selectedJob.fileName}</p>\n                  <p><strong>Print Type:</strong> {selectedJob.printType}</p>\n                  <p><strong>Copies:</strong> {selectedJob.copies}</p>\n                  <p><strong>Color Type:</strong> {selectedJob.colorType}</p>\n                  <p><strong>Paper Size:</strong> {selectedJob.paperSize}</p>\n                </Col>\n                <Col md={6}>\n                  <h6>Job Information</h6>\n                  <hr />\n                  <p><strong>Status:</strong> {getStatusBadge(selectedJob.status)}</p>\n                  <p><strong>Cost:</strong> {selectedJob.cost ? `$${selectedJob.cost.toFixed(2)}` : 'Not quoted yet'}</p>\n                  <p><strong>Xerox Center:</strong> {selectedJob.xeroxCenterName}</p>\n                  <p><strong>Created:</strong> {new Date(selectedJob.created).toLocaleString()}</p>\n                  {selectedJob.estimatedCompletionTime && (\n                    <p><strong>Estimated Completion:</strong> {new Date(selectedJob.estimatedCompletionTime).toLocaleString()}</p>\n                  )}\n                </Col>\n              </Row>\n              {selectedJob.remarks && (\n                <div className=\"mt-3\">\n                  <h6>Remarks</h6>\n                  <p className=\"text-muted\">{selectedJob.remarks}</p>\n                </div>\n              )}\n              {selectedJob.status === 'Quoted' && (\n                <Alert variant=\"warning\" className=\"mt-3\">\n                  <i className=\"fas fa-exclamation-triangle me-2\"></i>\n                  This job has been quoted. Please confirm to proceed with printing.\n                </Alert>\n              )}\n            </div>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          {selectedJob?.status === 'Quoted' && (\n            <Button\n              variant=\"success\"\n              onClick={() => {\n                handleConfirmJob(selectedJob.id);\n                setShowViewModal(false);\n              }}\n            >\n              <i className=\"fas fa-check me-2\"></i>\n              Confirm Quote\n            </Button>\n          )}\n          <Button variant=\"secondary\" onClick={() => setShowViewModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n\n      {/* Chat Modal */}\n      <Modal show={showChatModal} onHide={() => setShowChatModal(false)} size=\"lg\">\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-comment me-2\"></i>\n            Chat - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          <div style={{ height: '400px', overflowY: 'auto', border: '1px solid #dee2e6', borderRadius: '0.375rem', padding: '1rem', marginBottom: '1rem' }}>\n            {messages.length > 0 ? (\n              messages.map((message) => (\n                <div key={message.id} className={`mb-3 ${message.isFromCurrentUser ? 'text-end' : 'text-start'}`}>\n                  <div className={`d-inline-block p-2 rounded ${message.isFromCurrentUser ? 'bg-primary text-white' : 'bg-light'}`} style={{ maxWidth: '70%' }}>\n                    <div>{message.content}</div>\n                    <small className={`d-block mt-1 ${message.isFromCurrentUser ? 'text-light' : 'text-muted'}`}>\n                      {message.senderName} - {new Date(message.sentAt).toLocaleString()}\n                    </small>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-center text-muted\">\n                <i className=\"fas fa-comments fa-3x mb-3\"></i>\n                <p>No messages yet. Start a conversation!</p>\n              </div>\n            )}\n          </div>\n          <div className=\"d-flex\">\n            <Form.Control\n              type=\"text\"\n              placeholder=\"Type your message...\"\n              value={chatMessage}\n              onChange={(e) => setChatMessage(e.target.value)}\n              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}\n              className=\"me-2\"\n            />\n            <Button variant=\"primary\" onClick={handleSendMessage} disabled={!chatMessage.trim()}>\n              <i className=\"fas fa-paper-plane\"></i>\n            </Button>\n          </div>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowChatModal(false)}>\n            Close\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default StudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,QAAQ,iBAAiB;AACrG,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsBzF,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC;IAC3C0C,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF9C,SAAS,CAAC,MAAM;IACd,MAAM+C,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;QAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;;QAEpC;QACA,MAAMC,oBAAoB,GAAG,MAAMtC,cAAc,CAACuC,MAAM,CAAC,CAAC;QAC1D5B,eAAe,CAAC2B,oBAAoB,CAACD,IAAI,CAAC;MAC5C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA/B,YAAY,CAAC,EAAE,CAAC;QAChBE,eAAe,CAAC,EAAE,CAAC;MACrB;IACF,CAAC;IAEDuB,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACE1C,OAAA,CAACV,KAAK;MAACsD,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxB7C,OAAA;QAAG8C,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CX,MAAM;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAIC,WAAmB,IAAK;IAChD,IAAIA,WAAW,IAAI,CAAC,EAAE,OAAO,SAAS;IACtC,IAAIA,WAAW,IAAI,EAAE,EAAE,OAAO,SAAS;IACvC,OAAO,QAAQ;EACjB,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACrC,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExC,YAAY,CAAC;MACrCsC,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAElC,UAAU,CAACE,OAAO,CAAC;MAC9C8B,QAAQ,CAACE,MAAM,CAAC,wBAAwB,EAAElC,UAAU,CAACG,sBAAsB,CAAC;MAC5E6B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACI,SAAS,CAAC;MAClD4B,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAElC,UAAU,CAACK,MAAM,CAAC8B,QAAQ,CAAC,CAAC,CAAC;MACvDH,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACM,SAAS,CAAC;MAClD0B,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAElC,UAAU,CAACO,SAAS,CAAC;MAElD,MAAMhC,aAAa,CAAC6D,UAAU,CAACJ,QAAQ,CAAC;;MAExC;MACA,MAAMvB,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;MAEpCxB,kBAAkB,CAAC,KAAK,CAAC;MACzBQ,eAAe,CAAC,IAAI,CAAC;MACrBM,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C;IACF;EACF,CAAC;EAED,MAAMuB,aAAa,GAAIC,GAAQ,IAAK;IAClC7C,cAAc,CAAC6C,GAAG,CAAC;IACnBjD,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAMkD,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACF7C,cAAc,CAAC6C,GAAG,CAAC;MACnB/C,gBAAgB,CAAC,IAAI,CAAC;;MAEtB;MACA,MAAMiD,QAAQ,GAAG,MAAMhE,UAAU,CAACiE,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxD3C,WAAW,CAACyC,QAAQ,CAAC7B,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/Cf,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAM4C,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEnB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMjE,aAAa,CAACsE,YAAY,CAACD,KAAK,CAAC;;MAExD;MACA,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAAC7B,IAAI,CAAC,CAAC;MACtC,MAAMqC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAG/B,QAAQ;MACxB4B,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOlC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;IACF;EACF,CAAC;EAED,MAAMgD,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAMvE,WAAW,CAAC0F,UAAU,CAACnB,KAAK,CAAC;;MAEnC;MACA,MAAMnC,iBAAiB,GAAG,MAAMpC,WAAW,CAACqC,cAAc,CAAC,CAAC;MAC5D3B,YAAY,CAAC0B,iBAAiB,CAACE,IAAI,CAAC;IACtC,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMkD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACpE,WAAW,CAACqE,IAAI,CAAC,CAAC,IAAI,CAACzE,WAAW,EAAE;IAEzC,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAMhE,UAAU,CAAC0F,WAAW,CAAC1E,WAAW,CAACkD,EAAE,EAAE9C,WAAW,CAACqE,IAAI,CAAC,CAAC,CAAC;;MAEjF;MACAlE,WAAW,CAACoE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAAC7B,IAAI,CAAC,CAAC;MAC7Cd,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,oBACEpC,OAAA,CAAChB,SAAS;IAAC0G,KAAK;IAAA7C,QAAA,gBACd7C,OAAA,CAACf,GAAG;MAAC6D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB7C,OAAA,CAACd,GAAG;QAAA2D,QAAA,gBACF7C,OAAA;UAAA6C,QAAA,gBACE7C,OAAA;YAAG8C,SAAS,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,qBAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlD,OAAA;UAAG8C,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAAC1C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwF,QAAQ,EAAC,GAAC;QAAA;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACNlD,OAAA,CAACd,GAAG;QAAC0G,EAAE,EAAC,MAAM;QAAA/C,QAAA,eACZ7C,OAAA,CAACZ,MAAM;UAACqD,OAAO,EAAC,SAAS;UAACoD,OAAO,EAAEA,CAAA,KAAMpF,kBAAkB,CAAC,IAAI,CAAE;UAAAoC,QAAA,gBAChE7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,gBAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACf,GAAG;MAAC6D,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnB7C,OAAA,CAACd,GAAG;QAAC4G,EAAE,EAAE,CAAE;QAAAjD,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAC2D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlD,OAAA;cAAA6C,QAAA,EAAI;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEzC,SAAS,CAAC4F;YAAM;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACd,GAAG;QAAC4G,EAAE,EAAE,CAAE;QAAAjD,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAC2D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAsC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDlD,OAAA;cAAA6C,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBzC,SAAS,CAAC6F,MAAM,CAACrC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACsC,QAAQ,CAACtC,GAAG,CAACrB,MAAM,CAAC,CAAC,CAACyD;YAAM;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACd,GAAG;QAAC4G,EAAE,EAAE,CAAE;QAAAjD,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAC2D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/DlD,OAAA;cAAA6C,QAAA,EAAI;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClBlD,OAAA;cAAI8C,SAAS,EAAC,cAAc;cAAAD,QAAA,EACzBzC,SAAS,CAAC6F,MAAM,CAACrC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACsC,QAAQ,CAACtC,GAAG,CAACrB,MAAM,CAAC,CAAC,CAACyD;YAAM;cAAAjD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACd,GAAG;QAAC4G,EAAE,EAAE,CAAE;QAAAjD,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAC2D,SAAS,EAAC,aAAa;UAAAD,QAAA,eAC3B7C,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,gBACR7C,OAAA;cAAG8C,SAAS,EAAC;YAAyC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlD,OAAA;cAAA6C,QAAA,EAAI;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpBlD,OAAA;cAAI8C,SAAS,EAAC,WAAW;cAAAD,QAAA,GAAC,GACvB,EAACzC,SAAS,CAAC+F,MAAM,CAAC,CAACC,GAAG,EAAExC,GAAG,KAAKwC,GAAG,IAAIxC,GAAG,CAACyC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlD,OAAA,CAACf,GAAG;MAAA4D,QAAA,gBAEF7C,OAAA,CAACd,GAAG;QAACqH,EAAE,EAAE,CAAE;QAAA1D,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAA0D,QAAA,gBACH7C,OAAA,CAACb,IAAI,CAACqH,MAAM;YAAA3D,QAAA,eACV7C,OAAA;cAAI8C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdlD,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,EACPzC,SAAS,CAAC4F,MAAM,GAAG,CAAC,gBACnBhG,OAAA,CAACX,KAAK;cAACoH,UAAU;cAACC,KAAK;cAAA7D,QAAA,gBACrB7C,OAAA;gBAAA6C,QAAA,eACE7C,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAA6C,QAAA,EAAI;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACdlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClBlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACflD,OAAA;oBAAA6C,QAAA,EAAI;kBAAI;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACblD,OAAA;oBAAA6C,QAAA,EAAI;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrBlD,OAAA;oBAAA6C,QAAA,EAAI;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRlD,OAAA;gBAAA6C,QAAA,EACGzC,SAAS,CAACuG,GAAG,CAAC/C,GAAG,iBAChB5D,OAAA;kBAAA6C,QAAA,gBACE7C,OAAA;oBAAA6C,QAAA,eACE7C,OAAA;sBAAA6C,QAAA,EAASe,GAAG,CAACgD;oBAAS;sBAAA7D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG8C,SAAS,EAAC;oBAAkC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EACnDU,GAAG,CAACb,QAAQ,CAACiD,MAAM,GAAG,EAAE,GAAGpC,GAAG,CAACb,QAAQ,CAAC8D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGjD,GAAG,CAACb,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,EAAKP,cAAc,CAACsB,GAAG,CAACrB,MAAM;kBAAC;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrClD,OAAA;oBAAA6C,QAAA,EACGe,GAAG,CAACyC,IAAI,GAAG,IAAIzC,GAAG,CAACyC,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;kBAAG;oBAAAvD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC,CAAC,eACLlD,OAAA;oBAAA6C,QAAA,EAAKe,GAAG,CAACkD;kBAAe;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC9BlD,OAAA;oBAAA6C,QAAA,gBAE0B7C,OAAA,CAACZ,MAAM;sBACLqD,OAAO,EAAC,cAAc;sBACtBsE,IAAI,EAAC,IAAI;sBACTlB,OAAO,EAAEA,CAAA,KAAM5B,kBAAkB,CAACL,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAACb,QAAQ,CAAE;sBACxDiE,KAAK,EAAC,eAAe;sBAAAnE,QAAA,gBAErB7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,YAE1C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACjClD,OAAA,CAACZ,MAAM;sBACLqD,OAAO,EAAC,iBAAiB;sBACzBsE,IAAI,EAAC,IAAI;sBACTjE,SAAS,EAAC,MAAM;sBAChB+C,OAAO,EAAEA,CAAA,KAAMlC,aAAa,CAACC,GAAG,CAAE;sBAClCoD,KAAK,EAAC,cAAc;sBAAAnE,QAAA,eAEpB7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACTlD,OAAA,CAACZ,MAAM;sBACLqD,OAAO,EAAC,mBAAmB;sBAC3BsE,IAAI,EAAC,IAAI;sBACTjE,SAAS,EAAC,MAAM;sBAChB+C,OAAO,EAAEA,CAAA,KAAMhC,cAAc,CAACD,GAAG,CAAE;sBACnCoD,KAAK,EAAC,MAAM;sBAAAnE,QAAA,eAEZ7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B,CAAC,EACRU,GAAG,CAACrB,MAAM,KAAK,QAAQ,iBACtBvC,OAAA,CAACZ,MAAM;sBACLqD,OAAO,EAAC,SAAS;sBACjBsE,IAAI,EAAC,IAAI;sBACTlB,OAAO,EAAEA,CAAA,KAAMT,gBAAgB,CAACxB,GAAG,CAACI,EAAE,CAAE;sBACxCgD,KAAK,EAAC,eAAe;sBAAAnE,QAAA,eAErB7C,OAAA;wBAAG8C,SAAS,EAAC;sBAAc;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA,GApDEU,GAAG,CAACI,EAAE;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqDX,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAERlD,OAAA,CAACT,KAAK;cAACkD,OAAO,EAAC,MAAM;cAAAI,QAAA,gBACnB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,6DAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UACR;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNlD,OAAA,CAACd,GAAG;QAACqH,EAAE,EAAE,CAAE;QAAA1D,QAAA,eACT7C,OAAA,CAACb,IAAI;UAAA0D,QAAA,gBACH7C,OAAA,CAACb,IAAI,CAACqH,MAAM;YAAA3D,QAAA,eACV7C,OAAA;cAAI8C,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAClB7C,OAAA;gBAAG8C,SAAS,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,qBAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eACdlD,OAAA,CAACb,IAAI,CAAC4G,IAAI;YAAAlD,QAAA,EACPvC,YAAY,CAACqG,GAAG,CAACM,MAAM,iBACtBjH,OAAA,CAACb,IAAI;cAAiB2D,SAAS,EAAC,wBAAwB;cAAAD,QAAA,eACtD7C,OAAA,CAACb,IAAI,CAAC4G,IAAI;gBAACjD,SAAS,EAAC,KAAK;gBAAAD,QAAA,gBACxB7C,OAAA;kBAAK8C,SAAS,EAAC,uDAAuD;kBAAAD,QAAA,gBACpE7C,OAAA;oBAAI8C,SAAS,EAAC,MAAM;oBAAAD,QAAA,EAAEoE,MAAM,CAACC;kBAAI;oBAAAnE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACvClD,OAAA,CAACV,KAAK;oBAACsD,EAAE,EAAEO,gBAAgB,CAAC8D,MAAM,CAAC7D,WAAW,CAAE;oBAAAP,QAAA,GAC7CoE,MAAM,CAAC7D,WAAW,EAAC,OACtB;kBAAA;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNlD,OAAA;kBAAG8C,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAClC7C,OAAA;oBAAG8C,SAAS,EAAC;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,EAC7C+D,MAAM,CAACE,QAAQ;gBAAA;kBAAApE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACJlD,OAAA;kBAAK8C,SAAS,EAAC,mDAAmD;kBAAAD,QAAA,gBAChE7C,OAAA;oBAAA6C,QAAA,gBACE7C,OAAA;sBAAG8C,SAAS,EAAC;oBAA+B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDlD,OAAA;sBAAM8C,SAAS,EAAC,OAAO;sBAAAD,QAAA,EAAEoE,MAAM,CAACG,aAAa,CAACd,OAAO,CAAC,CAAC;oBAAC;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNlD,OAAA,CAACZ,MAAM;oBAACqD,OAAO,EAAC,iBAAiB;oBAACsE,IAAI,EAAC,IAAI;oBAAAlE,QAAA,EAAC;kBAE5C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC,GArBH+D,MAAM,CAACjD,EAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBd,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACP,KAAK;MAAC4H,IAAI,EAAE7G,eAAgB;MAAC8G,MAAM,EAAEA,CAAA,KAAM7G,kBAAkB,CAAC,KAAK,CAAE;MAACsG,IAAI,EAAC,IAAI;MAAAlE,QAAA,gBAC9E7C,OAAA,CAACP,KAAK,CAAC+G,MAAM;QAACe,WAAW;QAAA1E,QAAA,eACvB7C,OAAA,CAACP,KAAK,CAAC+H,KAAK;UAAA3E,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,6BAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACP,KAAK,CAACsG,IAAI;QAAAlD,QAAA,eACT7C,OAAA,CAACR,IAAI;UAAAqD,QAAA,gBACH7C,OAAA,CAACR,IAAI,CAACiI,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;cAAA7E,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACpClD,OAAA,CAACR,IAAI,CAACmI,OAAO;cACXC,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,2CAA2C;cAClDC,QAAQ,EAAGC,CAAC,IAAK;gBACf,MAAMC,KAAK,GAAID,CAAC,CAACE,MAAM,CAAsBD,KAAK;gBAClD/G,eAAe,CAAC+G,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;cAC1C;YAAE;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFlD,OAAA,CAACR,IAAI,CAAC0I,IAAI;cAACpF,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAC;YAElC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEblD,OAAA,CAACf,GAAG;YAAA4D,QAAA,gBACF7C,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACT7C,OAAA,CAACR,IAAI,CAACiI,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;kBAAA7E,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACR,IAAI,CAAC2I,MAAM;kBACVC,KAAK,EAAE9G,UAAU,CAACI,SAAU;kBAC5BoG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE/D,SAAS,EAAEqG,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAvF,QAAA,gBAEjF7C,OAAA;oBAAQoI,KAAK,EAAC,OAAO;oBAAAvF,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClD,OAAA;oBAAQoI,KAAK,EAAC,OAAO;oBAAAvF,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClD,OAAA;oBAAQoI,KAAK,EAAC,SAAS;oBAAAvF,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxClD,OAAA;oBAAQoI,KAAK,EAAC,YAAY;oBAAAvF,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlD,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACT7C,OAAA,CAACR,IAAI,CAACiI,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;kBAAA7E,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACzClD,OAAA,CAACR,IAAI,CAACmI,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbS,GAAG,EAAC,GAAG;kBACPD,KAAK,EAAE9G,UAAU,CAACK,MAAO;kBACzBmG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE9D,MAAM,EAAE2G,QAAQ,CAACP,CAAC,CAACE,MAAM,CAACG,KAAK;kBAAE,CAAC,CAAC;gBAAE;kBAAArF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA,CAACf,GAAG;YAAA4D,QAAA,gBACF7C,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACT7C,OAAA,CAACR,IAAI,CAACiI,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;kBAAA7E,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACR,IAAI,CAAC2I,MAAM;kBACVC,KAAK,EAAE9G,UAAU,CAACM,SAAU;kBAC5BkG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE7D,SAAS,EAAEmG,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAvF,QAAA,gBAEjF7C,OAAA;oBAAQoI,KAAK,EAAC,YAAY;oBAAAvF,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjDlD,OAAA;oBAAQoI,KAAK,EAAC,OAAO;oBAAAvF,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNlD,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,eACT7C,OAAA,CAACR,IAAI,CAACiI,KAAK;gBAAC3E,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;kBAAA7E,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACnClD,OAAA,CAACR,IAAI,CAAC2I,MAAM;kBACVC,KAAK,EAAE9G,UAAU,CAACO,SAAU;kBAC5BiG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5D,SAAS,EAAEkG,CAAC,CAACE,MAAM,CAACG;kBAAM,CAAC,CAAC,CAAE;kBAAAvF,QAAA,gBAEjF7C,OAAA;oBAAQoI,KAAK,EAAC,IAAI;oBAAAvF,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BlD,OAAA;oBAAQoI,KAAK,EAAC,IAAI;oBAAAvF,QAAA,EAAC;kBAAE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9BlD,OAAA;oBAAQoI,KAAK,EAAC,QAAQ;oBAAAvF,QAAA,EAAC;kBAAM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtClD,OAAA;oBAAQoI,KAAK,EAAC,OAAO;oBAAAvF,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlD,OAAA,CAACR,IAAI,CAACiI,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;cAAA7E,QAAA,EAAC;YAAsB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/ClD,OAAA,CAACR,IAAI,CAAC2I,MAAM;cACVC,KAAK,EAAE9G,UAAU,CAACG,sBAAuB;cACzCqG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEhE,sBAAsB,EAAEsG,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC,CAAE;cAAAvF,QAAA,gBAE9F7C,OAAA;gBAAQoI,KAAK,EAAC,EAAE;gBAAAvF,QAAA,EAAC;cAA0B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnD5C,YAAY,CAACqG,GAAG,CAACM,MAAM,iBACtBjH,OAAA;gBAAwBoI,KAAK,EAAEnB,MAAM,CAACjD,EAAG;gBAAAnB,QAAA,GACtCoE,MAAM,CAACC,IAAI,EAAC,KAAG,EAACD,MAAM,CAAC7D,WAAW,EAAC,eACtC;cAAA,GAFa6D,MAAM,CAACjD,EAAE;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEblD,OAAA,CAACR,IAAI,CAACiI,KAAK;YAAC3E,SAAS,EAAC,MAAM;YAAAD,QAAA,gBAC1B7C,OAAA,CAACR,IAAI,CAACkI,KAAK;cAAA7E,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChClD,OAAA,CAACR,IAAI,CAACmI,OAAO;cACXY,EAAE,EAAC,UAAU;cACbC,IAAI,EAAE,CAAE;cACRC,WAAW,EAAC,wCAAwC;cACpDL,KAAK,EAAE9G,UAAU,CAACE,OAAQ;cAC1BsG,QAAQ,EAAGC,CAAC,IAAKxG,aAAa,CAACkE,IAAI,KAAK;gBAAE,GAAGA,IAAI;gBAAEjE,OAAO,EAAEuG,CAAC,CAACE,MAAM,CAACG;cAAM,CAAC,CAAC;YAAE;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eACblD,OAAA,CAACP,KAAK,CAACiJ,MAAM;QAAA7F,QAAA,gBACX7C,OAAA,CAACZ,MAAM;UAACqD,OAAO,EAAC,WAAW;UAACoD,OAAO,EAAEA,CAAA,KAAMpF,kBAAkB,CAAC,KAAK,CAAE;UAAAoC,QAAA,EAAC;QAEtE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlD,OAAA,CAACZ,MAAM;UAACqD,OAAO,EAAC,SAAS;UAACoD,OAAO,EAAExC,gBAAiB;UAACsF,QAAQ,EAAE,CAAC3H,YAAa;UAAA6B,QAAA,gBAC3E7C,OAAA;YAAG8C,SAAS,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRlD,OAAA,CAACP,KAAK;MAAC4H,IAAI,EAAE3G,aAAc;MAAC4G,MAAM,EAAEA,CAAA,KAAM3G,gBAAgB,CAAC,KAAK,CAAE;MAACoG,IAAI,EAAC,IAAI;MAAAlE,QAAA,gBAC1E7C,OAAA,CAACP,KAAK,CAAC+G,MAAM;QAACe,WAAW;QAAA1E,QAAA,eACvB7C,OAAA,CAACP,KAAK,CAAC+H,KAAK;UAAA3E,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,kBACrB,EAACpC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8F,SAAS;QAAA;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACP,KAAK,CAACsG,IAAI;QAAAlD,QAAA,EACR/B,WAAW,iBACVd,OAAA;UAAA6C,QAAA,gBACE7C,OAAA,CAACf,GAAG;YAAA4D,QAAA,gBACF7C,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,gBACT7C,OAAA;gBAAA6C,QAAA,EAAI;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzBlD,OAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACiC,QAAQ,CAACiD,MAAM,GAAG,EAAE,GAAGlF,WAAW,CAACiC,QAAQ,CAAC8D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG/F,WAAW,CAACiC,QAAQ;cAAA;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxIlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACY,SAAS;cAAA;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACa,MAAM;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpDlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACc,SAAS;cAAA;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACe,SAAS;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNlD,OAAA,CAACd,GAAG;cAAC4G,EAAE,EAAE,CAAE;cAAAjD,QAAA,gBACT7C,OAAA;gBAAA6C,QAAA,EAAI;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxBlD,OAAA;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACZ,cAAc,CAACxB,WAAW,CAACyB,MAAM,CAAC;cAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpElD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACuF,IAAI,GAAG,IAAIvF,WAAW,CAACuF,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,gBAAgB;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvGlD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACpC,WAAW,CAACgG,eAAe;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnElD,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI0F,IAAI,CAAC9H,WAAW,CAAC+H,OAAO,CAAC,CAACC,cAAc,CAAC,CAAC;cAAA;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAChFpC,WAAW,CAACiI,uBAAuB,iBAClC/I,OAAA;gBAAA6C,QAAA,gBAAG7C,OAAA;kBAAA6C,QAAA,EAAQ;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI0F,IAAI,CAAC9H,WAAW,CAACiI,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC;cAAA;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAC9G;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACLpC,WAAW,CAACU,OAAO,iBAClBxB,OAAA;YAAK8C,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB7C,OAAA;cAAA6C,QAAA,EAAI;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChBlD,OAAA;cAAG8C,SAAS,EAAC,YAAY;cAAAD,QAAA,EAAE/B,WAAW,CAACU;YAAO;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CACN,EACApC,WAAW,CAACyB,MAAM,KAAK,QAAQ,iBAC9BvC,OAAA,CAACT,KAAK;YAACkD,OAAO,EAAC,SAAS;YAACK,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACvC7C,OAAA;cAAG8C,SAAS,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,sEAEtD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACblD,OAAA,CAACP,KAAK,CAACiJ,MAAM;QAAA7F,QAAA,GACV,CAAA/B,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEyB,MAAM,MAAK,QAAQ,iBAC/BvC,OAAA,CAACZ,MAAM;UACLqD,OAAO,EAAC,SAAS;UACjBoD,OAAO,EAAEA,CAAA,KAAM;YACbT,gBAAgB,CAACtE,WAAW,CAACkD,EAAE,CAAC;YAChCrD,gBAAgB,CAAC,KAAK,CAAC;UACzB,CAAE;UAAAkC,QAAA,gBAEF7C,OAAA;YAAG8C,SAAS,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,iBAEvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,eACDlD,OAAA,CAACZ,MAAM;UAACqD,OAAO,EAAC,WAAW;UAACoD,OAAO,EAAEA,CAAA,KAAMlF,gBAAgB,CAAC,KAAK,CAAE;UAAAkC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGRlD,OAAA,CAACP,KAAK;MAAC4H,IAAI,EAAEzG,aAAc;MAAC0G,MAAM,EAAEA,CAAA,KAAMzG,gBAAgB,CAAC,KAAK,CAAE;MAACkG,IAAI,EAAC,IAAI;MAAAlE,QAAA,gBAC1E7C,OAAA,CAACP,KAAK,CAAC+G,MAAM;QAACe,WAAW;QAAA1E,QAAA,eACvB7C,OAAA,CAACP,KAAK,CAAC+H,KAAK;UAAA3E,QAAA,gBACV7C,OAAA;YAAG8C,SAAS,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WAChC,EAACpC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE8F,SAAS;QAAA;UAAA7D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACflD,OAAA,CAACP,KAAK,CAACsG,IAAI;QAAAlD,QAAA,gBACT7C,OAAA;UAAKgJ,KAAK,EAAE;YAAEC,MAAM,EAAE,OAAO;YAAEC,SAAS,EAAE,MAAM;YAAEC,MAAM,EAAE,mBAAmB;YAAEC,YAAY,EAAE,UAAU;YAAEC,OAAO,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAzG,QAAA,EAC9IzB,QAAQ,CAAC4E,MAAM,GAAG,CAAC,GAClB5E,QAAQ,CAACuF,GAAG,CAAE4C,OAAO,iBACnBvJ,OAAA;YAAsB8C,SAAS,EAAE,QAAQyG,OAAO,CAACC,iBAAiB,GAAG,UAAU,GAAG,YAAY,EAAG;YAAA3G,QAAA,eAC/F7C,OAAA;cAAK8C,SAAS,EAAE,8BAA8ByG,OAAO,CAACC,iBAAiB,GAAG,uBAAuB,GAAG,UAAU,EAAG;cAACR,KAAK,EAAE;gBAAES,QAAQ,EAAE;cAAM,CAAE;cAAA5G,QAAA,gBAC3I7C,OAAA;gBAAA6C,QAAA,EAAM0G,OAAO,CAACG;cAAO;gBAAA3G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5BlD,OAAA;gBAAO8C,SAAS,EAAE,gBAAgByG,OAAO,CAACC,iBAAiB,GAAG,YAAY,GAAG,YAAY,EAAG;gBAAA3G,QAAA,GACzF0G,OAAO,CAACI,UAAU,EAAC,KAAG,EAAC,IAAIf,IAAI,CAACW,OAAO,CAACK,MAAM,CAAC,CAACd,cAAc,CAAC,CAAC;cAAA;gBAAA/F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC,GANEqG,OAAO,CAACvF,EAAE;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOf,CACN,CAAC,gBAEFlD,OAAA;YAAK8C,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrC7C,OAAA;cAAG8C,SAAS,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9ClD,OAAA;cAAA6C,QAAA,EAAG;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNlD,OAAA;UAAK8C,SAAS,EAAC,QAAQ;UAAAD,QAAA,gBACrB7C,OAAA,CAACR,IAAI,CAACmI,OAAO;YACXC,IAAI,EAAC,MAAM;YACXa,WAAW,EAAC,sBAAsB;YAClCL,KAAK,EAAElH,WAAY;YACnB4G,QAAQ,EAAGC,CAAC,IAAK5G,cAAc,CAAC4G,CAAC,CAACE,MAAM,CAACG,KAAK,CAAE;YAChDyB,SAAS,EAAG9B,CAAC,IAAKA,CAAC,CAAC+B,GAAG,KAAK,OAAO,IAAIxE,iBAAiB,CAAC,CAAE;YAC3DxC,SAAS,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACFlD,OAAA,CAACZ,MAAM;YAACqD,OAAO,EAAC,SAAS;YAACoD,OAAO,EAAEP,iBAAkB;YAACqD,QAAQ,EAAE,CAACzH,WAAW,CAACqE,IAAI,CAAC,CAAE;YAAA1C,QAAA,eAClF7C,OAAA;cAAG8C,SAAS,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACblD,OAAA,CAACP,KAAK,CAACiJ,MAAM;QAAA7F,QAAA,eACX7C,OAAA,CAACZ,MAAM;UAACqD,OAAO,EAAC,WAAW;UAACoD,OAAO,EAAEA,CAAA,KAAMhF,gBAAgB,CAAC,KAAK,CAAE;UAAAgC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAAChD,EAAA,CA5lBID,gBAA0B;EAAA,QACbP,OAAO;AAAA;AAAAqK,EAAA,GADpB9J,gBAA0B;AA8lBhC,eAAeA,gBAAgB;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}