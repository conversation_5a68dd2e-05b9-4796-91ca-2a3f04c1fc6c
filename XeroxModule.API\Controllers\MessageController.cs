using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using XeroxModule.Core.Interfaces;
using XeroxModule.Core.Entities;

namespace XeroxModule.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class MessageController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;

        public MessageController(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        [HttpGet("job/{jobId}")]
        public async Task<IActionResult> GetJobMessages(int jobId)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                // Verify user has access to this job
                var printJob = await _unitOfWork.PrintJobs.GetFirstOrDefaultAsync(
                    pj => pj.PrintJobID == jobId,
                    includeProperties: "FileUpload,XeroxCenter"
                );

                if (printJob == null)
                {
                    return NotFound("Print job not found");
                }

                // Check if user is either the student who owns the job or the xerox center assigned to it
                var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
                var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);

                bool hasAccess = false;
                if (student != null && printJob.FileUpload.StudentID == student.StudentID)
                {
                    hasAccess = true;
                }
                else if (xeroxCenter != null && printJob.XeroxCenterID == xeroxCenter.XeroxCenterID)
                {
                    hasAccess = true;
                }

                if (!hasAccess)
                {
                    return StatusCode(403, new { message = "You don't have permission to view these messages" });
                }

                // Get messages for this job
                var messages = await _unitOfWork.Messages.GetAllAsync(
                    m => m.PrintJobID == jobId,
                    includeProperties: "SenderUser"
                );

                var result = messages.OrderBy(m => m.SentAt).Select(m => new
                {
                    id = m.MessageID,
                    content = m.MessageText,
                    senderUserId = m.SenderUserID,
                    senderName = m.SenderUser.Username,
                    sentAt = m.SentAt.ToString("yyyy-MM-ddTHH:mm:ss"),
                    timeAgo = m.TimeAgo,
                    isRead = m.IsRead,
                    isFromCurrentUser = m.SenderUserID == userId
                });

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error retrieving messages", error = ex.Message });
            }
        }

        [HttpPost("job/{jobId}")]
        public async Task<IActionResult> SendMessage(int jobId, [FromBody] SendMessageRequest request)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                // Verify user has access to this job
                var printJob = await _unitOfWork.PrintJobs.GetFirstOrDefaultAsync(
                    pj => pj.PrintJobID == jobId,
                    includeProperties: "FileUpload,XeroxCenter"
                );

                if (printJob == null)
                {
                    return NotFound("Print job not found");
                }

                // Check if user is either the student who owns the job or the xerox center assigned to it
                var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
                var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);

                int receiverUserId = 0;
                bool hasAccess = false;

                if (student != null && printJob.FileUpload.StudentID == student.StudentID)
                {
                    hasAccess = true;
                    receiverUserId = printJob.XeroxCenter.UserID; // Send to xerox center
                }
                else if (xeroxCenter != null && printJob.XeroxCenterID == xeroxCenter.XeroxCenterID)
                {
                    hasAccess = true;
                    // Get student user ID
                    var jobStudent = await _unitOfWork.Students.GetByIdAsync(printJob.FileUpload.StudentID);
                    receiverUserId = jobStudent.UserID; // Send to student
                }

                if (!hasAccess)
                {
                    return StatusCode(403, new { message = "You don't have permission to send messages for this job" });
                }

                // Create new message
                var message = new Message
                {
                    PrintJobID = jobId,
                    SenderUserID = userId,
                    ReceiverUserID = receiverUserId,
                    MessageText = request.Content,
                    SentAt = DateTime.UtcNow,
                    IsRead = false
                };

                await _unitOfWork.Messages.AddAsync(message);
                await _unitOfWork.SaveChangesAsync();

                // Get the created message with sender info
                var createdMessage = await _unitOfWork.Messages.GetFirstOrDefaultAsync(
                    m => m.MessageID == message.MessageID,
                    includeProperties: "SenderUser"
                );

                var result = new
                {
                    id = createdMessage.MessageID,
                    content = createdMessage.MessageText,
                    senderUserId = createdMessage.SenderUserID,
                    senderName = createdMessage.SenderUser.Username,
                    sentAt = createdMessage.SentAt.ToString("yyyy-MM-ddTHH:mm:ss"),
                    timeAgo = createdMessage.TimeAgo,
                    isRead = createdMessage.IsRead,
                    isFromCurrentUser = true
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error sending message", error = ex.Message });
            }
        }

        [HttpPut("{messageId}/read")]
        public async Task<IActionResult> MarkAsRead(int messageId)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
                
                var message = await _unitOfWork.Messages.GetByIdAsync(messageId);
                if (message == null)
                {
                    return NotFound("Message not found");
                }

                // Only the receiver can mark message as read
                if (message.ReceiverUserID != userId)
                {
                    return StatusCode(403, new { message = "You can only mark your own messages as read" });
                }

                message.IsRead = true;
                await _unitOfWork.SaveChangesAsync();

                return Ok(new { message = "Message marked as read" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error marking message as read", error = ex.Message });
            }
        }
    }

    public class SendMessageRequest
    {
        public string Content { get; set; } = string.Empty;
    }
}
