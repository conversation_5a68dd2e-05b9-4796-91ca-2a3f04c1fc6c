{"ast": null, "code": "import { complex } from '../complex/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { getDefaultValueType } from '../maps/defaults.mjs';\nfunction getAnimatableNone(key, value) {\n  let defaultValueType = getDefaultValueType(key);\n  if (defaultValueType !== filter) defaultValueType = complex;\n  // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n  return defaultValueType.getAnimatableNone ? defaultValueType.getAnimatableNone(value) : undefined;\n}\nexport { getAnimatableNone };", "map": {"version": 3, "names": ["complex", "filter", "getDefaultValueType", "getAnimatableNone", "key", "value", "defaultValueType", "undefined"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/value/types/utils/animatable-none.mjs"], "sourcesContent": ["import { complex } from '../complex/index.mjs';\nimport { filter } from '../complex/filter.mjs';\nimport { getDefaultValueType } from '../maps/defaults.mjs';\n\nfunction getAnimatableNone(key, value) {\n    let defaultValueType = getDefaultValueType(key);\n    if (defaultValueType !== filter)\n        defaultValueType = complex;\n    // If value is not recognised as animatable, ie \"none\", create an animatable version origin based on the target\n    return defaultValueType.getAnimatableNone\n        ? defaultValueType.getAnimatableNone(value)\n        : undefined;\n}\n\nexport { getAnimatableNone };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,MAAM,QAAQ,uBAAuB;AAC9C,SAASC,mBAAmB,QAAQ,sBAAsB;AAE1D,SAASC,iBAAiBA,CAACC,GAAG,EAAEC,KAAK,EAAE;EACnC,IAAIC,gBAAgB,GAAGJ,mBAAmB,CAACE,GAAG,CAAC;EAC/C,IAAIE,gBAAgB,KAAKL,MAAM,EAC3BK,gBAAgB,GAAGN,OAAO;EAC9B;EACA,OAAOM,gBAAgB,CAACH,iBAAiB,GACnCG,gBAAgB,CAACH,iBAAiB,CAACE,KAAK,CAAC,GACzCE,SAAS;AACnB;AAEA,SAASJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}