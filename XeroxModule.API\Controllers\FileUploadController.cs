using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using XeroxModule.Core.Interfaces;
using XeroxModule.Core.Entities;

namespace XeroxModule.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FileUploadController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IConfiguration _configuration;

        public FileUploadController(IUnitOfWork unitOfWork, IConfiguration configuration)
        {
            _unitOfWork = unitOfWork;
            _configuration = configuration;
        }

        [HttpPost]
        public async Task<IActionResult> UploadFile([FromForm] FileUploadRequest request)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            // Get student by user ID
            var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
            if (student == null)
            {
                return BadRequest("Student not found");
            }

            if (request.File == null || request.File.Length == 0)
            {
                return BadRequest("No file uploaded");
            }

            // Validate file
            var maxFileSize = _configuration.GetValue<long>("FileUploadSettings:MaxFileSize");
            var allowedTypes = _configuration.GetSection("FileUploadSettings:AllowedFileTypes").Get<string[]>();
            
            if (request.File.Length > maxFileSize)
            {
                return BadRequest($"File size exceeds maximum allowed size of {maxFileSize / (1024 * 1024)}MB");
            }

            var fileExtension = Path.GetExtension(request.File.FileName).ToLower();
            if (allowedTypes == null || !allowedTypes.Contains(fileExtension))
            {
                return BadRequest($"File type {fileExtension} is not allowed");
            }

            // Create uploads directory if it doesn't exist
            var uploadPath = _configuration.GetValue<string>("FileUploadSettings:UploadPath") ?? "uploads";
            var fullUploadPath = Path.Combine(Directory.GetCurrentDirectory(), uploadPath);
            Directory.CreateDirectory(fullUploadPath);

            // Generate unique filename
            var uniqueFileName = $"{Guid.NewGuid()}{fileExtension}";
            var filePath = Path.Combine(fullUploadPath, uniqueFileName);

            // Save file to disk
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await request.File.CopyToAsync(stream);
            }

            // Create file upload record
            var fileUpload = new FileUpload
            {
                FileName = uniqueFileName,
                OriginalFileName = request.File.FileName,
                FilePath = Path.Combine(uploadPath, uniqueFileName),
                FileSize = request.File.Length,
                FileType = request.File.ContentType,
                Remarks = request.Remarks,
                StudentID = student.StudentID,
                PreferredXeroxCenterID = request.PreferredXeroxCenterId > 0 ? request.PreferredXeroxCenterId : null,
                UploadedAt = DateTime.UtcNow,
                PrintType = request.PrintType,
                Copies = request.Copies,
                ColorType = request.ColorType,
                PaperSize = request.PaperSize,
                CreatedUserID = userId,
                Created = DateTime.UtcNow
            };

            await _unitOfWork.FileUploads.AddAsync(fileUpload);
            await _unitOfWork.SaveChangesAsync();

            // Create print jobs for all active xerox centers or just the preferred one
            var xeroxCenters = new List<XeroxCenter>();
            
            if (fileUpload.PreferredXeroxCenterID.HasValue)
            {
                var preferredCenter = await _unitOfWork.XeroxCenters.GetByIdAsync(fileUpload.PreferredXeroxCenterID.Value);
                if (preferredCenter != null && preferredCenter.IsActive)
                {
                    xeroxCenters.Add(preferredCenter);
                }
            }
            else
            {
                // Send to all active xerox centers
                xeroxCenters = (await _unitOfWork.XeroxCenters.GetAllAsync(xc => xc.IsActive)).ToList();
            }

            // Create print jobs
            foreach (var xeroxCenter in xeroxCenters)
            {
                var printJob = new PrintJob
                {
                    JobNumber = $"JOB-{DateTime.Now:yyyyMMdd}-{fileUpload.FileUploadID:D4}",
                    XeroxCenterID = xeroxCenter.XeroxCenterID,
                    FileUploadID = fileUpload.FileUploadID,
                    Status = "Requested",
                    Priority = "Normal",
                    CreatedUserID = userId,
                    Created = DateTime.UtcNow
                };

                await _unitOfWork.PrintJobs.AddAsync(printJob);
            }

            await _unitOfWork.SaveChangesAsync();

            return Ok(new
            {
                message = "File uploaded successfully",
                fileId = fileUpload.FileUploadID,
                fileName = fileUpload.OriginalFileName,
                printJobsCreated = xeroxCenters.Count
            });
        }

        [HttpGet("student")]
        public async Task<IActionResult> GetStudentFiles()
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
            if (student == null)
            {
                return BadRequest("Student not found");
            }

            var files = await _unitOfWork.FileUploads.GetAllAsync(
                f => f.StudentID == student.StudentID,
                includeProperties: "PrintJobs,PreferredXeroxCenter"
            );

            var result = files.Select(f => new
            {
                id = f.FileUploadID,
                fileName = f.OriginalFileName,
                fileSize = f.FileSize,
                printType = f.PrintType,
                copies = f.Copies,
                colorType = f.ColorType,
                paperSize = f.PaperSize,
                remarks = f.Remarks,
                uploadedAt = f.UploadedAt.ToString("yyyy-MM-ddTHH:mm:ss"),
                preferredXeroxCenter = f.PreferredXeroxCenter?.XeroxCenterName,
                printJobsCount = f.PrintJobs.Count,
                status = f.PrintJobs.Any() ? f.PrintJobs.First().Status : "Pending"
            });

            return Ok(result);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFile(int id)
        {
            var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");
            
            var student = await _unitOfWork.Students.GetFirstOrDefaultAsync(s => s.UserID == userId);
            if (student == null)
            {
                return BadRequest("Student not found");
            }

            var fileUpload = await _unitOfWork.FileUploads.GetFirstOrDefaultAsync(
                f => f.FileUploadID == id && f.StudentID == student.StudentID,
                includeProperties: "PrintJobs"
            );

            if (fileUpload == null)
            {
                return NotFound("File not found");
            }

            // Check if any print jobs are in progress
            if (fileUpload.PrintJobs.Any(pj => new[] { "Confirmed", "InProgress" }.Contains(pj.Status)))
            {
                return BadRequest("Cannot delete file with print jobs in progress");
            }

            // Delete physical file
            var fullPath = Path.Combine(Directory.GetCurrentDirectory(), fileUpload.FilePath);
            if (System.IO.File.Exists(fullPath))
            {
                System.IO.File.Delete(fullPath);
            }

            // Delete related print jobs first
            foreach (var printJob in fileUpload.PrintJobs)
            {
                _unitOfWork.PrintJobs.Remove(printJob);
            }

            // Delete file upload record
            _unitOfWork.FileUploads.Remove(fileUpload);
            await _unitOfWork.SaveChangesAsync();

            return Ok(new { message = "File deleted successfully" });
        }

        [HttpGet("download/{jobId}")]
        public async Task<IActionResult> DownloadFile(int jobId)
        {
            try
            {
                var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                // Get the print job with file upload information
                var printJob = await _unitOfWork.PrintJobs.GetFirstOrDefaultAsync(
                    pj => pj.PrintJobID == jobId,
                    includeProperties: "FileUpload"
                );

                if (printJob == null)
                {
                    return NotFound("Print job not found");
                }

                // Verify the user is the xerox center assigned to this job
                var xeroxCenter = await _unitOfWork.XeroxCenters.GetFirstOrDefaultAsync(xc => xc.UserID == userId);
                if (xeroxCenter == null || printJob.XeroxCenterID != xeroxCenter.XeroxCenterID)
                {
                    return StatusCode(403, new { message = "You don't have permission to download this file" });
                }

                var fileUpload = printJob.FileUpload;
                if (fileUpload == null)
                {
                    return NotFound("File not found");
                }

                // Build full file path
                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), fileUpload.FilePath);

                // Check if file exists on disk
                if (!System.IO.File.Exists(fullPath))
                {
                    return NotFound("File not found on disk");
                }

                // Read file and return as download
                var fileBytes = await System.IO.File.ReadAllBytesAsync(fullPath);

                return File(fileBytes, fileUpload.FileType ?? "application/octet-stream", fileUpload.OriginalFileName);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Error downloading file", error = ex.Message });
            }
        }
    }

    public class FileUploadRequest
    {
        public IFormFile File { get; set; } = null!;
        public string? Remarks { get; set; }
        public int? PreferredXeroxCenterId { get; set; }
        public string PrintType { get; set; } = "Print";
        public int Copies { get; set; } = 1;
        public string ColorType { get; set; } = "BlackWhite";
        public string PaperSize { get; set; } = "A4";
    }
}
