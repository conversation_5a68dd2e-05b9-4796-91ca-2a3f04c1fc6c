{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Container,Row,Col,Card,Button,Badge,Form,Modal,InputGroup,Nav,Table,ProgressBar}from'react-bootstrap';import{motion,AnimatePresence}from'framer-motion';import{Upload,FileText,Clock,CheckCircle,DollarSign,Download,MessageCircle,Eye,Activity,Printer,Star,History,Settings,User,CreditCard,Bell,Search,TrendingUp,BarChart3,PieChart,Target,Award,Heart,RefreshCw,AlertCircle,CheckCircle2,XCircle,Send,Phone,MapPin as Location}from'lucide-react';import{useAuth}from'../../contexts/AuthContext';import{printJobApi,xeroxCenterApi,fileUploadApi,messageApi}from'../../services/api';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AceternityStudentDashboard=()=>{const{user}=useAuth();const[activeTab,setActiveTab]=useState('dashboard');const[printJobs,setPrintJobs]=useState([]);const[xeroxCenters,setXeroxCenters]=useState([]);const[favoritesCenters,setFavoritesCenters]=useState([]);const[showUploadModal,setShowUploadModal]=useState(false);const[showViewModal,setShowViewModal]=useState(false);const[showChatModal,setShowChatModal]=useState(false);const[showSettingsModal,setShowSettingsModal]=useState(false);const[selectedJob,setSelectedJob]=useState(null);const[selectedCenter,setSelectedCenter]=useState(null);const[selectedFile,setSelectedFile]=useState(null);const[chatMessage,setChatMessage]=useState('');const[messages,setMessages]=useState([]);const[searchTerm,setSearchTerm]=useState('');const[filterStatus,setFilterStatus]=useState('All');const[sortBy,setSortBy]=useState('created');const[isRefreshing,setIsRefreshing]=useState(false);const[uploadData,setUploadData]=useState({remarks:'',preferredXeroxCenterId:'',printType:'Print',copies:1,colorType:'BlackWhite',paperSize:'A4',priority:'Normal'});const[userSettings,setUserSettings]=useState({notifications:{email:true,push:true,sms:false},preferences:{defaultPrintType:'Print',defaultColorType:'BlackWhite',defaultPaperSize:'A4',autoConfirmQuotes:false},profile:{phone:'',address:'',university:'',studentId:''}});useEffect(()=>{fetchData();const interval=setInterval(fetchData,30000);return()=>clearInterval(interval);},[]);const fetchData=async()=>{setIsRefreshing(true);try{const[printJobsResponse,xeroxCentersResponse]=await Promise.all([printJobApi.getStudentJobs(),xeroxCenterApi.getAll()]);setPrintJobs(printJobsResponse.data||[]);setXeroxCenters(xeroxCentersResponse.data||[]);}catch(error){console.error('Error fetching data:',error);setPrintJobs([]);setXeroxCenters([]);}finally{setIsRefreshing(false);}};const getStatusBadge=status=>{const statusConfig={'Requested':{bg:'secondary',icon:Clock},'UnderReview':{bg:'info',icon:Eye},'Quoted':{bg:'warning',icon:DollarSign},'WaitingConfirmation':{bg:'warning',icon:AlertCircle},'Confirmed':{bg:'info',icon:CheckCircle},'InProgress':{bg:'primary',icon:Activity},'Completed':{bg:'success',icon:CheckCircle2},'Delivered':{bg:'success',icon:CheckCircle2},'Rejected':{bg:'danger',icon:XCircle},'Cancelled':{bg:'secondary',icon:XCircle}};const config=statusConfig[status]||{bg:'secondary',icon:Clock};const IconComponent=config.icon;return/*#__PURE__*/_jsxs(Badge,{bg:config.bg,className:\"d-flex align-items-center gap-1 px-3 py-2\",children:[/*#__PURE__*/_jsx(IconComponent,{size:14}),status]});};const getPriorityBadge=priority=>{const priorityConfig={'Low':{bg:'success',icon:'🟢'},'Normal':{bg:'secondary',icon:'🔵'},'High':{bg:'warning',icon:'🟡'},'Urgent':{bg:'danger',icon:'🔴'}};const config=priorityConfig[priority]||{bg:'secondary',icon:'🔵'};return/*#__PURE__*/_jsxs(Badge,{bg:config.bg,className:\"px-2 py-1\",children:[config.icon,\" \",priority]});};const handleFileUpload=async()=>{if(!selectedFile)return;try{const formData=new FormData();formData.append('file',selectedFile);Object.entries(uploadData).forEach(_ref=>{let[key,value]=_ref;formData.append(key,value.toString());});await fileUploadApi.uploadFile(formData);await fetchData();setShowUploadModal(false);setSelectedFile(null);setUploadData({remarks:'',preferredXeroxCenterId:'',printType:'Print',copies:1,colorType:'BlackWhite',paperSize:'A4',priority:'Normal'});}catch(error){console.error('Error uploading file:',error);}};const handleViewJob=job=>{setSelectedJob(job);setShowViewModal(true);};const handleOpenChat=async job=>{try{setSelectedJob(job);setShowChatModal(true);const response=await messageApi.getJobMessages(job.id);setMessages(response.data||[]);}catch(error){console.error('Error loading messages:',error);setMessages([]);}};const handleDownloadFile=async(jobId,fileName)=>{try{const response=await fileUploadApi.downloadFile(jobId);const blob=new Blob([response.data]);const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=fileName;document.body.appendChild(link);link.click();document.body.removeChild(link);window.URL.revokeObjectURL(url);}catch(error){console.error('Error downloading file:',error);}};const handleConfirmJob=async jobId=>{try{await printJobApi.confirmJob(jobId);await fetchData();}catch(error){console.error('Error confirming job:',error);}};const handleSendMessage=async()=>{if(!chatMessage.trim()||!selectedJob)return;try{const response=await messageApi.sendMessage(selectedJob.id,chatMessage.trim());setMessages(prev=>[...prev,response.data]);setChatMessage('');}catch(error){console.error('Error sending message:',error);}};const toggleFavorite=centerId=>{setFavoritesCenters(prev=>prev.includes(centerId)?prev.filter(id=>id!==centerId):[...prev,centerId]);};const filteredJobs=printJobs.filter(job=>{const matchesSearch=searchTerm===''||job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase())||job.fileName.toLowerCase().includes(searchTerm.toLowerCase())||job.xeroxCenterName.toLowerCase().includes(searchTerm.toLowerCase());const matchesStatus=filterStatus==='All'||job.status===filterStatus;return matchesSearch&&matchesStatus;});const totalSpent=printJobs.reduce((sum,job)=>sum+(job.cost||0),0);const inProgressJobs=printJobs.filter(job=>['InProgress','Confirmed','UnderReview'].includes(job.status)).length;const completedJobs=printJobs.filter(job=>['Completed','Delivered'].includes(job.status)).length;const pendingJobs=printJobs.filter(job=>['Requested','UnderReview','Quoted'].includes(job.status)).length;const containerVariants={hidden:{opacity:0},visible:{opacity:1,transition:{duration:0.6,staggerChildren:0.1}}};const itemVariants={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:0.5}}};const floatingShapes=Array.from({length:8},(_,i)=>/*#__PURE__*/_jsx(motion.div,{className:\"position-absolute rounded-circle\",style:{background:\"linear-gradient(135deg, \".concat(i%3===0?'#667eea':i%3===1?'#764ba2':'#f093fb',\", \").concat(i%3===0?'#764ba2':i%3===1?'#f093fb':'#f5576c',\")\"),width:\"\".concat(Math.random()*60+20,\"px\"),height:\"\".concat(Math.random()*60+20,\"px\"),left:\"\".concat(Math.random()*100,\"%\"),top:\"\".concat(Math.random()*100,\"%\"),opacity:0.1,zIndex:0},animate:{x:[0,Math.random()*100-50],y:[0,Math.random()*100-50],rotate:[0,360],scale:[1,1.2,1]},transition:{duration:Math.random()*20+10,repeat:Infinity,repeatType:\"reverse\",ease:\"easeInOut\"}},i));return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen position-relative\",style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',minHeight:'100vh'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"position-absolute w-100 h-100 overflow-hidden\",children:[floatingShapes,/*#__PURE__*/_jsx(\"div\",{className:\"position-absolute w-100 h-100\",style:{background:'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)'}})]}),/*#__PURE__*/_jsx(Container,{fluid:true,className:\"position-relative py-4\",style:{zIndex:1},children:/*#__PURE__*/_jsxs(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",children:[/*#__PURE__*/_jsxs(motion.div,{variants:itemVariants,className:\"text-center mb-5\",children:[/*#__PURE__*/_jsx(motion.div,{animate:{rotate:[0,360],scale:[1,1.1,1]},transition:{duration:4,repeat:Infinity,ease:\"easeInOut\"},className:\"d-inline-flex align-items-center justify-content-center mb-3\",style:{width:'80px',height:'80px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'20px',boxShadow:'0 20px 40px rgba(102, 126, 234, 0.3)'},children:/*#__PURE__*/_jsx(User,{className:\"text-white\",size:40})}),/*#__PURE__*/_jsxs(\"h1\",{className:\"text-white fw-bold mb-2\",style:{fontSize:'2.5rem'},children:[\"Welcome back, \",user===null||user===void 0?void 0:user.username,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50 fs-5 mb-4\",children:\"Manage your printing jobs and explore xerox centers\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-center gap-3 flex-wrap\",children:[/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.05},whileTap:{scale:0.95},children:/*#__PURE__*/_jsxs(Button,{size:\"lg\",onClick:()=>setShowUploadModal(true),className:\"px-5 py-3 rounded-4 border-0 fw-semibold\",style:{background:'rgba(255, 255, 255, 0.2)',backdropFilter:'blur(20px)',color:'#fff',boxShadow:'0 10px 30px rgba(255, 255, 255, 0.1)'},children:[/*#__PURE__*/_jsx(Upload,{className:\"me-2\",size:20}),\"Upload Files\"]})}),/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.05},whileTap:{scale:0.95},children:/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"lg\",onClick:fetchData,disabled:isRefreshing,className:\"px-4 py-3 rounded-4 fw-semibold\",children:[/*#__PURE__*/_jsx(RefreshCw,{className:\"me-2 \".concat(isRefreshing?'spin':''),size:20}),\"Refresh\"]})})]})]}),/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,className:\"mb-4\",children:/*#__PURE__*/_jsx(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsx(Card.Body,{className:\"p-2\",children:/*#__PURE__*/_jsx(Nav,{variant:\"pills\",className:\"justify-content-center flex-wrap\",children:[{key:'dashboard',label:'Dashboard',icon:BarChart3},{key:'jobs',label:'My Jobs',icon:FileText},{key:'centers',label:'Xerox Centers',icon:Printer},{key:'history',label:'History',icon:History},{key:'favorites',label:'Favorites',icon:Heart},{key:'analytics',label:'Analytics',icon:PieChart},{key:'settings',label:'Settings',icon:Settings}].map(tab=>{const IconComponent=tab.icon;return/*#__PURE__*/_jsx(Nav.Item,{className:\"m-1\",children:/*#__PURE__*/_jsx(motion.div,{whileHover:{scale:1.05},whileTap:{scale:0.95},children:/*#__PURE__*/_jsxs(Nav.Link,{active:activeTab===tab.key,onClick:()=>setActiveTab(tab.key),className:\"px-4 py-3 rounded-3 fw-semibold d-flex align-items-center \".concat(activeTab===tab.key?'text-primary':'text-white-50'),style:{background:activeTab===tab.key?'rgba(255, 255, 255, 0.9)':'transparent',border:'none',transition:'all 0.3s ease',boxShadow:activeTab===tab.key?'0 10px 30px rgba(255, 255, 255, 0.2)':'none'},children:[/*#__PURE__*/_jsx(IconComponent,{size:18,className:\"me-2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"d-none d-md-inline\",children:tab.label})]})})},tab.key);})})})})}),/*#__PURE__*/_jsx(AnimatePresence,{mode:\"wait\",children:/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:0.3},children:[activeTab==='dashboard'&&/*#__PURE__*/_jsx(DashboardTab,{printJobs:printJobs,xeroxCenters:xeroxCenters,totalSpent:totalSpent,inProgressJobs:inProgressJobs,completedJobs:completedJobs,pendingJobs:pendingJobs,onViewJob:handleViewJob,onDownloadFile:handleDownloadFile,onOpenChat:handleOpenChat,onConfirmJob:handleConfirmJob,getStatusBadge:getStatusBadge,getPriorityBadge:getPriorityBadge}),activeTab==='jobs'&&/*#__PURE__*/_jsx(JobsTab,{jobs:filteredJobs,searchTerm:searchTerm,setSearchTerm:setSearchTerm,filterStatus:filterStatus,setFilterStatus:setFilterStatus,sortBy:sortBy,setSortBy:setSortBy,onViewJob:handleViewJob,onDownloadFile:handleDownloadFile,onOpenChat:handleOpenChat,onConfirmJob:handleConfirmJob,getStatusBadge:getStatusBadge,getPriorityBadge:getPriorityBadge}),activeTab==='centers'&&/*#__PURE__*/_jsx(CentersTab,{centers:xeroxCenters,favoritesCenters:favoritesCenters,onToggleFavorite:toggleFavorite,onSelectCenter:setSelectedCenter}),activeTab==='history'&&/*#__PURE__*/_jsx(HistoryTab,{jobs:printJobs.filter(job=>['Completed','Delivered','Cancelled','Rejected'].includes(job.status)),onViewJob:handleViewJob,getStatusBadge:getStatusBadge}),activeTab==='favorites'&&/*#__PURE__*/_jsx(FavoritesTab,{centers:xeroxCenters.filter(center=>favoritesCenters.includes(center.id)),onToggleFavorite:toggleFavorite,onSelectCenter:setSelectedCenter}),activeTab==='analytics'&&/*#__PURE__*/_jsx(AnalyticsTab,{jobs:printJobs,totalSpent:totalSpent}),activeTab==='settings'&&/*#__PURE__*/_jsx(SettingsTab,{userSettings:userSettings,setUserSettings:setUserSettings})]},activeTab)})]})}),/*#__PURE__*/_jsx(UploadModal,{show:showUploadModal,onHide:()=>setShowUploadModal(false),uploadData:uploadData,setUploadData:setUploadData,selectedFile:selectedFile,setSelectedFile:setSelectedFile,xeroxCenters:xeroxCenters,onUpload:handleFileUpload}),/*#__PURE__*/_jsx(ViewJobModal,{show:showViewModal,onHide:()=>setShowViewModal(false),job:selectedJob,getStatusBadge:getStatusBadge,getPriorityBadge:getPriorityBadge}),/*#__PURE__*/_jsx(ChatModal,{show:showChatModal,onHide:()=>setShowChatModal(false),job:selectedJob,messages:messages,chatMessage:chatMessage,setChatMessage:setChatMessage,onSendMessage:handleSendMessage})]});};// Dashboard Tab Component\nconst DashboardTab=_ref2=>{let{printJobs,xeroxCenters,totalSpent,inProgressJobs,completedJobs,pendingJobs,onViewJob,onDownloadFile,onOpenChat,onConfirmJob,getStatusBadge,getPriorityBadge}=_ref2;return/*#__PURE__*/_jsxs(Row,{className:\"g-4\",children:[/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.5},whileHover:{y:-5,scale:1.02},children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'15px'},children:/*#__PURE__*/_jsx(FileText,{className:\"text-white\",size:24})})}),/*#__PURE__*/_jsx(\"h6\",{className:\"text-white-50 mb-1\",children:\"Total Jobs\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"fw-bold text-white\",children:printJobs.length}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-success\",children:[/*#__PURE__*/_jsx(TrendingUp,{size:12,className:\"me-1\"}),\"+12% vs last month\"]})]})})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:0.1},whileHover:{y:-5,scale:1.02},children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',borderRadius:'15px'},children:/*#__PURE__*/_jsx(Clock,{className:\"text-white\",size:24})})}),/*#__PURE__*/_jsx(\"h6\",{className:\"text-white-50 mb-1\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"fw-bold text-white\",children:inProgressJobs}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-warning\",children:[/*#__PURE__*/_jsx(Activity,{size:12,className:\"me-1\"}),\"Active jobs\"]})]})})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:0.2},whileHover:{y:-5,scale:1.02},children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',borderRadius:'15px'},children:/*#__PURE__*/_jsx(CheckCircle,{className:\"text-white\",size:24})})}),/*#__PURE__*/_jsx(\"h6\",{className:\"text-white-50 mb-1\",children:\"Completed\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"fw-bold text-white\",children:completedJobs}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-success\",children:[/*#__PURE__*/_jsx(Award,{size:12,className:\"me-1\"}),\"Finished jobs\"]})]})})})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},transition:{duration:0.5,delay:0.3},whileHover:{y:-5,scale:1.02},children:/*#__PURE__*/_jsx(Card,{className:\"h-100 border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'60px',height:'60px',background:'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',borderRadius:'15px'},children:/*#__PURE__*/_jsx(DollarSign,{className:\"text-white\",size:24})})}),/*#__PURE__*/_jsx(\"h6\",{className:\"text-white-50 mb-1\",children:\"Total Spent\"}),/*#__PURE__*/_jsxs(\"h2\",{className:\"fw-bold text-white\",children:[\"$\",totalSpent.toFixed(2)]}),/*#__PURE__*/_jsxs(\"small\",{className:\"text-info\",children:[/*#__PURE__*/_jsx(CreditCard,{size:12,className:\"me-1\"}),\"This month\"]})]})})})}),/*#__PURE__*/_jsx(Col,{lg:8,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg h-100\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(Activity,{className:\"me-2\",size:20}),\"Recent Jobs\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"light\",text:\"dark\",className:\"px-3 py-2\",children:[printJobs.length,\" total\"]})]})}),/*#__PURE__*/_jsx(Card.Body,{children:printJobs.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:printJobs.slice(0,5).map((job,index)=>/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:index*0.1},className:\"p-3 rounded-3\",style:{background:'rgba(255, 255, 255, 0.1)',border:'1px solid rgba(255, 255, 255, 0.2)'},children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'50px',height:'50px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'12px'},children:/*#__PURE__*/_jsx(FileText,{className:\"text-white\",size:20})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"fw-semibold mb-1 text-white\",children:job.jobNumber}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50 small mb-1\",children:job.fileName.length>40?job.fileName.slice(0,40)+'...':job.fileName}),/*#__PURE__*/_jsx(\"small\",{className:\"text-white-50\",children:job.xeroxCenterName})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-end me-3\",children:[getStatusBadge(job.status),job.cost&&/*#__PURE__*/_jsxs(\"div\",{className:\"fw-semibold text-success mt-1\",children:[\"$\",job.cost.toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-1\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onDownloadFile(job.id,job.fileName),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(Download,{size:14})}),/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onViewJob(job),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(Eye,{size:14})}),/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onOpenChat(job),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(MessageCircle,{size:14})}),job.status==='Quoted'&&/*#__PURE__*/_jsx(Button,{variant:\"success\",size:\"sm\",onClick:()=>onConfirmJob(job.id),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(CheckCircle,{size:14})})]})]})]})},job.id))}):/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'80px',height:'80px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'20px'},children:/*#__PURE__*/_jsx(Upload,{className:\"text-white\",size:40})})}),/*#__PURE__*/_jsx(\"h5\",{className:\"fw-semibold mb-2 text-white\",children:\"No jobs yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50 mb-4\",children:\"Upload your first file to get started!\"})]})})]})}),/*#__PURE__*/_jsx(Col,{lg:4,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg h-100\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(Target,{className:\"me-2\",size:20}),\"Quick Stats\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between p-3 rounded-3\",style:{background:'rgba(255, 255, 255, 0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",style:{width:'40px',height:'40px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'10px',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Clock,{className:\"text-white\",size:20})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 text-white\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-white-50\",children:\"Awaiting review\"})]})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0 text-warning\",children:pendingJobs})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between p-3 rounded-3\",style:{background:'rgba(255, 255, 255, 0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",style:{width:'40px',height:'40px',background:'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',borderRadius:'10px',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Printer,{className:\"text-white\",size:20})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 text-white\",children:\"Centers\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-white-50\",children:\"Available now\"})]})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0 text-info\",children:xeroxCenters.filter(c=>c.isActive).length})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between p-3 rounded-3\",style:{background:'rgba(255, 255, 255, 0.1)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",style:{width:'40px',height:'40px',background:'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',borderRadius:'10px',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Star,{className:\"text-white\",size:20})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"mb-0 text-white\",children:\"Avg Rating\"}),/*#__PURE__*/_jsx(\"small\",{className:\"text-white-50\",children:\"Your experience\"})]})]}),/*#__PURE__*/_jsx(\"h4\",{className:\"mb-0 text-success\",children:\"4.8\"})]})]})})]})})]});};// Jobs Tab Component\nconst JobsTab=_ref3=>{let{jobs,searchTerm,setSearchTerm,filterStatus,setFilterStatus,sortBy,setSortBy,onViewJob,onDownloadFile,onOpenChat,onConfirmJob,getStatusBadge,getPriorityBadge}=_ref3;return/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsxs(Card.Header,{className:\"border-0 bg-transparent\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between mb-3\",children:[/*#__PURE__*/_jsxs(\"h4\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(FileText,{className:\"me-2\",size:20}),\"All Jobs\"]}),/*#__PURE__*/_jsxs(Badge,{bg:\"light\",text:\"dark\",className:\"px-3 py-2\",children:[jobs.length,\" jobs\"]})]}),/*#__PURE__*/_jsxs(Row,{className:\"g-3\",children:[/*#__PURE__*/_jsx(Col,{md:4,children:/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(InputGroup.Text,{style:{background:'rgba(255, 255, 255, 0.1)',border:'none'},children:/*#__PURE__*/_jsx(Search,{className:\"text-white-50\",size:16})}),/*#__PURE__*/_jsx(Form.Control,{placeholder:\"Search jobs...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'}})]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Select,{value:filterStatus,onChange:e=>setFilterStatus(e.target.value),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"All\",children:\"All Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Requested\",children:\"Requested\"}),/*#__PURE__*/_jsx(\"option\",{value:\"UnderReview\",children:\"Under Review\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Quoted\",children:\"Quoted\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Confirmed\",children:\"Confirmed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"InProgress\",children:\"In Progress\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Completed\",children:\"Completed\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Delivered\",children:\"Delivered\"})]})}),/*#__PURE__*/_jsx(Col,{md:3,children:/*#__PURE__*/_jsxs(Form.Select,{value:sortBy,onChange:e=>setSortBy(e.target.value),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"created\",children:\"Sort by Date\"}),/*#__PURE__*/_jsx(\"option\",{value:\"status\",children:\"Sort by Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"cost\",children:\"Sort by Cost\"}),/*#__PURE__*/_jsx(\"option\",{value:\"priority\",children:\"Sort by Priority\"})]})})]})]}),/*#__PURE__*/_jsx(Card.Body,{children:jobs.length>0?/*#__PURE__*/_jsx(Row,{className:\"g-4\",children:jobs.map((job,index)=>/*#__PURE__*/_jsx(Col,{md:6,lg:4,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.05},children:/*#__PURE__*/_jsxs(Card,{className:\"h-100 border-0\",style:{background:'rgba(255, 255, 255, 0.1)',borderRadius:'15px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-start justify-content-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"fw-bold mb-1 text-white\",children:job.jobNumber}),getPriorityBadge(job.priority)]}),getStatusBadge(job.status)]})}),/*#__PURE__*/_jsxs(Card.Body,{className:\"pt-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center text-white-50 small mb-2\",children:[/*#__PURE__*/_jsx(FileText,{size:14,className:\"me-1\"}),job.fileName.length>25?job.fileName.slice(0,25)+'...':job.fileName]}),/*#__PURE__*/_jsxs(\"div\",{className:\"small text-white-50\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",job.printType]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Copies:\"}),\" \",job.copies]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Color:\"}),\" \",job.colorType]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Size:\"}),\" \",job.paperSize]})]})]}),job.cost&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"h5 fw-bold text-success\",children:[\"$\",job.cost.toFixed(2)]})}),/*#__PURE__*/_jsx(\"div\",{className:\"small text-white-50 mb-3\",children:new Date(job.created).toLocaleDateString()}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex flex-wrap gap-1\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onDownloadFile(job.id,job.fileName),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(Download,{size:12})}),/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onViewJob(job),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(Eye,{size:12})}),/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onOpenChat(job),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(MessageCircle,{size:12})}),job.status==='Quoted'&&/*#__PURE__*/_jsx(Button,{variant:\"success\",size:\"sm\",onClick:()=>onConfirmJob(job.id),style:{borderRadius:'8px'},children:/*#__PURE__*/_jsx(CheckCircle,{size:12})})]})]})]})})},job.id))}):/*#__PURE__*/_jsxs(motion.div,{initial:{opacity:0,scale:0.9},animate:{opacity:1,scale:1},className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-flex align-items-center justify-content-center\",style:{width:'80px',height:'80px',background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',borderRadius:'20px'},children:/*#__PURE__*/_jsx(FileText,{className:\"text-white\",size:40})})}),/*#__PURE__*/_jsx(\"h5\",{className:\"fw-semibold mb-2 text-white\",children:\"No jobs found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50\",children:\"No jobs match your current filter criteria.\"})]})})]});};// Centers Tab Component\nconst CentersTab=_ref4=>{let{centers,favoritesCenters,onToggleFavorite,onSelectCenter}=_ref4;return/*#__PURE__*/_jsx(Row,{className:\"g-4\",children:centers.map((center,index)=>/*#__PURE__*/_jsx(Col,{md:6,lg:4,children:/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:index*0.1},whileHover:{y:-5,scale:1.02},children:/*#__PURE__*/_jsxs(Card,{className:\"h-100 border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-start justify-content-between\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h5\",{className:\"fw-bold mb-1 text-white\",children:center.name}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center text-white-50 small\",children:[/*#__PURE__*/_jsx(Location,{size:12,className:\"me-1\"}),center.location]})]}),/*#__PURE__*/_jsx(motion.button,{whileHover:{scale:1.1},whileTap:{scale:0.9},onClick:()=>onToggleFavorite(center.id),className:\"btn btn-link p-0 border-0\",children:/*#__PURE__*/_jsx(Heart,{size:20,className:favoritesCenters.includes(center.id)?'text-danger':'text-white-50',fill:favoritesCenters.includes(center.id)?'currentColor':'none'})})]})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between mb-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(Star,{className:\"text-warning me-1\",size:16}),/*#__PURE__*/_jsx(\"span\",{className:\"fw-semibold text-white\",children:center.averageRating.toFixed(1)})]}),/*#__PURE__*/_jsxs(Badge,{bg:center.pendingJobs<=5?'success':center.pendingJobs<=10?'warning':'danger',className:\"px-2 py-1\",children:[center.pendingJobs,\" jobs\"]})]}),center.services&&/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:/*#__PURE__*/_jsx(\"div\",{className:\"d-flex flex-wrap gap-1\",children:center.services.slice(0,3).map((service,idx)=>/*#__PURE__*/_jsx(Badge,{bg:\"light\",text:\"dark\",className:\"px-2 py-1\",children:service},idx))})}),center.priceRange&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-white-50 small mb-2\",children:[/*#__PURE__*/_jsx(DollarSign,{size:12,className:\"me-1\"}),center.priceRange]}),center.workingHours&&/*#__PURE__*/_jsxs(\"div\",{className:\"text-white-50 small mb-3\",children:[/*#__PURE__*/_jsx(Clock,{size:12,className:\"me-1\"}),center.workingHours]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex gap-2\",children:[/*#__PURE__*/_jsxs(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onSelectCenter(center),className:\"flex-grow-1\",style:{borderRadius:'10px'},children:[/*#__PURE__*/_jsx(Eye,{size:14,className:\"me-1\"}),\"View Details\"]}),center.phone&&/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",href:\"tel:\".concat(center.phone),style:{borderRadius:'10px'},children:/*#__PURE__*/_jsx(Phone,{size:14})})]})]})]})})},center.id))});};// History Tab Component\nconst HistoryTab=_ref5=>{let{jobs,onViewJob,getStatusBadge}=_ref5;return/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h4\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(History,{className:\"me-2\",size:20}),\"Job History\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:jobs.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"table-responsive\",children:/*#__PURE__*/_jsxs(Table,{className:\"table-dark table-hover\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{children:\"Job Number\"}),/*#__PURE__*/_jsx(\"th\",{children:\"File Name\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Cost\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Date\"}),/*#__PURE__*/_jsx(\"th\",{children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:jobs.map(job=>/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"fw-semibold\",children:job.jobNumber}),/*#__PURE__*/_jsx(\"td\",{children:job.fileName.length>30?job.fileName.slice(0,30)+'...':job.fileName}),/*#__PURE__*/_jsx(\"td\",{children:getStatusBadge(job.status)}),/*#__PURE__*/_jsx(\"td\",{className:\"text-success fw-semibold\",children:job.cost?\"$\".concat(job.cost.toFixed(2)):'-'}),/*#__PURE__*/_jsx(\"td\",{children:new Date(job.created).toLocaleDateString()}),/*#__PURE__*/_jsx(\"td\",{children:/*#__PURE__*/_jsx(Button,{variant:\"outline-light\",size:\"sm\",onClick:()=>onViewJob(job),children:/*#__PURE__*/_jsx(Eye,{size:14})})})]},job.id))})]})}):/*#__PURE__*/_jsxs(\"div\",{className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(History,{className:\"text-white-50 mb-3\",size:60}),/*#__PURE__*/_jsx(\"h5\",{className:\"text-white\",children:\"No history yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50\",children:\"Your completed jobs will appear here.\"})]})})]});};// Favorites Tab Component\nconst FavoritesTab=_ref6=>{let{centers,onToggleFavorite,onSelectCenter}=_ref6;return/*#__PURE__*/_jsx(\"div\",{children:centers.length>0?/*#__PURE__*/_jsx(CentersTab,{centers:centers,favoritesCenters:centers.map(c=>c.id),onToggleFavorite:onToggleFavorite,onSelectCenter:onSelectCenter}):/*#__PURE__*/_jsx(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:/*#__PURE__*/_jsxs(Card.Body,{className:\"text-center py-5\",children:[/*#__PURE__*/_jsx(Heart,{className:\"text-white-50 mb-3\",size:60}),/*#__PURE__*/_jsx(\"h5\",{className:\"text-white\",children:\"No favorites yet\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50\",children:\"Add xerox centers to your favorites for quick access.\"})]})})});};// Analytics Tab Component\nconst AnalyticsTab=_ref7=>{let{jobs,totalSpent}=_ref7;const monthlySpending=jobs.reduce((acc,job)=>{const month=new Date(job.created).toLocaleDateString('en-US',{month:'short',year:'numeric'});acc[month]=(acc[month]||0)+(job.cost||0);return acc;},{});const statusCounts=jobs.reduce((acc,job)=>{acc[job.status]=(acc[job.status]||0)+1;return acc;},{});return/*#__PURE__*/_jsxs(Row,{className:\"g-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg h-100\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h5\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(BarChart3,{className:\"me-2\",size:20}),\"Spending Overview\"]})}),/*#__PURE__*/_jsxs(Card.Body,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-success fw-bold\",children:[\"$\",totalSpent.toFixed(2)]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-white-50\",children:\"Total spent this year\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(monthlySpending).slice(-6).map(_ref8=>{let[month,amount]=_ref8;return/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-white\",children:month}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",style:{width:'100px'},children:/*#__PURE__*/_jsx(ProgressBar,{now:amount/Math.max(...Object.values(monthlySpending).map(v=>Number(v)))*100,style:{height:'8px'},variant:\"success\"})}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-success fw-semibold\",children:[\"$\",amount.toFixed(2)]})]})]},month);})})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg h-100\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h5\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(PieChart,{className:\"me-2\",size:20}),\"Job Status Distribution\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(statusCounts).map(_ref9=>{let[status,count]=_ref9;return/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center justify-content-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-white\",children:status}),/*#__PURE__*/_jsxs(\"div\",{className:\"d-flex align-items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"me-3\",style:{width:'100px'},children:/*#__PURE__*/_jsx(ProgressBar,{now:count/jobs.length*100,style:{height:'8px'},variant:\"info\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-info fw-semibold\",children:count})]})]},status);})})})]})})]});};// Settings Tab Component\nconst SettingsTab=_ref0=>{let{userSettings,setUserSettings}=_ref0;return/*#__PURE__*/_jsxs(Row,{className:\"g-4\",children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h5\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(Bell,{className:\"me-2\",size:20}),\"Notification Settings\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsx(Form.Check,{type:\"switch\",id:\"email-notifications\",label:\"Email Notifications\",checked:userSettings.notifications.email,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{notifications:_objectSpread(_objectSpread({},userSettings.notifications),{},{email:e.target.checked})})),className:\"text-white mb-3\"}),/*#__PURE__*/_jsx(Form.Check,{type:\"switch\",id:\"push-notifications\",label:\"Push Notifications\",checked:userSettings.notifications.push,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{notifications:_objectSpread(_objectSpread({},userSettings.notifications),{},{push:e.target.checked})})),className:\"text-white mb-3\"}),/*#__PURE__*/_jsx(Form.Check,{type:\"switch\",id:\"sms-notifications\",label:\"SMS Notifications\",checked:userSettings.notifications.sms,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{notifications:_objectSpread(_objectSpread({},userSettings.notifications),{},{sms:e.target.checked})})),className:\"text-white\"})]})})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Card,{className:\"border-0 shadow-lg\",style:{background:'rgba(255, 255, 255, 0.1)',backdropFilter:'blur(20px)',borderRadius:'20px'},children:[/*#__PURE__*/_jsx(Card.Header,{className:\"border-0 bg-transparent\",children:/*#__PURE__*/_jsxs(\"h5\",{className:\"fw-bold mb-0 text-white\",children:[/*#__PURE__*/_jsx(Settings,{className:\"me-2\",size:20}),\"Print Preferences\"]})}),/*#__PURE__*/_jsx(Card.Body,{children:/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-white\",children:\"Default Print Type\"}),/*#__PURE__*/_jsxs(Form.Select,{value:userSettings.preferences.defaultPrintType,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{preferences:_objectSpread(_objectSpread({},userSettings.preferences),{},{defaultPrintType:e.target.value})})),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"Print\",children:\"Print\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Xerox\",children:\"Xerox\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Binding\",children:\"Binding\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Lamination\",children:\"Lamination\"})]})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-white\",children:\"Default Color Type\"}),/*#__PURE__*/_jsxs(Form.Select,{value:userSettings.preferences.defaultColorType,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{preferences:_objectSpread(_objectSpread({},userSettings.preferences),{},{defaultColorType:e.target.value})})),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"BlackWhite\",children:\"Black & White\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Color\",children:\"Color\"})]})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{className:\"text-white\",children:\"Default Paper Size\"}),/*#__PURE__*/_jsxs(Form.Select,{value:userSettings.preferences.defaultPaperSize,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{preferences:_objectSpread(_objectSpread({},userSettings.preferences),{},{defaultPaperSize:e.target.value})})),style:{background:'rgba(255, 255, 255, 0.1)',border:'none',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"A4\",children:\"A4\"}),/*#__PURE__*/_jsx(\"option\",{value:\"A3\",children:\"A3\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Letter\",children:\"Letter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Legal\",children:\"Legal\"})]})]}),/*#__PURE__*/_jsx(Form.Check,{type:\"switch\",id:\"auto-confirm\",label:\"Auto-confirm quotes under $10\",checked:userSettings.preferences.autoConfirmQuotes,onChange:e=>setUserSettings(_objectSpread(_objectSpread({},userSettings),{},{preferences:_objectSpread(_objectSpread({},userSettings.preferences),{},{autoConfirmQuotes:e.target.checked})})),className:\"text-white\"})]})})]})})]});};// Upload Modal Component\nconst UploadModal=_ref1=>{let{show,onHide,uploadData,setUploadData,selectedFile,setSelectedFile,xeroxCenters,onUpload}=_ref1;return/*#__PURE__*/_jsxs(Modal,{show:show,onHide:onHide,size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',border:'none'},children:/*#__PURE__*/_jsxs(Modal.Title,{className:\"text-white\",children:[/*#__PURE__*/_jsx(Upload,{className:\"me-2\",size:20}),\"Upload Files for Printing\"]})}),/*#__PURE__*/_jsx(Modal.Body,{style:{background:'#1a1a1a',color:'#fff'},children:/*#__PURE__*/_jsxs(Form,{children:[/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Select File\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"file\",accept:\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",onChange:e=>{const files=e.target.files;setSelectedFile(files?files[0]:null);},style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'}}),/*#__PURE__*/_jsx(Form.Text,{className:\"text-muted\",children:\"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Print Type\"}),/*#__PURE__*/_jsxs(Form.Select,{value:uploadData.printType,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{printType:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"Print\",children:\"Print\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Xerox\",children:\"Xerox\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Binding\",children:\"Binding\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Lamination\",children:\"Lamination\"})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Number of Copies\"}),/*#__PURE__*/_jsx(Form.Control,{type:\"number\",min:\"1\",value:uploadData.copies,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{copies:parseInt(e.target.value)})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'}})]})})]}),/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Color Type\"}),/*#__PURE__*/_jsxs(Form.Select,{value:uploadData.colorType,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{colorType:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"BlackWhite\",children:\"Black & White\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Color\",children:\"Color\"})]})]})}),/*#__PURE__*/_jsx(Col,{md:6,children:/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Paper Size\"}),/*#__PURE__*/_jsxs(Form.Select,{value:uploadData.paperSize,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{paperSize:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"A4\",children:\"A4\"}),/*#__PURE__*/_jsx(\"option\",{value:\"A3\",children:\"A3\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Letter\",children:\"Letter\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Legal\",children:\"Legal\"})]})]})})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Priority\"}),/*#__PURE__*/_jsxs(Form.Select,{value:uploadData.priority,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{priority:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"Low\",children:\"Low\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Normal\",children:\"Normal\"}),/*#__PURE__*/_jsx(\"option\",{value:\"High\",children:\"High\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Urgent\",children:\"Urgent\"})]})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Preferred Xerox Center\"}),/*#__PURE__*/_jsxs(Form.Select,{value:uploadData.preferredXeroxCenterId,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{preferredXeroxCenterId:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a center (optional)\"}),xeroxCenters.map(center=>/*#__PURE__*/_jsxs(\"option\",{value:center.id,children:[center.name,\" - \",center.pendingJobs,\" pending jobs\"]},center.id))]})]}),/*#__PURE__*/_jsxs(Form.Group,{className:\"mb-3\",children:[/*#__PURE__*/_jsx(Form.Label,{children:\"Remarks\"}),/*#__PURE__*/_jsx(Form.Control,{as:\"textarea\",rows:3,placeholder:\"Any special instructions or remarks...\",value:uploadData.remarks,onChange:e=>setUploadData(prev=>_objectSpread(_objectSpread({},prev),{},{remarks:e.target.value})),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'}})]})]})}),/*#__PURE__*/_jsxs(Modal.Footer,{style:{background:'#1a1a1a',border:'none'},children:[/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:onHide,children:\"Cancel\"}),/*#__PURE__*/_jsxs(Button,{onClick:onUpload,disabled:!selectedFile,style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',border:'none'},children:[/*#__PURE__*/_jsx(Upload,{className:\"me-2\",size:16}),\"Upload File\"]})]})]});};// View Job Modal Component\nconst ViewJobModal=_ref10=>{let{show,onHide,job,getStatusBadge,getPriorityBadge}=_ref10;return/*#__PURE__*/_jsxs(Modal,{show:show,onHide:onHide,size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',border:'none'},children:/*#__PURE__*/_jsxs(Modal.Title,{className:\"text-white\",children:[/*#__PURE__*/_jsx(Eye,{className:\"me-2\",size:20}),\"Job Details\"]})}),/*#__PURE__*/_jsx(Modal.Body,{style:{background:'#1a1a1a',color:'#fff'},children:job&&/*#__PURE__*/_jsxs(Row,{children:[/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"h6\",{className:\"text-primary\",children:\"Job Information\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Job Number:\"}),\" \",job.jobNumber]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Status:\"}),\" \",getStatusBadge(job.status)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Priority:\"}),\" \",getPriorityBadge(job.priority)]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"File Name:\"}),\" \",job.fileName]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Xerox Center:\"}),\" \",job.xeroxCenterName]}),job.cost&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Cost:\"}),\" \",/*#__PURE__*/_jsxs(\"span\",{className:\"text-success\",children:[\"$\",job.cost.toFixed(2)]})]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Created:\"}),\" \",new Date(job.created).toLocaleString()]}),job.estimatedCompletionTime&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Estimated Completion:\"}),\" \",new Date(job.estimatedCompletionTime).toLocaleString()]})]}),/*#__PURE__*/_jsxs(Col,{md:6,children:[/*#__PURE__*/_jsx(\"h6\",{className:\"text-primary\",children:\"Print Specifications\"}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Type:\"}),\" \",job.printType]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Copies:\"}),\" \",job.copies]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Color:\"}),\" \",job.colorType]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Paper Size:\"}),\" \",job.paperSize]}),job.remarks&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"h6\",{className:\"text-primary mt-3\",children:\"Remarks\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted\",children:job.remarks})]})]})]})}),/*#__PURE__*/_jsx(Modal.Footer,{style:{background:'#1a1a1a',border:'none'},children:/*#__PURE__*/_jsx(Button,{variant:\"secondary\",onClick:onHide,children:\"Close\"})})]});};// Chat Modal Component\nconst ChatModal=_ref11=>{let{show,onHide,job,messages,chatMessage,setChatMessage,onSendMessage}=_ref11;return/*#__PURE__*/_jsxs(Modal,{show:show,onHide:onHide,size:\"lg\",centered:true,children:[/*#__PURE__*/_jsx(Modal.Header,{closeButton:true,style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',border:'none'},children:/*#__PURE__*/_jsxs(Modal.Title,{className:\"text-white\",children:[/*#__PURE__*/_jsx(MessageCircle,{className:\"me-2\",size:20}),\"Chat - \",job===null||job===void 0?void 0:job.jobNumber]})}),/*#__PURE__*/_jsx(Modal.Body,{style:{background:'#1a1a1a',color:'#fff',height:'400px',overflowY:'auto'},children:/*#__PURE__*/_jsx(\"div\",{className:\"mb-3\",children:messages.map((message,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2 \".concat(message.isFromStudent?'text-end':'text-start'),children:[/*#__PURE__*/_jsx(\"div\",{className:\"d-inline-block p-2 rounded \".concat(message.isFromStudent?'text-white':'bg-secondary text-white'),style:{background:message.isFromStudent?'linear-gradient(135deg, #667eea 0%, #764ba2 100%)':'#6c757d'},children:message.content}),/*#__PURE__*/_jsx(\"div\",{className:\"small text-muted\",children:new Date(message.timestamp).toLocaleString()})]},index))})}),/*#__PURE__*/_jsx(Modal.Footer,{style:{background:'#1a1a1a',border:'none'},children:/*#__PURE__*/_jsxs(InputGroup,{children:[/*#__PURE__*/_jsx(Form.Control,{type:\"text\",placeholder:\"Type your message...\",value:chatMessage,onChange:e=>setChatMessage(e.target.value),onKeyPress:e=>e.key==='Enter'&&onSendMessage(),style:{background:'#2a2a2a',border:'1px solid #444',color:'#fff'}}),/*#__PURE__*/_jsx(Button,{onClick:onSendMessage,style:{background:'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',border:'none'},children:/*#__PURE__*/_jsx(Send,{size:16})})]})})]});};export default AceternityStudentDashboard;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Form", "Modal", "InputGroup", "Nav", "Table", "ProgressBar", "motion", "AnimatePresence", "Upload", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Eye", "Activity", "Printer", "Star", "History", "Settings", "User", "CreditCard", "Bell", "Search", "TrendingUp", "BarChart3", "<PERSON><PERSON><PERSON>", "Target", "Award", "Heart", "RefreshCw", "AlertCircle", "CheckCircle2", "XCircle", "Send", "Phone", "MapPin", "Location", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AceternityStudentDashboard", "user", "activeTab", "setActiveTab", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "favoritesCenters", "setFavoritesCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "showSettingsModal", "setShowSettingsModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedCenter", "setSelectedCenter", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "sortBy", "setSortBy", "isRefreshing", "setIsRefreshing", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "priority", "userSettings", "setUserSettings", "notifications", "email", "push", "sms", "preferences", "defaultPrintType", "defaultColorType", "defaultPaperSize", "autoConfirmQuotes", "profile", "phone", "address", "university", "studentId", "fetchData", "interval", "setInterval", "clearInterval", "printJobsResponse", "xeroxCentersResponse", "Promise", "all", "getStudentJobs", "getAll", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "bg", "icon", "config", "IconComponent", "className", "children", "size", "getPriorityBadge", "priorityConfig", "handleFileUpload", "formData", "FormData", "append", "Object", "entries", "for<PERSON>ach", "_ref", "key", "value", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "fileName", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "toggleFavorite", "centerId", "includes", "filter", "filteredJobs", "matchesSearch", "jobNumber", "toLowerCase", "xeroxCenterName", "matchesStatus", "totalSpent", "reduce", "sum", "cost", "inProgressJobs", "length", "completedJobs", "pendingJobs", "containerVariants", "hidden", "opacity", "visible", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "floatingShapes", "Array", "from", "_", "i", "div", "style", "background", "concat", "width", "Math", "random", "height", "left", "top", "zIndex", "animate", "x", "rotate", "scale", "repeat", "Infinity", "repeatType", "ease", "minHeight", "fluid", "variants", "initial", "borderRadius", "boxShadow", "fontSize", "username", "whileHover", "whileTap", "onClick", "<PERSON><PERSON>ilter", "color", "variant", "disabled", "Body", "label", "map", "tab", "<PERSON><PERSON>", "Link", "active", "border", "mode", "exit", "DashboardTab", "onViewJob", "onDownloadFile", "onOpenChat", "onConfirmJob", "JobsTab", "jobs", "CentersTab", "centers", "onToggleFavorite", "onSelectCenter", "HistoryTab", "FavoritesTab", "center", "AnalyticsTab", "SettingsTab", "UploadModal", "show", "onHide", "onUpload", "ViewJobModal", "ChatModal", "onSendMessage", "_ref2", "md", "delay", "toFixed", "lg", "Header", "text", "slice", "index", "display", "alignItems", "justifyContent", "c", "isActive", "_ref3", "Text", "Control", "placeholder", "onChange", "e", "target", "Select", "Date", "created", "toLocaleDateString", "_ref4", "name", "location", "button", "fill", "averageRating", "services", "service", "idx", "priceRange", "workingHours", "_ref5", "_ref6", "_ref7", "monthlySpending", "acc", "month", "year", "statusCounts", "_ref8", "amount", "now", "max", "values", "v", "Number", "_ref9", "count", "_ref0", "Check", "type", "checked", "_objectSpread", "Group", "Label", "_ref1", "centered", "closeButton", "Title", "accept", "files", "min", "parseInt", "as", "rows", "Footer", "_ref10", "toLocaleString", "estimatedCompletionTime", "_ref11", "overflowY", "message", "isFromStudent", "content", "timestamp", "onKeyPress"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityStudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Badge, Form, Modal, InputGroup, Nav, Tab, Table, ProgressBar } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Eye,\n  Plus,\n  Activity,\n  Printer,\n  MapPin,\n  Star,\n  History,\n  Settings,\n  User,\n  CreditCard,\n  Bell,\n  Search,\n  Filter,\n  Calendar,\n  TrendingUp,\n  BarChart3,\n  PieChart,\n  Zap,\n  Target,\n  Award,\n  Bookmark,\n  Heart,\n  Share2,\n  RefreshCw,\n  AlertCircle,\n  Info,\n  CheckCircle2,\n  XCircle,\n  Trash2,\n  Edit3,\n  Send,\n  Phone,\n  Mail,\n  Globe,\n  MapPin as Location\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  priority: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n  phone?: string;\n  email?: string;\n  website?: string;\n  description?: string;\n  services?: string[];\n  priceRange?: string;\n  workingHours?: string;\n}\n\nconst AceternityStudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [favoritesCenters, setFavoritesCenters] = useState<number[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedCenter, setSelectedCenter] = useState<XeroxCenter | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('All');\n  const [sortBy, setSortBy] = useState('created');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4',\n    priority: 'Normal'\n  });\n\n  const [userSettings, setUserSettings] = useState({\n    notifications: {\n      email: true,\n      push: true,\n      sms: false\n    },\n    preferences: {\n      defaultPrintType: 'Print',\n      defaultColorType: 'BlackWhite',\n      defaultPaperSize: 'A4',\n      autoConfirmQuotes: false\n    },\n    profile: {\n      phone: '',\n      address: '',\n      university: '',\n      studentId: ''\n    }\n  });\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchData = async () => {\n    setIsRefreshing(true);\n    try {\n      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([\n        printJobApi.getStudentJobs(),\n        xeroxCenterApi.getAll()\n      ]);\n\n      setPrintJobs(printJobsResponse.data || []);\n      setXeroxCenters(xeroxCentersResponse.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setPrintJobs([]);\n      setXeroxCenters([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { bg: 'secondary', icon: Clock },\n      'UnderReview': { bg: 'info', icon: Eye },\n      'Quoted': { bg: 'warning', icon: DollarSign },\n      'WaitingConfirmation': { bg: 'warning', icon: AlertCircle },\n      'Confirmed': { bg: 'info', icon: CheckCircle },\n      'InProgress': { bg: 'primary', icon: Activity },\n      'Completed': { bg: 'success', icon: CheckCircle2 },\n      'Delivered': { bg: 'success', icon: CheckCircle2 },\n      'Rejected': { bg: 'danger', icon: XCircle },\n      'Cancelled': { bg: 'secondary', icon: XCircle }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { bg: 'secondary', icon: Clock };\n    const IconComponent = config.icon;\n\n    return (\n      <Badge bg={config.bg} className=\"d-flex align-items-center gap-1 px-3 py-2\">\n        <IconComponent size={14} />\n        {status}\n      </Badge>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      'Low': { bg: 'success', icon: '🟢' },\n      'Normal': { bg: 'secondary', icon: '🔵' },\n      'High': { bg: 'warning', icon: '🟡' },\n      'Urgent': { bg: 'danger', icon: '🔴' }\n    };\n\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || { bg: 'secondary', icon: '🔵' };\n\n    return (\n      <Badge bg={config.bg} className=\"px-2 py-1\">\n        {config.icon} {priority}\n      </Badge>\n    );\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      Object.entries(uploadData).forEach(([key, value]) => {\n        formData.append(key, value.toString());\n      });\n\n      await fileUploadApi.uploadFile(formData);\n      await fetchData();\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4',\n        priority: 'Normal'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data || []);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n      await fetchData();\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const toggleFavorite = (centerId: number) => {\n    setFavoritesCenters(prev =>\n      prev.includes(centerId)\n        ? prev.filter(id => id !== centerId)\n        : [...prev, centerId]\n    );\n  };\n\n  const filteredJobs = printJobs.filter(job => {\n    const matchesSearch = searchTerm === '' ||\n      job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      job.xeroxCenterName.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesStatus = filterStatus === 'All' || job.status === filterStatus;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const pendingJobs = printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted'].includes(job.status)).length;\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const floatingShapes = Array.from({ length: 8 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"position-absolute rounded-circle\"\n      style={{\n        background: `linear-gradient(135deg, ${i % 3 === 0 ? '#667eea' : i % 3 === 1 ? '#764ba2' : '#f093fb'}, ${i % 3 === 0 ? '#764ba2' : i % 3 === 1 ? '#f093fb' : '#f5576c'})`,\n        width: `${Math.random() * 60 + 20}px`,\n        height: `${Math.random() * 60 + 20}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        opacity: 0.1,\n        zIndex: 0\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n        scale: [1, 1.2, 1],\n      }}\n      transition={{\n        duration: Math.random() * 20 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n        ease: \"easeInOut\"\n      }}\n    />\n  ));\n\n  return (\n    <div className=\"min-h-screen position-relative\" style={{\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh'\n    }}>\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100 overflow-hidden\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)',\n        }} />\n      </div>\n\n      <Container fluid className=\"position-relative py-4\" style={{ zIndex: 1 }}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-5\">\n            <motion.div\n              animate={{\n                rotate: [0, 360],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"d-inline-flex align-items-center justify-content-center mb-3\"\n              style={{\n                width: '80px',\n                height: '80px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px',\n                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n              }}\n            >\n              <User className=\"text-white\" size={40} />\n            </motion.div>\n            <h1 className=\"text-white fw-bold mb-2\" style={{ fontSize: '2.5rem' }}>\n              Welcome back, {user?.username}!\n            </h1>\n            <p className=\"text-white-50 fs-5 mb-4\">\n              Manage your printing jobs and explore xerox centers\n            </p>\n\n            <div className=\"d-flex align-items-center justify-content-center gap-3 flex-wrap\">\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  size=\"lg\"\n                  onClick={() => setShowUploadModal(true)}\n                  className=\"px-5 py-3 rounded-4 border-0 fw-semibold\"\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    backdropFilter: 'blur(20px)',\n                    color: '#fff',\n                    boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'\n                  }}\n                >\n                  <Upload className=\"me-2\" size={20} />\n                  Upload Files\n                </Button>\n              </motion.div>\n\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  variant=\"outline-light\"\n                  size=\"lg\"\n                  onClick={fetchData}\n                  disabled={isRefreshing}\n                  className=\"px-4 py-3 rounded-4 fw-semibold\"\n                >\n                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />\n                  Refresh\n                </Button>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Navigation Tabs */}\n          <motion.div variants={itemVariants} className=\"mb-4\">\n            <Card className=\"border-0 shadow-lg\" style={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            }}>\n              <Card.Body className=\"p-2\">\n                <Nav variant=\"pills\" className=\"justify-content-center flex-wrap\">\n                  {[\n                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },\n                    { key: 'jobs', label: 'My Jobs', icon: FileText },\n                    { key: 'centers', label: 'Xerox Centers', icon: Printer },\n                    { key: 'history', label: 'History', icon: History },\n                    { key: 'favorites', label: 'Favorites', icon: Heart },\n                    { key: 'analytics', label: 'Analytics', icon: PieChart },\n                    { key: 'settings', label: 'Settings', icon: Settings }\n                  ].map(tab => {\n                    const IconComponent = tab.icon;\n                    return (\n                      <Nav.Item key={tab.key} className=\"m-1\">\n                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                          <Nav.Link\n                            active={activeTab === tab.key}\n                            onClick={() => setActiveTab(tab.key)}\n                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${\n                              activeTab === tab.key\n                                ? 'text-primary'\n                                : 'text-white-50'\n                            }`}\n                            style={{\n                              background: activeTab === tab.key\n                                ? 'rgba(255, 255, 255, 0.9)'\n                                : 'transparent',\n                              border: 'none',\n                              transition: 'all 0.3s ease',\n                              boxShadow: activeTab === tab.key\n                                ? '0 10px 30px rgba(255, 255, 255, 0.2)'\n                                : 'none'\n                            }}\n                          >\n                            <IconComponent size={18} className=\"me-2\" />\n                            <span className=\"d-none d-md-inline\">{tab.label}</span>\n                          </Nav.Link>\n                        </motion.div>\n                      </Nav.Item>\n                    );\n                  })}\n                </Nav>\n              </Card.Body>\n            </Card>\n          </motion.div>\n\n          {/* Tab Content */}\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeTab}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {activeTab === 'dashboard' && (\n                <DashboardTab\n                  printJobs={printJobs}\n                  xeroxCenters={xeroxCenters}\n                  totalSpent={totalSpent}\n                  inProgressJobs={inProgressJobs}\n                  completedJobs={completedJobs}\n                  pendingJobs={pendingJobs}\n                  onViewJob={handleViewJob}\n                  onDownloadFile={handleDownloadFile}\n                  onOpenChat={handleOpenChat}\n                  onConfirmJob={handleConfirmJob}\n                  getStatusBadge={getStatusBadge}\n                  getPriorityBadge={getPriorityBadge}\n                />\n              )}\n\n              {activeTab === 'jobs' && (\n                <JobsTab\n                  jobs={filteredJobs}\n                  searchTerm={searchTerm}\n                  setSearchTerm={setSearchTerm}\n                  filterStatus={filterStatus}\n                  setFilterStatus={setFilterStatus}\n                  sortBy={sortBy}\n                  setSortBy={setSortBy}\n                  onViewJob={handleViewJob}\n                  onDownloadFile={handleDownloadFile}\n                  onOpenChat={handleOpenChat}\n                  onConfirmJob={handleConfirmJob}\n                  getStatusBadge={getStatusBadge}\n                  getPriorityBadge={getPriorityBadge}\n                />\n              )}\n\n              {activeTab === 'centers' && (\n                <CentersTab\n                  centers={xeroxCenters}\n                  favoritesCenters={favoritesCenters}\n                  onToggleFavorite={toggleFavorite}\n                  onSelectCenter={setSelectedCenter}\n                />\n              )}\n\n              {activeTab === 'history' && (\n                <HistoryTab\n                  jobs={printJobs.filter(job => ['Completed', 'Delivered', 'Cancelled', 'Rejected'].includes(job.status))}\n                  onViewJob={handleViewJob}\n                  getStatusBadge={getStatusBadge}\n                />\n              )}\n\n              {activeTab === 'favorites' && (\n                <FavoritesTab\n                  centers={xeroxCenters.filter(center => favoritesCenters.includes(center.id))}\n                  onToggleFavorite={toggleFavorite}\n                  onSelectCenter={setSelectedCenter}\n                />\n              )}\n\n              {activeTab === 'analytics' && (\n                <AnalyticsTab\n                  jobs={printJobs}\n                  totalSpent={totalSpent}\n                />\n              )}\n\n              {activeTab === 'settings' && (\n                <SettingsTab\n                  userSettings={userSettings}\n                  setUserSettings={setUserSettings}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n        </motion.div>\n      </Container>\n\n      {/* Modals */}\n      <UploadModal\n        show={showUploadModal}\n        onHide={() => setShowUploadModal(false)}\n        uploadData={uploadData}\n        setUploadData={setUploadData}\n        selectedFile={selectedFile}\n        setSelectedFile={setSelectedFile}\n        xeroxCenters={xeroxCenters}\n        onUpload={handleFileUpload}\n      />\n\n      <ViewJobModal\n        show={showViewModal}\n        onHide={() => setShowViewModal(false)}\n        job={selectedJob}\n        getStatusBadge={getStatusBadge}\n        getPriorityBadge={getPriorityBadge}\n      />\n\n      <ChatModal\n        show={showChatModal}\n        onHide={() => setShowChatModal(false)}\n        job={selectedJob}\n        messages={messages}\n        chatMessage={chatMessage}\n        setChatMessage={setChatMessage}\n        onSendMessage={handleSendMessage}\n      />\n    </div>\n  );\n};\n\n// Dashboard Tab Component\nconst DashboardTab: React.FC<any> = ({\n  printJobs, xeroxCenters, totalSpent, inProgressJobs, completedJobs, pendingJobs,\n  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge\n}) => {\n  return (\n    <Row className=\"g-4\">\n      {/* Statistics Cards */}\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <FileText className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Total Jobs</h6>\n              <h2 className=\"fw-bold text-white\">{printJobs.length}</h2>\n              <small className=\"text-success\">\n                <TrendingUp size={12} className=\"me-1\" />\n                +12% vs last month\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <Clock className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">In Progress</h6>\n              <h2 className=\"fw-bold text-white\">{inProgressJobs}</h2>\n              <small className=\"text-warning\">\n                <Activity size={12} className=\"me-1\" />\n                Active jobs\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <CheckCircle className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Completed</h6>\n              <h2 className=\"fw-bold text-white\">{completedJobs}</h2>\n              <small className=\"text-success\">\n                <Award size={12} className=\"me-1\" />\n                Finished jobs\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <DollarSign className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Total Spent</h6>\n              <h2 className=\"fw-bold text-white\">${totalSpent.toFixed(2)}</h2>\n              <small className=\"text-info\">\n                <CreditCard size={12} className=\"me-1\" />\n                This month\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      {/* Recent Jobs */}\n      <Col lg={8}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <div className=\"d-flex align-items-center justify-content-between\">\n              <h4 className=\"fw-bold mb-0 text-white\">\n                <Activity className=\"me-2\" size={20} />\n                Recent Jobs\n              </h4>\n              <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n                {printJobs.length} total\n              </Badge>\n            </div>\n          </Card.Header>\n          <Card.Body>\n            {printJobs.length > 0 ? (\n              <div className=\"space-y-3\">\n                {printJobs.slice(0, 5).map((job: PrintJob, index: number) => (\n                  <motion.div\n                    key={job.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"p-3 rounded-3\"\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      border: '1px solid rgba(255, 255, 255, 0.2)'\n                    }}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-between\">\n                      <div className=\"d-flex align-items-center\">\n                        <div className=\"me-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '50px',\n                            height: '50px',\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            borderRadius: '12px'\n                          }}>\n                            <FileText className=\"text-white\" size={20} />\n                          </div>\n                        </div>\n                        <div>\n                          <h6 className=\"fw-semibold mb-1 text-white\">{job.jobNumber}</h6>\n                          <p className=\"text-white-50 small mb-1\">\n                            {job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName}\n                          </p>\n                          <small className=\"text-white-50\">{job.xeroxCenterName}</small>\n                        </div>\n                      </div>\n\n                      <div className=\"d-flex align-items-center\">\n                        <div className=\"text-end me-3\">\n                          {getStatusBadge(job.status)}\n                          {job.cost && (\n                            <div className=\"fw-semibold text-success mt-1\">\n                              ${job.cost.toFixed(2)}\n                            </div>\n                          )}\n                        </div>\n\n                        <div className=\"d-flex gap-1\">\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onDownloadFile(job.id, job.fileName)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <Download size={14} />\n                          </Button>\n\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onViewJob(job)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <Eye size={14} />\n                          </Button>\n\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onOpenChat(job)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <MessageCircle size={14} />\n                          </Button>\n\n                          {job.status === 'Quoted' && (\n                            <Button\n                              variant=\"success\"\n                              size=\"sm\"\n                              onClick={() => onConfirmJob(job.id)}\n                              style={{ borderRadius: '8px' }}\n                            >\n                              <CheckCircle size={14} />\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"text-center py-5\"\n              >\n                <div className=\"mb-4\">\n                  <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                    width: '80px',\n                    height: '80px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '20px'\n                  }}>\n                    <Upload className=\"text-white\" size={40} />\n                  </div>\n                </div>\n                <h5 className=\"fw-semibold mb-2 text-white\">No jobs yet</h5>\n                <p className=\"text-white-50 mb-4\">Upload your first file to get started!</p>\n              </motion.div>\n            )}\n          </Card.Body>\n        </Card>\n      </Col>\n\n      {/* Quick Stats */}\n      <Col lg={4}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h4 className=\"fw-bold mb-0 text-white\">\n              <Target className=\"me-2\" size={20} />\n              Quick Stats\n            </h4>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"space-y-4\">\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Clock className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Pending</h6>\n                    <small className=\"text-white-50\">Awaiting review</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-warning\">{pendingJobs}</h4>\n              </div>\n\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Printer className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Centers</h6>\n                    <small className=\"text-white-50\">Available now</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-info\">{xeroxCenters.filter((c: XeroxCenter) => c.isActive).length}</h4>\n              </div>\n\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Star className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Avg Rating</h6>\n                    <small className=\"text-white-50\">Your experience</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-success\">4.8</h4>\n              </div>\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Jobs Tab Component\nconst JobsTab: React.FC<any> = ({\n  jobs, searchTerm, setSearchTerm, filterStatus, setFilterStatus, sortBy, setSortBy,\n  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge\n}) => {\n  return (\n    <Card className=\"border-0 shadow-lg\" style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    }}>\n      <Card.Header className=\"border-0 bg-transparent\">\n        <div className=\"d-flex align-items-center justify-content-between mb-3\">\n          <h4 className=\"fw-bold mb-0 text-white\">\n            <FileText className=\"me-2\" size={20} />\n            All Jobs\n          </h4>\n          <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n            {jobs.length} jobs\n          </Badge>\n        </div>\n\n        {/* Search and Filter Controls */}\n        <Row className=\"g-3\">\n          <Col md={4}>\n            <InputGroup>\n              <InputGroup.Text style={{ background: 'rgba(255, 255, 255, 0.1)', border: 'none' }}>\n                <Search className=\"text-white-50\" size={16} />\n              </InputGroup.Text>\n              <Form.Control\n                placeholder=\"Search jobs...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                style={{\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  border: 'none',\n                  color: '#fff'\n                }}\n              />\n            </InputGroup>\n          </Col>\n\n          <Col md={3}>\n            <Form.Select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value)}\n              style={{\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none',\n                color: '#fff'\n              }}\n            >\n              <option value=\"All\">All Status</option>\n              <option value=\"Requested\">Requested</option>\n              <option value=\"UnderReview\">Under Review</option>\n              <option value=\"Quoted\">Quoted</option>\n              <option value=\"Confirmed\">Confirmed</option>\n              <option value=\"InProgress\">In Progress</option>\n              <option value=\"Completed\">Completed</option>\n              <option value=\"Delivered\">Delivered</option>\n            </Form.Select>\n          </Col>\n\n          <Col md={3}>\n            <Form.Select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              style={{\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none',\n                color: '#fff'\n              }}\n            >\n              <option value=\"created\">Sort by Date</option>\n              <option value=\"status\">Sort by Status</option>\n              <option value=\"cost\">Sort by Cost</option>\n              <option value=\"priority\">Sort by Priority</option>\n            </Form.Select>\n          </Col>\n        </Row>\n      </Card.Header>\n\n      <Card.Body>\n        {jobs.length > 0 ? (\n          <Row className=\"g-4\">\n            {jobs.map((job: any, index: number) => (\n              <Col key={job.id} md={6} lg={4}>\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.05 }}\n                >\n                  <Card className=\"h-100 border-0\" style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    borderRadius: '15px'\n                  }}>\n                    <Card.Header className=\"border-0 bg-transparent\">\n                      <div className=\"d-flex align-items-start justify-content-between\">\n                        <div>\n                          <h6 className=\"fw-bold mb-1 text-white\">{job.jobNumber}</h6>\n                          {getPriorityBadge(job.priority)}\n                        </div>\n                        {getStatusBadge(job.status)}\n                      </div>\n                    </Card.Header>\n\n                    <Card.Body className=\"pt-0\">\n                      <div className=\"mb-3\">\n                        <div className=\"d-flex align-items-center text-white-50 small mb-2\">\n                          <FileText size={14} className=\"me-1\" />\n                          {job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}\n                        </div>\n                        <div className=\"small text-white-50\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                      </div>\n\n                      {job.cost && (\n                        <div className=\"mb-3\">\n                          <span className=\"h5 fw-bold text-success\">\n                            ${job.cost.toFixed(2)}\n                          </span>\n                        </div>\n                      )}\n\n                      <div className=\"small text-white-50 mb-3\">\n                        {new Date(job.created).toLocaleDateString()}\n                      </div>\n\n                      <div className=\"d-flex flex-wrap gap-1\">\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onDownloadFile(job.id, job.fileName)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <Download size={12} />\n                        </Button>\n\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onViewJob(job)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <Eye size={12} />\n                        </Button>\n\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onOpenChat(job)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <MessageCircle size={12} />\n                        </Button>\n\n                        {job.status === 'Quoted' && (\n                          <Button\n                            variant=\"success\"\n                            size=\"sm\"\n                            onClick={() => onConfirmJob(job.id)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <CheckCircle size={12} />\n                          </Button>\n                        )}\n                      </div>\n                    </Card.Body>\n                  </Card>\n                </motion.div>\n              </Col>\n            ))}\n          </Row>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"text-center py-5\"\n          >\n            <div className=\"mb-4\">\n              <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                width: '80px',\n                height: '80px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px'\n              }}>\n                <FileText className=\"text-white\" size={40} />\n              </div>\n            </div>\n            <h5 className=\"fw-semibold mb-2 text-white\">No jobs found</h5>\n            <p className=\"text-white-50\">No jobs match your current filter criteria.</p>\n          </motion.div>\n        )}\n      </Card.Body>\n    </Card>\n  );\n};\n\n// Centers Tab Component\nconst CentersTab: React.FC<any> = ({ centers, favoritesCenters, onToggleFavorite, onSelectCenter }) => {\n  return (\n    <Row className=\"g-4\">\n      {centers.map((center: any, index: number) => (\n        <Col key={center.id} md={6} lg={4}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ y: -5, scale: 1.02 }}\n          >\n            <Card className=\"h-100 border-0 shadow-lg\" style={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            }}>\n              <Card.Header className=\"border-0 bg-transparent\">\n                <div className=\"d-flex align-items-start justify-content-between\">\n                  <div>\n                    <h5 className=\"fw-bold mb-1 text-white\">{center.name}</h5>\n                    <div className=\"d-flex align-items-center text-white-50 small\">\n                      <Location size={12} className=\"me-1\" />\n                      {center.location}\n                    </div>\n                  </div>\n                  <motion.button\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={() => onToggleFavorite(center.id)}\n                    className=\"btn btn-link p-0 border-0\"\n                  >\n                    <Heart\n                      size={20}\n                      className={favoritesCenters.includes(center.id) ? 'text-danger' : 'text-white-50'}\n                      fill={favoritesCenters.includes(center.id) ? 'currentColor' : 'none'}\n                    />\n                  </motion.button>\n                </div>\n              </Card.Header>\n\n              <Card.Body>\n                <div className=\"mb-3\">\n                  <div className=\"d-flex align-items-center justify-content-between mb-2\">\n                    <div className=\"d-flex align-items-center\">\n                      <Star className=\"text-warning me-1\" size={16} />\n                      <span className=\"fw-semibold text-white\">\n                        {center.averageRating.toFixed(1)}\n                      </span>\n                    </div>\n                    <Badge\n                      bg={center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'danger'}\n                      className=\"px-2 py-1\"\n                    >\n                      {center.pendingJobs} jobs\n                    </Badge>\n                  </div>\n\n                  {center.services && (\n                    <div className=\"mb-3\">\n                      <div className=\"d-flex flex-wrap gap-1\">\n                        {center.services.slice(0, 3).map((service: string, idx: number) => (\n                          <Badge key={idx} bg=\"light\" text=\"dark\" className=\"px-2 py-1\">\n                            {service}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {center.priceRange && (\n                    <div className=\"text-white-50 small mb-2\">\n                      <DollarSign size={12} className=\"me-1\" />\n                      {center.priceRange}\n                    </div>\n                  )}\n\n                  {center.workingHours && (\n                    <div className=\"text-white-50 small mb-3\">\n                      <Clock size={12} className=\"me-1\" />\n                      {center.workingHours}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"d-flex gap-2\">\n                  <Button\n                    variant=\"outline-light\"\n                    size=\"sm\"\n                    onClick={() => onSelectCenter(center)}\n                    className=\"flex-grow-1\"\n                    style={{ borderRadius: '10px' }}\n                  >\n                    <Eye size={14} className=\"me-1\" />\n                    View Details\n                  </Button>\n\n                  {center.phone && (\n                    <Button\n                      variant=\"outline-light\"\n                      size=\"sm\"\n                      href={`tel:${center.phone}`}\n                      style={{ borderRadius: '10px' }}\n                    >\n                      <Phone size={14} />\n                    </Button>\n                  )}\n                </div>\n              </Card.Body>\n            </Card>\n          </motion.div>\n        </Col>\n      ))}\n    </Row>\n  );\n};\n\n// History Tab Component\nconst HistoryTab: React.FC<any> = ({ jobs, onViewJob, getStatusBadge }) => {\n  return (\n    <Card className=\"border-0 shadow-lg\" style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    }}>\n      <Card.Header className=\"border-0 bg-transparent\">\n        <h4 className=\"fw-bold mb-0 text-white\">\n          <History className=\"me-2\" size={20} />\n          Job History\n        </h4>\n      </Card.Header>\n      <Card.Body>\n        {jobs.length > 0 ? (\n          <div className=\"table-responsive\">\n            <Table className=\"table-dark table-hover\">\n              <thead>\n                <tr>\n                  <th>Job Number</th>\n                  <th>File Name</th>\n                  <th>Status</th>\n                  <th>Cost</th>\n                  <th>Date</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {jobs.map((job: any) => (\n                  <tr key={job.id}>\n                    <td className=\"fw-semibold\">{job.jobNumber}</td>\n                    <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>\n                    <td>{getStatusBadge(job.status)}</td>\n                    <td className=\"text-success fw-semibold\">\n                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                    </td>\n                    <td>{new Date(job.created).toLocaleDateString()}</td>\n                    <td>\n                      <Button\n                        variant=\"outline-light\"\n                        size=\"sm\"\n                        onClick={() => onViewJob(job)}\n                      >\n                        <Eye size={14} />\n                      </Button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </Table>\n          </div>\n        ) : (\n          <div className=\"text-center py-5\">\n            <History className=\"text-white-50 mb-3\" size={60} />\n            <h5 className=\"text-white\">No history yet</h5>\n            <p className=\"text-white-50\">Your completed jobs will appear here.</p>\n          </div>\n        )}\n      </Card.Body>\n    </Card>\n  );\n};\n\n// Favorites Tab Component\nconst FavoritesTab: React.FC<any> = ({ centers, onToggleFavorite, onSelectCenter }) => {\n  return (\n    <div>\n      {centers.length > 0 ? (\n        <CentersTab\n          centers={centers}\n          favoritesCenters={centers.map((c: any) => c.id)}\n          onToggleFavorite={onToggleFavorite}\n          onSelectCenter={onSelectCenter}\n        />\n      ) : (\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Body className=\"text-center py-5\">\n            <Heart className=\"text-white-50 mb-3\" size={60} />\n            <h5 className=\"text-white\">No favorites yet</h5>\n            <p className=\"text-white-50\">Add xerox centers to your favorites for quick access.</p>\n          </Card.Body>\n        </Card>\n      )}\n    </div>\n  );\n};\n\n// Analytics Tab Component\nconst AnalyticsTab: React.FC<any> = ({ jobs, totalSpent }) => {\n  const monthlySpending = jobs.reduce((acc: any, job: any) => {\n    const month = new Date(job.created).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    acc[month] = (acc[month] || 0) + (job.cost || 0);\n    return acc;\n  }, {});\n\n  const statusCounts = jobs.reduce((acc: any, job: any) => {\n    acc[job.status] = (acc[job.status] || 0) + 1;\n    return acc;\n  }, {});\n\n  return (\n    <Row className=\"g-4\">\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <BarChart3 className=\"me-2\" size={20} />\n              Spending Overview\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"mb-4\">\n              <h3 className=\"text-success fw-bold\">${totalSpent.toFixed(2)}</h3>\n              <p className=\"text-white-50\">Total spent this year</p>\n            </div>\n\n            <div className=\"space-y-3\">\n              {Object.entries(monthlySpending).slice(-6).map(([month, amount]: [string, any]) => (\n                <div key={month} className=\"d-flex align-items-center justify-content-between\">\n                  <span className=\"text-white\">{month}</span>\n                  <div className=\"d-flex align-items-center\">\n                    <div className=\"me-3\" style={{ width: '100px' }}>\n                      <ProgressBar\n                        now={(amount / Math.max(...Object.values(monthlySpending).map(v => Number(v)))) * 100}\n                        style={{ height: '8px' }}\n                        variant=\"success\"\n                      />\n                    </div>\n                    <span className=\"text-success fw-semibold\">${amount.toFixed(2)}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <PieChart className=\"me-2\" size={20} />\n              Job Status Distribution\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"space-y-3\">\n              {Object.entries(statusCounts).map(([status, count]: [string, any]) => (\n                <div key={status} className=\"d-flex align-items-center justify-content-between\">\n                  <span className=\"text-white\">{status}</span>\n                  <div className=\"d-flex align-items-center\">\n                    <div className=\"me-3\" style={{ width: '100px' }}>\n                      <ProgressBar\n                        now={(count / jobs.length) * 100}\n                        style={{ height: '8px' }}\n                        variant=\"info\"\n                      />\n                    </div>\n                    <span className=\"text-info fw-semibold\">{count}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Settings Tab Component\nconst SettingsTab: React.FC<any> = ({ userSettings, setUserSettings }) => {\n  return (\n    <Row className=\"g-4\">\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <Bell className=\"me-2\" size={20} />\n              Notification Settings\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <Form>\n              <Form.Check\n                type=\"switch\"\n                id=\"email-notifications\"\n                label=\"Email Notifications\"\n                checked={userSettings.notifications.email}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, email: e.target.checked }\n                })}\n                className=\"text-white mb-3\"\n              />\n              <Form.Check\n                type=\"switch\"\n                id=\"push-notifications\"\n                label=\"Push Notifications\"\n                checked={userSettings.notifications.push}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, push: e.target.checked }\n                })}\n                className=\"text-white mb-3\"\n              />\n              <Form.Check\n                type=\"switch\"\n                id=\"sms-notifications\"\n                label=\"SMS Notifications\"\n                checked={userSettings.notifications.sms}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, sms: e.target.checked }\n                })}\n                className=\"text-white\"\n              />\n            </Form>\n          </Card.Body>\n        </Card>\n      </Col>\n\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <Settings className=\"me-2\" size={20} />\n              Print Preferences\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Print Type</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultPrintType}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultPrintType: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"Print\">Print</option>\n                  <option value=\"Xerox\">Xerox</option>\n                  <option value=\"Binding\">Binding</option>\n                  <option value=\"Lamination\">Lamination</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Color Type</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultColorType}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultColorType: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"BlackWhite\">Black & White</option>\n                  <option value=\"Color\">Color</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Paper Size</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultPaperSize}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultPaperSize: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"A4\">A4</option>\n                  <option value=\"A3\">A3</option>\n                  <option value=\"Letter\">Letter</option>\n                  <option value=\"Legal\">Legal</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Check\n                type=\"switch\"\n                id=\"auto-confirm\"\n                label=\"Auto-confirm quotes under $10\"\n                checked={userSettings.preferences.autoConfirmQuotes}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  preferences: { ...userSettings.preferences, autoConfirmQuotes: e.target.checked }\n                })}\n                className=\"text-white\"\n              />\n            </Form>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Upload Modal Component\nconst UploadModal: React.FC<any> = ({\n  show, onHide, uploadData, setUploadData, selectedFile, setSelectedFile, xeroxCenters, onUpload\n}) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <Upload className=\"me-2\" size={20} />\n          Upload Files for Printing\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>\n        <Form>\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Select File</Form.Label>\n            <Form.Control\n              type=\"file\"\n              accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n              onChange={(e) => {\n                const files = (e.target as HTMLInputElement).files;\n                setSelectedFile(files ? files[0] : null);\n              }}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            />\n            <Form.Text className=\"text-muted\">\n              Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n            </Form.Text>\n          </Form.Group>\n\n          <Row>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Print Type</Form.Label>\n                <Form.Select\n                  value={uploadData.printType}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, printType: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"Print\">Print</option>\n                  <option value=\"Xerox\">Xerox</option>\n                  <option value=\"Binding\">Binding</option>\n                  <option value=\"Lamination\">Lamination</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Number of Copies</Form.Label>\n                <Form.Control\n                  type=\"number\"\n                  min=\"1\"\n                  value={uploadData.copies}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                />\n              </Form.Group>\n            </Col>\n          </Row>\n\n          <Row>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Color Type</Form.Label>\n                <Form.Select\n                  value={uploadData.colorType}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, colorType: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"BlackWhite\">Black & White</option>\n                  <option value=\"Color\">Color</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Paper Size</Form.Label>\n                <Form.Select\n                  value={uploadData.paperSize}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, paperSize: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"A4\">A4</option>\n                  <option value=\"A3\">A3</option>\n                  <option value=\"Letter\">Letter</option>\n                  <option value=\"Legal\">Legal</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n          </Row>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Priority</Form.Label>\n            <Form.Select\n              value={uploadData.priority}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, priority: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            >\n              <option value=\"Low\">Low</option>\n              <option value=\"Normal\">Normal</option>\n              <option value=\"High\">High</option>\n              <option value=\"Urgent\">Urgent</option>\n            </Form.Select>\n          </Form.Group>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Preferred Xerox Center</Form.Label>\n            <Form.Select\n              value={uploadData.preferredXeroxCenterId}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            >\n              <option value=\"\">Select a center (optional)</option>\n              {xeroxCenters.map((center: any) => (\n                <option key={center.id} value={center.id}>\n                  {center.name} - {center.pendingJobs} pending jobs\n                </option>\n              ))}\n            </Form.Select>\n          </Form.Group>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Remarks</Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              placeholder=\"Any special instructions or remarks...\"\n              value={uploadData.remarks}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, remarks: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            />\n          </Form.Group>\n        </Form>\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Cancel\n        </Button>\n        <Button\n          onClick={onUpload}\n          disabled={!selectedFile}\n          style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}\n        >\n          <Upload className=\"me-2\" size={16} />\n          Upload File\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\n// View Job Modal Component\nconst ViewJobModal: React.FC<any> = ({ show, onHide, job, getStatusBadge, getPriorityBadge }) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <Eye className=\"me-2\" size={20} />\n          Job Details\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>\n        {job && (\n          <Row>\n            <Col md={6}>\n              <h6 className=\"text-primary\">Job Information</h6>\n              <p><strong>Job Number:</strong> {job.jobNumber}</p>\n              <p><strong>Status:</strong> {getStatusBadge(job.status)}</p>\n              <p><strong>Priority:</strong> {getPriorityBadge(job.priority)}</p>\n              <p><strong>File Name:</strong> {job.fileName}</p>\n              <p><strong>Xerox Center:</strong> {job.xeroxCenterName}</p>\n              {job.cost && <p><strong>Cost:</strong> <span className=\"text-success\">${job.cost.toFixed(2)}</span></p>}\n              <p><strong>Created:</strong> {new Date(job.created).toLocaleString()}</p>\n              {job.estimatedCompletionTime && (\n                <p><strong>Estimated Completion:</strong> {new Date(job.estimatedCompletionTime).toLocaleString()}</p>\n              )}\n            </Col>\n            <Col md={6}>\n              <h6 className=\"text-primary\">Print Specifications</h6>\n              <p><strong>Type:</strong> {job.printType}</p>\n              <p><strong>Copies:</strong> {job.copies}</p>\n              <p><strong>Color:</strong> {job.colorType}</p>\n              <p><strong>Paper Size:</strong> {job.paperSize}</p>\n              {job.remarks && (\n                <>\n                  <h6 className=\"text-primary mt-3\">Remarks</h6>\n                  <p className=\"text-muted\">{job.remarks}</p>\n                </>\n              )}\n            </Col>\n          </Row>\n        )}\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Close\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\n// Chat Modal Component\nconst ChatModal: React.FC<any> = ({\n  show, onHide, job, messages, chatMessage, setChatMessage, onSendMessage\n}) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <MessageCircle className=\"me-2\" size={20} />\n          Chat - {job?.jobNumber}\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff', height: '400px', overflowY: 'auto' }}>\n        <div className=\"mb-3\">\n          {messages.map((message: any, index: number) => (\n            <div key={index} className={`mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`}>\n              <div className={`d-inline-block p-2 rounded ${\n                message.isFromStudent\n                  ? 'text-white'\n                  : 'bg-secondary text-white'\n              }`} style={{\n                background: message.isFromStudent\n                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                  : '#6c757d'\n              }}>\n                {message.content}\n              </div>\n              <div className=\"small text-muted\">\n                {new Date(message.timestamp).toLocaleString()}\n              </div>\n            </div>\n          ))}\n        </div>\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <InputGroup>\n          <Form.Control\n            type=\"text\"\n            placeholder=\"Type your message...\"\n            value={chatMessage}\n            onChange={(e) => setChatMessage(e.target.value)}\n            onKeyPress={(e) => e.key === 'Enter' && onSendMessage()}\n            style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n          />\n          <Button\n            onClick={onSendMessage}\n            style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}\n          >\n            <Send size={16} />\n          </Button>\n        </InputGroup>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default AceternityStudentDashboard;"], "mappings": "uIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,SAAS,CAAEC,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,MAAM,CAAEC,KAAK,CAAEC,IAAI,CAAEC,KAAK,CAAEC,UAAU,CAAEC,GAAG,CAAOC,KAAK,CAAEC,WAAW,KAAQ,iBAAiB,CACjI,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OACEC,MAAM,CACNC,QAAQ,CACRC,KAAK,CACLC,WAAW,CACXC,UAAU,CACVC,QAAQ,CACRC,aAAa,CACbC,GAAG,CAEHC,QAAQ,CACRC,OAAO,CAEPC,IAAI,CACJC,OAAO,CACPC,QAAQ,CACRC,IAAI,CACJC,UAAU,CACVC,IAAI,CACJC,MAAM,CAGNC,UAAU,CACVC,SAAS,CACTC,QAAQ,CAERC,MAAM,CACNC,KAAK,CAELC,KAAK,CAELC,SAAS,CACTC,WAAW,CAEXC,YAAY,CACZC,OAAO,CAGPC,IAAI,CACJC,KAAK,CAGLC,MAAM,GAAI,CAAAC,QAAQ,KACb,cAAc,CACrB,OAASC,OAAO,KAAQ,4BAA4B,CACpD,OAASC,WAAW,CAAEC,cAAc,CAAEC,aAAa,CAAEC,UAAU,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAkC5F,KAAM,CAAAC,0BAAoC,CAAGA,CAAA,GAAM,CACjD,KAAM,CAAEC,IAAK,CAAC,CAAGZ,OAAO,CAAC,CAAC,CAC1B,KAAM,CAACa,SAAS,CAAEC,YAAY,CAAC,CAAG7D,QAAQ,CAAC,WAAW,CAAC,CACvD,KAAM,CAAC8D,SAAS,CAAEC,YAAY,CAAC,CAAG/D,QAAQ,CAAa,EAAE,CAAC,CAC1D,KAAM,CAACgE,YAAY,CAAEC,eAAe,CAAC,CAAGjE,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACkE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnE,QAAQ,CAAW,EAAE,CAAC,CACtE,KAAM,CAACoE,eAAe,CAAEC,kBAAkB,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACsE,aAAa,CAAEC,gBAAgB,CAAC,CAAGvE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACwE,aAAa,CAAEC,gBAAgB,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC0E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3E,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC4E,WAAW,CAAEC,cAAc,CAAC,CAAG7E,QAAQ,CAAM,IAAI,CAAC,CACzD,KAAM,CAAC8E,cAAc,CAAEC,iBAAiB,CAAC,CAAG/E,QAAQ,CAAqB,IAAI,CAAC,CAC9E,KAAM,CAACgF,YAAY,CAAEC,eAAe,CAAC,CAAGjF,QAAQ,CAAc,IAAI,CAAC,CACnE,KAAM,CAACkF,WAAW,CAAEC,cAAc,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACoF,QAAQ,CAAEC,WAAW,CAAC,CAAGrF,QAAQ,CAAQ,EAAE,CAAC,CACnD,KAAM,CAACsF,UAAU,CAAEC,aAAa,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACwF,YAAY,CAAEC,eAAe,CAAC,CAAGzF,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0F,MAAM,CAAEC,SAAS,CAAC,CAAG3F,QAAQ,CAAC,SAAS,CAAC,CAC/C,KAAM,CAAC4F,YAAY,CAAEC,eAAe,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC8F,UAAU,CAAEC,aAAa,CAAC,CAAG/F,QAAQ,CAAC,CAC3CgG,OAAO,CAAE,EAAE,CACXC,sBAAsB,CAAE,EAAE,CAC1BC,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,CAAC,CACTC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,IAAI,CACfC,QAAQ,CAAE,QACZ,CAAC,CAAC,CAEF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGxG,QAAQ,CAAC,CAC/CyG,aAAa,CAAE,CACbC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,GAAG,CAAE,KACP,CAAC,CACDC,WAAW,CAAE,CACXC,gBAAgB,CAAE,OAAO,CACzBC,gBAAgB,CAAE,YAAY,CAC9BC,gBAAgB,CAAE,IAAI,CACtBC,iBAAiB,CAAE,KACrB,CAAC,CACDC,OAAO,CAAE,CACPC,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,EACb,CACF,CAAC,CAAC,CAEFrH,SAAS,CAAC,IAAM,CACdsH,SAAS,CAAC,CAAC,CACX,KAAM,CAAAC,QAAQ,CAAGC,WAAW,CAACF,SAAS,CAAE,KAAK,CAAC,CAC9C,MAAO,IAAMG,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAD,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B1B,eAAe,CAAC,IAAI,CAAC,CACrB,GAAI,CACF,KAAM,CAAC8B,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAClE9E,WAAW,CAAC+E,cAAc,CAAC,CAAC,CAC5B9E,cAAc,CAAC+E,MAAM,CAAC,CAAC,CACxB,CAAC,CAEFjE,YAAY,CAAC4D,iBAAiB,CAACM,IAAI,EAAI,EAAE,CAAC,CAC1ChE,eAAe,CAAC2D,oBAAoB,CAACK,IAAI,EAAI,EAAE,CAAC,CAClD,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CnE,YAAY,CAAC,EAAE,CAAC,CAChBE,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,OAAS,CACR4B,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAAuC,cAAc,CAAIC,MAAc,EAAK,CACzC,KAAM,CAAAC,YAAY,CAAG,CACnB,WAAW,CAAE,CAAEC,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAEtH,KAAM,CAAC,CAC7C,aAAa,CAAE,CAAEqH,EAAE,CAAE,MAAM,CAAEC,IAAI,CAAEjH,GAAI,CAAC,CACxC,QAAQ,CAAE,CAAEgH,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAEpH,UAAW,CAAC,CAC7C,qBAAqB,CAAE,CAAEmH,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAEhG,WAAY,CAAC,CAC3D,WAAW,CAAE,CAAE+F,EAAE,CAAE,MAAM,CAAEC,IAAI,CAAErH,WAAY,CAAC,CAC9C,YAAY,CAAE,CAAEoH,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAEhH,QAAS,CAAC,CAC/C,WAAW,CAAE,CAAE+G,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE/F,YAAa,CAAC,CAClD,WAAW,CAAE,CAAE8F,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE/F,YAAa,CAAC,CAClD,UAAU,CAAE,CAAE8F,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE9F,OAAQ,CAAC,CAC3C,WAAW,CAAE,CAAE6F,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE9F,OAAQ,CAChD,CAAC,CAED,KAAM,CAAA+F,MAAM,CAAGH,YAAY,CAACD,MAAM,CAA8B,EAAI,CAAEE,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAEtH,KAAM,CAAC,CACpG,KAAM,CAAAwH,aAAa,CAAGD,MAAM,CAACD,IAAI,CAEjC,mBACEjF,KAAA,CAAChD,KAAK,EAACgI,EAAE,CAAEE,MAAM,CAACF,EAAG,CAACI,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACzEvF,IAAA,CAACqF,aAAa,EAACG,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1BR,MAAM,EACF,CAAC,CAEZ,CAAC,CAED,KAAM,CAAAS,gBAAgB,CAAIxC,QAAgB,EAAK,CAC7C,KAAM,CAAAyC,cAAc,CAAG,CACrB,KAAK,CAAE,CAAER,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CACpC,QAAQ,CAAE,CAAED,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE,IAAK,CAAC,CACzC,MAAM,CAAE,CAAED,EAAE,CAAE,SAAS,CAAEC,IAAI,CAAE,IAAK,CAAC,CACrC,QAAQ,CAAE,CAAED,EAAE,CAAE,QAAQ,CAAEC,IAAI,CAAE,IAAK,CACvC,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGM,cAAc,CAACzC,QAAQ,CAAgC,EAAI,CAAEiC,EAAE,CAAE,WAAW,CAAEC,IAAI,CAAE,IAAK,CAAC,CAEzG,mBACEjF,KAAA,CAAChD,KAAK,EAACgI,EAAE,CAAEE,MAAM,CAACF,EAAG,CAACI,SAAS,CAAC,WAAW,CAAAC,QAAA,EACxCH,MAAM,CAACD,IAAI,CAAC,GAAC,CAAClC,QAAQ,EAClB,CAAC,CAEZ,CAAC,CAED,KAAM,CAAA0C,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CAAChE,YAAY,CAAE,OAEnB,GAAI,CACF,KAAM,CAAAiE,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEnE,YAAY,CAAC,CACrCoE,MAAM,CAACC,OAAO,CAACvD,UAAU,CAAC,CAACwD,OAAO,CAACC,IAAA,EAAkB,IAAjB,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,IAAA,CAC9CN,QAAQ,CAACE,MAAM,CAACK,GAAG,CAAEC,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC,CACxC,CAAC,CAAC,CAEF,KAAM,CAAAxG,aAAa,CAACyG,UAAU,CAACV,QAAQ,CAAC,CACxC,KAAM,CAAA1B,SAAS,CAAC,CAAC,CACjBlD,kBAAkB,CAAC,KAAK,CAAC,CACzBY,eAAe,CAAC,IAAI,CAAC,CACrBc,aAAa,CAAC,CACZC,OAAO,CAAE,EAAE,CACXC,sBAAsB,CAAE,EAAE,CAC1BC,SAAS,CAAE,OAAO,CAClBC,MAAM,CAAE,CAAC,CACTC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,IAAI,CACfC,QAAQ,CAAE,QACZ,CAAC,CAAC,CACJ,CAAE,MAAO4B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAA0B,aAAa,CAAIC,GAAQ,EAAK,CAClChF,cAAc,CAACgF,GAAG,CAAC,CACnBtF,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAuF,cAAc,CAAG,KAAO,CAAAD,GAAQ,EAAK,CACzC,GAAI,CACFhF,cAAc,CAACgF,GAAG,CAAC,CACnBpF,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAAsF,QAAQ,CAAG,KAAM,CAAA5G,UAAU,CAAC6G,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC,CACxD5E,WAAW,CAAC0E,QAAQ,CAAC9B,IAAI,EAAI,EAAE,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/C7C,WAAW,CAAC,EAAE,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAA6E,kBAAkB,CAAG,KAAAA,CAAOC,KAAa,CAAEC,QAAgB,GAAK,CACpE,GAAI,CACF,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAA7G,aAAa,CAACmH,YAAY,CAACF,KAAK,CAAC,CACxD,KAAM,CAAAG,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACR,QAAQ,CAAC9B,IAAI,CAAC,CAAC,CACtC,KAAM,CAAAuC,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAC5C,KAAM,CAAAM,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGP,GAAG,CACfI,IAAI,CAACI,QAAQ,CAAGZ,QAAQ,CACxBS,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC,CAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC,CACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC,CAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC,CACjC,CAAE,MAAOtC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAAoD,gBAAgB,CAAG,KAAO,CAAAnB,KAAa,EAAK,CAChD,GAAI,CACF,KAAM,CAAAnH,WAAW,CAACuI,UAAU,CAACpB,KAAK,CAAC,CACnC,KAAM,CAAA5C,SAAS,CAAC,CAAC,CACnB,CAAE,MAAOW,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,CAAEA,KAAK,CAAC,CAC/C,CACF,CAAC,CAED,KAAM,CAAAsD,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CAACtG,WAAW,CAACuG,IAAI,CAAC,CAAC,EAAI,CAAC7G,WAAW,CAAE,OAEzC,GAAI,CACF,KAAM,CAAAmF,QAAQ,CAAG,KAAM,CAAA5G,UAAU,CAACuI,WAAW,CAAC9G,WAAW,CAACqF,EAAE,CAAE/E,WAAW,CAACuG,IAAI,CAAC,CAAC,CAAC,CACjFpG,WAAW,CAACsG,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAE5B,QAAQ,CAAC9B,IAAI,CAAC,CAAC,CAC7C9C,cAAc,CAAC,EAAE,CAAC,CACpB,CAAE,MAAO+C,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAA0D,cAAc,CAAIC,QAAgB,EAAK,CAC3C1H,mBAAmB,CAACwH,IAAI,EACtBA,IAAI,CAACG,QAAQ,CAACD,QAAQ,CAAC,CACnBF,IAAI,CAACI,MAAM,CAAC9B,EAAE,EAAIA,EAAE,GAAK4B,QAAQ,CAAC,CAClC,CAAC,GAAGF,IAAI,CAAEE,QAAQ,CACxB,CAAC,CACH,CAAC,CAED,KAAM,CAAAG,YAAY,CAAGlI,SAAS,CAACiI,MAAM,CAAClC,GAAG,EAAI,CAC3C,KAAM,CAAAoC,aAAa,CAAG3G,UAAU,GAAK,EAAE,EACrCuE,GAAG,CAACqC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACL,QAAQ,CAACxG,UAAU,CAAC6G,WAAW,CAAC,CAAC,CAAC,EAC9DtC,GAAG,CAACO,QAAQ,CAAC+B,WAAW,CAAC,CAAC,CAACL,QAAQ,CAACxG,UAAU,CAAC6G,WAAW,CAAC,CAAC,CAAC,EAC7DtC,GAAG,CAACuC,eAAe,CAACD,WAAW,CAAC,CAAC,CAACL,QAAQ,CAACxG,UAAU,CAAC6G,WAAW,CAAC,CAAC,CAAC,CAEtE,KAAM,CAAAE,aAAa,CAAG7G,YAAY,GAAK,KAAK,EAAIqE,GAAG,CAACxB,MAAM,GAAK7C,YAAY,CAE3E,MAAO,CAAAyG,aAAa,EAAII,aAAa,CACvC,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAU,CAAGxI,SAAS,CAACyI,MAAM,CAAC,CAACC,GAAG,CAAE3C,GAAG,GAAK2C,GAAG,EAAI3C,GAAG,CAAC4C,IAAI,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAC3E,KAAM,CAAAC,cAAc,CAAG5I,SAAS,CAACiI,MAAM,CAAClC,GAAG,EAAI,CAAC,YAAY,CAAE,WAAW,CAAE,aAAa,CAAC,CAACiC,QAAQ,CAACjC,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACsE,MAAM,CACtH,KAAM,CAAAC,aAAa,CAAG9I,SAAS,CAACiI,MAAM,CAAClC,GAAG,EAAI,CAAC,WAAW,CAAE,WAAW,CAAC,CAACiC,QAAQ,CAACjC,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACsE,MAAM,CACrG,KAAM,CAAAE,WAAW,CAAG/I,SAAS,CAACiI,MAAM,CAAClC,GAAG,EAAI,CAAC,WAAW,CAAE,aAAa,CAAE,QAAQ,CAAC,CAACiC,QAAQ,CAACjC,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACsE,MAAM,CAE/G,KAAM,CAAAG,iBAAiB,CAAG,CACxBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVC,QAAQ,CAAE,GAAG,CACbC,eAAe,CAAE,GACnB,CACF,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBN,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,EAAG,CAAC,CAC7BL,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVM,CAAC,CAAE,CAAC,CACJJ,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAC9B,CACF,CAAC,CAED,KAAM,CAAAI,cAAc,CAAGC,KAAK,CAACC,IAAI,CAAC,CAAEd,MAAM,CAAE,CAAE,CAAC,CAAE,CAACe,CAAC,CAAEC,CAAC,gBACpDtK,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAETjF,SAAS,CAAC,kCAAkC,CAC5CkF,KAAK,CAAE,CACLC,UAAU,4BAAAC,MAAA,CAA6BJ,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAGA,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAG,SAAS,OAAAI,MAAA,CAAKJ,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAGA,CAAC,CAAG,CAAC,GAAK,CAAC,CAAG,SAAS,CAAG,SAAS,KAAG,CACzKK,KAAK,IAAAD,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,MAAI,CACrCC,MAAM,IAAAJ,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,MAAI,CACtCE,IAAI,IAAAL,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC/BG,GAAG,IAAAN,MAAA,CAAKE,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,KAAG,CAC9BlB,OAAO,CAAE,GAAG,CACZsB,MAAM,CAAE,CACV,CAAE,CACFC,OAAO,CAAE,CACPC,CAAC,CAAE,CAAC,CAAC,CAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,CAChCZ,CAAC,CAAE,CAAC,CAAC,CAAEW,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,EAAE,CAAC,CAChCO,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAC,CAChBC,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CACnB,CAAE,CACFxB,UAAU,CAAE,CACVC,QAAQ,CAAEc,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CACjCS,MAAM,CAAEC,QAAQ,CAChBC,UAAU,CAAE,SAAS,CACrBC,IAAI,CAAE,WACR,CAAE,EAtBGnB,CAuBN,CACF,CAAC,CAEF,mBACEpK,KAAA,QAAKoF,SAAS,CAAC,gCAAgC,CAACkF,KAAK,CAAE,CACrDC,UAAU,CAAE,mDAAmD,CAC/DiB,SAAS,CAAE,OACb,CAAE,CAAAnG,QAAA,eAEArF,KAAA,QAAKoF,SAAS,CAAC,+CAA+C,CAAAC,QAAA,EAC3D2E,cAAc,cACflK,IAAA,QAAKsF,SAAS,CAAC,+BAA+B,CAACkF,KAAK,CAAE,CACpDC,UAAU,CAAE,oKACd,CAAE,CAAE,CAAC,EACF,CAAC,cAENzK,IAAA,CAACnD,SAAS,EAAC8O,KAAK,MAACrG,SAAS,CAAC,wBAAwB,CAACkF,KAAK,CAAE,CAAES,MAAM,CAAE,CAAE,CAAE,CAAA1F,QAAA,cACvErF,KAAA,CAACzC,MAAM,CAAC8M,GAAG,EACTqB,QAAQ,CAAEnC,iBAAkB,CAC5BoC,OAAO,CAAC,QAAQ,CAChBX,OAAO,CAAC,SAAS,CAAA3F,QAAA,eAGjBrF,KAAA,CAACzC,MAAM,CAAC8M,GAAG,EAACqB,QAAQ,CAAE5B,YAAa,CAAC1E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC9DvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTW,OAAO,CAAE,CACPE,MAAM,CAAE,CAAC,CAAC,CAAE,GAAG,CAAC,CAChBC,KAAK,CAAE,CAAC,CAAC,CAAE,GAAG,CAAE,CAAC,CACnB,CAAE,CACFxB,UAAU,CAAE,CACVC,QAAQ,CAAE,CAAC,CACXwB,MAAM,CAAEC,QAAQ,CAChBE,IAAI,CAAE,WACR,CAAE,CACFnG,SAAS,CAAC,8DAA8D,CACxEkF,KAAK,CAAE,CACLG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,sCACb,CAAE,CAAAxG,QAAA,cAEFvF,IAAA,CAACxB,IAAI,EAAC8G,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC/B,CAAC,cACbtF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAACkF,KAAK,CAAE,CAAEwB,QAAQ,CAAE,QAAS,CAAE,CAAAzG,QAAA,EAAC,gBACvD,CAACjF,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2L,QAAQ,CAAC,GAChC,EAAI,CAAC,cACLjM,IAAA,MAAGsF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,qDAEvC,CAAG,CAAC,cAEJrF,KAAA,QAAKoF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,eAC/EvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAAC2B,UAAU,CAAE,CAAEb,KAAK,CAAE,IAAK,CAAE,CAACc,QAAQ,CAAE,CAAEd,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cACjErF,KAAA,CAACjD,MAAM,EACLuI,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMpL,kBAAkB,CAAC,IAAI,CAAE,CACxCsE,SAAS,CAAC,0CAA0C,CACpDkF,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BC,KAAK,CAAE,MAAM,CACbP,SAAS,CAAE,sCACb,CAAE,CAAAxG,QAAA,eAEFvF,IAAA,CAACrC,MAAM,EAAC2H,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,eAEvC,EAAQ,CAAC,CACC,CAAC,cAEbxF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAAC2B,UAAU,CAAE,CAAEb,KAAK,CAAE,IAAK,CAAE,CAACc,QAAQ,CAAE,CAAEd,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cACjErF,KAAA,CAACjD,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAElI,SAAU,CACnBsI,QAAQ,CAAEjK,YAAa,CACvB+C,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3CvF,IAAA,CAACd,SAAS,EAACoG,SAAS,SAAAoF,MAAA,CAAUnI,YAAY,CAAG,MAAM,CAAG,EAAE,CAAG,CAACiD,IAAI,CAAE,EAAG,CAAE,CAAC,UAE1E,EAAQ,CAAC,CACC,CAAC,EACV,CAAC,EACI,CAAC,cAGbxF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAACqB,QAAQ,CAAE5B,YAAa,CAAC1E,SAAS,CAAC,MAAM,CAAAC,QAAA,cAClDvF,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,KAAK,CAAAC,QAAA,cACxBvF,IAAA,CAAC1C,GAAG,EAACiP,OAAO,CAAC,OAAO,CAACjH,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC9D,CACC,CAAEY,GAAG,CAAE,WAAW,CAAEuG,KAAK,CAAE,WAAW,CAAEvH,IAAI,CAAEtG,SAAU,CAAC,CACzD,CAAEsH,GAAG,CAAE,MAAM,CAAEuG,KAAK,CAAE,SAAS,CAAEvH,IAAI,CAAEvH,QAAS,CAAC,CACjD,CAAEuI,GAAG,CAAE,SAAS,CAAEuG,KAAK,CAAE,eAAe,CAAEvH,IAAI,CAAE/G,OAAQ,CAAC,CACzD,CAAE+H,GAAG,CAAE,SAAS,CAAEuG,KAAK,CAAE,SAAS,CAAEvH,IAAI,CAAE7G,OAAQ,CAAC,CACnD,CAAE6H,GAAG,CAAE,WAAW,CAAEuG,KAAK,CAAE,WAAW,CAAEvH,IAAI,CAAElG,KAAM,CAAC,CACrD,CAAEkH,GAAG,CAAE,WAAW,CAAEuG,KAAK,CAAE,WAAW,CAAEvH,IAAI,CAAErG,QAAS,CAAC,CACxD,CAAEqH,GAAG,CAAE,UAAU,CAAEuG,KAAK,CAAE,UAAU,CAAEvH,IAAI,CAAE5G,QAAS,CAAC,CACvD,CAACoO,GAAG,CAACC,GAAG,EAAI,CACX,KAAM,CAAAvH,aAAa,CAAGuH,GAAG,CAACzH,IAAI,CAC9B,mBACEnF,IAAA,CAAC1C,GAAG,CAACuP,IAAI,EAAevH,SAAS,CAAC,KAAK,CAAAC,QAAA,cACrCvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAAC2B,UAAU,CAAE,CAAEb,KAAK,CAAE,IAAK,CAAE,CAACc,QAAQ,CAAE,CAAEd,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cACjErF,KAAA,CAAC5C,GAAG,CAACwP,IAAI,EACPC,MAAM,CAAExM,SAAS,GAAKqM,GAAG,CAACzG,GAAI,CAC9BiG,OAAO,CAAEA,CAAA,GAAM5L,YAAY,CAACoM,GAAG,CAACzG,GAAG,CAAE,CACrCb,SAAS,8DAAAoF,MAAA,CACPnK,SAAS,GAAKqM,GAAG,CAACzG,GAAG,CACjB,cAAc,CACd,eAAe,CAClB,CACHqE,KAAK,CAAE,CACLC,UAAU,CAAElK,SAAS,GAAKqM,GAAG,CAACzG,GAAG,CAC7B,0BAA0B,CAC1B,aAAa,CACjB6G,MAAM,CAAE,MAAM,CACdnD,UAAU,CAAE,eAAe,CAC3BkC,SAAS,CAAExL,SAAS,GAAKqM,GAAG,CAACzG,GAAG,CAC5B,sCAAsC,CACtC,MACN,CAAE,CAAAZ,QAAA,eAEFvF,IAAA,CAACqF,aAAa,EAACG,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cAC5CtF,IAAA,SAAMsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEqH,GAAG,CAACF,KAAK,CAAO,CAAC,EAC/C,CAAC,CACD,CAAC,EAxBAE,GAAG,CAACzG,GAyBT,CAAC,CAEf,CAAC,CAAC,CACC,CAAC,CACG,CAAC,CACR,CAAC,CACG,CAAC,cAGbnG,IAAA,CAACtC,eAAe,EAACuP,IAAI,CAAC,MAAM,CAAA1H,QAAA,cAC1BrF,KAAA,CAACzC,MAAM,CAAC8M,GAAG,EAETsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,EAAG,CAAE,CAC/BiB,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAE,CAAE,CAC9BiD,IAAI,CAAE,CAAEvD,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAC,EAAG,CAAE,CAC7BJ,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAAvE,QAAA,EAE7BhF,SAAS,GAAK,WAAW,eACxBP,IAAA,CAACmN,YAAY,EACX1M,SAAS,CAAEA,SAAU,CACrBE,YAAY,CAAEA,YAAa,CAC3BsI,UAAU,CAAEA,UAAW,CACvBI,cAAc,CAAEA,cAAe,CAC/BE,aAAa,CAAEA,aAAc,CAC7BC,WAAW,CAAEA,WAAY,CACzB4D,SAAS,CAAE7G,aAAc,CACzB8G,cAAc,CAAExG,kBAAmB,CACnCyG,UAAU,CAAE7G,cAAe,CAC3B8G,YAAY,CAAEtF,gBAAiB,CAC/BlD,cAAc,CAAEA,cAAe,CAC/BU,gBAAgB,CAAEA,gBAAiB,CACpC,CACF,CAEAlF,SAAS,GAAK,MAAM,eACnBP,IAAA,CAACwN,OAAO,EACNC,IAAI,CAAE9E,YAAa,CACnB1G,UAAU,CAAEA,UAAW,CACvBC,aAAa,CAAEA,aAAc,CAC7BC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,MAAM,CAAEA,MAAO,CACfC,SAAS,CAAEA,SAAU,CACrB8K,SAAS,CAAE7G,aAAc,CACzB8G,cAAc,CAAExG,kBAAmB,CACnCyG,UAAU,CAAE7G,cAAe,CAC3B8G,YAAY,CAAEtF,gBAAiB,CAC/BlD,cAAc,CAAEA,cAAe,CAC/BU,gBAAgB,CAAEA,gBAAiB,CACpC,CACF,CAEAlF,SAAS,GAAK,SAAS,eACtBP,IAAA,CAAC0N,UAAU,EACTC,OAAO,CAAEhN,YAAa,CACtBE,gBAAgB,CAAEA,gBAAiB,CACnC+M,gBAAgB,CAAErF,cAAe,CACjCsF,cAAc,CAAEnM,iBAAkB,CACnC,CACF,CAEAnB,SAAS,GAAK,SAAS,eACtBP,IAAA,CAAC8N,UAAU,EACTL,IAAI,CAAEhN,SAAS,CAACiI,MAAM,CAAClC,GAAG,EAAI,CAAC,WAAW,CAAE,WAAW,CAAE,WAAW,CAAE,UAAU,CAAC,CAACiC,QAAQ,CAACjC,GAAG,CAACxB,MAAM,CAAC,CAAE,CACxGoI,SAAS,CAAE7G,aAAc,CACzBxB,cAAc,CAAEA,cAAe,CAChC,CACF,CAEAxE,SAAS,GAAK,WAAW,eACxBP,IAAA,CAAC+N,YAAY,EACXJ,OAAO,CAAEhN,YAAY,CAAC+H,MAAM,CAACsF,MAAM,EAAInN,gBAAgB,CAAC4H,QAAQ,CAACuF,MAAM,CAACpH,EAAE,CAAC,CAAE,CAC7EgH,gBAAgB,CAAErF,cAAe,CACjCsF,cAAc,CAAEnM,iBAAkB,CACnC,CACF,CAEAnB,SAAS,GAAK,WAAW,eACxBP,IAAA,CAACiO,YAAY,EACXR,IAAI,CAAEhN,SAAU,CAChBwI,UAAU,CAAEA,UAAW,CACxB,CACF,CAEA1I,SAAS,GAAK,UAAU,eACvBP,IAAA,CAACkO,WAAW,EACVhL,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CAClC,CACF,GA9EI5C,SA+EK,CAAC,CACE,CAAC,EACR,CAAC,CACJ,CAAC,cAGZP,IAAA,CAACmO,WAAW,EACVC,IAAI,CAAErN,eAAgB,CACtBsN,MAAM,CAAEA,CAAA,GAAMrN,kBAAkB,CAAC,KAAK,CAAE,CACxCyB,UAAU,CAAEA,UAAW,CACvBC,aAAa,CAAEA,aAAc,CAC7Bf,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCjB,YAAY,CAAEA,YAAa,CAC3B2N,QAAQ,CAAE3I,gBAAiB,CAC5B,CAAC,cAEF3F,IAAA,CAACuO,YAAY,EACXH,IAAI,CAAEnN,aAAc,CACpBoN,MAAM,CAAEA,CAAA,GAAMnN,gBAAgB,CAAC,KAAK,CAAE,CACtCsF,GAAG,CAAEjF,WAAY,CACjBwD,cAAc,CAAEA,cAAe,CAC/BU,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,cAEFzF,IAAA,CAACwO,SAAS,EACRJ,IAAI,CAAEjN,aAAc,CACpBkN,MAAM,CAAEA,CAAA,GAAMjN,gBAAgB,CAAC,KAAK,CAAE,CACtCoF,GAAG,CAAEjF,WAAY,CACjBQ,QAAQ,CAAEA,QAAS,CACnBF,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/B2M,aAAa,CAAEtG,iBAAkB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAgF,YAA2B,CAAGuB,KAAA,EAG9B,IAH+B,CACnCjO,SAAS,CAAEE,YAAY,CAAEsI,UAAU,CAAEI,cAAc,CAAEE,aAAa,CAAEC,WAAW,CAC/E4D,SAAS,CAAEC,cAAc,CAAEC,UAAU,CAAEC,YAAY,CAAExI,cAAc,CAAEU,gBACvE,CAAC,CAAAiJ,KAAA,CACC,mBACExO,KAAA,CAACpD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElBvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClCxB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAC9BoC,UAAU,CAAE,CAAEjC,CAAC,CAAE,CAAC,CAAC,CAAEoB,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cAEnCvF,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACArF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eACpCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACpC,QAAQ,EAAC0H,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1C,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAClDvF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE9E,SAAS,CAAC6I,MAAM,CAAK,CAAC,cAC1DpJ,KAAA,UAAOoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BvF,IAAA,CAACpB,UAAU,EAAC4G,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,qBAE3C,EAAO,CAAC,EACC,CAAC,CACR,CAAC,CACG,CAAC,CACV,CAAC,cAENtF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClCxB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAE8E,KAAK,CAAE,GAAI,CAAE,CAC1C1C,UAAU,CAAE,CAAEjC,CAAC,CAAE,CAAC,CAAC,CAAEoB,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cAEnCvF,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACArF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eACpCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACnC,KAAK,EAACyH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACvC,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cACnDvF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAE8D,cAAc,CAAK,CAAC,cACxDnJ,KAAA,UAAOoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BvF,IAAA,CAAC7B,QAAQ,EAACqH,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,cAEzC,EAAO,CAAC,EACC,CAAC,CACR,CAAC,CACG,CAAC,CACV,CAAC,cAENtF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClCxB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAE8E,KAAK,CAAE,GAAI,CAAE,CAC1C1C,UAAU,CAAE,CAAEjC,CAAC,CAAE,CAAC,CAAC,CAAEoB,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cAEnCvF,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACArF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eACpCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAAClC,WAAW,EAACwH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC7C,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACjDvF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAEgE,aAAa,CAAK,CAAC,cACvDrJ,KAAA,UAAOoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC7BvF,IAAA,CAAChB,KAAK,EAACwG,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,gBAEtC,EAAO,CAAC,EACC,CAAC,CACR,CAAC,CACG,CAAC,CACV,CAAC,cAENtF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClCxB,UAAU,CAAE,CAAEC,QAAQ,CAAE,GAAG,CAAE8E,KAAK,CAAE,GAAI,CAAE,CAC1C1C,UAAU,CAAE,CAAEjC,CAAC,CAAE,CAAC,CAAC,CAAEoB,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cAEnCvF,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACArF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eACpCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACjC,UAAU,EAACuH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5C,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cACnDrF,KAAA,OAAIoF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,EAAC,GAAC,CAAC0D,UAAU,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAK,CAAC,cAChE3O,KAAA,UAAOoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eAC1BvF,IAAA,CAACvB,UAAU,EAAC+G,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,aAE3C,EAAO,CAAC,EACC,CAAC,CACR,CAAC,CACG,CAAC,CACV,CAAC,cAGNtF,IAAA,CAACjD,GAAG,EAAC+R,EAAE,CAAE,CAAE,CAAAvJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,QAAKoF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChErF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAAC7B,QAAQ,EAACmH,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAEzC,EAAI,CAAC,cACLtF,KAAA,CAAChD,KAAK,EAACgI,EAAE,CAAC,OAAO,CAAC8J,IAAI,CAAC,MAAM,CAAC1J,SAAS,CAAC,WAAW,CAAAC,QAAA,EAChD9E,SAAS,CAAC6I,MAAM,CAAC,QACpB,EAAO,CAAC,EACL,CAAC,CACK,CAAC,cACdtJ,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,CACP9E,SAAS,CAAC6I,MAAM,CAAG,CAAC,cACnBtJ,IAAA,QAAKsF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB9E,SAAS,CAACwO,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACtC,GAAG,CAAC,CAACnG,GAAa,CAAE0I,KAAa,gBACtDlP,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EAETsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAEwB,CAAC,CAAE,CAAC,EAAG,CAAE,CAChCD,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEwB,CAAC,CAAE,CAAE,CAAE,CAC9BtB,UAAU,CAAE,CAAE+E,KAAK,CAAEM,KAAK,CAAG,GAAI,CAAE,CACnC5J,SAAS,CAAC,eAAe,CACzBkF,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,oCACV,CAAE,CAAAzH,QAAA,cAEFrF,KAAA,QAAKoF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChErF,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACpC,QAAQ,EAAC0H,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1C,CAAC,CACH,CAAC,cACNtF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAEiB,GAAG,CAACqC,SAAS,CAAK,CAAC,cAChE7I,IAAA,MAAGsF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACpCiB,GAAG,CAACO,QAAQ,CAACuC,MAAM,CAAG,EAAE,CAAG9C,GAAG,CAACO,QAAQ,CAACkI,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGzI,GAAG,CAACO,QAAQ,CAC3E,CAAC,cACJ/G,IAAA,UAAOsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAEiB,GAAG,CAACuC,eAAe,CAAQ,CAAC,EAC3D,CAAC,EACH,CAAC,cAEN7I,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCrF,KAAA,QAAKoF,SAAS,CAAC,eAAe,CAAAC,QAAA,EAC3BR,cAAc,CAACyB,GAAG,CAACxB,MAAM,CAAC,CAC1BwB,GAAG,CAAC4C,IAAI,eACPlJ,KAAA,QAAKoF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,EAAC,GAC5C,CAACiB,GAAG,CAAC4C,IAAI,CAACyF,OAAO,CAAC,CAAC,CAAC,EAClB,CACN,EACE,CAAC,cAEN3O,KAAA,QAAKoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMiB,cAAc,CAAC7G,GAAG,CAACI,EAAE,CAAEJ,GAAG,CAACO,QAAQ,CAAE,CACpDyD,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAChC,QAAQ,EAACwH,IAAI,CAAE,EAAG,CAAE,CAAC,CAChB,CAAC,cAETxF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMgB,SAAS,CAAC5G,GAAG,CAAE,CAC9BgE,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAC9B,GAAG,EAACsH,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,cAETxF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMkB,UAAU,CAAC9G,GAAG,CAAE,CAC/BgE,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAC/B,aAAa,EAACuH,IAAI,CAAE,EAAG,CAAE,CAAC,CACrB,CAAC,CAERgB,GAAG,CAACxB,MAAM,GAAK,QAAQ,eACtBhF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,SAAS,CACjB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMmB,YAAY,CAAC/G,GAAG,CAACI,EAAE,CAAE,CACpC4D,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAClC,WAAW,EAAC0H,IAAI,CAAE,EAAG,CAAE,CAAC,CACnB,CACT,EACE,CAAC,EACH,CAAC,EACH,CAAC,EAjFDgB,GAAG,CAACI,EAkFC,CACb,CAAC,CACC,CAAC,cAEN1G,KAAA,CAACzC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClC/F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5BvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACrC,MAAM,EAAC2H,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACxC,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cAC5DvF,IAAA,MAAGsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,wCAAsC,CAAG,CAAC,EAClE,CACb,CACQ,CAAC,EACR,CAAC,CACJ,CAAC,cAGNvF,IAAA,CAACjD,GAAG,EAAC+R,EAAE,CAAE,CAAE,CAAAvJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAACjB,MAAM,EAACuG,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAEvC,EAAI,CAAC,CACM,CAAC,cACdxF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,cACRrF,KAAA,QAAKoF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBrF,KAAA,QAAKoF,SAAS,CAAC,iEAAiE,CAACkF,KAAK,CAAE,CACtFC,UAAU,CAAE,0BACd,CAAE,CAAAlF,QAAA,eACArF,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAACkF,KAAK,CAAE,CAC3BG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAAM,CACpBqD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAA9J,QAAA,cACAvF,IAAA,CAACnC,KAAK,EAACyH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACvC,CAAC,cACNtF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC5CvF,IAAA,UAAOsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,EACrD,CAAC,EACH,CAAC,cACNvF,IAAA,OAAIsF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAEiE,WAAW,CAAK,CAAC,EACjD,CAAC,cAENtJ,KAAA,QAAKoF,SAAS,CAAC,iEAAiE,CAACkF,KAAK,CAAE,CACtFC,UAAU,CAAE,0BACd,CAAE,CAAAlF,QAAA,eACArF,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAACkF,KAAK,CAAE,CAC3BG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAAM,CACpBqD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAA9J,QAAA,cACAvF,IAAA,CAAC5B,OAAO,EAACkH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACzC,CAAC,cACNtF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC5CvF,IAAA,UAAOsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,eAAa,CAAO,CAAC,EACnD,CAAC,EACH,CAAC,cACNvF,IAAA,OAAIsF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAE5E,YAAY,CAAC+H,MAAM,CAAE4G,CAAc,EAAKA,CAAC,CAACC,QAAQ,CAAC,CAACjG,MAAM,CAAK,CAAC,EAC7F,CAAC,cAENpJ,KAAA,QAAKoF,SAAS,CAAC,iEAAiE,CAACkF,KAAK,CAAE,CACtFC,UAAU,CAAE,0BACd,CAAE,CAAAlF,QAAA,eACArF,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAACkF,KAAK,CAAE,CAC3BG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAAM,CACpBqD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAA9J,QAAA,cACAvF,IAAA,CAAC3B,IAAI,EAACiH,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CACtC,CAAC,cACNtF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAC/CvF,IAAA,UAAOsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iBAAe,CAAO,CAAC,EACrD,CAAC,EACH,CAAC,cACNvF,IAAA,OAAIsF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,EACvC,CAAC,EACH,CAAC,CACG,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAiI,OAAsB,CAAGgC,KAAA,EAGzB,IAH0B,CAC9B/B,IAAI,CAAExL,UAAU,CAAEC,aAAa,CAAEC,YAAY,CAAEC,eAAe,CAAEC,MAAM,CAAEC,SAAS,CACjF8K,SAAS,CAAEC,cAAc,CAAEC,UAAU,CAAEC,YAAY,CAAExI,cAAc,CAAEU,gBACvE,CAAC,CAAA+J,KAAA,CACC,mBACEtP,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACArF,KAAA,CAAClD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC9CrF,KAAA,QAAKoF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAACpC,QAAQ,EAAC0H,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,WAEzC,EAAI,CAAC,cACLtF,KAAA,CAAChD,KAAK,EAACgI,EAAE,CAAC,OAAO,CAAC8J,IAAI,CAAC,MAAM,CAAC1J,SAAS,CAAC,WAAW,CAAAC,QAAA,EAChDkI,IAAI,CAACnE,MAAM,CAAC,OACf,EAAO,CAAC,EACL,CAAC,cAGNpJ,KAAA,CAACpD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC7C,UAAU,EAAAkI,QAAA,eACTvF,IAAA,CAAC3C,UAAU,CAACoS,IAAI,EAACjF,KAAK,CAAE,CAAEC,UAAU,CAAE,0BAA0B,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cACjFvF,IAAA,CAACrB,MAAM,EAAC2G,SAAS,CAAC,eAAe,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC/B,CAAC,cAClBxF,IAAA,CAAC7C,IAAI,CAACuS,OAAO,EACXC,WAAW,CAAC,gBAAgB,CAC5BvJ,KAAK,CAAEnE,UAAW,CAClB2N,QAAQ,CAAGC,CAAC,EAAK3N,aAAa,CAAC2N,CAAC,CAACC,MAAM,CAAC1J,KAAK,CAAE,CAC/CoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CACH,CAAC,EACQ,CAAC,CACV,CAAC,cAENtM,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAEjE,YAAa,CACpByN,QAAQ,CAAGC,CAAC,EAAKzN,eAAe,CAACyN,CAAC,CAACC,MAAM,CAAC1J,KAAK,CAAE,CACjDoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CAAA/G,QAAA,eAEFvF,IAAA,WAAQoG,KAAK,CAAC,KAAK,CAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,cACvCvF,IAAA,WAAQoG,KAAK,CAAC,WAAW,CAAAb,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CvF,IAAA,WAAQoG,KAAK,CAAC,aAAa,CAAAb,QAAA,CAAC,cAAY,CAAQ,CAAC,cACjDvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCvF,IAAA,WAAQoG,KAAK,CAAC,WAAW,CAAAb,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CvF,IAAA,WAAQoG,KAAK,CAAC,YAAY,CAAAb,QAAA,CAAC,aAAW,CAAQ,CAAC,cAC/CvF,IAAA,WAAQoG,KAAK,CAAC,WAAW,CAAAb,QAAA,CAAC,WAAS,CAAQ,CAAC,cAC5CvF,IAAA,WAAQoG,KAAK,CAAC,WAAW,CAAAb,QAAA,CAAC,WAAS,CAAQ,CAAC,EACjC,CAAC,CACX,CAAC,cAENvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE/D,MAAO,CACduN,QAAQ,CAAGC,CAAC,EAAKvN,SAAS,CAACuN,CAAC,CAACC,MAAM,CAAC1J,KAAK,CAAE,CAC3CoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CAAA/G,QAAA,eAEFvF,IAAA,WAAQoG,KAAK,CAAC,SAAS,CAAAb,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC7CvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,gBAAc,CAAQ,CAAC,cAC9CvF,IAAA,WAAQoG,KAAK,CAAC,MAAM,CAAAb,QAAA,CAAC,cAAY,CAAQ,CAAC,cAC1CvF,IAAA,WAAQoG,KAAK,CAAC,UAAU,CAAAb,QAAA,CAAC,kBAAgB,CAAQ,CAAC,EACvC,CAAC,CACX,CAAC,EACH,CAAC,EACK,CAAC,cAEdvF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,CACPkI,IAAI,CAACnE,MAAM,CAAG,CAAC,cACdtJ,IAAA,CAAClD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,CACjBkI,IAAI,CAACd,GAAG,CAAC,CAACnG,GAAQ,CAAE0I,KAAa,gBAChClP,IAAA,CAACjD,GAAG,EAAc4R,EAAE,CAAE,CAAE,CAACG,EAAE,CAAE,CAAE,CAAAvJ,QAAA,cAC7BvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,EAAG,CAAE,CAC/BiB,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAE,CAAE,CAC9BJ,UAAU,CAAE,CAAE+E,KAAK,CAAEM,KAAK,CAAG,IAAK,CAAE,CAAA3J,QAAA,cAEpCrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,gBAAgB,CAACkF,KAAK,CAAE,CACtCC,UAAU,CAAE,0BAA0B,CACtCqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,QAAKoF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEiB,GAAG,CAACqC,SAAS,CAAK,CAAC,CAC3DpD,gBAAgB,CAACe,GAAG,CAACvD,QAAQ,CAAC,EAC5B,CAAC,CACL8B,cAAc,CAACyB,GAAG,CAACxB,MAAM,CAAC,EACxB,CAAC,CACK,CAAC,cAEd9E,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,MAAM,CAAAC,QAAA,eACzBrF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrF,KAAA,QAAKoF,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEvF,IAAA,CAACpC,QAAQ,EAAC4H,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,CACtCkB,GAAG,CAACO,QAAQ,CAACuC,MAAM,CAAG,EAAE,CAAG9C,GAAG,CAACO,QAAQ,CAACkI,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGzI,GAAG,CAACO,QAAQ,EACzE,CAAC,cACN7G,KAAA,QAAKoF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCrF,KAAA,QAAAqF,QAAA,eAAKvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAAC3D,SAAS,EAAM,CAAC,cACjD3C,KAAA,QAAAqF,QAAA,eAAKvF,IAAA,WAAAuF,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAAC1D,MAAM,EAAM,CAAC,cAChD5C,KAAA,QAAAqF,QAAA,eAAKvF,IAAA,WAAAuF,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACzD,SAAS,EAAM,CAAC,cAClD7C,KAAA,QAAAqF,QAAA,eAAKvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACxD,SAAS,EAAM,CAAC,EAC9C,CAAC,EACH,CAAC,CAELwD,GAAG,CAAC4C,IAAI,eACPpJ,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBrF,KAAA,SAAMoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,EAAC,GACvC,CAACiB,GAAG,CAAC4C,IAAI,CAACyF,OAAO,CAAC,CAAC,CAAC,EACjB,CAAC,CACJ,CACN,cAED7O,IAAA,QAAKsF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtC,GAAI,CAAAyK,IAAI,CAACxJ,GAAG,CAACyJ,OAAO,CAAC,CAACC,kBAAkB,CAAC,CAAC,CACxC,CAAC,cAENhQ,KAAA,QAAKoF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCvF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMiB,cAAc,CAAC7G,GAAG,CAACI,EAAE,CAAEJ,GAAG,CAACO,QAAQ,CAAE,CACpDyD,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAChC,QAAQ,EAACwH,IAAI,CAAE,EAAG,CAAE,CAAC,CAChB,CAAC,cAETxF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMgB,SAAS,CAAC5G,GAAG,CAAE,CAC9BgE,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAC9B,GAAG,EAACsH,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,cAETxF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMkB,UAAU,CAAC9G,GAAG,CAAE,CAC/BgE,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAC/B,aAAa,EAACuH,IAAI,CAAE,EAAG,CAAE,CAAC,CACrB,CAAC,CAERgB,GAAG,CAACxB,MAAM,GAAK,QAAQ,eACtBhF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,SAAS,CACjB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMmB,YAAY,CAAC/G,GAAG,CAACI,EAAE,CAAE,CACpC4D,KAAK,CAAE,CAAEsB,YAAY,CAAE,KAAM,CAAE,CAAAvG,QAAA,cAE/BvF,IAAA,CAAClC,WAAW,EAAC0H,IAAI,CAAE,EAAG,CAAE,CAAC,CACnB,CACT,EACE,CAAC,EACG,CAAC,EACR,CAAC,CACG,CAAC,EAvFLgB,GAAG,CAACI,EAwFT,CACN,CAAC,CACC,CAAC,cAEN1G,KAAA,CAACzC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,GAAI,CAAE,CACpCH,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAE0B,KAAK,CAAE,CAAE,CAAE,CAClC/F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE5BvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,yDAAyD,CAACkF,KAAK,CAAE,CAC9EG,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACdL,UAAU,CAAE,mDAAmD,CAC/DqB,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACAvF,IAAA,CAACpC,QAAQ,EAAC0H,SAAS,CAAC,YAAY,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,CAC1C,CAAC,CACH,CAAC,cACNxF,IAAA,OAAIsF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cAC9DvF,IAAA,MAAGsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6CAA2C,CAAG,CAAC,EAClE,CACb,CACQ,CAAC,EACR,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAAmI,UAAyB,CAAGyC,KAAA,EAAqE,IAApE,CAAExC,OAAO,CAAE9M,gBAAgB,CAAE+M,gBAAgB,CAAEC,cAAe,CAAC,CAAAsC,KAAA,CAChG,mBACEnQ,IAAA,CAAClD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,CACjBoI,OAAO,CAAChB,GAAG,CAAC,CAACqB,MAAW,CAAEkB,KAAa,gBACtClP,IAAA,CAACjD,GAAG,EAAiB4R,EAAE,CAAE,CAAE,CAACG,EAAE,CAAE,CAAE,CAAAvJ,QAAA,cAChCvF,IAAA,CAACvC,MAAM,CAAC8M,GAAG,EACTsB,OAAO,CAAE,CAAElC,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,EAAG,CAAE,CAC/BiB,OAAO,CAAE,CAAEvB,OAAO,CAAE,CAAC,CAAEM,CAAC,CAAE,CAAE,CAAE,CAC9BJ,UAAU,CAAE,CAAE+E,KAAK,CAAEM,KAAK,CAAG,GAAI,CAAE,CACnChD,UAAU,CAAE,CAAEjC,CAAC,CAAE,CAAC,CAAC,CAAEoB,KAAK,CAAE,IAAK,CAAE,CAAA9F,QAAA,cAEnCrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,QAAKoF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DrF,KAAA,QAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAEyI,MAAM,CAACoC,IAAI,CAAK,CAAC,cAC1DlQ,KAAA,QAAKoF,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DvF,IAAA,CAACP,QAAQ,EAAC+F,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,CACtC0I,MAAM,CAACqC,QAAQ,EACb,CAAC,EACH,CAAC,cACNrQ,IAAA,CAACvC,MAAM,CAAC6S,MAAM,EACZpE,UAAU,CAAE,CAAEb,KAAK,CAAE,GAAI,CAAE,CAC3Bc,QAAQ,CAAE,CAAEd,KAAK,CAAE,GAAI,CAAE,CACzBe,OAAO,CAAEA,CAAA,GAAMwB,gBAAgB,CAACI,MAAM,CAACpH,EAAE,CAAE,CAC3CtB,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cAErCvF,IAAA,CAACf,KAAK,EACJuG,IAAI,CAAE,EAAG,CACTF,SAAS,CAAEzE,gBAAgB,CAAC4H,QAAQ,CAACuF,MAAM,CAACpH,EAAE,CAAC,CAAG,aAAa,CAAG,eAAgB,CAClF2J,IAAI,CAAE1P,gBAAgB,CAAC4H,QAAQ,CAACuF,MAAM,CAACpH,EAAE,CAAC,CAAG,cAAc,CAAG,MAAO,CACtE,CAAC,CACW,CAAC,EACb,CAAC,CACK,CAAC,cAEd1G,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,eACRrF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrF,KAAA,QAAKoF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrErF,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,CAAC3B,IAAI,EAACiH,SAAS,CAAC,mBAAmB,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAChDxF,IAAA,SAAMsF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACrCyI,MAAM,CAACwC,aAAa,CAAC3B,OAAO,CAAC,CAAC,CAAC,CAC5B,CAAC,EACJ,CAAC,cACN3O,KAAA,CAAChD,KAAK,EACJgI,EAAE,CAAE8I,MAAM,CAACxE,WAAW,EAAI,CAAC,CAAG,SAAS,CAAGwE,MAAM,CAACxE,WAAW,EAAI,EAAE,CAAG,SAAS,CAAG,QAAS,CAC1FlE,SAAS,CAAC,WAAW,CAAAC,QAAA,EAEpByI,MAAM,CAACxE,WAAW,CAAC,OACtB,EAAO,CAAC,EACL,CAAC,CAELwE,MAAM,CAACyC,QAAQ,eACdzQ,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBvF,IAAA,QAAKsF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACpCyI,MAAM,CAACyC,QAAQ,CAACxB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACtC,GAAG,CAAC,CAAC+D,OAAe,CAAEC,GAAW,gBAC5D3Q,IAAA,CAAC9C,KAAK,EAAWgI,EAAE,CAAC,OAAO,CAAC8J,IAAI,CAAC,MAAM,CAAC1J,SAAS,CAAC,WAAW,CAAAC,QAAA,CAC1DmL,OAAO,EADEC,GAEL,CACR,CAAC,CACC,CAAC,CACH,CACN,CAEA3C,MAAM,CAAC4C,UAAU,eAChB1Q,KAAA,QAAKoF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACvCvF,IAAA,CAACjC,UAAU,EAACyH,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,CACxC0I,MAAM,CAAC4C,UAAU,EACf,CACN,CAEA5C,MAAM,CAAC6C,YAAY,eAClB3Q,KAAA,QAAKoF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACvCvF,IAAA,CAACnC,KAAK,EAAC2H,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,CACnC0I,MAAM,CAAC6C,YAAY,EACjB,CACN,EACE,CAAC,cAEN3Q,KAAA,QAAKoF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BrF,KAAA,CAACjD,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMyB,cAAc,CAACG,MAAM,CAAE,CACtC1I,SAAS,CAAC,aAAa,CACvBkF,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAvG,QAAA,eAEhCvF,IAAA,CAAC9B,GAAG,EAACsH,IAAI,CAAE,EAAG,CAACF,SAAS,CAAC,MAAM,CAAE,CAAC,eAEpC,EAAQ,CAAC,CAER0I,MAAM,CAAClK,KAAK,eACX9D,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACTkC,IAAI,QAAAgD,MAAA,CAASsD,MAAM,CAAClK,KAAK,CAAG,CAC5B0G,KAAK,CAAE,CAAEsB,YAAY,CAAE,MAAO,CAAE,CAAAvG,QAAA,cAEhCvF,IAAA,CAACT,KAAK,EAACiG,IAAI,CAAE,EAAG,CAAE,CAAC,CACb,CACT,EACE,CAAC,EACG,CAAC,EACR,CAAC,CACG,CAAC,EAzGLwI,MAAM,CAACpH,EA0GZ,CACN,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAkH,UAAyB,CAAGgD,KAAA,EAAyC,IAAxC,CAAErD,IAAI,CAAEL,SAAS,CAAErI,cAAe,CAAC,CAAA+L,KAAA,CACpE,mBACE5Q,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAAC1B,OAAO,EAACgH,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAExC,EAAI,CAAC,CACM,CAAC,cACdxF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,CACPkI,IAAI,CAACnE,MAAM,CAAG,CAAC,cACdtJ,IAAA,QAAKsF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BrF,KAAA,CAAC3C,KAAK,EAAC+H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACvCvF,IAAA,UAAAuF,QAAA,cACErF,KAAA,OAAAqF,QAAA,eACEvF,IAAA,OAAAuF,QAAA,CAAI,YAAU,CAAI,CAAC,cACnBvF,IAAA,OAAAuF,QAAA,CAAI,WAAS,CAAI,CAAC,cAClBvF,IAAA,OAAAuF,QAAA,CAAI,QAAM,CAAI,CAAC,cACfvF,IAAA,OAAAuF,QAAA,CAAI,MAAI,CAAI,CAAC,cACbvF,IAAA,OAAAuF,QAAA,CAAI,MAAI,CAAI,CAAC,cACbvF,IAAA,OAAAuF,QAAA,CAAI,SAAO,CAAI,CAAC,EACd,CAAC,CACA,CAAC,cACRvF,IAAA,UAAAuF,QAAA,CACGkI,IAAI,CAACd,GAAG,CAAEnG,GAAQ,eACjBtG,KAAA,OAAAqF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEiB,GAAG,CAACqC,SAAS,CAAK,CAAC,cAChD7I,IAAA,OAAAuF,QAAA,CAAKiB,GAAG,CAACO,QAAQ,CAACuC,MAAM,CAAG,EAAE,CAAG9C,GAAG,CAACO,QAAQ,CAACkI,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAG,KAAK,CAAGzI,GAAG,CAACO,QAAQ,CAAK,CAAC,cACtF/G,IAAA,OAAAuF,QAAA,CAAKR,cAAc,CAACyB,GAAG,CAACxB,MAAM,CAAC,CAAK,CAAC,cACrChF,IAAA,OAAIsF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACrCiB,GAAG,CAAC4C,IAAI,KAAAsB,MAAA,CAAOlE,GAAG,CAAC4C,IAAI,CAACyF,OAAO,CAAC,CAAC,CAAC,EAAK,GAAG,CACzC,CAAC,cACL7O,IAAA,OAAAuF,QAAA,CAAK,GAAI,CAAAyK,IAAI,CAACxJ,GAAG,CAACyJ,OAAO,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAAK,CAAC,cACrDlQ,IAAA,OAAAuF,QAAA,cACEvF,IAAA,CAAC/C,MAAM,EACLsP,OAAO,CAAC,eAAe,CACvB/G,IAAI,CAAC,IAAI,CACT4G,OAAO,CAAEA,CAAA,GAAMgB,SAAS,CAAC5G,GAAG,CAAE,CAAAjB,QAAA,cAE9BvF,IAAA,CAAC9B,GAAG,EAACsH,IAAI,CAAE,EAAG,CAAE,CAAC,CACX,CAAC,CACP,CAAC,GAhBEgB,GAAG,CAACI,EAiBT,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,cAEN1G,KAAA,QAAKoF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BvF,IAAA,CAAC1B,OAAO,EAACgH,SAAS,CAAC,oBAAoB,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cACpDxF,IAAA,OAAIsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC9CvF,IAAA,MAAGsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uCAAqC,CAAG,CAAC,EACnE,CACN,CACQ,CAAC,EACR,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAAwI,YAA2B,CAAGgD,KAAA,EAAmD,IAAlD,CAAEpD,OAAO,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAAkD,KAAA,CAChF,mBACE/Q,IAAA,QAAAuF,QAAA,CACGoI,OAAO,CAACrE,MAAM,CAAG,CAAC,cACjBtJ,IAAA,CAAC0N,UAAU,EACTC,OAAO,CAAEA,OAAQ,CACjB9M,gBAAgB,CAAE8M,OAAO,CAAChB,GAAG,CAAE2C,CAAM,EAAKA,CAAC,CAAC1I,EAAE,CAAE,CAChDgH,gBAAgB,CAAEA,gBAAiB,CACnCC,cAAc,CAAEA,cAAe,CAChC,CAAC,cAEF7N,IAAA,CAAChD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,cACArF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAACnH,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eACrCvF,IAAA,CAACf,KAAK,EAACqG,SAAS,CAAC,oBAAoB,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAClDxF,IAAA,OAAIsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAChDvF,IAAA,MAAGsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uDAAqD,CAAG,CAAC,EAC7E,CAAC,CACR,CACP,CACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA0I,YAA2B,CAAG+C,KAAA,EAA0B,IAAzB,CAAEvD,IAAI,CAAExE,UAAW,CAAC,CAAA+H,KAAA,CACvD,KAAM,CAAAC,eAAe,CAAGxD,IAAI,CAACvE,MAAM,CAAC,CAACgI,GAAQ,CAAE1K,GAAQ,GAAK,CAC1D,KAAM,CAAA2K,KAAK,CAAG,GAAI,CAAAnB,IAAI,CAACxJ,GAAG,CAACyJ,OAAO,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAE,CAAEiB,KAAK,CAAE,OAAO,CAAEC,IAAI,CAAE,SAAU,CAAC,CAAC,CACpGF,GAAG,CAACC,KAAK,CAAC,CAAG,CAACD,GAAG,CAACC,KAAK,CAAC,EAAI,CAAC,GAAK3K,GAAG,CAAC4C,IAAI,EAAI,CAAC,CAAC,CAChD,MAAO,CAAA8H,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAG,YAAY,CAAG5D,IAAI,CAACvE,MAAM,CAAC,CAACgI,GAAQ,CAAE1K,GAAQ,GAAK,CACvD0K,GAAG,CAAC1K,GAAG,CAACxB,MAAM,CAAC,CAAG,CAACkM,GAAG,CAAC1K,GAAG,CAACxB,MAAM,CAAC,EAAI,CAAC,EAAI,CAAC,CAC5C,MAAO,CAAAkM,GAAG,CACZ,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,mBACEhR,KAAA,CAACpD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAACnB,SAAS,EAACyG,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,oBAE1C,EAAI,CAAC,CACM,CAAC,cACdtF,KAAA,CAAClD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,eACRrF,KAAA,QAAKoF,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBrF,KAAA,OAAIoF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,EAAC,GAAC,CAAC0D,UAAU,CAAC4F,OAAO,CAAC,CAAC,CAAC,EAAK,CAAC,cAClE7O,IAAA,MAAGsF,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,EACnD,CAAC,cAENvF,IAAA,QAAKsF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBQ,MAAM,CAACC,OAAO,CAACiL,eAAe,CAAC,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACtC,GAAG,CAAC2E,KAAA,MAAC,CAACH,KAAK,CAAEI,MAAM,CAAgB,CAAAD,KAAA,oBAC5EpR,KAAA,QAAiBoF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC5EvF,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAE4L,KAAK,CAAO,CAAC,cAC3CjR,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAACkF,KAAK,CAAE,CAAEG,KAAK,CAAE,OAAQ,CAAE,CAAApF,QAAA,cAC9CvF,IAAA,CAACxC,WAAW,EACVgU,GAAG,CAAGD,MAAM,CAAG3G,IAAI,CAAC6G,GAAG,CAAC,GAAG1L,MAAM,CAAC2L,MAAM,CAACT,eAAe,CAAC,CAACtE,GAAG,CAACgF,CAAC,EAAIC,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC,CAAI,GAAI,CACtFnH,KAAK,CAAE,CAAEM,MAAM,CAAE,KAAM,CAAE,CACzByB,OAAO,CAAC,SAAS,CAClB,CAAC,CACC,CAAC,cACNrM,KAAA,SAAMoF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,EAAC,GAAC,CAACgM,MAAM,CAAC1C,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EACnE,CAAC,GAXEsC,KAYL,CAAC,EACP,CAAC,CACC,CAAC,EACG,CAAC,EACR,CAAC,CACJ,CAAC,cAENnR,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,0BAA0B,CAACkF,KAAK,CAAE,CAChDC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAAClB,QAAQ,EAACwG,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,0BAEzC,EAAI,CAAC,CACM,CAAC,cACdxF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,cACRvF,IAAA,QAAKsF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBQ,MAAM,CAACC,OAAO,CAACqL,YAAY,CAAC,CAAC1E,GAAG,CAACkF,KAAA,MAAC,CAAC7M,MAAM,CAAE8M,KAAK,CAAgB,CAAAD,KAAA,oBAC/D3R,KAAA,QAAkBoF,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAC7EvF,IAAA,SAAMsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEP,MAAM,CAAO,CAAC,cAC5C9E,KAAA,QAAKoF,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAACkF,KAAK,CAAE,CAAEG,KAAK,CAAE,OAAQ,CAAE,CAAApF,QAAA,cAC9CvF,IAAA,CAACxC,WAAW,EACVgU,GAAG,CAAGM,KAAK,CAAGrE,IAAI,CAACnE,MAAM,CAAI,GAAI,CACjCkB,KAAK,CAAE,CAAEM,MAAM,CAAE,KAAM,CAAE,CACzByB,OAAO,CAAC,MAAM,CACf,CAAC,CACC,CAAC,cACNvM,IAAA,SAAMsF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEuM,KAAK,CAAO,CAAC,EACnD,CAAC,GAXE9M,MAYL,CAAC,EACP,CAAC,CACC,CAAC,CACG,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAkJ,WAA0B,CAAG6D,KAAA,EAAuC,IAAtC,CAAE7O,YAAY,CAAEC,eAAgB,CAAC,CAAA4O,KAAA,CACnE,mBACE7R,KAAA,CAACpD,GAAG,EAACwI,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAACtB,IAAI,EAAC4G,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,wBAErC,EAAI,CAAC,CACM,CAAC,cACdxF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,cACRrF,KAAA,CAAC/C,IAAI,EAAAoI,QAAA,eACHvF,IAAA,CAAC7C,IAAI,CAAC6U,KAAK,EACTC,IAAI,CAAC,QAAQ,CACbrL,EAAE,CAAC,qBAAqB,CACxB8F,KAAK,CAAC,qBAAqB,CAC3BwF,OAAO,CAAEhP,YAAY,CAACE,aAAa,CAACC,KAAM,CAC1CuM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfE,aAAa,CAAA+O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACE,aAAa,MAAEC,KAAK,CAAEwM,CAAC,CAACC,MAAM,CAACoC,OAAO,EAAE,EAC1E,CAAE,CACH5M,SAAS,CAAC,iBAAiB,CAC5B,CAAC,cACFtF,IAAA,CAAC7C,IAAI,CAAC6U,KAAK,EACTC,IAAI,CAAC,QAAQ,CACbrL,EAAE,CAAC,oBAAoB,CACvB8F,KAAK,CAAC,oBAAoB,CAC1BwF,OAAO,CAAEhP,YAAY,CAACE,aAAa,CAACE,IAAK,CACzCsM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfE,aAAa,CAAA+O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACE,aAAa,MAAEE,IAAI,CAAEuM,CAAC,CAACC,MAAM,CAACoC,OAAO,EAAE,EACzE,CAAE,CACH5M,SAAS,CAAC,iBAAiB,CAC5B,CAAC,cACFtF,IAAA,CAAC7C,IAAI,CAAC6U,KAAK,EACTC,IAAI,CAAC,QAAQ,CACbrL,EAAE,CAAC,mBAAmB,CACtB8F,KAAK,CAAC,mBAAmB,CACzBwF,OAAO,CAAEhP,YAAY,CAACE,aAAa,CAACG,GAAI,CACxCqM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfE,aAAa,CAAA+O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACE,aAAa,MAAEG,GAAG,CAAEsM,CAAC,CAACC,MAAM,CAACoC,OAAO,EAAE,EACxE,CAAE,CACH5M,SAAS,CAAC,YAAY,CACvB,CAAC,EACE,CAAC,CACE,CAAC,EACR,CAAC,CACJ,CAAC,cAENtF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAClD,IAAI,EAACsI,SAAS,CAAC,oBAAoB,CAACkF,KAAK,CAAE,CAC1CC,UAAU,CAAE,0BAA0B,CACtC4B,cAAc,CAAE,YAAY,CAC5BP,YAAY,CAAE,MAChB,CAAE,CAAAvG,QAAA,eACAvF,IAAA,CAAChD,IAAI,CAAC+R,MAAM,EAACzJ,SAAS,CAAC,yBAAyB,CAAAC,QAAA,cAC9CrF,KAAA,OAAIoF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACrCvF,IAAA,CAACzB,QAAQ,EAAC+G,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,oBAEzC,EAAI,CAAC,CACM,CAAC,cACdxF,IAAA,CAAChD,IAAI,CAACyP,IAAI,EAAAlH,QAAA,cACRrF,KAAA,CAAC/C,IAAI,EAAAoI,QAAA,eACHrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAC/M,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAY,CAAC,cAClErF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAElD,YAAY,CAACM,WAAW,CAACC,gBAAiB,CACjDmM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfM,WAAW,CAAA2O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACM,WAAW,MAAEC,gBAAgB,CAAEoM,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAE,EAC/E,CAAE,CACHoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CAAA/G,QAAA,eAEFvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvF,IAAA,WAAQoG,KAAK,CAAC,SAAS,CAAAb,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCvF,IAAA,WAAQoG,KAAK,CAAC,YAAY,CAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,EACnC,CAAC,EACJ,CAAC,cAEbrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAC/M,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAY,CAAC,cAClErF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAElD,YAAY,CAACM,WAAW,CAACE,gBAAiB,CACjDkM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfM,WAAW,CAAA2O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACM,WAAW,MAAEE,gBAAgB,CAAEmM,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAE,EAC/E,CAAE,CACHoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CAAA/G,QAAA,eAEFvF,IAAA,WAAQoG,KAAK,CAAC,YAAY,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,cACjDvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,cAEbrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAC/M,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAkB,CAAY,CAAC,cAClErF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAElD,YAAY,CAACM,WAAW,CAACG,gBAAiB,CACjDiM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfM,WAAW,CAAA2O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACM,WAAW,MAAEG,gBAAgB,CAAEkM,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAE,EAC/E,CAAE,CACHoE,KAAK,CAAE,CACLC,UAAU,CAAE,0BAA0B,CACtCuC,MAAM,CAAE,MAAM,CACdV,KAAK,CAAE,MACT,CAAE,CAAA/G,QAAA,eAEFvF,IAAA,WAAQoG,KAAK,CAAC,IAAI,CAAAb,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BvF,IAAA,WAAQoG,KAAK,CAAC,IAAI,CAAAb,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,cAEbvF,IAAA,CAAC7C,IAAI,CAAC6U,KAAK,EACTC,IAAI,CAAC,QAAQ,CACbrL,EAAE,CAAC,cAAc,CACjB8F,KAAK,CAAC,+BAA+B,CACrCwF,OAAO,CAAEhP,YAAY,CAACM,WAAW,CAACI,iBAAkB,CACpDgM,QAAQ,CAAGC,CAAC,EAAK1M,eAAe,CAAAgP,aAAA,CAAAA,aAAA,IAC3BjP,YAAY,MACfM,WAAW,CAAA2O,aAAA,CAAAA,aAAA,IAAOjP,YAAY,CAACM,WAAW,MAAEI,iBAAiB,CAAEiM,CAAC,CAACC,MAAM,CAACoC,OAAO,EAAE,EAClF,CAAE,CACH5M,SAAS,CAAC,YAAY,CACvB,CAAC,EACE,CAAC,CACE,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA6I,WAA0B,CAAGmE,KAAA,EAE7B,IAF8B,CAClClE,IAAI,CAAEC,MAAM,CAAE5L,UAAU,CAAEC,aAAa,CAAEf,YAAY,CAAEC,eAAe,CAAEjB,YAAY,CAAE2N,QACxF,CAAC,CAAAgE,KAAA,CACC,mBACEpS,KAAA,CAAC9C,KAAK,EAACgR,IAAI,CAAEA,IAAK,CAACC,MAAM,CAAEA,MAAO,CAAC7I,IAAI,CAAC,IAAI,CAAC+M,QAAQ,MAAAhN,QAAA,eACnDvF,IAAA,CAAC5C,KAAK,CAAC2R,MAAM,EAACyD,WAAW,MAAChI,KAAK,CAAE,CAAEC,UAAU,CAAE,mDAAmD,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cACnHrF,KAAA,CAAC9C,KAAK,CAACqV,KAAK,EAACnN,SAAS,CAAC,YAAY,CAAAC,QAAA,eACjCvF,IAAA,CAACrC,MAAM,EAAC2H,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,4BAEvC,EAAa,CAAC,CACF,CAAC,cACfxF,IAAA,CAAC5C,KAAK,CAACqP,IAAI,EAACjC,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAE6B,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,cAC1DrF,KAAA,CAAC/C,IAAI,EAAAoI,QAAA,eACHrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,aAAW,CAAY,CAAC,cACpCvF,IAAA,CAAC7C,IAAI,CAACuS,OAAO,EACXuC,IAAI,CAAC,MAAM,CACXS,MAAM,CAAC,2CAA2C,CAClD9C,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAA8C,KAAK,CAAI9C,CAAC,CAACC,MAAM,CAAsB6C,KAAK,CAClD/Q,eAAe,CAAC+Q,KAAK,CAAGA,KAAK,CAAC,CAAC,CAAC,CAAG,IAAI,CAAC,CAC1C,CAAE,CACFnI,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAC3E,CAAC,cACFtM,IAAA,CAAC7C,IAAI,CAACsS,IAAI,EAACnK,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,wEAElC,CAAW,CAAC,EACF,CAAC,cAEbrF,KAAA,CAACpD,GAAG,EAAAyI,QAAA,eACFvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,YAAU,CAAY,CAAC,cACnCrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE3D,UAAU,CAACI,SAAU,CAC5B+M,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAEzF,SAAS,CAAEgN,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACxFoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,eAE1EvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,cACpCvF,IAAA,WAAQoG,KAAK,CAAC,SAAS,CAAAb,QAAA,CAAC,SAAO,CAAQ,CAAC,cACxCvF,IAAA,WAAQoG,KAAK,CAAC,YAAY,CAAAb,QAAA,CAAC,YAAU,CAAQ,CAAC,EACnC,CAAC,EACJ,CAAC,CACV,CAAC,cACNvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,kBAAgB,CAAY,CAAC,cACzCvF,IAAA,CAAC7C,IAAI,CAACuS,OAAO,EACXuC,IAAI,CAAC,QAAQ,CACbW,GAAG,CAAC,GAAG,CACPxM,KAAK,CAAE3D,UAAU,CAACK,MAAO,CACzB8M,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAExF,MAAM,CAAE+P,QAAQ,CAAChD,CAAC,CAACC,MAAM,CAAC1J,KAAK,CAAC,EAAG,CAAE,CAC/FoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAC3E,CAAC,EACQ,CAAC,CACV,CAAC,EACH,CAAC,cAENpM,KAAA,CAACpD,GAAG,EAAAyI,QAAA,eACFvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,YAAU,CAAY,CAAC,cACnCrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE3D,UAAU,CAACM,SAAU,CAC5B6M,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAEvF,SAAS,CAAE8M,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACxFoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,eAE1EvF,IAAA,WAAQoG,KAAK,CAAC,YAAY,CAAAb,QAAA,CAAC,eAAa,CAAQ,CAAC,cACjDvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,CACV,CAAC,cACNvF,IAAA,CAACjD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,cACTrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,YAAU,CAAY,CAAC,cACnCrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE3D,UAAU,CAACO,SAAU,CAC5B4M,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAEtF,SAAS,CAAE6M,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACxFoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,eAE1EvF,IAAA,WAAQoG,KAAK,CAAC,IAAI,CAAAb,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BvF,IAAA,WAAQoG,KAAK,CAAC,IAAI,CAAAb,QAAA,CAAC,IAAE,CAAQ,CAAC,cAC9BvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCvF,IAAA,WAAQoG,KAAK,CAAC,OAAO,CAAAb,QAAA,CAAC,OAAK,CAAQ,CAAC,EACzB,CAAC,EACJ,CAAC,CACV,CAAC,EACH,CAAC,cAENrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,UAAQ,CAAY,CAAC,cACjCrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE3D,UAAU,CAACQ,QAAS,CAC3B2M,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAErF,QAAQ,CAAE4M,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACvFoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,eAE1EvF,IAAA,WAAQoG,KAAK,CAAC,KAAK,CAAAb,QAAA,CAAC,KAAG,CAAQ,CAAC,cAChCvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,cACtCvF,IAAA,WAAQoG,KAAK,CAAC,MAAM,CAAAb,QAAA,CAAC,MAAI,CAAQ,CAAC,cAClCvF,IAAA,WAAQoG,KAAK,CAAC,QAAQ,CAAAb,QAAA,CAAC,QAAM,CAAQ,CAAC,EAC3B,CAAC,EACJ,CAAC,cAEbrF,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,wBAAsB,CAAY,CAAC,cAC/CrF,KAAA,CAAC/C,IAAI,CAAC4S,MAAM,EACV3J,KAAK,CAAE3D,UAAU,CAACG,sBAAuB,CACzCgN,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAE1F,sBAAsB,CAAEiN,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACrGoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,eAE1EvF,IAAA,WAAQoG,KAAK,CAAC,EAAE,CAAAb,QAAA,CAAC,4BAA0B,CAAQ,CAAC,CACnD5E,YAAY,CAACgM,GAAG,CAAEqB,MAAW,eAC5B9N,KAAA,WAAwBkG,KAAK,CAAE4H,MAAM,CAACpH,EAAG,CAAArB,QAAA,EACtCyI,MAAM,CAACoC,IAAI,CAAC,KAAG,CAACpC,MAAM,CAACxE,WAAW,CAAC,eACtC,GAFawE,MAAM,CAACpH,EAEZ,CACT,CAAC,EACS,CAAC,EACJ,CAAC,cAEb1G,KAAA,CAAC/C,IAAI,CAACiV,KAAK,EAAC9M,SAAS,CAAC,MAAM,CAAAC,QAAA,eAC1BvF,IAAA,CAAC7C,IAAI,CAACkV,KAAK,EAAA9M,QAAA,CAAC,SAAO,CAAY,CAAC,cAChCvF,IAAA,CAAC7C,IAAI,CAACuS,OAAO,EACXoD,EAAE,CAAC,UAAU,CACbC,IAAI,CAAE,CAAE,CACRpD,WAAW,CAAC,wCAAwC,CACpDvJ,KAAK,CAAE3D,UAAU,CAACE,OAAQ,CAC1BiN,QAAQ,CAAGC,CAAC,EAAKnN,aAAa,CAAE4F,IAAS,EAAA6J,aAAA,CAAAA,aAAA,IAAW7J,IAAI,MAAE3F,OAAO,CAAEkN,CAAC,CAACC,MAAM,CAAC1J,KAAK,EAAG,CAAE,CACtFoE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAC3E,CAAC,EACQ,CAAC,EACT,CAAC,CACG,CAAC,cACbpM,KAAA,CAAC9C,KAAK,CAAC4V,MAAM,EAACxI,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,eAC7DvF,IAAA,CAAC/C,MAAM,EAACsP,OAAO,CAAC,WAAW,CAACH,OAAO,CAAEiC,MAAO,CAAA9I,QAAA,CAAC,QAE7C,CAAQ,CAAC,cACTrF,KAAA,CAACjD,MAAM,EACLmP,OAAO,CAAEkC,QAAS,CAClB9B,QAAQ,CAAE,CAAC7K,YAAa,CACxB6I,KAAK,CAAE,CAAEC,UAAU,CAAE,mDAAmD,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,eAE3FvF,IAAA,CAACrC,MAAM,EAAC2H,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAEvC,EAAQ,CAAC,EACG,CAAC,EACV,CAAC,CAEZ,CAAC,CAED;AACA,KAAM,CAAA+I,YAA2B,CAAG0E,MAAA,EAA6D,IAA5D,CAAE7E,IAAI,CAAEC,MAAM,CAAE7H,GAAG,CAAEzB,cAAc,CAAEU,gBAAiB,CAAC,CAAAwN,MAAA,CAC1F,mBACE/S,KAAA,CAAC9C,KAAK,EAACgR,IAAI,CAAEA,IAAK,CAACC,MAAM,CAAEA,MAAO,CAAC7I,IAAI,CAAC,IAAI,CAAC+M,QAAQ,MAAAhN,QAAA,eACnDvF,IAAA,CAAC5C,KAAK,CAAC2R,MAAM,EAACyD,WAAW,MAAChI,KAAK,CAAE,CAAEC,UAAU,CAAE,mDAAmD,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cACnHrF,KAAA,CAAC9C,KAAK,CAACqV,KAAK,EAACnN,SAAS,CAAC,YAAY,CAAAC,QAAA,eACjCvF,IAAA,CAAC9B,GAAG,EAACoH,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,cAEpC,EAAa,CAAC,CACF,CAAC,cACfxF,IAAA,CAAC5C,KAAK,CAACqP,IAAI,EAACjC,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAE6B,KAAK,CAAE,MAAO,CAAE,CAAA/G,QAAA,CACzDiB,GAAG,eACFtG,KAAA,CAACpD,GAAG,EAAAyI,QAAA,eACFrF,KAAA,CAACnD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,eACTvF,IAAA,OAAIsF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACjDrF,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACqC,SAAS,EAAI,CAAC,cACnD3I,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACR,cAAc,CAACyB,GAAG,CAACxB,MAAM,CAAC,EAAI,CAAC,cAC5D9E,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,WAAS,CAAQ,CAAC,IAAC,CAACE,gBAAgB,CAACe,GAAG,CAACvD,QAAQ,CAAC,EAAI,CAAC,cAClE/C,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,YAAU,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACO,QAAQ,EAAI,CAAC,cACjD7G,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,eAAa,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACuC,eAAe,EAAI,CAAC,CAC1DvC,GAAG,CAAC4C,IAAI,eAAIlJ,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,cAAArF,KAAA,SAAMoF,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAC,GAAC,CAACiB,GAAG,CAAC4C,IAAI,CAACyF,OAAO,CAAC,CAAC,CAAC,EAAO,CAAC,EAAG,CAAC,cACvG3O,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,UAAQ,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAAyK,IAAI,CAACxJ,GAAG,CAACyJ,OAAO,CAAC,CAACiD,cAAc,CAAC,CAAC,EAAI,CAAC,CACxE1M,GAAG,CAAC2M,uBAAuB,eAC1BjT,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,uBAAqB,CAAQ,CAAC,IAAC,CAAC,GAAI,CAAAyK,IAAI,CAACxJ,GAAG,CAAC2M,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC,EAAI,CACtG,EACE,CAAC,cACNhT,KAAA,CAACnD,GAAG,EAAC4R,EAAE,CAAE,CAAE,CAAApJ,QAAA,eACTvF,IAAA,OAAIsF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cACtDrF,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,OAAK,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAAC3D,SAAS,EAAI,CAAC,cAC7C3C,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,SAAO,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAAC1D,MAAM,EAAI,CAAC,cAC5C5C,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACzD,SAAS,EAAI,CAAC,cAC9C7C,KAAA,MAAAqF,QAAA,eAAGvF,IAAA,WAAAuF,QAAA,CAAQ,aAAW,CAAQ,CAAC,IAAC,CAACiB,GAAG,CAACxD,SAAS,EAAI,CAAC,CAClDwD,GAAG,CAAC7D,OAAO,eACVzC,KAAA,CAAAE,SAAA,EAAAmF,QAAA,eACEvF,IAAA,OAAIsF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,SAAO,CAAI,CAAC,cAC9CvF,IAAA,MAAGsF,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEiB,GAAG,CAAC7D,OAAO,CAAI,CAAC,EAC3C,CACH,EACE,CAAC,EACH,CACN,CACS,CAAC,cACb3C,IAAA,CAAC5C,KAAK,CAAC4V,MAAM,EAACxI,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cAC7DvF,IAAA,CAAC/C,MAAM,EAACsP,OAAO,CAAC,WAAW,CAACH,OAAO,CAAEiC,MAAO,CAAA9I,QAAA,CAAC,OAE7C,CAAQ,CAAC,CACG,CAAC,EACV,CAAC,CAEZ,CAAC,CAED;AACA,KAAM,CAAAiJ,SAAwB,CAAG4E,MAAA,EAE3B,IAF4B,CAChChF,IAAI,CAAEC,MAAM,CAAE7H,GAAG,CAAEzE,QAAQ,CAAEF,WAAW,CAAEC,cAAc,CAAE2M,aAC5D,CAAC,CAAA2E,MAAA,CACC,mBACElT,KAAA,CAAC9C,KAAK,EAACgR,IAAI,CAAEA,IAAK,CAACC,MAAM,CAAEA,MAAO,CAAC7I,IAAI,CAAC,IAAI,CAAC+M,QAAQ,MAAAhN,QAAA,eACnDvF,IAAA,CAAC5C,KAAK,CAAC2R,MAAM,EAACyD,WAAW,MAAChI,KAAK,CAAE,CAAEC,UAAU,CAAE,mDAAmD,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cACnHrF,KAAA,CAAC9C,KAAK,CAACqV,KAAK,EAACnN,SAAS,CAAC,YAAY,CAAAC,QAAA,eACjCvF,IAAA,CAAC/B,aAAa,EAACqH,SAAS,CAAC,MAAM,CAACE,IAAI,CAAE,EAAG,CAAE,CAAC,UACrC,CAACgB,GAAG,SAAHA,GAAG,iBAAHA,GAAG,CAAEqC,SAAS,EACX,CAAC,CACF,CAAC,cACf7I,IAAA,CAAC5C,KAAK,CAACqP,IAAI,EAACjC,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAE6B,KAAK,CAAE,MAAM,CAAExB,MAAM,CAAE,OAAO,CAAEuI,SAAS,CAAE,MAAO,CAAE,CAAA9N,QAAA,cAC9FvF,IAAA,QAAKsF,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClBxD,QAAQ,CAAC4K,GAAG,CAAC,CAAC2G,OAAY,CAAEpE,KAAa,gBACxChP,KAAA,QAAiBoF,SAAS,SAAAoF,MAAA,CAAU4I,OAAO,CAACC,aAAa,CAAG,UAAU,CAAG,YAAY,CAAG,CAAAhO,QAAA,eACtFvF,IAAA,QAAKsF,SAAS,+BAAAoF,MAAA,CACZ4I,OAAO,CAACC,aAAa,CACjB,YAAY,CACZ,yBAAyB,CAC5B,CAAC/I,KAAK,CAAE,CACTC,UAAU,CAAE6I,OAAO,CAACC,aAAa,CAC7B,mDAAmD,CACnD,SACN,CAAE,CAAAhO,QAAA,CACC+N,OAAO,CAACE,OAAO,CACb,CAAC,cACNxT,IAAA,QAAKsF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9B,GAAI,CAAAyK,IAAI,CAACsD,OAAO,CAACG,SAAS,CAAC,CAACP,cAAc,CAAC,CAAC,CAC1C,CAAC,GAdEhE,KAeL,CACN,CAAC,CACC,CAAC,CACI,CAAC,cACblP,IAAA,CAAC5C,KAAK,CAAC4V,MAAM,EAACxI,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cAC7DrF,KAAA,CAAC7C,UAAU,EAAAkI,QAAA,eACTvF,IAAA,CAAC7C,IAAI,CAACuS,OAAO,EACXuC,IAAI,CAAC,MAAM,CACXtC,WAAW,CAAC,sBAAsB,CAClCvJ,KAAK,CAAEvE,WAAY,CACnB+N,QAAQ,CAAGC,CAAC,EAAK/N,cAAc,CAAC+N,CAAC,CAACC,MAAM,CAAC1J,KAAK,CAAE,CAChDsN,UAAU,CAAG7D,CAAC,EAAKA,CAAC,CAAC1J,GAAG,GAAK,OAAO,EAAIsI,aAAa,CAAC,CAAE,CACxDjE,KAAK,CAAE,CAAEC,UAAU,CAAE,SAAS,CAAEuC,MAAM,CAAE,gBAAgB,CAAEV,KAAK,CAAE,MAAO,CAAE,CAC3E,CAAC,cACFtM,IAAA,CAAC/C,MAAM,EACLmP,OAAO,CAAEqC,aAAc,CACvBjE,KAAK,CAAE,CAAEC,UAAU,CAAE,mDAAmD,CAAEuC,MAAM,CAAE,MAAO,CAAE,CAAAzH,QAAA,cAE3FvF,IAAA,CAACV,IAAI,EAACkG,IAAI,CAAE,EAAG,CAAE,CAAC,CACZ,CAAC,EACC,CAAC,CACD,CAAC,EACV,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAnF,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}