/* Enhanced Job Queue Styles */

.job-queue-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 20px 0;
}

.stats-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: none;
  border-radius: 15px;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.border-left-primary {
  border-left: 4px solid #007bff !important;
}

.border-left-warning {
  border-left: 4px solid #ffc107 !important;
}

.border-left-info {
  border-left: 4px solid #17a2b8 !important;
}

.border-left-success {
  border-left: 4px solid #28a745 !important;
}

.job-card {
  transition: all 0.3s ease;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.job-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.job-card .card-header {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0 !important;
  border: none;
}

.job-card .card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 12px 12px;
}

.priority-urgent {
  animation: pulse-urgent 2s infinite;
}

@keyframes pulse-urgent {
  0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.priority-high {
  border-left: 4px solid #ffc107;
}

.priority-normal {
  border-left: 4px solid #6c757d;
}

.priority-low {
  border-left: 4px solid #28a745;
}

.job-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.job-table thead th {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.85rem;
  letter-spacing: 0.5px;
}

.job-table tbody tr {
  transition: background-color 0.2s ease;
}

.job-table tbody tr:hover {
  background-color: #f8f9fa;
}

.status-badge {
  font-size: 0.75rem;
  padding: 0.4em 0.8em;
  border-radius: 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-badge {
  font-size: 0.7rem;
  padding: 0.3em 0.6em;
  border-radius: 15px;
  font-weight: 500;
}

.action-buttons .btn {
  margin: 2px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: scale(1.05);
}

.search-controls {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  margin-bottom: 20px;
}

.filter-badge {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
  margin: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-badge:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.filter-badge.active {
  background: linear-gradient(45deg, #764ba2 0%, #667eea 100%);
  box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

.refresh-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0,123,255,0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 25px;
  font-size: 0.85rem;
  z-index: 1000;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.job-progress {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 10px;
}

.job-progress-bar {
  height: 100%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.time-ago {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
}

.file-icon {
  font-size: 1.2rem;
  margin-right: 8px;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.student-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.job-details-modal .modal-content {
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.job-details-modal .modal-header {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  border: none;
}

.job-details-modal .card {
  border: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.08);
  border-radius: 10px;
}

.job-details-modal .card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  border-radius: 10px 10px 0 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-controls {
    padding: 15px;
  }
  
  .stats-card {
    margin-bottom: 15px;
  }
  
  .job-card {
    margin-bottom: 15px;
  }
  
  .action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .action-buttons .btn {
    flex: 1;
    min-width: 80px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .job-queue-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .job-table {
    background: #343a40;
    color: white;
  }
  
  .job-card {
    background: #343a40;
    color: white;
  }
  
  .search-controls {
    background: #343a40;
    color: white;
  }
}
