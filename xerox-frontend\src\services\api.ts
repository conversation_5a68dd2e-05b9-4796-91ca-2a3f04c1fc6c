import axios from 'axios';

const API_BASE_URL = 'http://localhost:5007/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Print Job API
export const printJobApi = {
  getStudentJobs: () => api.get('/printjob/student'),
  getXeroxCenterJobs: () => api.get('/printjob/xerox-center'),
  updateJobStatus: (jobId: number, status: string) =>
    api.put(`/printjob/${jobId}/status`, { status }),
  confirmJob: (jobId: number) =>
    api.put(`/printjob/${jobId}/confirm`),
  setJobQuote: (jobId: number, cost: number, estimatedCompletionTime: string, notes?: string) =>
    api.put(`/printjob/${jobId}/quote`, { cost, estimatedCompletionTime, notes }),
};

// Xerox Center API
export const xeroxCenterApi = {
  getAll: () => api.get('/xeroxcenter'),
  getById: (id: number) => api.get(`/xeroxcenter/${id}`),
  getDashboardStats: () => api.get('/xeroxcenter/dashboard-stats'),
  updateProfile: (data: any) => api.put('/xeroxcenter/profile', data),
};

// File Upload API
export const fileUploadApi = {
  uploadFile: (formData: FormData) => 
    api.post('/fileupload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),
  getStudentFiles: () => api.get('/fileupload/student'),
  deleteFile: (fileId: number) => api.delete(`/fileupload/${fileId}`),
};

// Auth API (already exists in AuthContext, but adding here for completeness)
export const authApi = {
  login: (email: string, password: string) => 
    api.post('/auth/login', { email, password }),
  registerStudent: (data: any) => 
    api.post('/auth/register/student', data),
  registerXeroxCenter: (data: any) => 
    api.post('/auth/register/xerox-center', data),
  getCurrentUser: () => api.get('/auth/me'),
};

export default api;
