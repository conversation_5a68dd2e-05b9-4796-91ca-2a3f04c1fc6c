{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport Register from './components/Register';\nimport XeroxCenterDashboard from './components/XeroxCenterDashboard';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  userType\n}) => {\n  _s();\n  const {\n    user,\n    token\n  } = useAuth();\n  if (!token || !user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 12\n    }, this);\n  }\n  if (userType && user.userType !== userType) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"VFxct4SzMpAPlFX99BNTgx+1HA8=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst AppRoutes = () => {\n  _s2();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 37\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/register\",\n      element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 40\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: (user === null || user === void 0 ? void 0 : user.userType) === 'Student' ? /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 47\n          }, this) : /*#__PURE__*/_jsxDEV(XeroxCenterDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 70\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/student-dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        userType: \"Student\",\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(StudentDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/xerox-dashboard\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        userType: \"XeroxCenter\",\n        children: /*#__PURE__*/_jsxDEV(Layout, {\n          children: /*#__PURE__*/_jsxDEV(XeroxCenterDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/dashboard\",\n        replace: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 32\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s2(AppRoutes, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c2 = AppRoutes;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"App\",\n          children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"AppRoutes\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "ThemeProvider", "Register", "XeroxCenterDashboard", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "userType", "_s", "user", "token", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AppRoutes", "_s2", "path", "element", "<PERSON><PERSON>", "Layout", "StudentDashboard", "_c2", "App", "className", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { ThemeProvider } from './contexts/ThemeContext';\nimport AceternityLayout from './components/ui/AceternityLayout';\nimport AceternityLogin from './components/ui/AceternityLogin';\nimport Register from './components/Register';\nimport SimpleAceternityStudentDashboard from './components/ui/SimpleAceternityStudentDashboard';\nimport XeroxCenterDashboard from './components/XeroxCenterDashboard';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport './App.css';\n\nconst ProtectedRoute: React.FC<{ children: React.ReactNode; userType?: string }> = ({\n  children,\n  userType\n}) => {\n  const { user, token } = useAuth();\n\n  if (!token || !user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n\n  if (userType && user.userType !== userType) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  return <>{children}</>;\n};\n\nconst AppRoutes: React.FC = () => {\n  const { user } = useAuth();\n\n  return (\n    <Routes>\n      <Route path=\"/login\" element={<Login />} />\n      <Route path=\"/register\" element={<Register />} />\n      <Route\n        path=\"/dashboard\"\n        element={\n          <ProtectedRoute>\n            <Layout>\n              {user?.userType === 'Student' ? <StudentDashboard /> : <XeroxCenterDashboard />}\n            </Layout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/student-dashboard\"\n        element={\n          <ProtectedRoute userType=\"Student\">\n            <Layout>\n              <StudentDashboard />\n            </Layout>\n          </ProtectedRoute>\n        }\n      />\n      <Route\n        path=\"/xerox-dashboard\"\n        element={\n          <ProtectedRoute userType=\"XeroxCenter\">\n            <Layout>\n              <XeroxCenterDashboard />\n            </Layout>\n          </ProtectedRoute>\n        }\n      />\n      <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n    </Routes>\n  );\n};\n\nfunction App() {\n  return (\n    <ThemeProvider>\n      <AuthProvider>\n        <Router>\n          <div className=\"App\">\n            <AppRoutes />\n          </div>\n        </Router>\n      </AuthProvider>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,SAASC,aAAa,QAAQ,yBAAyB;AAGvD,OAAOC,QAAQ,MAAM,uBAAuB;AAE5C,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAO,sCAAsC;AAC7C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,MAAMC,cAA0E,GAAGA,CAAC;EAClFC,QAAQ;EACRC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EAEjC,IAAI,CAACa,KAAK,IAAI,CAACD,IAAI,EAAE;IACnB,oBAAOP,OAAA,CAACP,QAAQ;MAACgB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,IAAIT,QAAQ,IAAIE,IAAI,CAACF,QAAQ,KAAKA,QAAQ,EAAE;IAC1C,oBAAOL,OAAA,CAACP,QAAQ;MAACgB,EAAE,EAAC,YAAY;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C;EAEA,oBAAOd,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACE,EAAA,CAfIH,cAA0E;EAAA,QAItDR,OAAO;AAAA;AAAAoB,EAAA,GAJ3BZ,cAA0E;AAiBhF,MAAMa,SAAmB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,MAAM;IAAEV;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAE1B,oBACEK,OAAA,CAACT,MAAM;IAAAa,QAAA,gBACLJ,OAAA,CAACR,KAAK;MAAC0B,IAAI,EAAC,QAAQ;MAACC,OAAO,eAAEnB,OAAA,CAACoB,KAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAC3Cd,OAAA,CAACR,KAAK;MAAC0B,IAAI,EAAC,WAAW;MAACC,OAAO,eAAEnB,OAAA,CAACH,QAAQ;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjDd,OAAA,CAACR,KAAK;MACJ0B,IAAI,EAAC,YAAY;MACjBC,OAAO,eACLnB,OAAA,CAACG,cAAc;QAAAC,QAAA,eACbJ,OAAA,CAACqB,MAAM;UAAAjB,QAAA,EACJ,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEF,QAAQ,MAAK,SAAS,gBAAGL,OAAA,CAACsB,gBAAgB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGd,OAAA,CAACF,oBAAoB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFd,OAAA,CAACR,KAAK;MACJ0B,IAAI,EAAC,oBAAoB;MACzBC,OAAO,eACLnB,OAAA,CAACG,cAAc;QAACE,QAAQ,EAAC,SAAS;QAAAD,QAAA,eAChCJ,OAAA,CAACqB,MAAM;UAAAjB,QAAA,eACLJ,OAAA,CAACsB,gBAAgB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFd,OAAA,CAACR,KAAK;MACJ0B,IAAI,EAAC,kBAAkB;MACvBC,OAAO,eACLnB,OAAA,CAACG,cAAc;QAACE,QAAQ,EAAC,aAAa;QAAAD,QAAA,eACpCJ,OAAA,CAACqB,MAAM;UAAAjB,QAAA,eACLJ,OAAA,CAACF,oBAAoB;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACFd,OAAA,CAACR,KAAK;MAAC0B,IAAI,EAAC,GAAG;MAACC,OAAO,eAAEnB,OAAA,CAACP,QAAQ;QAACgB,EAAE,EAAC,YAAY;QAACC,OAAO;MAAA;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AAEb,CAAC;AAACG,GAAA,CAxCID,SAAmB;EAAA,QACNrB,OAAO;AAAA;AAAA4B,GAAA,GADpBP,SAAmB;AA0CzB,SAASQ,GAAGA,CAAA,EAAG;EACb,oBACExB,OAAA,CAACJ,aAAa;IAAAQ,QAAA,eACZJ,OAAA,CAACN,YAAY;MAAAU,QAAA,eACXJ,OAAA,CAACV,MAAM;QAAAc,QAAA,eACLJ,OAAA;UAAKyB,SAAS,EAAC,KAAK;UAAArB,QAAA,eAClBJ,OAAA,CAACgB,SAAS;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB;AAACY,GAAA,GAZQF,GAAG;AAcZ,eAAeA,GAAG;AAAC,IAAAT,EAAA,EAAAQ,GAAA,EAAAG,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAJ,GAAA;AAAAI,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}