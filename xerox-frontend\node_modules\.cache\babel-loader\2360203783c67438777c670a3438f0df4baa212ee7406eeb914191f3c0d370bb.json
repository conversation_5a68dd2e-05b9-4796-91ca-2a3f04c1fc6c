{"ast": null, "code": "function isNullish(v) {\n  return v == null;\n}\nexport { isNullish };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "v"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/value/types/utils/is-nullish.mjs"], "sourcesContent": ["function isNullish(v) {\n    return v == null;\n}\n\nexport { isNullish };\n"], "mappings": "AAAA,SAASA,SAASA,CAACC,CAAC,EAAE;EAClB,OAAOA,CAAC,IAAI,IAAI;AACpB;AAEA,SAASD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}