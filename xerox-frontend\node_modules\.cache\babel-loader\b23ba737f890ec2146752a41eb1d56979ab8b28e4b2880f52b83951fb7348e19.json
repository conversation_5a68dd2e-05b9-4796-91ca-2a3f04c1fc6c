{"ast": null, "code": "import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler) {\n  let capture = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const documentTarget = useCallback(() => document, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}", "map": {"version": 3, "names": ["useEventListener", "useCallback", "useGlobalListener", "event", "handler", "capture", "arguments", "length", "undefined", "documentTarget", "document"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useGlobalListener.js"], "sourcesContent": ["import useEventListener from './useEventListener';\nimport { useCallback } from 'react';\n/**\n * Attaches an event handler outside directly to the `document`,\n * bypassing the react synthetic event system.\n *\n * ```ts\n * useGlobalListener('keydown', (event) => {\n *  console.log(event.key)\n * })\n * ```\n *\n * @param event The DOM event name\n * @param handler An event handler\n * @param capture Whether or not to listen during the capture event phase\n */\nexport default function useGlobalListener(event, handler, capture = false) {\n  const documentTarget = useCallback(() => document, []);\n  return useEventListener(documentTarget, event, handler, capture);\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,WAAW,QAAQ,OAAO;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,EAAmB;EAAA,IAAjBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACvE,MAAMG,cAAc,GAAGR,WAAW,CAAC,MAAMS,QAAQ,EAAE,EAAE,CAAC;EACtD,OAAOV,gBAAgB,CAACS,cAAc,EAAEN,KAAK,EAAEC,OAAO,EAAEC,OAAO,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}