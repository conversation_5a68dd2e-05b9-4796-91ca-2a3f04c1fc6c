{"ast": null, "code": "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "map": {"version": 3, "names": ["max", "Math", "min", "round"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@popperjs/core/lib/utils/math.js"], "sourcesContent": ["export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAGC,IAAI,CAACD,GAAG;AACzB,OAAO,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAG;AACzB,OAAO,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}