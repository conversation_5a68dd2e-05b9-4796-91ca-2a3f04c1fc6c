{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:5007/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add request interceptor to include auth token\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add response interceptor to handle errors\napi.interceptors.response.use(response => response, error => {\n  var _error$response;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    // Token expired or invalid\n    localStorage.removeItem('token');\n    window.location.href = '/login';\n  }\n  return Promise.reject(error);\n});\n\n// Print Job API\nexport const printJobApi = {\n  getStudentJobs: () => api.get('/printjob/student'),\n  getXeroxCenterJobs: () => api.get('/printjob/xerox-center'),\n  updateJobStatus: (jobId, status) => api.put(`/printjob/${jobId}/status`, {\n    status\n  }),\n  confirmJob: jobId => api.put(`/printjob/${jobId}/confirm`),\n  setJobQuote: (jobId, cost, estimatedCompletionTime, notes) => api.put(`/printjob/${jobId}/quote`, {\n    cost,\n    estimatedCompletionTime,\n    notes\n  })\n};\n\n// Xerox Center API\nexport const xeroxCenterApi = {\n  getAll: () => api.get('/xeroxcenter'),\n  getById: id => api.get(`/xeroxcenter/${id}`),\n  getDashboardStats: () => api.get('/xeroxcenter/dashboard-stats'),\n  updateProfile: data => api.put('/xeroxcenter/profile', data)\n};\n\n// File Upload API\nexport const fileUploadApi = {\n  uploadFile: formData => api.post('/fileupload', formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  }),\n  getStudentFiles: () => api.get('/fileupload/student'),\n  deleteFile: fileId => api.delete(`/fileupload/${fileId}`)\n};\n\n// Auth API (already exists in AuthContext, but adding here for completeness)\nexport const authApi = {\n  login: (email, password) => api.post('/auth/login', {\n    email,\n    password\n  }),\n  registerStudent: data => api.post('/auth/register/student', data),\n  registerXeroxCenter: data => api.post('/auth/register/xerox-center', data),\n  getCurrentUser: () => api.get('/auth/me')\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "status", "removeItem", "window", "location", "href", "printJobApi", "getStudentJobs", "get", "getXeroxCenterJobs", "updateJobStatus", "jobId", "put", "<PERSON><PERSON>ob", "setJobQuote", "cost", "estimatedCompletionTime", "notes", "xeroxCenterApi", "getAll", "getById", "id", "getDashboardStats", "updateProfile", "data", "fileUploadApi", "uploadFile", "formData", "post", "getStudentFiles", "deleteFile", "fileId", "delete", "authApi", "login", "email", "password", "registerStudent", "registerXeroxCenter", "getCurrentUser"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = 'http://localhost:5007/api';\n\n// Create axios instance with default config\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Add request interceptor to include auth token\napi.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Add response interceptor to handle errors\napi.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    if (error.response?.status === 401) {\n      // Token expired or invalid\n      localStorage.removeItem('token');\n      window.location.href = '/login';\n    }\n    return Promise.reject(error);\n  }\n);\n\n// Print Job API\nexport const printJobApi = {\n  getStudentJobs: () => api.get('/printjob/student'),\n  getXeroxCenterJobs: () => api.get('/printjob/xerox-center'),\n  updateJobStatus: (jobId: number, status: string) =>\n    api.put(`/printjob/${jobId}/status`, { status }),\n  confirmJob: (jobId: number) =>\n    api.put(`/printjob/${jobId}/confirm`),\n  setJobQuote: (jobId: number, cost: number, estimatedCompletionTime: string, notes?: string) =>\n    api.put(`/printjob/${jobId}/quote`, { cost, estimatedCompletionTime, notes }),\n};\n\n// Xerox Center API\nexport const xeroxCenterApi = {\n  getAll: () => api.get('/xeroxcenter'),\n  getById: (id: number) => api.get(`/xeroxcenter/${id}`),\n  getDashboardStats: () => api.get('/xeroxcenter/dashboard-stats'),\n  updateProfile: (data: any) => api.put('/xeroxcenter/profile', data),\n};\n\n// File Upload API\nexport const fileUploadApi = {\n  uploadFile: (formData: FormData) => \n    api.post('/fileupload', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data',\n      },\n    }),\n  getStudentFiles: () => api.get('/fileupload/student'),\n  deleteFile: (fileId: number) => api.delete(`/fileupload/${fileId}`),\n};\n\n// Auth API (already exists in AuthContext, but adding here for completeness)\nexport const authApi = {\n  login: (email: string, password: string) => \n    api.post('/auth/login', { email, password }),\n  registerStudent: (data: any) => \n    api.post('/auth/register/student', data),\n  registerXeroxCenter: (data: any) => \n    api.post('/auth/register/xerox-center', data),\n  getCurrentUser: () => api.get('/auth/me'),\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;;AAEhD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,GAAG,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAKA,QAAQ,EACrBH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACT,IAAI,EAAAA,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;IAClC;IACAR,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;IAChCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;EACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMU,WAAW,GAAG;EACzBC,cAAc,EAAEA,CAAA,KAAMvB,GAAG,CAACwB,GAAG,CAAC,mBAAmB,CAAC;EAClDC,kBAAkB,EAAEA,CAAA,KAAMzB,GAAG,CAACwB,GAAG,CAAC,wBAAwB,CAAC;EAC3DE,eAAe,EAAEA,CAACC,KAAa,EAAEV,MAAc,KAC7CjB,GAAG,CAAC4B,GAAG,CAAC,aAAaD,KAAK,SAAS,EAAE;IAAEV;EAAO,CAAC,CAAC;EAClDY,UAAU,EAAGF,KAAa,IACxB3B,GAAG,CAAC4B,GAAG,CAAC,aAAaD,KAAK,UAAU,CAAC;EACvCG,WAAW,EAAEA,CAACH,KAAa,EAAEI,IAAY,EAAEC,uBAA+B,EAAEC,KAAc,KACxFjC,GAAG,CAAC4B,GAAG,CAAC,aAAaD,KAAK,QAAQ,EAAE;IAAEI,IAAI;IAAEC,uBAAuB;IAAEC;EAAM,CAAC;AAChF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,MAAM,EAAEA,CAAA,KAAMnC,GAAG,CAACwB,GAAG,CAAC,cAAc,CAAC;EACrCY,OAAO,EAAGC,EAAU,IAAKrC,GAAG,CAACwB,GAAG,CAAC,gBAAgBa,EAAE,EAAE,CAAC;EACtDC,iBAAiB,EAAEA,CAAA,KAAMtC,GAAG,CAACwB,GAAG,CAAC,8BAA8B,CAAC;EAChEe,aAAa,EAAGC,IAAS,IAAKxC,GAAG,CAAC4B,GAAG,CAAC,sBAAsB,EAAEY,IAAI;AACpE,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,UAAU,EAAGC,QAAkB,IAC7B3C,GAAG,CAAC4C,IAAI,CAAC,aAAa,EAAED,QAAQ,EAAE;IAChCxC,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;EACJ0C,eAAe,EAAEA,CAAA,KAAM7C,GAAG,CAACwB,GAAG,CAAC,qBAAqB,CAAC;EACrDsB,UAAU,EAAGC,MAAc,IAAK/C,GAAG,CAACgD,MAAM,CAAC,eAAeD,MAAM,EAAE;AACpE,CAAC;;AAED;AACA,OAAO,MAAME,OAAO,GAAG;EACrBC,KAAK,EAAEA,CAACC,KAAa,EAAEC,QAAgB,KACrCpD,GAAG,CAAC4C,IAAI,CAAC,aAAa,EAAE;IAAEO,KAAK;IAAEC;EAAS,CAAC,CAAC;EAC9CC,eAAe,EAAGb,IAAS,IACzBxC,GAAG,CAAC4C,IAAI,CAAC,wBAAwB,EAAEJ,IAAI,CAAC;EAC1Cc,mBAAmB,EAAGd,IAAS,IAC7BxC,GAAG,CAAC4C,IAAI,CAAC,6BAA6B,EAAEJ,IAAI,CAAC;EAC/Ce,cAAc,EAAEA,CAAA,KAAMvD,GAAG,CAACwB,GAAG,CAAC,UAAU;AAC1C,CAAC;AAED,eAAexB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}