{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\AceternityStudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Form, Modal, InputGroup, Nav, Table, ProgressBar } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Upload, FileText, Clock, CheckCircle, DollarSign, Download, MessageCircle, Eye, Activity, Printer, Star, History, Settings, User, CreditCard, Bell, Search, TrendingUp, BarChart3, PieChart, Target, Award, Heart, RefreshCw, AlertCircle, CheckCircle2, XCircle, Send, Phone, MapPin as Location } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AceternityStudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [favoritesCenters, setFavoritesCenters] = useState([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [selectedCenter, setSelectedCenter] = useState(null);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('All');\n  const [sortBy, setSortBy] = useState('created');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4',\n    priority: 'Normal'\n  });\n  const [userSettings, setUserSettings] = useState({\n    notifications: {\n      email: true,\n      push: true,\n      sms: false\n    },\n    preferences: {\n      defaultPrintType: 'Print',\n      defaultColorType: 'BlackWhite',\n      defaultPaperSize: 'A4',\n      autoConfirmQuotes: false\n    },\n    profile: {\n      phone: '',\n      address: '',\n      university: '',\n      studentId: ''\n    }\n  });\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchData = async () => {\n    setIsRefreshing(true);\n    try {\n      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([printJobApi.getStudentJobs(), xeroxCenterApi.getAll()]);\n      setPrintJobs(printJobsResponse.data || []);\n      setXeroxCenters(xeroxCentersResponse.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setPrintJobs([]);\n      setXeroxCenters([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        bg: 'secondary',\n        icon: Clock\n      },\n      'UnderReview': {\n        bg: 'info',\n        icon: Eye\n      },\n      'Quoted': {\n        bg: 'warning',\n        icon: DollarSign\n      },\n      'WaitingConfirmation': {\n        bg: 'warning',\n        icon: AlertCircle\n      },\n      'Confirmed': {\n        bg: 'info',\n        icon: CheckCircle\n      },\n      'InProgress': {\n        bg: 'primary',\n        icon: Activity\n      },\n      'Completed': {\n        bg: 'success',\n        icon: CheckCircle2\n      },\n      'Delivered': {\n        bg: 'success',\n        icon: CheckCircle2\n      },\n      'Rejected': {\n        bg: 'danger',\n        icon: XCircle\n      },\n      'Cancelled': {\n        bg: 'secondary',\n        icon: XCircle\n      }\n    };\n    const config = statusConfig[status] || {\n      bg: 'secondary',\n      icon: Clock\n    };\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.bg,\n      className: \"d-flex align-items-center gap-1 px-3 py-2\",\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 7\n    }, this);\n  };\n  const getPriorityBadge = priority => {\n    const priorityConfig = {\n      'Low': {\n        bg: 'success',\n        icon: '🟢'\n      },\n      'Normal': {\n        bg: 'secondary',\n        icon: '🔵'\n      },\n      'High': {\n        bg: 'warning',\n        icon: '🟡'\n      },\n      'Urgent': {\n        bg: 'danger',\n        icon: '🔴'\n      }\n    };\n    const config = priorityConfig[priority] || {\n      bg: 'secondary',\n      icon: '🔵'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.bg,\n      className: \"px-2 py-1\",\n      children: [config.icon, \" \", priority]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this);\n  };\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      Object.entries(uploadData).forEach(([key, value]) => {\n        formData.append(key, value.toString());\n      });\n      await fileUploadApi.uploadFile(formData);\n      await fetchData();\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4',\n        priority: 'Normal'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n  const handleViewJob = job => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n  const handleOpenChat = async job => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data || []);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n  const handleDownloadFile = async (jobId, fileName) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n  const handleConfirmJob = async jobId => {\n    try {\n      await printJobApi.confirmJob(jobId);\n      await fetchData();\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n  const toggleFavorite = centerId => {\n    setFavoritesCenters(prev => prev.includes(centerId) ? prev.filter(id => id !== centerId) : [...prev, centerId]);\n  };\n  const filteredJobs = printJobs.filter(job => {\n    const matchesSearch = searchTerm === '' || job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) || job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) || job.xeroxCenterName.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesStatus = filterStatus === 'All' || job.status === filterStatus;\n    return matchesSearch && matchesStatus;\n  });\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const pendingJobs = printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted'].includes(job.status)).length;\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  const floatingShapes = Array.from({\n    length: 8\n  }, (_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"position-absolute rounded-circle\",\n    style: {\n      background: `linear-gradient(135deg, ${i % 3 === 0 ? '#667eea' : i % 3 === 1 ? '#764ba2' : '#f093fb'}, ${i % 3 === 0 ? '#764ba2' : i % 3 === 1 ? '#f093fb' : '#f5576c'})`,\n      width: `${Math.random() * 60 + 20}px`,\n      height: `${Math.random() * 60 + 20}px`,\n      left: `${Math.random() * 100}%`,\n      top: `${Math.random() * 100}%`,\n      opacity: 0.1,\n      zIndex: 0\n    },\n    animate: {\n      x: [0, Math.random() * 100 - 50],\n      y: [0, Math.random() * 100 - 50],\n      rotate: [0, 360],\n      scale: [1, 1.2, 1]\n    },\n    transition: {\n      duration: Math.random() * 20 + 10,\n      repeat: Infinity,\n      repeatType: \"reverse\",\n      ease: \"easeInOut\"\n    }\n  }, i, false, {\n    fileName: _jsxFileName,\n    lineNumber: 327,\n    columnNumber: 5\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen position-relative\",\n    style: {\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute w-100 h-100 overflow-hidden\",\n      children: [floatingShapes, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute w-100 h-100\",\n        style: {\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 362,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"position-relative py-4\",\n      style: {\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          variants: itemVariants,\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: [0, 360],\n              scale: [1, 1.1, 1]\n            },\n            transition: {\n              duration: 4,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"d-inline-flex align-items-center justify-content-center mb-3\",\n            style: {\n              width: '80px',\n              height: '80px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '20px',\n              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(User, {\n              className: \"text-white\",\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-white fw-bold mb-2\",\n            style: {\n              fontSize: '2.5rem'\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white-50 fs-5 mb-4\",\n            children: \"Manage your printing jobs and explore xerox centers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-center gap-3 flex-wrap\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"lg\",\n                onClick: () => setShowUploadModal(true),\n                className: \"px-5 py-3 rounded-4 border-0 fw-semibold\",\n                style: {\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  backdropFilter: 'blur(20px)',\n                  color: '#fff',\n                  boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"me-2\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this), \"Upload Files\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-light\",\n                size: \"lg\",\n                onClick: fetchData,\n                disabled: isRefreshing,\n                className: \"px-4 py-3 rounded-4 fw-semibold\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `me-2 ${isRefreshing ? 'spin' : ''}`,\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 19\n                }, this), \"Refresh\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: itemVariants,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"border-0 shadow-lg\",\n            style: {\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-2\",\n              children: /*#__PURE__*/_jsxDEV(Nav, {\n                variant: \"pills\",\n                className: \"justify-content-center flex-wrap\",\n                children: [{\n                  key: 'dashboard',\n                  label: 'Dashboard',\n                  icon: BarChart3\n                }, {\n                  key: 'jobs',\n                  label: 'My Jobs',\n                  icon: FileText\n                }, {\n                  key: 'centers',\n                  label: 'Xerox Centers',\n                  icon: Printer\n                }, {\n                  key: 'history',\n                  label: 'History',\n                  icon: History\n                }, {\n                  key: 'favorites',\n                  label: 'Favorites',\n                  icon: Heart\n                }, {\n                  key: 'analytics',\n                  label: 'Analytics',\n                  icon: PieChart\n                }, {\n                  key: 'settings',\n                  label: 'Settings',\n                  icon: Settings\n                }].map(tab => {\n                  const IconComponent = tab.icon;\n                  return /*#__PURE__*/_jsxDEV(Nav.Item, {\n                    className: \"m-1\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === tab.key,\n                        onClick: () => setActiveTab(tab.key),\n                        className: `px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${activeTab === tab.key ? 'text-primary' : 'text-white-50'}`,\n                        style: {\n                          background: activeTab === tab.key ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                          border: 'none',\n                          transition: 'all 0.3s ease',\n                          boxShadow: activeTab === tab.key ? '0 10px 30px rgba(255, 255, 255, 0.2)' : 'none'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 18,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 477,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"d-none d-md-inline\",\n                          children: tab.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 478,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 457,\n                      columnNumber: 25\n                    }, this)\n                  }, tab.key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 456,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            exit: {\n              opacity: 0,\n              y: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(DashboardTab, {\n              printJobs: printJobs,\n              xeroxCenters: xeroxCenters,\n              totalSpent: totalSpent,\n              inProgressJobs: inProgressJobs,\n              completedJobs: completedJobs,\n              pendingJobs: pendingJobs,\n              onViewJob: handleViewJob,\n              onDownloadFile: handleDownloadFile,\n              onOpenChat: handleOpenChat,\n              onConfirmJob: handleConfirmJob,\n              getStatusBadge: getStatusBadge,\n              getPriorityBadge: getPriorityBadge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 17\n            }, this), activeTab === 'jobs' && /*#__PURE__*/_jsxDEV(JobsTab, {\n              jobs: filteredJobs,\n              searchTerm: searchTerm,\n              setSearchTerm: setSearchTerm,\n              filterStatus: filterStatus,\n              setFilterStatus: setFilterStatus,\n              sortBy: sortBy,\n              setSortBy: setSortBy,\n              onViewJob: handleViewJob,\n              onDownloadFile: handleDownloadFile,\n              onOpenChat: handleOpenChat,\n              onConfirmJob: handleConfirmJob,\n              getStatusBadge: getStatusBadge,\n              getPriorityBadge: getPriorityBadge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this), activeTab === 'centers' && /*#__PURE__*/_jsxDEV(CentersTab, {\n              centers: xeroxCenters,\n              favoritesCenters: favoritesCenters,\n              onToggleFavorite: toggleFavorite,\n              onSelectCenter: setSelectedCenter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this), activeTab === 'history' && /*#__PURE__*/_jsxDEV(HistoryTab, {\n              jobs: printJobs.filter(job => ['Completed', 'Delivered', 'Cancelled', 'Rejected'].includes(job.status)),\n              onViewJob: handleViewJob,\n              getStatusBadge: getStatusBadge\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this), activeTab === 'favorites' && /*#__PURE__*/_jsxDEV(FavoritesTab, {\n              centers: xeroxCenters.filter(center => favoritesCenters.includes(center.id)),\n              onToggleFavorite: toggleFavorite,\n              onSelectCenter: setSelectedCenter\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this), activeTab === 'analytics' && /*#__PURE__*/_jsxDEV(AnalyticsTab, {\n              jobs: printJobs,\n              totalSpent: totalSpent\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), activeTab === 'settings' && /*#__PURE__*/_jsxDEV(SettingsTab, {\n              userSettings: userSettings,\n              setUserSettings: setUserSettings\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this)]\n          }, activeTab, true, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 367,\n      columnNumber: 7\n    }, this), showUploadModal && /*#__PURE__*/_jsxDEV(UploadModal, {\n      show: showUploadModal,\n      onHide: () => setShowUploadModal(false),\n      uploadData: uploadData,\n      setUploadData: setUploadData,\n      selectedFile: selectedFile,\n      setSelectedFile: setSelectedFile,\n      xeroxCenters: xeroxCenters,\n      onUpload: handleFileUpload\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 578,\n      columnNumber: 9\n    }, this), showViewModal && /*#__PURE__*/_jsxDEV(ViewJobModal, {\n      show: showViewModal,\n      onHide: () => setShowViewModal(false),\n      job: selectedJob,\n      getStatusBadge: getStatusBadge,\n      getPriorityBadge: getPriorityBadge\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 9\n    }, this), showChatModal && /*#__PURE__*/_jsxDEV(ChatModal, {\n      show: showChatModal,\n      onHide: () => setShowChatModal(false),\n      job: selectedJob,\n      messages: messages,\n      chatMessage: chatMessage,\n      setChatMessage: setChatMessage,\n      onSendMessage: handleSendMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 601,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 355,\n    columnNumber: 5\n  }, this);\n};\n\n// Dashboard Tab Component\n_s(AceternityStudentDashboard, \"CqFiH+/NGWBsy+O8qBLY6tvLtrk=\", false, function () {\n  return [useAuth];\n});\n_c = AceternityStudentDashboard;\nconst DashboardTab = ({\n  printJobs,\n  xeroxCenters,\n  totalSpent,\n  inProgressJobs,\n  completedJobs,\n  pendingJobs,\n  onViewJob,\n  onDownloadFile,\n  onOpenChat,\n  onConfirmJob,\n  getStatusBadge,\n  getPriorityBadge\n}) => {\n  return /*#__PURE__*/_jsxDEV(Row, {\n    className: \"g-4\",\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      md: 3,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        whileHover: {\n          y: -5,\n          scale: 1.02\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100 border-0 shadow-lg\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '15px'\n                },\n                children: /*#__PURE__*/_jsxDEV(FileText, {\n                  className: \"text-white\",\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-white-50 mb-1\",\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"fw-bold text-white\",\n              children: printJobs.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 647,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-success\",\n              children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                size: 12,\n                className: \"me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 649,\n                columnNumber: 17\n              }, this), \"+12% vs last month\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 623,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      md: 3,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.1\n        },\n        whileHover: {\n          y: -5,\n          scale: 1.02\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100 border-0 shadow-lg\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  borderRadius: '15px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Clock, {\n                  className: \"text-white\",\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 670,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-white-50 mb-1\",\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 680,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"fw-bold text-white\",\n              children: inProgressJobs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 681,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-warning\",\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                size: 12,\n                className: \"me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 683,\n                columnNumber: 17\n              }, this), \"Active jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 664,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      md: 3,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.2\n        },\n        whileHover: {\n          y: -5,\n          scale: 1.02\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100 border-0 shadow-lg\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                  borderRadius: '15px'\n                },\n                children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                  className: \"text-white\",\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 711,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 704,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-white-50 mb-1\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 714,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"fw-bold text-white\",\n              children: completedJobs\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 715,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-success\",\n              children: [/*#__PURE__*/_jsxDEV(Award, {\n                size: 12,\n                className: \"me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 717,\n                columnNumber: 17\n              }, this), \"Finished jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 698,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 692,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 691,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      md: 3,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        transition: {\n          duration: 0.5,\n          delay: 0.3\n        },\n        whileHover: {\n          y: -5,\n          scale: 1.02\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100 border-0 shadow-lg\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"text-center p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                  borderRadius: '15px'\n                },\n                children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                  className: \"text-white\",\n                  size: 24\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 738,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-white-50 mb-1\",\n              children: \"Total Spent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"fw-bold text-white\",\n              children: [\"$\", totalSpent.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 749,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-info\",\n              children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                size: 12,\n                className: \"me-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), \"This month\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 750,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 726,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 725,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      lg: 8,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg h-100\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"fw-bold mb-0 text-white\",\n              children: [/*#__PURE__*/_jsxDEV(Activity, {\n                className: \"me-2\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 17\n              }, this), \"Recent Jobs\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              bg: \"light\",\n              text: \"dark\",\n              className: \"px-3 py-2\",\n              children: [printJobs.length, \" total\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: printJobs.slice(0, 5).map((job, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              animate: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              className: \"p-3 rounded-3\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: '1px solid rgba(255, 255, 255, 0.2)'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center justify-content-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"me-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-inline-flex align-items-center justify-content-center\",\n                      style: {\n                        width: '50px',\n                        height: '50px',\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        borderRadius: '12px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FileText, {\n                        className: \"text-white\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 795,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"fw-semibold mb-1 text-white\",\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-white-50 small mb-1\",\n                      children: job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 806,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-white-50\",\n                      children: job.xeroxCenterName\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 809,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 793,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-end me-3\",\n                    children: [getStatusBadge(job.status), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"fw-semibold text-success mt-1\",\n                      children: [\"$\", job.cost.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 814,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-light\",\n                      size: \"sm\",\n                      onClick: () => onDownloadFile(job.id, job.fileName),\n                      style: {\n                        borderRadius: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Download, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 830,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 824,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-light\",\n                      size: \"sm\",\n                      onClick: () => onViewJob(job),\n                      style: {\n                        borderRadius: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Eye, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 839,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 833,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-light\",\n                      size: \"sm\",\n                      onClick: () => onOpenChat(job),\n                      style: {\n                        borderRadius: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 848,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 842,\n                      columnNumber: 27\n                    }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"success\",\n                      size: \"sm\",\n                      onClick: () => onConfirmJob(job.id),\n                      style: {\n                        borderRadius: '8px'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 858,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 852,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 813,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 792,\n                columnNumber: 21\n              }, this)\n            }, job.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 781,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              scale: 1\n            },\n            className: \"text-center py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-inline-flex align-items-center justify-content-center\",\n                style: {\n                  width: '80px',\n                  height: '80px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '20px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"text-white\",\n                  size: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              className: \"fw-semibold mb-2 text-white\",\n              children: \"No jobs yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white-50 mb-4\",\n              children: \"Upload your first file to get started!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 868,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 760,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg h-100\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"fw-bold mb-0 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(Target, {\n              className: \"me-2\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 15\n            }, this), \"Quick Stats\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 899,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 898,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between p-3 rounded-3\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3\",\n                  style: {\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Clock, {\n                    className: \"text-white\",\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 919,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0 text-white\",\n                    children: \"Pending\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-white-50\",\n                    children: \"Awaiting review\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0 text-warning\",\n                children: pendingJobs\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between p-3 rounded-3\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3\",\n                  style: {\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Printer, {\n                    className: \"text-white\",\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 933,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0 text-white\",\n                    children: \"Centers\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 945,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-white-50\",\n                    children: \"Available now\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 932,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0 text-info\",\n                children: xeroxCenters.filter(c => c.isActive).length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between p-3 rounded-3\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3\",\n                  style: {\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Star, {\n                    className: \"text-white\",\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-0 text-white\",\n                    children: \"Avg Rating\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-white-50\",\n                    children: \"Your experience\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 967,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 955,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-0 text-success\",\n                children: \"4.8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 952,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 905,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 904,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 893,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 892,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 621,\n    columnNumber: 5\n  }, this);\n};\n\n// Jobs Tab Component\n_c2 = DashboardTab;\nconst JobsTab = ({\n  jobs,\n  searchTerm,\n  setSearchTerm,\n  filterStatus,\n  setFilterStatus,\n  sortBy,\n  setSortBy,\n  onViewJob,\n  onDownloadFile,\n  onOpenChat,\n  onConfirmJob,\n  getStatusBadge,\n  getPriorityBadge\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"border-0 shadow-lg\",\n    style: {\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"border-0 bg-transparent\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center justify-content-between mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"fw-bold mb-0 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(FileText, {\n            className: \"me-2\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 996,\n            columnNumber: 13\n          }, this), \"All Jobs\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 995,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Badge, {\n          bg: \"light\",\n          text: \"dark\",\n          className: \"px-3 py-2\",\n          children: [jobs.length, \" jobs\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 999,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 994,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        className: \"g-3\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(InputGroup, {\n            children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none'\n              },\n              children: /*#__PURE__*/_jsxDEV(Search, {\n                className: \"text-white-50\",\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1009,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1008,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n              placeholder: \"Search jobs...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none',\n                color: '#fff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1007,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1006,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: filterStatus,\n            onChange: e => setFilterStatus(e.target.value),\n            style: {\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              color: '#fff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"All\",\n              children: \"All Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1034,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Requested\",\n              children: \"Requested\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1035,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"UnderReview\",\n              children: \"Under Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1036,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Quoted\",\n              children: \"Quoted\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1037,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Confirmed\",\n              children: \"Confirmed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1038,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"InProgress\",\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1039,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Completed\",\n              children: \"Completed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Delivered\",\n              children: \"Delivered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1041,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1025,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1024,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 3,\n          children: /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: sortBy,\n            onChange: e => setSortBy(e.target.value),\n            style: {\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              color: '#fff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"created\",\n              children: \"Sort by Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1055,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"status\",\n              children: \"Sort by Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1056,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"cost\",\n              children: \"Sort by Cost\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1057,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"priority\",\n              children: \"Sort by Priority\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1058,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1046,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1045,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1005,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 993,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: jobs.length > 0 ? /*#__PURE__*/_jsxDEV(Row, {\n        className: \"g-4\",\n        children: jobs.map((job, index) => /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          lg: 4,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.05\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"h-100 border-0\",\n              style: {\n                background: 'rgba(255, 255, 255, 0.1)',\n                borderRadius: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                className: \"border-0 bg-transparent\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-start justify-content-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"fw-bold mb-1 text-white\",\n                      children: job.jobNumber\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1081,\n                      columnNumber: 27\n                    }, this), getPriorityBadge(job.priority)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1080,\n                    columnNumber: 25\n                  }, this), getStatusBadge(job.status)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1079,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1078,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"pt-0\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center text-white-50 small mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(FileText, {\n                      size: 14,\n                      className: \"me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1091,\n                      columnNumber: 27\n                    }, this), job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1090,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"small text-white-50\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1095,\n                        columnNumber: 32\n                      }, this), \" \", job.printType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1095,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Copies:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1096,\n                        columnNumber: 32\n                      }, this), \" \", job.copies]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1096,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Color:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1097,\n                        columnNumber: 32\n                      }, this), \" \", job.colorType]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1097,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Size:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1098,\n                        columnNumber: 32\n                      }, this), \" \", job.paperSize]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1094,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1089,\n                  columnNumber: 23\n                }, this), job.cost && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"h5 fw-bold text-success\",\n                    children: [\"$\", job.cost.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1103,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small text-white-50 mb-3\",\n                  children: new Date(job.created).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1110,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-wrap gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-light\",\n                    size: \"sm\",\n                    onClick: () => onDownloadFile(job.id, job.fileName),\n                    style: {\n                      borderRadius: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Download, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1121,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1115,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-light\",\n                    size: \"sm\",\n                    onClick: () => onViewJob(job),\n                    style: {\n                      borderRadius: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Eye, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1130,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-light\",\n                    size: \"sm\",\n                    onClick: () => onOpenChat(job),\n                    style: {\n                      borderRadius: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1139,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1133,\n                    columnNumber: 25\n                  }, this), job.status === 'Quoted' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"success\",\n                    size: \"sm\",\n                    onClick: () => onConfirmJob(job.id),\n                    style: {\n                      borderRadius: '8px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1143,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1114,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1088,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1069,\n            columnNumber: 17\n          }, this)\n        }, job.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1066,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          scale: 0.9\n        },\n        animate: {\n          opacity: 1,\n          scale: 1\n        },\n        className: \"text-center py-5\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-inline-flex align-items-center justify-content-center\",\n            style: {\n              width: '80px',\n              height: '80px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '20px'\n            },\n            children: /*#__PURE__*/_jsxDEV(FileText, {\n              className: \"text-white\",\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1172,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1166,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1165,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"fw-semibold mb-2 text-white\",\n          children: \"No jobs found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white-50\",\n          children: \"No jobs match your current filter criteria.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1176,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1160,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 988,\n    columnNumber: 5\n  }, this);\n};\n\n// Centers Tab Component\n_c3 = JobsTab;\nconst CentersTab = ({\n  centers,\n  favoritesCenters,\n  onToggleFavorite,\n  onSelectCenter\n}) => {\n  return /*#__PURE__*/_jsxDEV(Row, {\n    className: \"g-4\",\n    children: centers.map((center, index) => /*#__PURE__*/_jsxDEV(Col, {\n      md: 6,\n      lg: 4,\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          delay: index * 0.1\n        },\n        whileHover: {\n          y: -5,\n          scale: 1.02\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"h-100 border-0 shadow-lg\",\n          style: {\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n            className: \"border-0 bg-transparent\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-start justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: \"fw-bold mb-1 text-white\",\n                  children: center.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1204,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center text-white-50 small\",\n                  children: [/*#__PURE__*/_jsxDEV(Location, {\n                    size: 12,\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1206,\n                    columnNumber: 23\n                  }, this), center.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1205,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1203,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n                whileHover: {\n                  scale: 1.1\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                onClick: () => onToggleFavorite(center.id),\n                className: \"btn btn-link p-0 border-0\",\n                children: /*#__PURE__*/_jsxDEV(Heart, {\n                  size: 20,\n                  className: favoritesCenters.includes(center.id) ? 'text-danger' : 'text-white-50',\n                  fill: favoritesCenters.includes(center.id) ? 'currentColor' : 'none'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1216,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1202,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center justify-content-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex align-items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Star, {\n                    className: \"text-warning me-1\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1229,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"fw-semibold text-white\",\n                    children: center.averageRating.toFixed(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1230,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1228,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                  bg: center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'danger',\n                  className: \"px-2 py-1\",\n                  children: [center.pendingJobs, \" jobs\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1234,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1227,\n                columnNumber: 19\n              }, this), center.services && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-wrap gap-1\",\n                  children: center.services.slice(0, 3).map((service, idx) => /*#__PURE__*/_jsxDEV(Badge, {\n                    bg: \"light\",\n                    text: \"dark\",\n                    className: \"px-2 py-1\",\n                    children: service\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1246,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1244,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 21\n              }, this), center.priceRange && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white-50 small mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(DollarSign, {\n                  size: 12,\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 23\n                }, this), center.priceRange]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 21\n              }, this), center.workingHours && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-white-50 small mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Clock, {\n                  size: 12,\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1263,\n                  columnNumber: 23\n                }, this), center.workingHours]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1262,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1226,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-light\",\n                size: \"sm\",\n                onClick: () => onSelectCenter(center),\n                className: \"flex-grow-1\",\n                style: {\n                  borderRadius: '10px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Eye, {\n                  size: 14,\n                  className: \"me-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1277,\n                  columnNumber: 21\n                }, this), \"View Details\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1270,\n                columnNumber: 19\n              }, this), center.phone && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-light\",\n                size: \"sm\",\n                href: `tel:${center.phone}`,\n                style: {\n                  borderRadius: '10px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Phone, {\n                  size: 14\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1288,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1225,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1196,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1190,\n        columnNumber: 11\n      }, this)\n    }, center.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1189,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1187,\n    columnNumber: 5\n  }, this);\n};\n\n// History Tab Component\n_c4 = CentersTab;\nconst HistoryTab = ({\n  jobs,\n  onViewJob,\n  getStatusBadge\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: \"border-0 shadow-lg\",\n    style: {\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n      className: \"border-0 bg-transparent\",\n      children: /*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"fw-bold mb-0 text-white\",\n        children: [/*#__PURE__*/_jsxDEV(History, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1311,\n          columnNumber: 11\n        }, this), \"Job History\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1310,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1309,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n      children: jobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"table-responsive\",\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          className: \"table-dark table-hover\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1321,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"File Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1324,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1325,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1326,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1320,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: jobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"fw-semibold\",\n                children: job.jobNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1332,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1333,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStatusBadge(job.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"text-success fw-semibold\",\n                children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: new Date(job.created).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1338,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outline-light\",\n                  size: \"sm\",\n                  onClick: () => onViewJob(job),\n                  children: /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1345,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1340,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1339,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1331,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1318,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1317,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-5\",\n        children: [/*#__PURE__*/_jsxDEV(History, {\n          className: \"text-white-50 mb-3\",\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-white\",\n          children: \"No history yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1356,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white-50\",\n          children: \"Your completed jobs will appear here.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1357,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1354,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1315,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1304,\n    columnNumber: 5\n  }, this);\n};\n\n// Favorites Tab Component\n_c5 = HistoryTab;\nconst FavoritesTab = ({\n  centers,\n  onToggleFavorite,\n  onSelectCenter\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: centers.length > 0 ? /*#__PURE__*/_jsxDEV(CentersTab, {\n      centers: centers,\n      favoritesCenters: centers.map(c => c.id),\n      onToggleFavorite: onToggleFavorite,\n      onSelectCenter: onSelectCenter\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1370,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Card, {\n      className: \"border-0 shadow-lg\",\n      style: {\n        background: 'rgba(255, 255, 255, 0.1)',\n        backdropFilter: 'blur(20px)',\n        borderRadius: '20px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Card.Body, {\n        className: \"text-center py-5\",\n        children: [/*#__PURE__*/_jsxDEV(Heart, {\n          className: \"text-white-50 mb-3\",\n          size: 60\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1383,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"text-white\",\n          children: \"No favorites yet\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1384,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-white-50\",\n          children: \"Add xerox centers to your favorites for quick access.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1385,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1382,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1377,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1368,\n    columnNumber: 5\n  }, this);\n};\n\n// Analytics Tab Component\n_c6 = FavoritesTab;\nconst AnalyticsTab = ({\n  jobs,\n  totalSpent\n}) => {\n  const monthlySpending = jobs.reduce((acc, job) => {\n    const month = new Date(job.created).toLocaleDateString('en-US', {\n      month: 'short',\n      year: 'numeric'\n    });\n    acc[month] = (acc[month] || 0) + (job.cost || 0);\n    return acc;\n  }, {});\n  const statusCounts = jobs.reduce((acc, job) => {\n    acc[job.status] = (acc[job.status] || 0) + 1;\n    return acc;\n  }, {});\n  return /*#__PURE__*/_jsxDEV(Row, {\n    className: \"g-4\",\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg h-100\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"fw-bold mb-0 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(BarChart3, {\n              className: \"me-2\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1416,\n              columnNumber: 15\n            }, this), \"Spending Overview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1415,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success fw-bold\",\n              children: [\"$\", totalSpent.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white-50\",\n              children: \"Total spent this year\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1423,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: Object.entries(monthlySpending).slice(-6).map(([month, amount]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white\",\n                children: month\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3\",\n                  style: {\n                    width: '100px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ProgressBar, {\n                    now: amount / Math.max(...Object.values(monthlySpending).map(v => Number(v))) * 100,\n                    style: {\n                      height: '8px'\n                    },\n                    variant: \"success\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1432,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1431,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-success fw-semibold\",\n                  children: [\"$\", amount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1430,\n                columnNumber: 19\n              }, this)]\n            }, month, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1428,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1426,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1420,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1409,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg h-100\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"fw-bold mb-0 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(PieChart, {\n              className: \"me-2\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1455,\n              columnNumber: 15\n            }, this), \"Job Status Distribution\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1454,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: Object.entries(statusCounts).map(([status, count]) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex align-items-center justify-content-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white\",\n                children: status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"me-3\",\n                  style: {\n                    width: '100px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(ProgressBar, {\n                    now: count / jobs.length * 100,\n                    style: {\n                      height: '8px'\n                    },\n                    variant: \"info\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1466,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1465,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-info fw-semibold\",\n                  children: count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1472,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1464,\n                columnNumber: 19\n              }, this)]\n            }, status, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1462,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1460,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1459,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1448,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1447,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1407,\n    columnNumber: 5\n  }, this);\n};\n\n// Settings Tab Component\n_c7 = AnalyticsTab;\nconst SettingsTab = ({\n  userSettings,\n  setUserSettings\n}) => {\n  return /*#__PURE__*/_jsxDEV(Row, {\n    className: \"g-4\",\n    children: [/*#__PURE__*/_jsxDEV(Col, {\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"fw-bold mb-0 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(Bell, {\n              className: \"me-2\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1496,\n              columnNumber: 15\n            }, this), \"Notification Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1495,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"switch\",\n              id: \"email-notifications\",\n              label: \"Email Notifications\",\n              checked: userSettings.notifications.email,\n              onChange: e => setUserSettings({\n                ...userSettings,\n                notifications: {\n                  ...userSettings.notifications,\n                  email: e.target.checked\n                }\n              }),\n              className: \"text-white mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1502,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"switch\",\n              id: \"push-notifications\",\n              label: \"Push Notifications\",\n              checked: userSettings.notifications.push,\n              onChange: e => setUserSettings({\n                ...userSettings,\n                notifications: {\n                  ...userSettings.notifications,\n                  push: e.target.checked\n                }\n              }),\n              className: \"text-white mb-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1513,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"switch\",\n              id: \"sms-notifications\",\n              label: \"SMS Notifications\",\n              checked: userSettings.notifications.sms,\n              onChange: e => setUserSettings({\n                ...userSettings,\n                notifications: {\n                  ...userSettings.notifications,\n                  sms: e.target.checked\n                }\n              }),\n              className: \"text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1524,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1501,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1489,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1488,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Col, {\n      md: 6,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"border-0 shadow-lg\",\n        style: {\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          className: \"border-0 bg-transparent\",\n          children: /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"fw-bold mb-0 text-white\",\n            children: [/*#__PURE__*/_jsxDEV(Settings, {\n              className: \"me-2\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1548,\n              columnNumber: 15\n            }, this), \"Print Preferences\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1547,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"text-white\",\n                children: \"Default Print Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: userSettings.preferences.defaultPrintType,\n                onChange: e => setUserSettings({\n                  ...userSettings,\n                  preferences: {\n                    ...userSettings.preferences,\n                    defaultPrintType: e.target.value\n                  }\n                }),\n                style: {\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  border: 'none',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Print\",\n                  children: \"Print\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1568,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Xerox\",\n                  children: \"Xerox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1569,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Binding\",\n                  children: \"Binding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1570,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Lamination\",\n                  children: \"Lamination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1571,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1556,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1554,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"text-white\",\n                children: \"Default Color Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1576,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: userSettings.preferences.defaultColorType,\n                onChange: e => setUserSettings({\n                  ...userSettings,\n                  preferences: {\n                    ...userSettings.preferences,\n                    defaultColorType: e.target.value\n                  }\n                }),\n                style: {\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  border: 'none',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"BlackWhite\",\n                  children: \"Black & White\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1589,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Color\",\n                  children: \"Color\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1577,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1575,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                className: \"text-white\",\n                children: \"Default Paper Size\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1595,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: userSettings.preferences.defaultPaperSize,\n                onChange: e => setUserSettings({\n                  ...userSettings,\n                  preferences: {\n                    ...userSettings.preferences,\n                    defaultPaperSize: e.target.value\n                  }\n                }),\n                style: {\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  border: 'none',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A4\",\n                  children: \"A4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1608,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A3\",\n                  children: \"A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1609,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Letter\",\n                  children: \"Letter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1610,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Legal\",\n                  children: \"Legal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1611,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1596,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1594,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Check, {\n              type: \"switch\",\n              id: \"auto-confirm\",\n              label: \"Auto-confirm quotes under $10\",\n              checked: userSettings.preferences.autoConfirmQuotes,\n              onChange: e => setUserSettings({\n                ...userSettings,\n                preferences: {\n                  ...userSettings.preferences,\n                  autoConfirmQuotes: e.target.checked\n                }\n              }),\n              className: \"text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1615,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1553,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1541,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1540,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1487,\n    columnNumber: 5\n  }, this);\n};\n\n// Upload Modal Component\n_c8 = SettingsTab;\nconst UploadModal = ({\n  show,\n  onHide,\n  uploadData,\n  setUploadData,\n  selectedFile,\n  setSelectedFile,\n  xeroxCenters,\n  onUpload\n}) => {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        border: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"text-white\",\n        children: [/*#__PURE__*/_jsxDEV(Upload, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1642,\n          columnNumber: 11\n        }, this), \"Upload Files for Printing\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1641,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1640,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        background: '#1a1a1a',\n        color: '#fff'\n      },\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Select File\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1649,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            type: \"file\",\n            accept: \".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\",\n            onChange: e => {\n              const files = e.target.files;\n              setSelectedFile(files ? files[0] : null);\n            },\n            style: {\n              background: '#2a2a2a',\n              border: '1px solid #444',\n              color: '#fff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1650,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n            className: \"text-muted\",\n            children: \"Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1659,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1648,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Print Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1667,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: uploadData.printType,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  printType: e.target.value\n                })),\n                style: {\n                  background: '#2a2a2a',\n                  border: '1px solid #444',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Print\",\n                  children: \"Print\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1673,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Xerox\",\n                  children: \"Xerox\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1674,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Binding\",\n                  children: \"Binding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1675,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Lamination\",\n                  children: \"Lamination\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1676,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1668,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1666,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1665,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Number of Copies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1682,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                type: \"number\",\n                min: \"1\",\n                value: uploadData.copies,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  copies: parseInt(e.target.value)\n                })),\n                style: {\n                  background: '#2a2a2a',\n                  border: '1px solid #444',\n                  color: '#fff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1683,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1681,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1680,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1664,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Color Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1697,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: uploadData.colorType,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  colorType: e.target.value\n                })),\n                style: {\n                  background: '#2a2a2a',\n                  border: '1px solid #444',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"BlackWhite\",\n                  children: \"Black & White\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1703,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Color\",\n                  children: \"Color\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1704,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1698,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1696,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1695,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Paper Size\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1710,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                value: uploadData.paperSize,\n                onChange: e => setUploadData(prev => ({\n                  ...prev,\n                  paperSize: e.target.value\n                })),\n                style: {\n                  background: '#2a2a2a',\n                  border: '1px solid #444',\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A4\",\n                  children: \"A4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1716,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"A3\",\n                  children: \"A3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1717,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Letter\",\n                  children: \"Letter\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1718,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"Legal\",\n                  children: \"Legal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1719,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1711,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1709,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1708,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1694,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Priority\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1726,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: uploadData.priority,\n            onChange: e => setUploadData(prev => ({\n              ...prev,\n              priority: e.target.value\n            })),\n            style: {\n              background: '#2a2a2a',\n              border: '1px solid #444',\n              color: '#fff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Low\",\n              children: \"Low\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1732,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Normal\",\n              children: \"Normal\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1733,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"High\",\n              children: \"High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1734,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Urgent\",\n              children: \"Urgent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1735,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1727,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Preferred Xerox Center\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1740,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n            value: uploadData.preferredXeroxCenterId,\n            onChange: e => setUploadData(prev => ({\n              ...prev,\n              preferredXeroxCenterId: e.target.value\n            })),\n            style: {\n              background: '#2a2a2a',\n              border: '1px solid #444',\n              color: '#fff'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a center (optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1746,\n              columnNumber: 15\n            }, this), xeroxCenters.map(center => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: center.id,\n              children: [center.name, \" - \", center.pendingJobs, \" pending jobs\"]\n            }, center.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1748,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1741,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1739,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n          className: \"mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n            children: \"Remarks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n            as: \"textarea\",\n            rows: 3,\n            placeholder: \"Any special instructions or remarks...\",\n            value: uploadData.remarks,\n            onChange: e => setUploadData(prev => ({\n              ...prev,\n              remarks: e.target.value\n            })),\n            style: {\n              background: '#2a2a2a',\n              border: '1px solid #444',\n              color: '#fff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1757,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1755,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1647,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1646,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        background: '#1a1a1a',\n        border: 'none'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onHide,\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1769,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onUpload,\n        disabled: !selectedFile,\n        style: {\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          border: 'none'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Upload, {\n          className: \"me-2\",\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1777,\n          columnNumber: 11\n        }, this), \"Upload File\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1772,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1768,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1639,\n    columnNumber: 5\n  }, this);\n};\n\n// View Job Modal Component\n_c9 = UploadModal;\nconst ViewJobModal = ({\n  show,\n  onHide,\n  job,\n  getStatusBadge,\n  getPriorityBadge\n}) => {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        border: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"text-white\",\n        children: [/*#__PURE__*/_jsxDEV(Eye, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1791,\n          columnNumber: 11\n        }, this), \"Job Details\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1790,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1789,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        background: '#1a1a1a',\n        color: '#fff'\n      },\n      children: job && /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"text-primary\",\n            children: \"Job Information\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1799,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Job Number:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1800,\n              columnNumber: 18\n            }, this), \" \", job.jobNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1800,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Status:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1801,\n              columnNumber: 18\n            }, this), \" \", getStatusBadge(job.status)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1801,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Priority:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1802,\n              columnNumber: 18\n            }, this), \" \", getPriorityBadge(job.priority)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1802,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"File Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1803,\n              columnNumber: 18\n            }, this), \" \", job.fileName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1803,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Xerox Center:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1804,\n              columnNumber: 18\n            }, this), \" \", job.xeroxCenterName]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1804,\n            columnNumber: 15\n          }, this), job.cost && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Cost:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1805,\n              columnNumber: 31\n            }, this), \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-success\",\n              children: [\"$\", job.cost.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1805,\n              columnNumber: 54\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1805,\n            columnNumber: 28\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Created:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1806,\n              columnNumber: 18\n            }, this), \" \", new Date(job.created).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1806,\n            columnNumber: 15\n          }, this), job.estimatedCompletionTime && /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Estimated Completion:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1808,\n              columnNumber: 20\n            }, this), \" \", new Date(job.estimatedCompletionTime).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1808,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1798,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            className: \"text-primary\",\n            children: \"Print Specifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1812,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1813,\n              columnNumber: 18\n            }, this), \" \", job.printType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1813,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Copies:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1814,\n              columnNumber: 18\n            }, this), \" \", job.copies]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1814,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Color:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1815,\n              columnNumber: 18\n            }, this), \" \", job.colorType]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1815,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Paper Size:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1816,\n              columnNumber: 18\n            }, this), \" \", job.paperSize]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1816,\n            columnNumber: 15\n          }, this), job.remarks && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mt-3\",\n              children: \"Remarks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1819,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted\",\n              children: job.remarks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1820,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1811,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1797,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1795,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        background: '#1a1a1a',\n        border: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"secondary\",\n        onClick: onHide,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1828,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1827,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1788,\n    columnNumber: 5\n  }, this);\n};\n\n// Chat Modal Component\n_c0 = ViewJobModal;\nconst ChatModal = ({\n  show,\n  onHide,\n  job,\n  messages,\n  chatMessage,\n  setChatMessage,\n  onSendMessage\n}) => {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    show: show,\n    onHide: onHide,\n    size: \"lg\",\n    centered: true,\n    children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n      closeButton: true,\n      style: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        border: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n        className: \"text-white\",\n        children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n          className: \"me-2\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1844,\n          columnNumber: 11\n        }, this), \"Chat - \", job === null || job === void 0 ? void 0 : job.jobNumber]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1843,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1842,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n      style: {\n        background: '#1a1a1a',\n        color: '#fff',\n        height: '400px',\n        overflowY: 'auto'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3\",\n        children: messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `d-inline-block p-2 rounded ${message.isFromStudent ? 'text-white' : 'bg-secondary text-white'}`,\n            style: {\n              background: message.isFromStudent ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' : '#6c757d'\n            },\n            children: message.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1852,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"small text-muted\",\n            children: new Date(message.timestamp).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1863,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1851,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1849,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1848,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n      style: {\n        background: '#1a1a1a',\n        border: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(InputGroup, {\n        children: [/*#__PURE__*/_jsxDEV(Form.Control, {\n          type: \"text\",\n          placeholder: \"Type your message...\",\n          value: chatMessage,\n          onChange: e => setChatMessage(e.target.value),\n          onKeyPress: e => e.key === 'Enter' && onSendMessage(),\n          style: {\n            background: '#2a2a2a',\n            border: '1px solid #444',\n            color: '#fff'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1872,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: onSendMessage,\n          style: {\n            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n            border: 'none'\n          },\n          children: /*#__PURE__*/_jsxDEV(Send, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1884,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1880,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1871,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1870,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1841,\n    columnNumber: 5\n  }, this);\n};\n_c1 = ChatModal;\nexport default AceternityStudentDashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"AceternityStudentDashboard\");\n$RefreshReg$(_c2, \"DashboardTab\");\n$RefreshReg$(_c3, \"JobsTab\");\n$RefreshReg$(_c4, \"CentersTab\");\n$RefreshReg$(_c5, \"HistoryTab\");\n$RefreshReg$(_c6, \"FavoritesTab\");\n$RefreshReg$(_c7, \"AnalyticsTab\");\n$RefreshReg$(_c8, \"SettingsTab\");\n$RefreshReg$(_c9, \"UploadModal\");\n$RefreshReg$(_c0, \"ViewJobModal\");\n$RefreshReg$(_c1, \"ChatModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Form", "Modal", "InputGroup", "Nav", "Table", "ProgressBar", "motion", "AnimatePresence", "Upload", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Eye", "Activity", "Printer", "Star", "History", "Settings", "User", "CreditCard", "Bell", "Search", "TrendingUp", "BarChart3", "<PERSON><PERSON><PERSON>", "Target", "Award", "Heart", "RefreshCw", "AlertCircle", "CheckCircle2", "XCircle", "Send", "Phone", "MapPin", "Location", "useAuth", "printJobApi", "xeroxCenterApi", "fileUploadApi", "messageApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AceternityStudentDashboard", "_s", "user", "activeTab", "setActiveTab", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "favoritesCenters", "setFavoritesCenters", "showUploadModal", "setShowUploadModal", "showViewModal", "setShowViewModal", "showChatModal", "setShowChatModal", "showSettingsModal", "setShowSettingsModal", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "selectedCenter", "setSelectedCenter", "selectedFile", "setSelectedFile", "chatMessage", "setChatMessage", "messages", "setMessages", "searchTerm", "setSearchTerm", "filterStatus", "setFilterStatus", "sortBy", "setSortBy", "isRefreshing", "setIsRefreshing", "uploadData", "setUploadData", "remarks", "preferredXeroxCenterId", "printType", "copies", "colorType", "paperSize", "priority", "userSettings", "setUserSettings", "notifications", "email", "push", "sms", "preferences", "defaultPrintType", "defaultColorType", "defaultPaperSize", "autoConfirmQuotes", "profile", "phone", "address", "university", "studentId", "fetchData", "interval", "setInterval", "clearInterval", "printJobsResponse", "xeroxCentersResponse", "Promise", "all", "getStudentJobs", "getAll", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "bg", "icon", "config", "IconComponent", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getPriorityBadge", "priorityConfig", "handleFileUpload", "formData", "FormData", "append", "Object", "entries", "for<PERSON>ach", "key", "value", "toString", "uploadFile", "handleViewJob", "job", "handleOpenChat", "response", "getJobMessages", "id", "handleDownloadFile", "jobId", "downloadFile", "blob", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "handleConfirmJob", "<PERSON><PERSON>ob", "handleSendMessage", "trim", "sendMessage", "prev", "toggleFavorite", "centerId", "includes", "filter", "filteredJobs", "matchesSearch", "jobNumber", "toLowerCase", "xeroxCenterName", "matchesStatus", "totalSpent", "reduce", "sum", "cost", "inProgressJobs", "length", "completedJobs", "pendingJobs", "containerVariants", "hidden", "opacity", "visible", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "floatingShapes", "Array", "from", "_", "i", "div", "style", "background", "width", "Math", "random", "height", "left", "top", "zIndex", "animate", "x", "rotate", "scale", "repeat", "Infinity", "repeatType", "ease", "minHeight", "fluid", "variants", "initial", "borderRadius", "boxShadow", "fontSize", "username", "whileHover", "whileTap", "onClick", "<PERSON><PERSON>ilter", "color", "variant", "disabled", "Body", "label", "map", "tab", "<PERSON><PERSON>", "Link", "active", "border", "mode", "exit", "DashboardTab", "onViewJob", "onDownloadFile", "onOpenChat", "onConfirmJob", "JobsTab", "jobs", "CentersTab", "centers", "onToggleFavorite", "onSelectCenter", "HistoryTab", "FavoritesTab", "center", "AnalyticsTab", "SettingsTab", "UploadModal", "show", "onHide", "onUpload", "ViewJobModal", "ChatModal", "onSendMessage", "_c", "md", "delay", "toFixed", "lg", "Header", "text", "slice", "index", "display", "alignItems", "justifyContent", "c", "isActive", "_c2", "Text", "Control", "placeholder", "onChange", "e", "target", "Select", "Date", "created", "toLocaleDateString", "_c3", "name", "location", "button", "fill", "averageRating", "services", "service", "idx", "priceRange", "workingHours", "_c4", "_c5", "_c6", "monthlySpending", "acc", "month", "year", "statusCounts", "amount", "now", "max", "values", "v", "Number", "count", "_c7", "Check", "type", "checked", "Group", "Label", "_c8", "centered", "closeButton", "Title", "accept", "files", "min", "parseInt", "as", "rows", "Footer", "_c9", "toLocaleString", "estimatedCompletionTime", "_c0", "overflowY", "message", "isFromStudent", "content", "timestamp", "onKeyPress", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/AceternityStudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Badge, Form, Modal, InputGroup, Nav, Tab, Table, ProgressBar } from 'react-bootstrap';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Upload,\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Eye,\n  Plus,\n  Activity,\n  Printer,\n  MapPin,\n  Star,\n  History,\n  Settings,\n  User,\n  CreditCard,\n  Bell,\n  Search,\n  Filter,\n  Calendar,\n  TrendingUp,\n  BarChart3,\n  PieChart,\n  Zap,\n  Target,\n  Award,\n  Bookmark,\n  Heart,\n  Share2,\n  RefreshCw,\n  AlertCircle,\n  Info,\n  CheckCircle2,\n  XCircle,\n  Trash2,\n  Edit3,\n  Send,\n  Phone,\n  Mail,\n  Globe,\n  MapPin as Location\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi, fileUploadApi, messageApi } from '../../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  xeroxCenterName: string;\n  created: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  priority: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n  phone?: string;\n  email?: string;\n  website?: string;\n  description?: string;\n  services?: string[];\n  priceRange?: string;\n  workingHours?: string;\n}\n\nconst AceternityStudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [favoritesCenters, setFavoritesCenters] = useState<number[]>([]);\n  const [showUploadModal, setShowUploadModal] = useState(false);\n  const [showViewModal, setShowViewModal] = useState(false);\n  const [showChatModal, setShowChatModal] = useState(false);\n  const [showSettingsModal, setShowSettingsModal] = useState(false);\n  const [selectedJob, setSelectedJob] = useState<any>(null);\n  const [selectedCenter, setSelectedCenter] = useState<XeroxCenter | null>(null);\n  const [selectedFile, setSelectedFile] = useState<File | null>(null);\n  const [chatMessage, setChatMessage] = useState('');\n  const [messages, setMessages] = useState<any[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState('All');\n  const [sortBy, setSortBy] = useState('created');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [uploadData, setUploadData] = useState({\n    remarks: '',\n    preferredXeroxCenterId: '',\n    printType: 'Print',\n    copies: 1,\n    colorType: 'BlackWhite',\n    paperSize: 'A4',\n    priority: 'Normal'\n  });\n\n  const [userSettings, setUserSettings] = useState({\n    notifications: {\n      email: true,\n      push: true,\n      sms: false\n    },\n    preferences: {\n      defaultPrintType: 'Print',\n      defaultColorType: 'BlackWhite',\n      defaultPaperSize: 'A4',\n      autoConfirmQuotes: false\n    },\n    profile: {\n      phone: '',\n      address: '',\n      university: '',\n      studentId: ''\n    }\n  });\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchData = async () => {\n    setIsRefreshing(true);\n    try {\n      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([\n        printJobApi.getStudentJobs(),\n        xeroxCenterApi.getAll()\n      ]);\n\n      setPrintJobs(printJobsResponse.data || []);\n      setXeroxCenters(xeroxCentersResponse.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setPrintJobs([]);\n      setXeroxCenters([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { bg: 'secondary', icon: Clock },\n      'UnderReview': { bg: 'info', icon: Eye },\n      'Quoted': { bg: 'warning', icon: DollarSign },\n      'WaitingConfirmation': { bg: 'warning', icon: AlertCircle },\n      'Confirmed': { bg: 'info', icon: CheckCircle },\n      'InProgress': { bg: 'primary', icon: Activity },\n      'Completed': { bg: 'success', icon: CheckCircle2 },\n      'Delivered': { bg: 'success', icon: CheckCircle2 },\n      'Rejected': { bg: 'danger', icon: XCircle },\n      'Cancelled': { bg: 'secondary', icon: XCircle }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { bg: 'secondary', icon: Clock };\n    const IconComponent = config.icon;\n\n    return (\n      <Badge bg={config.bg} className=\"d-flex align-items-center gap-1 px-3 py-2\">\n        <IconComponent size={14} />\n        {status}\n      </Badge>\n    );\n  };\n\n  const getPriorityBadge = (priority: string) => {\n    const priorityConfig = {\n      'Low': { bg: 'success', icon: '🟢' },\n      'Normal': { bg: 'secondary', icon: '🔵' },\n      'High': { bg: 'warning', icon: '🟡' },\n      'Urgent': { bg: 'danger', icon: '🔴' }\n    };\n\n    const config = priorityConfig[priority as keyof typeof priorityConfig] || { bg: 'secondary', icon: '🔵' };\n\n    return (\n      <Badge bg={config.bg} className=\"px-2 py-1\">\n        {config.icon} {priority}\n      </Badge>\n    );\n  };\n\n  const handleFileUpload = async () => {\n    if (!selectedFile) return;\n\n    try {\n      const formData = new FormData();\n      formData.append('file', selectedFile);\n      Object.entries(uploadData).forEach(([key, value]) => {\n        formData.append(key, value.toString());\n      });\n\n      await fileUploadApi.uploadFile(formData);\n      await fetchData();\n      setShowUploadModal(false);\n      setSelectedFile(null);\n      setUploadData({\n        remarks: '',\n        preferredXeroxCenterId: '',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4',\n        priority: 'Normal'\n      });\n    } catch (error) {\n      console.error('Error uploading file:', error);\n    }\n  };\n\n  const handleViewJob = (job: any) => {\n    setSelectedJob(job);\n    setShowViewModal(true);\n  };\n\n  const handleOpenChat = async (job: any) => {\n    try {\n      setSelectedJob(job);\n      setShowChatModal(true);\n      const response = await messageApi.getJobMessages(job.id);\n      setMessages(response.data || []);\n    } catch (error) {\n      console.error('Error loading messages:', error);\n      setMessages([]);\n    }\n  };\n\n  const handleDownloadFile = async (jobId: number, fileName: string) => {\n    try {\n      const response = await fileUploadApi.downloadFile(jobId);\n      const blob = new Blob([response.data]);\n      const url = window.URL.createObjectURL(blob);\n      const link = document.createElement('a');\n      link.href = url;\n      link.download = fileName;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      window.URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error downloading file:', error);\n    }\n  };\n\n  const handleConfirmJob = async (jobId: number) => {\n    try {\n      await printJobApi.confirmJob(jobId);\n      await fetchData();\n    } catch (error) {\n      console.error('Error confirming job:', error);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if (!chatMessage.trim() || !selectedJob) return;\n\n    try {\n      const response = await messageApi.sendMessage(selectedJob.id, chatMessage.trim());\n      setMessages(prev => [...prev, response.data]);\n      setChatMessage('');\n    } catch (error) {\n      console.error('Error sending message:', error);\n    }\n  };\n\n  const toggleFavorite = (centerId: number) => {\n    setFavoritesCenters(prev =>\n      prev.includes(centerId)\n        ? prev.filter(id => id !== centerId)\n        : [...prev, centerId]\n    );\n  };\n\n  const filteredJobs = printJobs.filter(job => {\n    const matchesSearch = searchTerm === '' ||\n      job.jobNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      job.fileName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      job.xeroxCenterName.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesStatus = filterStatus === 'All' || job.status === filterStatus;\n\n    return matchesSearch && matchesStatus;\n  });\n\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const pendingJobs = printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted'].includes(job.status)).length;\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const floatingShapes = Array.from({ length: 8 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"position-absolute rounded-circle\"\n      style={{\n        background: `linear-gradient(135deg, ${i % 3 === 0 ? '#667eea' : i % 3 === 1 ? '#764ba2' : '#f093fb'}, ${i % 3 === 0 ? '#764ba2' : i % 3 === 1 ? '#f093fb' : '#f5576c'})`,\n        width: `${Math.random() * 60 + 20}px`,\n        height: `${Math.random() * 60 + 20}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        opacity: 0.1,\n        zIndex: 0\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n        scale: [1, 1.2, 1],\n      }}\n      transition={{\n        duration: Math.random() * 20 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n        ease: \"easeInOut\"\n      }}\n    />\n  ));\n\n  return (\n    <div className=\"min-h-screen position-relative\" style={{\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh'\n    }}>\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100 overflow-hidden\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)',\n        }} />\n      </div>\n\n      <Container fluid className=\"position-relative py-4\" style={{ zIndex: 1 }}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-5\">\n            <motion.div\n              animate={{\n                rotate: [0, 360],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"d-inline-flex align-items-center justify-content-center mb-3\"\n              style={{\n                width: '80px',\n                height: '80px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px',\n                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n              }}\n            >\n              <User className=\"text-white\" size={40} />\n            </motion.div>\n            <h1 className=\"text-white fw-bold mb-2\" style={{ fontSize: '2.5rem' }}>\n              Welcome back, {user?.username}!\n            </h1>\n            <p className=\"text-white-50 fs-5 mb-4\">\n              Manage your printing jobs and explore xerox centers\n            </p>\n\n            <div className=\"d-flex align-items-center justify-content-center gap-3 flex-wrap\">\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  size=\"lg\"\n                  onClick={() => setShowUploadModal(true)}\n                  className=\"px-5 py-3 rounded-4 border-0 fw-semibold\"\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    backdropFilter: 'blur(20px)',\n                    color: '#fff',\n                    boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'\n                  }}\n                >\n                  <Upload className=\"me-2\" size={20} />\n                  Upload Files\n                </Button>\n              </motion.div>\n\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  variant=\"outline-light\"\n                  size=\"lg\"\n                  onClick={fetchData}\n                  disabled={isRefreshing}\n                  className=\"px-4 py-3 rounded-4 fw-semibold\"\n                >\n                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />\n                  Refresh\n                </Button>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Navigation Tabs */}\n          <motion.div variants={itemVariants} className=\"mb-4\">\n            <Card className=\"border-0 shadow-lg\" style={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            }}>\n              <Card.Body className=\"p-2\">\n                <Nav variant=\"pills\" className=\"justify-content-center flex-wrap\">\n                  {[\n                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },\n                    { key: 'jobs', label: 'My Jobs', icon: FileText },\n                    { key: 'centers', label: 'Xerox Centers', icon: Printer },\n                    { key: 'history', label: 'History', icon: History },\n                    { key: 'favorites', label: 'Favorites', icon: Heart },\n                    { key: 'analytics', label: 'Analytics', icon: PieChart },\n                    { key: 'settings', label: 'Settings', icon: Settings }\n                  ].map(tab => {\n                    const IconComponent = tab.icon;\n                    return (\n                      <Nav.Item key={tab.key} className=\"m-1\">\n                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                          <Nav.Link\n                            active={activeTab === tab.key}\n                            onClick={() => setActiveTab(tab.key)}\n                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${\n                              activeTab === tab.key\n                                ? 'text-primary'\n                                : 'text-white-50'\n                            }`}\n                            style={{\n                              background: activeTab === tab.key\n                                ? 'rgba(255, 255, 255, 0.9)'\n                                : 'transparent',\n                              border: 'none',\n                              transition: 'all 0.3s ease',\n                              boxShadow: activeTab === tab.key\n                                ? '0 10px 30px rgba(255, 255, 255, 0.2)'\n                                : 'none'\n                            }}\n                          >\n                            <IconComponent size={18} className=\"me-2\" />\n                            <span className=\"d-none d-md-inline\">{tab.label}</span>\n                          </Nav.Link>\n                        </motion.div>\n                      </Nav.Item>\n                    );\n                  })}\n                </Nav>\n              </Card.Body>\n            </Card>\n          </motion.div>\n\n          {/* Tab Content */}\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={activeTab}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              transition={{ duration: 0.3 }}\n            >\n              {activeTab === 'dashboard' && (\n                <DashboardTab\n                  printJobs={printJobs}\n                  xeroxCenters={xeroxCenters}\n                  totalSpent={totalSpent}\n                  inProgressJobs={inProgressJobs}\n                  completedJobs={completedJobs}\n                  pendingJobs={pendingJobs}\n                  onViewJob={handleViewJob}\n                  onDownloadFile={handleDownloadFile}\n                  onOpenChat={handleOpenChat}\n                  onConfirmJob={handleConfirmJob}\n                  getStatusBadge={getStatusBadge}\n                  getPriorityBadge={getPriorityBadge}\n                />\n              )}\n\n              {activeTab === 'jobs' && (\n                <JobsTab\n                  jobs={filteredJobs}\n                  searchTerm={searchTerm}\n                  setSearchTerm={setSearchTerm}\n                  filterStatus={filterStatus}\n                  setFilterStatus={setFilterStatus}\n                  sortBy={sortBy}\n                  setSortBy={setSortBy}\n                  onViewJob={handleViewJob}\n                  onDownloadFile={handleDownloadFile}\n                  onOpenChat={handleOpenChat}\n                  onConfirmJob={handleConfirmJob}\n                  getStatusBadge={getStatusBadge}\n                  getPriorityBadge={getPriorityBadge}\n                />\n              )}\n\n              {activeTab === 'centers' && (\n                <CentersTab\n                  centers={xeroxCenters}\n                  favoritesCenters={favoritesCenters}\n                  onToggleFavorite={toggleFavorite}\n                  onSelectCenter={setSelectedCenter}\n                />\n              )}\n\n              {activeTab === 'history' && (\n                <HistoryTab\n                  jobs={printJobs.filter(job => ['Completed', 'Delivered', 'Cancelled', 'Rejected'].includes(job.status))}\n                  onViewJob={handleViewJob}\n                  getStatusBadge={getStatusBadge}\n                />\n              )}\n\n              {activeTab === 'favorites' && (\n                <FavoritesTab\n                  centers={xeroxCenters.filter(center => favoritesCenters.includes(center.id))}\n                  onToggleFavorite={toggleFavorite}\n                  onSelectCenter={setSelectedCenter}\n                />\n              )}\n\n              {activeTab === 'analytics' && (\n                <AnalyticsTab\n                  jobs={printJobs}\n                  totalSpent={totalSpent}\n                />\n              )}\n\n              {activeTab === 'settings' && (\n                <SettingsTab\n                  userSettings={userSettings}\n                  setUserSettings={setUserSettings}\n                />\n              )}\n            </motion.div>\n          </AnimatePresence>\n        </motion.div>\n      </Container>\n\n      {/* Modals */}\n      {showUploadModal && (\n        <UploadModal\n          show={showUploadModal}\n          onHide={() => setShowUploadModal(false)}\n          uploadData={uploadData}\n          setUploadData={setUploadData}\n          selectedFile={selectedFile}\n          setSelectedFile={setSelectedFile}\n          xeroxCenters={xeroxCenters}\n          onUpload={handleFileUpload}\n        />\n      )}\n\n      {showViewModal && (\n        <ViewJobModal\n          show={showViewModal}\n          onHide={() => setShowViewModal(false)}\n          job={selectedJob}\n          getStatusBadge={getStatusBadge}\n          getPriorityBadge={getPriorityBadge}\n        />\n      )}\n\n      {showChatModal && (\n        <ChatModal\n          show={showChatModal}\n          onHide={() => setShowChatModal(false)}\n          job={selectedJob}\n          messages={messages}\n          chatMessage={chatMessage}\n          setChatMessage={setChatMessage}\n          onSendMessage={handleSendMessage}\n        />\n      )}\n    </div>\n  );\n};\n\n// Dashboard Tab Component\nconst DashboardTab: React.FC<any> = ({\n  printJobs, xeroxCenters, totalSpent, inProgressJobs, completedJobs, pendingJobs,\n  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge\n}) => {\n  return (\n    <Row className=\"g-4\">\n      {/* Statistics Cards */}\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <FileText className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Total Jobs</h6>\n              <h2 className=\"fw-bold text-white\">{printJobs.length}</h2>\n              <small className=\"text-success\">\n                <TrendingUp size={12} className=\"me-1\" />\n                +12% vs last month\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.1 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <Clock className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">In Progress</h6>\n              <h2 className=\"fw-bold text-white\">{inProgressJobs}</h2>\n              <small className=\"text-warning\">\n                <Activity size={12} className=\"me-1\" />\n                Active jobs\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <CheckCircle className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Completed</h6>\n              <h2 className=\"fw-bold text-white\">{completedJobs}</h2>\n              <small className=\"text-success\">\n                <Award size={12} className=\"me-1\" />\n                Finished jobs\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      <Col md={3}>\n        <motion.div\n          initial={{ opacity: 0, scale: 0.9 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n          whileHover={{ y: -5, scale: 1.02 }}\n        >\n          <Card className=\"h-100 border-0 shadow-lg\" style={{\n            background: 'rgba(255, 255, 255, 0.1)',\n            backdropFilter: 'blur(20px)',\n            borderRadius: '20px'\n          }}>\n            <Card.Body className=\"text-center p-4\">\n              <div className=\"mb-3\">\n                <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                  width: '60px',\n                  height: '60px',\n                  background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                  borderRadius: '15px'\n                }}>\n                  <DollarSign className=\"text-white\" size={24} />\n                </div>\n              </div>\n              <h6 className=\"text-white-50 mb-1\">Total Spent</h6>\n              <h2 className=\"fw-bold text-white\">${totalSpent.toFixed(2)}</h2>\n              <small className=\"text-info\">\n                <CreditCard size={12} className=\"me-1\" />\n                This month\n              </small>\n            </Card.Body>\n          </Card>\n        </motion.div>\n      </Col>\n\n      {/* Recent Jobs */}\n      <Col lg={8}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <div className=\"d-flex align-items-center justify-content-between\">\n              <h4 className=\"fw-bold mb-0 text-white\">\n                <Activity className=\"me-2\" size={20} />\n                Recent Jobs\n              </h4>\n              <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n                {printJobs.length} total\n              </Badge>\n            </div>\n          </Card.Header>\n          <Card.Body>\n            {printJobs.length > 0 ? (\n              <div className=\"space-y-3\">\n                {printJobs.slice(0, 5).map((job: PrintJob, index: number) => (\n                  <motion.div\n                    key={job.id}\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    className=\"p-3 rounded-3\"\n                    style={{\n                      background: 'rgba(255, 255, 255, 0.1)',\n                      border: '1px solid rgba(255, 255, 255, 0.2)'\n                    }}\n                  >\n                    <div className=\"d-flex align-items-center justify-content-between\">\n                      <div className=\"d-flex align-items-center\">\n                        <div className=\"me-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '50px',\n                            height: '50px',\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            borderRadius: '12px'\n                          }}>\n                            <FileText className=\"text-white\" size={20} />\n                          </div>\n                        </div>\n                        <div>\n                          <h6 className=\"fw-semibold mb-1 text-white\">{job.jobNumber}</h6>\n                          <p className=\"text-white-50 small mb-1\">\n                            {job.fileName.length > 40 ? job.fileName.slice(0, 40) + '...' : job.fileName}\n                          </p>\n                          <small className=\"text-white-50\">{job.xeroxCenterName}</small>\n                        </div>\n                      </div>\n\n                      <div className=\"d-flex align-items-center\">\n                        <div className=\"text-end me-3\">\n                          {getStatusBadge(job.status)}\n                          {job.cost && (\n                            <div className=\"fw-semibold text-success mt-1\">\n                              ${job.cost.toFixed(2)}\n                            </div>\n                          )}\n                        </div>\n\n                        <div className=\"d-flex gap-1\">\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onDownloadFile(job.id, job.fileName)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <Download size={14} />\n                          </Button>\n\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onViewJob(job)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <Eye size={14} />\n                          </Button>\n\n                          <Button\n                            variant=\"outline-light\"\n                            size=\"sm\"\n                            onClick={() => onOpenChat(job)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <MessageCircle size={14} />\n                          </Button>\n\n                          {job.status === 'Quoted' && (\n                            <Button\n                              variant=\"success\"\n                              size=\"sm\"\n                              onClick={() => onConfirmJob(job.id)}\n                              style={{ borderRadius: '8px' }}\n                            >\n                              <CheckCircle size={14} />\n                            </Button>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"text-center py-5\"\n              >\n                <div className=\"mb-4\">\n                  <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                    width: '80px',\n                    height: '80px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '20px'\n                  }}>\n                    <Upload className=\"text-white\" size={40} />\n                  </div>\n                </div>\n                <h5 className=\"fw-semibold mb-2 text-white\">No jobs yet</h5>\n                <p className=\"text-white-50 mb-4\">Upload your first file to get started!</p>\n              </motion.div>\n            )}\n          </Card.Body>\n        </Card>\n      </Col>\n\n      {/* Quick Stats */}\n      <Col lg={4}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h4 className=\"fw-bold mb-0 text-white\">\n              <Target className=\"me-2\" size={20} />\n              Quick Stats\n            </h4>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"space-y-4\">\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Clock className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Pending</h6>\n                    <small className=\"text-white-50\">Awaiting review</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-warning\">{pendingJobs}</h4>\n              </div>\n\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Printer className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Centers</h6>\n                    <small className=\"text-white-50\">Available now</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-info\">{xeroxCenters.filter((c: XeroxCenter) => c.isActive).length}</h4>\n              </div>\n\n              <div className=\"d-flex align-items-center justify-content-between p-3 rounded-3\" style={{\n                background: 'rgba(255, 255, 255, 0.1)'\n              }}>\n                <div className=\"d-flex align-items-center\">\n                  <div className=\"me-3\" style={{\n                    width: '40px',\n                    height: '40px',\n                    background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                    borderRadius: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}>\n                    <Star className=\"text-white\" size={20} />\n                  </div>\n                  <div>\n                    <h6 className=\"mb-0 text-white\">Avg Rating</h6>\n                    <small className=\"text-white-50\">Your experience</small>\n                  </div>\n                </div>\n                <h4 className=\"mb-0 text-success\">4.8</h4>\n              </div>\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Jobs Tab Component\nconst JobsTab: React.FC<any> = ({\n  jobs, searchTerm, setSearchTerm, filterStatus, setFilterStatus, sortBy, setSortBy,\n  onViewJob, onDownloadFile, onOpenChat, onConfirmJob, getStatusBadge, getPriorityBadge\n}) => {\n  return (\n    <Card className=\"border-0 shadow-lg\" style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    }}>\n      <Card.Header className=\"border-0 bg-transparent\">\n        <div className=\"d-flex align-items-center justify-content-between mb-3\">\n          <h4 className=\"fw-bold mb-0 text-white\">\n            <FileText className=\"me-2\" size={20} />\n            All Jobs\n          </h4>\n          <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n            {jobs.length} jobs\n          </Badge>\n        </div>\n\n        {/* Search and Filter Controls */}\n        <Row className=\"g-3\">\n          <Col md={4}>\n            <InputGroup>\n              <InputGroup.Text style={{ background: 'rgba(255, 255, 255, 0.1)', border: 'none' }}>\n                <Search className=\"text-white-50\" size={16} />\n              </InputGroup.Text>\n              <Form.Control\n                placeholder=\"Search jobs...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                style={{\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  border: 'none',\n                  color: '#fff'\n                }}\n              />\n            </InputGroup>\n          </Col>\n\n          <Col md={3}>\n            <Form.Select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value)}\n              style={{\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none',\n                color: '#fff'\n              }}\n            >\n              <option value=\"All\">All Status</option>\n              <option value=\"Requested\">Requested</option>\n              <option value=\"UnderReview\">Under Review</option>\n              <option value=\"Quoted\">Quoted</option>\n              <option value=\"Confirmed\">Confirmed</option>\n              <option value=\"InProgress\">In Progress</option>\n              <option value=\"Completed\">Completed</option>\n              <option value=\"Delivered\">Delivered</option>\n            </Form.Select>\n          </Col>\n\n          <Col md={3}>\n            <Form.Select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              style={{\n                background: 'rgba(255, 255, 255, 0.1)',\n                border: 'none',\n                color: '#fff'\n              }}\n            >\n              <option value=\"created\">Sort by Date</option>\n              <option value=\"status\">Sort by Status</option>\n              <option value=\"cost\">Sort by Cost</option>\n              <option value=\"priority\">Sort by Priority</option>\n            </Form.Select>\n          </Col>\n        </Row>\n      </Card.Header>\n\n      <Card.Body>\n        {jobs.length > 0 ? (\n          <Row className=\"g-4\">\n            {jobs.map((job: any, index: number) => (\n              <Col key={job.id} md={6} lg={4}>\n                <motion.div\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.05 }}\n                >\n                  <Card className=\"h-100 border-0\" style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    borderRadius: '15px'\n                  }}>\n                    <Card.Header className=\"border-0 bg-transparent\">\n                      <div className=\"d-flex align-items-start justify-content-between\">\n                        <div>\n                          <h6 className=\"fw-bold mb-1 text-white\">{job.jobNumber}</h6>\n                          {getPriorityBadge(job.priority)}\n                        </div>\n                        {getStatusBadge(job.status)}\n                      </div>\n                    </Card.Header>\n\n                    <Card.Body className=\"pt-0\">\n                      <div className=\"mb-3\">\n                        <div className=\"d-flex align-items-center text-white-50 small mb-2\">\n                          <FileText size={14} className=\"me-1\" />\n                          {job.fileName.length > 25 ? job.fileName.slice(0, 25) + '...' : job.fileName}\n                        </div>\n                        <div className=\"small text-white-50\">\n                          <div><strong>Type:</strong> {job.printType}</div>\n                          <div><strong>Copies:</strong> {job.copies}</div>\n                          <div><strong>Color:</strong> {job.colorType}</div>\n                          <div><strong>Size:</strong> {job.paperSize}</div>\n                        </div>\n                      </div>\n\n                      {job.cost && (\n                        <div className=\"mb-3\">\n                          <span className=\"h5 fw-bold text-success\">\n                            ${job.cost.toFixed(2)}\n                          </span>\n                        </div>\n                      )}\n\n                      <div className=\"small text-white-50 mb-3\">\n                        {new Date(job.created).toLocaleDateString()}\n                      </div>\n\n                      <div className=\"d-flex flex-wrap gap-1\">\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onDownloadFile(job.id, job.fileName)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <Download size={12} />\n                        </Button>\n\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onViewJob(job)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <Eye size={12} />\n                        </Button>\n\n                        <Button\n                          variant=\"outline-light\"\n                          size=\"sm\"\n                          onClick={() => onOpenChat(job)}\n                          style={{ borderRadius: '8px' }}\n                        >\n                          <MessageCircle size={12} />\n                        </Button>\n\n                        {job.status === 'Quoted' && (\n                          <Button\n                            variant=\"success\"\n                            size=\"sm\"\n                            onClick={() => onConfirmJob(job.id)}\n                            style={{ borderRadius: '8px' }}\n                          >\n                            <CheckCircle size={12} />\n                          </Button>\n                        )}\n                      </div>\n                    </Card.Body>\n                  </Card>\n                </motion.div>\n              </Col>\n            ))}\n          </Row>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            className=\"text-center py-5\"\n          >\n            <div className=\"mb-4\">\n              <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                width: '80px',\n                height: '80px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px'\n              }}>\n                <FileText className=\"text-white\" size={40} />\n              </div>\n            </div>\n            <h5 className=\"fw-semibold mb-2 text-white\">No jobs found</h5>\n            <p className=\"text-white-50\">No jobs match your current filter criteria.</p>\n          </motion.div>\n        )}\n      </Card.Body>\n    </Card>\n  );\n};\n\n// Centers Tab Component\nconst CentersTab: React.FC<any> = ({ centers, favoritesCenters, onToggleFavorite, onSelectCenter }) => {\n  return (\n    <Row className=\"g-4\">\n      {centers.map((center: any, index: number) => (\n        <Col key={center.id} md={6} lg={4}>\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: index * 0.1 }}\n            whileHover={{ y: -5, scale: 1.02 }}\n          >\n            <Card className=\"h-100 border-0 shadow-lg\" style={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            }}>\n              <Card.Header className=\"border-0 bg-transparent\">\n                <div className=\"d-flex align-items-start justify-content-between\">\n                  <div>\n                    <h5 className=\"fw-bold mb-1 text-white\">{center.name}</h5>\n                    <div className=\"d-flex align-items-center text-white-50 small\">\n                      <Location size={12} className=\"me-1\" />\n                      {center.location}\n                    </div>\n                  </div>\n                  <motion.button\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    onClick={() => onToggleFavorite(center.id)}\n                    className=\"btn btn-link p-0 border-0\"\n                  >\n                    <Heart\n                      size={20}\n                      className={favoritesCenters.includes(center.id) ? 'text-danger' : 'text-white-50'}\n                      fill={favoritesCenters.includes(center.id) ? 'currentColor' : 'none'}\n                    />\n                  </motion.button>\n                </div>\n              </Card.Header>\n\n              <Card.Body>\n                <div className=\"mb-3\">\n                  <div className=\"d-flex align-items-center justify-content-between mb-2\">\n                    <div className=\"d-flex align-items-center\">\n                      <Star className=\"text-warning me-1\" size={16} />\n                      <span className=\"fw-semibold text-white\">\n                        {center.averageRating.toFixed(1)}\n                      </span>\n                    </div>\n                    <Badge\n                      bg={center.pendingJobs <= 5 ? 'success' : center.pendingJobs <= 10 ? 'warning' : 'danger'}\n                      className=\"px-2 py-1\"\n                    >\n                      {center.pendingJobs} jobs\n                    </Badge>\n                  </div>\n\n                  {center.services && (\n                    <div className=\"mb-3\">\n                      <div className=\"d-flex flex-wrap gap-1\">\n                        {center.services.slice(0, 3).map((service: string, idx: number) => (\n                          <Badge key={idx} bg=\"light\" text=\"dark\" className=\"px-2 py-1\">\n                            {service}\n                          </Badge>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {center.priceRange && (\n                    <div className=\"text-white-50 small mb-2\">\n                      <DollarSign size={12} className=\"me-1\" />\n                      {center.priceRange}\n                    </div>\n                  )}\n\n                  {center.workingHours && (\n                    <div className=\"text-white-50 small mb-3\">\n                      <Clock size={12} className=\"me-1\" />\n                      {center.workingHours}\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"d-flex gap-2\">\n                  <Button\n                    variant=\"outline-light\"\n                    size=\"sm\"\n                    onClick={() => onSelectCenter(center)}\n                    className=\"flex-grow-1\"\n                    style={{ borderRadius: '10px' }}\n                  >\n                    <Eye size={14} className=\"me-1\" />\n                    View Details\n                  </Button>\n\n                  {center.phone && (\n                    <Button\n                      variant=\"outline-light\"\n                      size=\"sm\"\n                      href={`tel:${center.phone}`}\n                      style={{ borderRadius: '10px' }}\n                    >\n                      <Phone size={14} />\n                    </Button>\n                  )}\n                </div>\n              </Card.Body>\n            </Card>\n          </motion.div>\n        </Col>\n      ))}\n    </Row>\n  );\n};\n\n// History Tab Component\nconst HistoryTab: React.FC<any> = ({ jobs, onViewJob, getStatusBadge }) => {\n  return (\n    <Card className=\"border-0 shadow-lg\" style={{\n      background: 'rgba(255, 255, 255, 0.1)',\n      backdropFilter: 'blur(20px)',\n      borderRadius: '20px'\n    }}>\n      <Card.Header className=\"border-0 bg-transparent\">\n        <h4 className=\"fw-bold mb-0 text-white\">\n          <History className=\"me-2\" size={20} />\n          Job History\n        </h4>\n      </Card.Header>\n      <Card.Body>\n        {jobs.length > 0 ? (\n          <div className=\"table-responsive\">\n            <Table className=\"table-dark table-hover\">\n              <thead>\n                <tr>\n                  <th>Job Number</th>\n                  <th>File Name</th>\n                  <th>Status</th>\n                  <th>Cost</th>\n                  <th>Date</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {jobs.map((job: any) => (\n                  <tr key={job.id}>\n                    <td className=\"fw-semibold\">{job.jobNumber}</td>\n                    <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>\n                    <td>{getStatusBadge(job.status)}</td>\n                    <td className=\"text-success fw-semibold\">\n                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                    </td>\n                    <td>{new Date(job.created).toLocaleDateString()}</td>\n                    <td>\n                      <Button\n                        variant=\"outline-light\"\n                        size=\"sm\"\n                        onClick={() => onViewJob(job)}\n                      >\n                        <Eye size={14} />\n                      </Button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </Table>\n          </div>\n        ) : (\n          <div className=\"text-center py-5\">\n            <History className=\"text-white-50 mb-3\" size={60} />\n            <h5 className=\"text-white\">No history yet</h5>\n            <p className=\"text-white-50\">Your completed jobs will appear here.</p>\n          </div>\n        )}\n      </Card.Body>\n    </Card>\n  );\n};\n\n// Favorites Tab Component\nconst FavoritesTab: React.FC<any> = ({ centers, onToggleFavorite, onSelectCenter }) => {\n  return (\n    <div>\n      {centers.length > 0 ? (\n        <CentersTab\n          centers={centers}\n          favoritesCenters={centers.map((c: any) => c.id)}\n          onToggleFavorite={onToggleFavorite}\n          onSelectCenter={onSelectCenter}\n        />\n      ) : (\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Body className=\"text-center py-5\">\n            <Heart className=\"text-white-50 mb-3\" size={60} />\n            <h5 className=\"text-white\">No favorites yet</h5>\n            <p className=\"text-white-50\">Add xerox centers to your favorites for quick access.</p>\n          </Card.Body>\n        </Card>\n      )}\n    </div>\n  );\n};\n\n// Analytics Tab Component\nconst AnalyticsTab: React.FC<any> = ({ jobs, totalSpent }) => {\n  const monthlySpending = jobs.reduce((acc: any, job: any) => {\n    const month = new Date(job.created).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });\n    acc[month] = (acc[month] || 0) + (job.cost || 0);\n    return acc;\n  }, {});\n\n  const statusCounts = jobs.reduce((acc: any, job: any) => {\n    acc[job.status] = (acc[job.status] || 0) + 1;\n    return acc;\n  }, {});\n\n  return (\n    <Row className=\"g-4\">\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <BarChart3 className=\"me-2\" size={20} />\n              Spending Overview\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"mb-4\">\n              <h3 className=\"text-success fw-bold\">${totalSpent.toFixed(2)}</h3>\n              <p className=\"text-white-50\">Total spent this year</p>\n            </div>\n\n            <div className=\"space-y-3\">\n              {Object.entries(monthlySpending).slice(-6).map(([month, amount]: [string, any]) => (\n                <div key={month} className=\"d-flex align-items-center justify-content-between\">\n                  <span className=\"text-white\">{month}</span>\n                  <div className=\"d-flex align-items-center\">\n                    <div className=\"me-3\" style={{ width: '100px' }}>\n                      <ProgressBar\n                        now={(amount / Math.max(...Object.values(monthlySpending).map(v => Number(v)))) * 100}\n                        style={{ height: '8px' }}\n                        variant=\"success\"\n                      />\n                    </div>\n                    <span className=\"text-success fw-semibold\">${amount.toFixed(2)}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg h-100\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <PieChart className=\"me-2\" size={20} />\n              Job Status Distribution\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <div className=\"space-y-3\">\n              {Object.entries(statusCounts).map(([status, count]: [string, any]) => (\n                <div key={status} className=\"d-flex align-items-center justify-content-between\">\n                  <span className=\"text-white\">{status}</span>\n                  <div className=\"d-flex align-items-center\">\n                    <div className=\"me-3\" style={{ width: '100px' }}>\n                      <ProgressBar\n                        now={(count / jobs.length) * 100}\n                        style={{ height: '8px' }}\n                        variant=\"info\"\n                      />\n                    </div>\n                    <span className=\"text-info fw-semibold\">{count}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Settings Tab Component\nconst SettingsTab: React.FC<any> = ({ userSettings, setUserSettings }) => {\n  return (\n    <Row className=\"g-4\">\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <Bell className=\"me-2\" size={20} />\n              Notification Settings\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <Form>\n              <Form.Check\n                type=\"switch\"\n                id=\"email-notifications\"\n                label=\"Email Notifications\"\n                checked={userSettings.notifications.email}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, email: e.target.checked }\n                })}\n                className=\"text-white mb-3\"\n              />\n              <Form.Check\n                type=\"switch\"\n                id=\"push-notifications\"\n                label=\"Push Notifications\"\n                checked={userSettings.notifications.push}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, push: e.target.checked }\n                })}\n                className=\"text-white mb-3\"\n              />\n              <Form.Check\n                type=\"switch\"\n                id=\"sms-notifications\"\n                label=\"SMS Notifications\"\n                checked={userSettings.notifications.sms}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  notifications: { ...userSettings.notifications, sms: e.target.checked }\n                })}\n                className=\"text-white\"\n              />\n            </Form>\n          </Card.Body>\n        </Card>\n      </Col>\n\n      <Col md={6}>\n        <Card className=\"border-0 shadow-lg\" style={{\n          background: 'rgba(255, 255, 255, 0.1)',\n          backdropFilter: 'blur(20px)',\n          borderRadius: '20px'\n        }}>\n          <Card.Header className=\"border-0 bg-transparent\">\n            <h5 className=\"fw-bold mb-0 text-white\">\n              <Settings className=\"me-2\" size={20} />\n              Print Preferences\n            </h5>\n          </Card.Header>\n          <Card.Body>\n            <Form>\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Print Type</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultPrintType}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultPrintType: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"Print\">Print</option>\n                  <option value=\"Xerox\">Xerox</option>\n                  <option value=\"Binding\">Binding</option>\n                  <option value=\"Lamination\">Lamination</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Color Type</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultColorType}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultColorType: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"BlackWhite\">Black & White</option>\n                  <option value=\"Color\">Color</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Group className=\"mb-3\">\n                <Form.Label className=\"text-white\">Default Paper Size</Form.Label>\n                <Form.Select\n                  value={userSettings.preferences.defaultPaperSize}\n                  onChange={(e) => setUserSettings({\n                    ...userSettings,\n                    preferences: { ...userSettings.preferences, defaultPaperSize: e.target.value }\n                  })}\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    border: 'none',\n                    color: '#fff'\n                  }}\n                >\n                  <option value=\"A4\">A4</option>\n                  <option value=\"A3\">A3</option>\n                  <option value=\"Letter\">Letter</option>\n                  <option value=\"Legal\">Legal</option>\n                </Form.Select>\n              </Form.Group>\n\n              <Form.Check\n                type=\"switch\"\n                id=\"auto-confirm\"\n                label=\"Auto-confirm quotes under $10\"\n                checked={userSettings.preferences.autoConfirmQuotes}\n                onChange={(e) => setUserSettings({\n                  ...userSettings,\n                  preferences: { ...userSettings.preferences, autoConfirmQuotes: e.target.checked }\n                })}\n                className=\"text-white\"\n              />\n            </Form>\n          </Card.Body>\n        </Card>\n      </Col>\n    </Row>\n  );\n};\n\n// Upload Modal Component\nconst UploadModal: React.FC<any> = ({\n  show, onHide, uploadData, setUploadData, selectedFile, setSelectedFile, xeroxCenters, onUpload\n}) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <Upload className=\"me-2\" size={20} />\n          Upload Files for Printing\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>\n        <Form>\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Select File</Form.Label>\n            <Form.Control\n              type=\"file\"\n              accept=\".pdf,.doc,.docx,.zip,.rar,.jpg,.jpeg,.png\"\n              onChange={(e) => {\n                const files = (e.target as HTMLInputElement).files;\n                setSelectedFile(files ? files[0] : null);\n              }}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            />\n            <Form.Text className=\"text-muted\">\n              Supported formats: PDF, DOC, DOCX, ZIP, RAR, JPG, JPEG, PNG (Max 50MB)\n            </Form.Text>\n          </Form.Group>\n\n          <Row>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Print Type</Form.Label>\n                <Form.Select\n                  value={uploadData.printType}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, printType: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"Print\">Print</option>\n                  <option value=\"Xerox\">Xerox</option>\n                  <option value=\"Binding\">Binding</option>\n                  <option value=\"Lamination\">Lamination</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Number of Copies</Form.Label>\n                <Form.Control\n                  type=\"number\"\n                  min=\"1\"\n                  value={uploadData.copies}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, copies: parseInt(e.target.value) }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                />\n              </Form.Group>\n            </Col>\n          </Row>\n\n          <Row>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Color Type</Form.Label>\n                <Form.Select\n                  value={uploadData.colorType}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, colorType: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"BlackWhite\">Black & White</option>\n                  <option value=\"Color\">Color</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n            <Col md={6}>\n              <Form.Group className=\"mb-3\">\n                <Form.Label>Paper Size</Form.Label>\n                <Form.Select\n                  value={uploadData.paperSize}\n                  onChange={(e) => setUploadData((prev: any) => ({ ...prev, paperSize: e.target.value }))}\n                  style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n                >\n                  <option value=\"A4\">A4</option>\n                  <option value=\"A3\">A3</option>\n                  <option value=\"Letter\">Letter</option>\n                  <option value=\"Legal\">Legal</option>\n                </Form.Select>\n              </Form.Group>\n            </Col>\n          </Row>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Priority</Form.Label>\n            <Form.Select\n              value={uploadData.priority}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, priority: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            >\n              <option value=\"Low\">Low</option>\n              <option value=\"Normal\">Normal</option>\n              <option value=\"High\">High</option>\n              <option value=\"Urgent\">Urgent</option>\n            </Form.Select>\n          </Form.Group>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Preferred Xerox Center</Form.Label>\n            <Form.Select\n              value={uploadData.preferredXeroxCenterId}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, preferredXeroxCenterId: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            >\n              <option value=\"\">Select a center (optional)</option>\n              {xeroxCenters.map((center: any) => (\n                <option key={center.id} value={center.id}>\n                  {center.name} - {center.pendingJobs} pending jobs\n                </option>\n              ))}\n            </Form.Select>\n          </Form.Group>\n\n          <Form.Group className=\"mb-3\">\n            <Form.Label>Remarks</Form.Label>\n            <Form.Control\n              as=\"textarea\"\n              rows={3}\n              placeholder=\"Any special instructions or remarks...\"\n              value={uploadData.remarks}\n              onChange={(e) => setUploadData((prev: any) => ({ ...prev, remarks: e.target.value }))}\n              style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n            />\n          </Form.Group>\n        </Form>\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Cancel\n        </Button>\n        <Button\n          onClick={onUpload}\n          disabled={!selectedFile}\n          style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}\n        >\n          <Upload className=\"me-2\" size={16} />\n          Upload File\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\n// View Job Modal Component\nconst ViewJobModal: React.FC<any> = ({ show, onHide, job, getStatusBadge, getPriorityBadge }) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <Eye className=\"me-2\" size={20} />\n          Job Details\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff' }}>\n        {job && (\n          <Row>\n            <Col md={6}>\n              <h6 className=\"text-primary\">Job Information</h6>\n              <p><strong>Job Number:</strong> {job.jobNumber}</p>\n              <p><strong>Status:</strong> {getStatusBadge(job.status)}</p>\n              <p><strong>Priority:</strong> {getPriorityBadge(job.priority)}</p>\n              <p><strong>File Name:</strong> {job.fileName}</p>\n              <p><strong>Xerox Center:</strong> {job.xeroxCenterName}</p>\n              {job.cost && <p><strong>Cost:</strong> <span className=\"text-success\">${job.cost.toFixed(2)}</span></p>}\n              <p><strong>Created:</strong> {new Date(job.created).toLocaleString()}</p>\n              {job.estimatedCompletionTime && (\n                <p><strong>Estimated Completion:</strong> {new Date(job.estimatedCompletionTime).toLocaleString()}</p>\n              )}\n            </Col>\n            <Col md={6}>\n              <h6 className=\"text-primary\">Print Specifications</h6>\n              <p><strong>Type:</strong> {job.printType}</p>\n              <p><strong>Copies:</strong> {job.copies}</p>\n              <p><strong>Color:</strong> {job.colorType}</p>\n              <p><strong>Paper Size:</strong> {job.paperSize}</p>\n              {job.remarks && (\n                <>\n                  <h6 className=\"text-primary mt-3\">Remarks</h6>\n                  <p className=\"text-muted\">{job.remarks}</p>\n                </>\n              )}\n            </Col>\n          </Row>\n        )}\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <Button variant=\"secondary\" onClick={onHide}>\n          Close\n        </Button>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\n// Chat Modal Component\nconst ChatModal: React.FC<any> = ({\n  show, onHide, job, messages, chatMessage, setChatMessage, onSendMessage\n}) => {\n  return (\n    <Modal show={show} onHide={onHide} size=\"lg\" centered>\n      <Modal.Header closeButton style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}>\n        <Modal.Title className=\"text-white\">\n          <MessageCircle className=\"me-2\" size={20} />\n          Chat - {job?.jobNumber}\n        </Modal.Title>\n      </Modal.Header>\n      <Modal.Body style={{ background: '#1a1a1a', color: '#fff', height: '400px', overflowY: 'auto' }}>\n        <div className=\"mb-3\">\n          {messages.map((message: any, index: number) => (\n            <div key={index} className={`mb-2 ${message.isFromStudent ? 'text-end' : 'text-start'}`}>\n              <div className={`d-inline-block p-2 rounded ${\n                message.isFromStudent\n                  ? 'text-white'\n                  : 'bg-secondary text-white'\n              }`} style={{\n                background: message.isFromStudent\n                  ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'\n                  : '#6c757d'\n              }}>\n                {message.content}\n              </div>\n              <div className=\"small text-muted\">\n                {new Date(message.timestamp).toLocaleString()}\n              </div>\n            </div>\n          ))}\n        </div>\n      </Modal.Body>\n      <Modal.Footer style={{ background: '#1a1a1a', border: 'none' }}>\n        <InputGroup>\n          <Form.Control\n            type=\"text\"\n            placeholder=\"Type your message...\"\n            value={chatMessage}\n            onChange={(e) => setChatMessage(e.target.value)}\n            onKeyPress={(e) => e.key === 'Enter' && onSendMessage()}\n            style={{ background: '#2a2a2a', border: '1px solid #444', color: '#fff' }}\n          />\n          <Button\n            onClick={onSendMessage}\n            style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: 'none' }}\n          >\n            <Send size={16} />\n          </Button>\n        </InputGroup>\n      </Modal.Footer>\n    </Modal>\n  );\n};\n\nexport default AceternityStudentDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,EAAEC,GAAG,EAAOC,KAAK,EAAEC,WAAW,QAAQ,iBAAiB;AACjI,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,GAAG,EAEHC,QAAQ,EACRC,OAAO,EAEPC,IAAI,EACJC,OAAO,EACPC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,IAAI,EACJC,MAAM,EAGNC,UAAU,EACVC,SAAS,EACTC,QAAQ,EAERC,MAAM,EACNC,KAAK,EAELC,KAAK,EAELC,SAAS,EACTC,WAAW,EAEXC,YAAY,EACZC,OAAO,EAGPC,IAAI,EACJC,KAAK,EAGLC,MAAM,IAAIC,QAAQ,QACb,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,WAAW,EAAEC,cAAc,EAAEC,aAAa,EAAEC,UAAU,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAkC5F,MAAMC,0BAAoC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjD,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAW,EAAE,CAAC;EACtE,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxE,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACyE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAM,IAAI,CAAC;EACzD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAqB,IAAI,CAAC;EAC9E,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAGhF,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACqF,UAAU,EAAEC,aAAa,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuF,YAAY,EAAEC,eAAe,CAAC,GAAGxF,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyF,MAAM,EAAEC,SAAS,CAAC,GAAG1F,QAAQ,CAAC,SAAS,CAAC;EAC/C,MAAM,CAAC2F,YAAY,EAAEC,eAAe,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6F,UAAU,EAAEC,aAAa,CAAC,GAAG9F,QAAQ,CAAC;IAC3C+F,OAAO,EAAE,EAAE;IACXC,sBAAsB,EAAE,EAAE;IAC1BC,SAAS,EAAE,OAAO;IAClBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,IAAI;IACfC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvG,QAAQ,CAAC;IAC/CwG,aAAa,EAAE;MACbC,KAAK,EAAE,IAAI;MACXC,IAAI,EAAE,IAAI;MACVC,GAAG,EAAE;IACP,CAAC;IACDC,WAAW,EAAE;MACXC,gBAAgB,EAAE,OAAO;MACzBC,gBAAgB,EAAE,YAAY;MAC9BC,gBAAgB,EAAE,IAAI;MACtBC,iBAAiB,EAAE;IACrB,CAAC;IACDC,OAAO,EAAE;MACPC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE,EAAE;MACXC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE;IACb;EACF,CAAC,CAAC;EAEFpH,SAAS,CAAC,MAAM;IACdqH,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B1B,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClE7E,WAAW,CAAC8E,cAAc,CAAC,CAAC,EAC5B7E,cAAc,CAAC8E,MAAM,CAAC,CAAC,CACxB,CAAC;MAEFjE,YAAY,CAAC4D,iBAAiB,CAACM,IAAI,IAAI,EAAE,CAAC;MAC1ChE,eAAe,CAAC2D,oBAAoB,CAACK,IAAI,IAAI,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CnE,YAAY,CAAC,EAAE,CAAC;MAChBE,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACR4B,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMuC,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAErH;MAAM,CAAC;MAC7C,aAAa,EAAE;QAAEoH,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAEhH;MAAI,CAAC;MACxC,QAAQ,EAAE;QAAE+G,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAEnH;MAAW,CAAC;MAC7C,qBAAqB,EAAE;QAAEkH,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE/F;MAAY,CAAC;MAC3D,WAAW,EAAE;QAAE8F,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAEpH;MAAY,CAAC;MAC9C,YAAY,EAAE;QAAEmH,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE/G;MAAS,CAAC;MAC/C,WAAW,EAAE;QAAE8G,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE9F;MAAa,CAAC;MAClD,WAAW,EAAE;QAAE6F,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE9F;MAAa,CAAC;MAClD,UAAU,EAAE;QAAE6F,EAAE,EAAE,QAAQ;QAAEC,IAAI,EAAE7F;MAAQ,CAAC;MAC3C,WAAW,EAAE;QAAE4F,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE7F;MAAQ;IAChD,CAAC;IAED,MAAM8F,MAAM,GAAGH,YAAY,CAACD,MAAM,CAA8B,IAAI;MAAEE,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAErH;IAAM,CAAC;IACpG,MAAMuH,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACElF,OAAA,CAAC9C,KAAK;MAAC+H,EAAE,EAAEE,MAAM,CAACF,EAAG;MAACI,SAAS,EAAC,2CAA2C;MAAAC,QAAA,gBACzEtF,OAAA,CAACoF,aAAa;QAACG,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1BZ,MAAM;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,gBAAgB,GAAI5C,QAAgB,IAAK;IAC7C,MAAM6C,cAAc,GAAG;MACrB,KAAK,EAAE;QAAEZ,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAK,CAAC;MACpC,QAAQ,EAAE;QAAED,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAK,CAAC;MACzC,MAAM,EAAE;QAAED,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAK,CAAC;MACrC,QAAQ,EAAE;QAAED,EAAE,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK;IACvC,CAAC;IAED,MAAMC,MAAM,GAAGU,cAAc,CAAC7C,QAAQ,CAAgC,IAAI;MAAEiC,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAK,CAAC;IAEzG,oBACElF,OAAA,CAAC9C,KAAK;MAAC+H,EAAE,EAAEE,MAAM,CAACF,EAAG;MAACI,SAAS,EAAC,WAAW;MAAAC,QAAA,GACxCH,MAAM,CAACD,IAAI,EAAC,GAAC,EAAClC,QAAQ;IAAA;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEZ,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACpE,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMqE,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEvE,YAAY,CAAC;MACrCwE,MAAM,CAACC,OAAO,CAAC3D,UAAU,CAAC,CAAC4D,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACnDP,QAAQ,CAACE,MAAM,CAACI,GAAG,EAAEC,KAAK,CAACC,QAAQ,CAAC,CAAC,CAAC;MACxC,CAAC,CAAC;MAEF,MAAM1G,aAAa,CAAC2G,UAAU,CAACT,QAAQ,CAAC;MACxC,MAAM9B,SAAS,CAAC,CAAC;MACjBlD,kBAAkB,CAAC,KAAK,CAAC;MACzBY,eAAe,CAAC,IAAI,CAAC;MACrBc,aAAa,CAAC;QACZC,OAAO,EAAE,EAAE;QACXC,sBAAsB,EAAE,EAAE;QAC1BC,SAAS,EAAE,OAAO;QAClBC,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,YAAY;QACvBC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC,OAAO4B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAM6B,aAAa,GAAIC,GAAQ,IAAK;IAClCnF,cAAc,CAACmF,GAAG,CAAC;IACnBzF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM0F,cAAc,GAAG,MAAOD,GAAQ,IAAK;IACzC,IAAI;MACFnF,cAAc,CAACmF,GAAG,CAAC;MACnBvF,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMyF,QAAQ,GAAG,MAAM9G,UAAU,CAAC+G,cAAc,CAACH,GAAG,CAACI,EAAE,CAAC;MACxD/E,WAAW,CAAC6E,QAAQ,CAACjC,IAAI,IAAI,EAAE,CAAC;IAClC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC;EAED,MAAMgF,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAExB,QAAgB,KAAK;IACpE,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAM/G,aAAa,CAACoH,YAAY,CAACD,KAAK,CAAC;MACxD,MAAME,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACP,QAAQ,CAACjC,IAAI,CAAC,CAAC;MACtC,MAAMyC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC5C,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;MACfI,IAAI,CAACI,QAAQ,GAAGpC,QAAQ;MACxBiC,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;MAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;MACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;MAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;IACjC,CAAC,CAAC,OAAOxC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAMsD,gBAAgB,GAAG,MAAOlB,KAAa,IAAK;IAChD,IAAI;MACF,MAAMrH,WAAW,CAACwI,UAAU,CAACnB,KAAK,CAAC;MACnC,MAAM/C,SAAS,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C;EACF,CAAC;EAED,MAAMwD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACxG,WAAW,CAACyG,IAAI,CAAC,CAAC,IAAI,CAAC/G,WAAW,EAAE;IAEzC,IAAI;MACF,MAAMsF,QAAQ,GAAG,MAAM9G,UAAU,CAACwI,WAAW,CAAChH,WAAW,CAACwF,EAAE,EAAElF,WAAW,CAACyG,IAAI,CAAC,CAAC,CAAC;MACjFtG,WAAW,CAACwG,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE3B,QAAQ,CAACjC,IAAI,CAAC,CAAC;MAC7C9C,cAAc,CAAC,EAAE,CAAC;IACpB,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD;EACF,CAAC;EAED,MAAM4D,cAAc,GAAIC,QAAgB,IAAK;IAC3C5H,mBAAmB,CAAC0H,IAAI,IACtBA,IAAI,CAACG,QAAQ,CAACD,QAAQ,CAAC,GACnBF,IAAI,CAACI,MAAM,CAAC7B,EAAE,IAAIA,EAAE,KAAK2B,QAAQ,CAAC,GAClC,CAAC,GAAGF,IAAI,EAAEE,QAAQ,CACxB,CAAC;EACH,CAAC;EAED,MAAMG,YAAY,GAAGpI,SAAS,CAACmI,MAAM,CAACjC,GAAG,IAAI;IAC3C,MAAMmC,aAAa,GAAG7G,UAAU,KAAK,EAAE,IACrC0E,GAAG,CAACoC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACL,QAAQ,CAAC1G,UAAU,CAAC+G,WAAW,CAAC,CAAC,CAAC,IAC9DrC,GAAG,CAAClB,QAAQ,CAACuD,WAAW,CAAC,CAAC,CAACL,QAAQ,CAAC1G,UAAU,CAAC+G,WAAW,CAAC,CAAC,CAAC,IAC7DrC,GAAG,CAACsC,eAAe,CAACD,WAAW,CAAC,CAAC,CAACL,QAAQ,CAAC1G,UAAU,CAAC+G,WAAW,CAAC,CAAC,CAAC;IAEtE,MAAME,aAAa,GAAG/G,YAAY,KAAK,KAAK,IAAIwE,GAAG,CAAC3B,MAAM,KAAK7C,YAAY;IAE3E,OAAO2G,aAAa,IAAII,aAAa;EACvC,CAAC,CAAC;EAEF,MAAMC,UAAU,GAAG1I,SAAS,CAAC2I,MAAM,CAAC,CAACC,GAAG,EAAE1C,GAAG,KAAK0C,GAAG,IAAI1C,GAAG,CAAC2C,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E,MAAMC,cAAc,GAAG9I,SAAS,CAACmI,MAAM,CAACjC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACgC,QAAQ,CAAChC,GAAG,CAAC3B,MAAM,CAAC,CAAC,CAACwE,MAAM;EACtH,MAAMC,aAAa,GAAGhJ,SAAS,CAACmI,MAAM,CAACjC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACgC,QAAQ,CAAChC,GAAG,CAAC3B,MAAM,CAAC,CAAC,CAACwE,MAAM;EACrG,MAAME,WAAW,GAAGjJ,SAAS,CAACmI,MAAM,CAACjC,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAACgC,QAAQ,CAAChC,GAAG,CAAC3B,MAAM,CAAC,CAAC,CAACwE,MAAM;EAE/G,MAAMG,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBN,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEM,CAAC,EAAE;IAAG,CAAC;IAC7BL,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVM,CAAC,EAAE,CAAC;MACJJ,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;EAED,MAAMI,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEd,MAAM,EAAE;EAAE,CAAC,EAAE,CAACe,CAAC,EAAEC,CAAC,kBACpDvK,OAAA,CAACvC,MAAM,CAAC+M,GAAG;IAETnF,SAAS,EAAC,kCAAkC;IAC5CoF,KAAK,EAAE;MACLC,UAAU,EAAE,2BAA2BH,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,KAAKA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,GAAG;MACzKI,KAAK,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACrCC,MAAM,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACtCE,IAAI,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC/BG,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC9BjB,OAAO,EAAE,GAAG;MACZqB,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAE;MACPC,CAAC,EAAE,CAAC,CAAC,EAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCX,CAAC,EAAE,CAAC,CAAC,EAAEU,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCO,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;MAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAE;IACFvB,UAAU,EAAE;MACVC,QAAQ,EAAEa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACjCS,MAAM,EAAEC,QAAQ;MAChBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE;IACR;EAAE,GAtBGlB,CAAC;IAAA/E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAuBP,CACF,CAAC;EAEF,oBACE3F,OAAA;IAAKqF,SAAS,EAAC,gCAAgC;IAACoF,KAAK,EAAE;MACrDC,UAAU,EAAE,mDAAmD;MAC/DgB,SAAS,EAAE;IACb,CAAE;IAAApG,QAAA,gBAEAtF,OAAA;MAAKqF,SAAS,EAAC,+CAA+C;MAAAC,QAAA,GAC3D6E,cAAc,eACfnK,OAAA;QAAKqF,SAAS,EAAC,+BAA+B;QAACoF,KAAK,EAAE;UACpDC,UAAU,EAAE;QACd;MAAE;QAAAlF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEN3F,OAAA,CAACnD,SAAS;MAAC8O,KAAK;MAACtG,SAAS,EAAC,wBAAwB;MAACoF,KAAK,EAAE;QAAEQ,MAAM,EAAE;MAAE,CAAE;MAAA3F,QAAA,eACvEtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACToB,QAAQ,EAAElC,iBAAkB;QAC5BmC,OAAO,EAAC,QAAQ;QAChBX,OAAO,EAAC,SAAS;QAAA5F,QAAA,gBAGjBtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;UAACoB,QAAQ,EAAE3B,YAAa;UAAC5E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC9DtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;YACTU,OAAO,EAAE;cACPE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;cAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACnB,CAAE;YACFvB,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXuB,MAAM,EAAEC,QAAQ;cAChBE,IAAI,EAAE;YACR,CAAE;YACFpG,SAAS,EAAC,8DAA8D;YACxEoF,KAAK,EAAE;cACLE,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACdJ,UAAU,EAAE,mDAAmD;cAC/DoB,YAAY,EAAE,MAAM;cACpBC,SAAS,EAAE;YACb,CAAE;YAAAzG,QAAA,eAEFtF,OAAA,CAACxB,IAAI;cAAC6G,SAAS,EAAC,YAAY;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACb3F,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAACoF,KAAK,EAAE;cAAEuB,QAAQ,EAAE;YAAS,CAAE;YAAA1G,QAAA,GAAC,gBACvD,EAACjF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4L,QAAQ,EAAC,GAChC;UAAA;YAAAzG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL3F,OAAA;YAAGqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ3F,OAAA;YAAKqF,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/EtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;cAAC0B,UAAU,EAAE;gBAAEb,KAAK,EAAE;cAAK,CAAE;cAACc,QAAQ,EAAE;gBAAEd,KAAK,EAAE;cAAK,CAAE;cAAA/F,QAAA,eACjEtF,OAAA,CAAC/C,MAAM;gBACLsI,IAAI,EAAC,IAAI;gBACT6G,OAAO,EAAEA,CAAA,KAAMrL,kBAAkB,CAAC,IAAI,CAAE;gBACxCsE,SAAS,EAAC,0CAA0C;gBACpDoF,KAAK,EAAE;kBACLC,UAAU,EAAE,0BAA0B;kBACtC2B,cAAc,EAAE,YAAY;kBAC5BC,KAAK,EAAE,MAAM;kBACbP,SAAS,EAAE;gBACb,CAAE;gBAAAzG,QAAA,gBAEFtF,OAAA,CAACrC,MAAM;kBAAC0H,SAAS,EAAC,MAAM;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEb3F,OAAA,CAACvC,MAAM,CAAC+M,GAAG;cAAC0B,UAAU,EAAE;gBAAEb,KAAK,EAAE;cAAK,CAAE;cAACc,QAAQ,EAAE;gBAAEd,KAAK,EAAE;cAAK,CAAE;cAAA/F,QAAA,eACjEtF,OAAA,CAAC/C,MAAM;gBACLsP,OAAO,EAAC,eAAe;gBACvBhH,IAAI,EAAC,IAAI;gBACT6G,OAAO,EAAEnI,SAAU;gBACnBuI,QAAQ,EAAElK,YAAa;gBACvB+C,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAE3CtF,OAAA,CAACd,SAAS;kBAACmG,SAAS,EAAE,QAAQ/C,YAAY,GAAG,MAAM,GAAG,EAAE,EAAG;kBAACiD,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb3F,OAAA,CAACvC,MAAM,CAAC+M,GAAG;UAACoB,QAAQ,EAAE3B,YAAa;UAAC5E,SAAS,EAAC,MAAM;UAAAC,QAAA,eAClDtF,OAAA,CAAChD,IAAI;YAACqI,SAAS,EAAC,oBAAoB;YAACoF,KAAK,EAAE;cAC1CC,UAAU,EAAE,0BAA0B;cACtC2B,cAAc,EAAE,YAAY;cAC5BP,YAAY,EAAE;YAChB,CAAE;YAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;cAACpH,SAAS,EAAC,KAAK;cAAAC,QAAA,eACxBtF,OAAA,CAAC1C,GAAG;gBAACiP,OAAO,EAAC,OAAO;gBAAClH,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9D,CACC;kBAAEe,GAAG,EAAE,WAAW;kBAAEqG,KAAK,EAAE,WAAW;kBAAExH,IAAI,EAAErG;gBAAU,CAAC,EACzD;kBAAEwH,GAAG,EAAE,MAAM;kBAAEqG,KAAK,EAAE,SAAS;kBAAExH,IAAI,EAAEtH;gBAAS,CAAC,EACjD;kBAAEyI,GAAG,EAAE,SAAS;kBAAEqG,KAAK,EAAE,eAAe;kBAAExH,IAAI,EAAE9G;gBAAQ,CAAC,EACzD;kBAAEiI,GAAG,EAAE,SAAS;kBAAEqG,KAAK,EAAE,SAAS;kBAAExH,IAAI,EAAE5G;gBAAQ,CAAC,EACnD;kBAAE+H,GAAG,EAAE,WAAW;kBAAEqG,KAAK,EAAE,WAAW;kBAAExH,IAAI,EAAEjG;gBAAM,CAAC,EACrD;kBAAEoH,GAAG,EAAE,WAAW;kBAAEqG,KAAK,EAAE,WAAW;kBAAExH,IAAI,EAAEpG;gBAAS,CAAC,EACxD;kBAAEuH,GAAG,EAAE,UAAU;kBAAEqG,KAAK,EAAE,UAAU;kBAAExH,IAAI,EAAE3G;gBAAS,CAAC,CACvD,CAACoO,GAAG,CAACC,GAAG,IAAI;kBACX,MAAMxH,aAAa,GAAGwH,GAAG,CAAC1H,IAAI;kBAC9B,oBACElF,OAAA,CAAC1C,GAAG,CAACuP,IAAI;oBAAexH,SAAS,EAAC,KAAK;oBAAAC,QAAA,eACrCtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;sBAAC0B,UAAU,EAAE;wBAAEb,KAAK,EAAE;sBAAK,CAAE;sBAACc,QAAQ,EAAE;wBAAEd,KAAK,EAAE;sBAAK,CAAE;sBAAA/F,QAAA,eACjEtF,OAAA,CAAC1C,GAAG,CAACwP,IAAI;wBACPC,MAAM,EAAEzM,SAAS,KAAKsM,GAAG,CAACvG,GAAI;wBAC9B+F,OAAO,EAAEA,CAAA,KAAM7L,YAAY,CAACqM,GAAG,CAACvG,GAAG,CAAE;wBACrChB,SAAS,EAAE,6DACT/E,SAAS,KAAKsM,GAAG,CAACvG,GAAG,GACjB,cAAc,GACd,eAAe,EAClB;wBACHoE,KAAK,EAAE;0BACLC,UAAU,EAAEpK,SAAS,KAAKsM,GAAG,CAACvG,GAAG,GAC7B,0BAA0B,GAC1B,aAAa;0BACjB2G,MAAM,EAAE,MAAM;0BACdlD,UAAU,EAAE,eAAe;0BAC3BiC,SAAS,EAAEzL,SAAS,KAAKsM,GAAG,CAACvG,GAAG,GAC5B,sCAAsC,GACtC;wBACN,CAAE;wBAAAf,QAAA,gBAEFtF,OAAA,CAACoF,aAAa;0BAACG,IAAI,EAAE,EAAG;0BAACF,SAAS,EAAC;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5C3F,OAAA;0BAAMqF,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAAEsH,GAAG,CAACF;wBAAK;0BAAAlH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GAxBAiH,GAAG,CAACvG,GAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBZ,CAAC;gBAEf,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGb3F,OAAA,CAACtC,eAAe;UAACuP,IAAI,EAAC,MAAM;UAAA3H,QAAA,eAC1BtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;YAETqB,OAAO,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAG,CAAE;YAC/BgB,OAAO,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAE,CAAE;YAC9BgD,IAAI,EAAE;cAAEtD,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BJ,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAzE,QAAA,GAE7BhF,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAACmN,YAAY;cACX3M,SAAS,EAAEA,SAAU;cACrBE,YAAY,EAAEA,YAAa;cAC3BwI,UAAU,EAAEA,UAAW;cACvBI,cAAc,EAAEA,cAAe;cAC/BE,aAAa,EAAEA,aAAc;cAC7BC,WAAW,EAAEA,WAAY;cACzB2D,SAAS,EAAE3G,aAAc;cACzB4G,cAAc,EAAEtG,kBAAmB;cACnCuG,UAAU,EAAE3G,cAAe;cAC3B4G,YAAY,EAAErF,gBAAiB;cAC/BpD,cAAc,EAAEA,cAAe;cAC/Bc,gBAAgB,EAAEA;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACF,EAEArF,SAAS,KAAK,MAAM,iBACnBN,OAAA,CAACwN,OAAO;cACNC,IAAI,EAAE7E,YAAa;cACnB5G,UAAU,EAAEA,UAAW;cACvBC,aAAa,EAAEA,aAAc;cAC7BC,YAAY,EAAEA,YAAa;cAC3BC,eAAe,EAAEA,eAAgB;cACjCC,MAAM,EAAEA,MAAO;cACfC,SAAS,EAAEA,SAAU;cACrB+K,SAAS,EAAE3G,aAAc;cACzB4G,cAAc,EAAEtG,kBAAmB;cACnCuG,UAAU,EAAE3G,cAAe;cAC3B4G,YAAY,EAAErF,gBAAiB;cAC/BpD,cAAc,EAAEA,cAAe;cAC/Bc,gBAAgB,EAAEA;YAAiB;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CACF,EAEArF,SAAS,KAAK,SAAS,iBACtBN,OAAA,CAAC0N,UAAU;cACTC,OAAO,EAAEjN,YAAa;cACtBE,gBAAgB,EAAEA,gBAAiB;cACnCgN,gBAAgB,EAAEpF,cAAe;cACjCqF,cAAc,EAAEpM;YAAkB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACF,EAEArF,SAAS,KAAK,SAAS,iBACtBN,OAAA,CAAC8N,UAAU;cACTL,IAAI,EAAEjN,SAAS,CAACmI,MAAM,CAACjC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACgC,QAAQ,CAAChC,GAAG,CAAC3B,MAAM,CAAC,CAAE;cACxGqI,SAAS,EAAE3G,aAAc;cACzB3B,cAAc,EAAEA;YAAe;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACF,EAEArF,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAC+N,YAAY;cACXJ,OAAO,EAAEjN,YAAY,CAACiI,MAAM,CAACqF,MAAM,IAAIpN,gBAAgB,CAAC8H,QAAQ,CAACsF,MAAM,CAAClH,EAAE,CAAC,CAAE;cAC7E8G,gBAAgB,EAAEpF,cAAe;cACjCqF,cAAc,EAAEpM;YAAkB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CACF,EAEArF,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAACiO,YAAY;cACXR,IAAI,EAAEjN,SAAU;cAChB0I,UAAU,EAAEA;YAAW;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CACF,EAEArF,SAAS,KAAK,UAAU,iBACvBN,OAAA,CAACkO,WAAW;cACVjL,YAAY,EAAEA,YAAa;cAC3BC,eAAe,EAAEA;YAAgB;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CACF;UAAA,GA9EIrF,SAAS;YAAAkF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+EJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGX7E,eAAe,iBACdd,OAAA,CAACmO,WAAW;MACVC,IAAI,EAAEtN,eAAgB;MACtBuN,MAAM,EAAEA,CAAA,KAAMtN,kBAAkB,CAAC,KAAK,CAAE;MACxCyB,UAAU,EAAEA,UAAW;MACvBC,aAAa,EAAEA,aAAc;MAC7Bf,YAAY,EAAEA,YAAa;MAC3BC,eAAe,EAAEA,eAAgB;MACjCjB,YAAY,EAAEA,YAAa;MAC3B4N,QAAQ,EAAExI;IAAiB;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF,EAEA3E,aAAa,iBACZhB,OAAA,CAACuO,YAAY;MACXH,IAAI,EAAEpN,aAAc;MACpBqN,MAAM,EAAEA,CAAA,KAAMpN,gBAAgB,CAAC,KAAK,CAAE;MACtCyF,GAAG,EAAEpF,WAAY;MACjBwD,cAAc,EAAEA,cAAe;MAC/Bc,gBAAgB,EAAEA;IAAiB;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CACF,EAEAzE,aAAa,iBACZlB,OAAA,CAACwO,SAAS;MACRJ,IAAI,EAAElN,aAAc;MACpBmN,MAAM,EAAEA,CAAA,KAAMlN,gBAAgB,CAAC,KAAK,CAAE;MACtCuF,GAAG,EAAEpF,WAAY;MACjBQ,QAAQ,EAAEA,QAAS;MACnBF,WAAW,EAAEA,WAAY;MACzBC,cAAc,EAAEA,cAAe;MAC/B4M,aAAa,EAAErG;IAAkB;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAvF,EAAA,CAphBMD,0BAAoC;EAAA,QACvBT,OAAO;AAAA;AAAAgP,EAAA,GADpBvO,0BAAoC;AAqhB1C,MAAMgN,YAA2B,GAAGA,CAAC;EACnC3M,SAAS;EAAEE,YAAY;EAAEwI,UAAU;EAAEI,cAAc;EAAEE,aAAa;EAAEC,WAAW;EAC/E2D,SAAS;EAAEC,cAAc;EAAEC,UAAU;EAAEC,YAAY;EAAEzI,cAAc;EAAEc;AACvE,CAAC,KAAK;EACJ,oBACE5F,OAAA,CAAClD,GAAG;IAACuI,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBtF,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAI,CAAE;QACpCH,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAE,CAAE;QAClCvB,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BmC,UAAU,EAAE;UAAEhC,CAAC,EAAE,CAAC,CAAC;UAAEmB,KAAK,EAAE;QAAK,CAAE;QAAA/F,QAAA,eAEnCtF,OAAA,CAAChD,IAAI;UAACqI,SAAS,EAAC,0BAA0B;UAACoF,KAAK,EAAE;YAChDC,UAAU,EAAE,0BAA0B;YACtC2B,cAAc,EAAE,YAAY;YAC5BP,YAAY,EAAE;UAChB,CAAE;UAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;YAACpH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBACpCtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtF,OAAA;gBAAKqF,SAAS,EAAC,yDAAyD;gBAACoF,KAAK,EAAE;kBAC9EE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DoB,YAAY,EAAE;gBAChB,CAAE;gBAAAxG,QAAA,eACAtF,OAAA,CAACpC,QAAQ;kBAACyH,SAAS,EAAC,YAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClD3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAE9E,SAAS,CAAC+I;YAAM;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1D3F,OAAA;cAAOqF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7BtF,OAAA,CAACpB,UAAU;gBAAC2G,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN3F,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAI,CAAE;QACpCH,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAE,CAAE;QAClCvB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAE6E,KAAK,EAAE;QAAI,CAAE;QAC1C1C,UAAU,EAAE;UAAEhC,CAAC,EAAE,CAAC,CAAC;UAAEmB,KAAK,EAAE;QAAK,CAAE;QAAA/F,QAAA,eAEnCtF,OAAA,CAAChD,IAAI;UAACqI,SAAS,EAAC,0BAA0B;UAACoF,KAAK,EAAE;YAChDC,UAAU,EAAE,0BAA0B;YACtC2B,cAAc,EAAE,YAAY;YAC5BP,YAAY,EAAE;UAChB,CAAE;UAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;YAACpH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBACpCtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtF,OAAA;gBAAKqF,SAAS,EAAC,yDAAyD;gBAACoF,KAAK,EAAE;kBAC9EE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DoB,YAAY,EAAE;gBAChB,CAAE;gBAAAxG,QAAA,eACAtF,OAAA,CAACnC,KAAK;kBAACwH,SAAS,EAAC,YAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEgE;YAAc;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD3F,OAAA;cAAOqF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7BtF,OAAA,CAAC7B,QAAQ;gBAACoH,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN3F,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAI,CAAE;QACpCH,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAE,CAAE;QAClCvB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAE6E,KAAK,EAAE;QAAI,CAAE;QAC1C1C,UAAU,EAAE;UAAEhC,CAAC,EAAE,CAAC,CAAC;UAAEmB,KAAK,EAAE;QAAK,CAAE;QAAA/F,QAAA,eAEnCtF,OAAA,CAAChD,IAAI;UAACqI,SAAS,EAAC,0BAA0B;UAACoF,KAAK,EAAE;YAChDC,UAAU,EAAE,0BAA0B;YACtC2B,cAAc,EAAE,YAAY;YAC5BP,YAAY,EAAE;UAChB,CAAE;UAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;YAACpH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBACpCtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtF,OAAA;gBAAKqF,SAAS,EAAC,yDAAyD;gBAACoF,KAAK,EAAE;kBAC9EE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DoB,YAAY,EAAE;gBAChB,CAAE;gBAAAxG,QAAA,eACAtF,OAAA,CAAClC,WAAW;kBAACuH,SAAS,EAAC,YAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAEkE;YAAa;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACvD3F,OAAA;cAAOqF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC7BtF,OAAA,CAAChB,KAAK;gBAACuG,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAEtC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAEN3F,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAI,CAAE;QACpCH,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAE,CAAE;QAClCvB,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAE6E,KAAK,EAAE;QAAI,CAAE;QAC1C1C,UAAU,EAAE;UAAEhC,CAAC,EAAE,CAAC,CAAC;UAAEmB,KAAK,EAAE;QAAK,CAAE;QAAA/F,QAAA,eAEnCtF,OAAA,CAAChD,IAAI;UAACqI,SAAS,EAAC,0BAA0B;UAACoF,KAAK,EAAE;YAChDC,UAAU,EAAE,0BAA0B;YACtC2B,cAAc,EAAE,YAAY;YAC5BP,YAAY,EAAE;UAChB,CAAE;UAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;YAACpH,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBACpCtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtF,OAAA;gBAAKqF,SAAS,EAAC,yDAAyD;gBAACoF,KAAK,EAAE;kBAC9EE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DoB,YAAY,EAAE;gBAChB,CAAE;gBAAAxG,QAAA,eACAtF,OAAA,CAACjC,UAAU;kBAACsH,SAAS,EAAC,YAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD3F,OAAA;cAAIqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,GAAC,GAAC,EAAC4D,UAAU,CAAC2F,OAAO,CAAC,CAAC,CAAC;YAAA;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChE3F,OAAA;cAAOqF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAC1BtF,OAAA,CAACvB,UAAU;gBAAC8G,IAAI,EAAE,EAAG;gBAACF,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,cAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN3F,OAAA,CAACjD,GAAG;MAAC+R,EAAE,EAAE,CAAE;MAAAxJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,0BAA0B;QAACoF,KAAK,EAAE;UAChDC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAKqF,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEtF,OAAA;cAAIqF,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACrCtF,OAAA,CAAC7B,QAAQ;gBAACkH,SAAS,EAAC,MAAM;gBAACE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEzC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL3F,OAAA,CAAC9C,KAAK;cAAC+H,EAAE,EAAC,OAAO;cAAC+J,IAAI,EAAC,MAAM;cAAC3J,SAAS,EAAC,WAAW;cAAAC,QAAA,GAChD9E,SAAS,CAAC+I,MAAM,EAAC,QACpB;YAAA;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,EACP9E,SAAS,CAAC+I,MAAM,GAAG,CAAC,gBACnBvJ,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB9E,SAAS,CAACyO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtC,GAAG,CAAC,CAACjG,GAAa,EAAEwI,KAAa,kBACtDlP,OAAA,CAACvC,MAAM,CAAC+M,GAAG;cAETqB,OAAO,EAAE;gBAAEjC,OAAO,EAAE,CAAC;gBAAEuB,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCD,OAAO,EAAE;gBAAEtB,OAAO,EAAE,CAAC;gBAAEuB,CAAC,EAAE;cAAE,CAAE;cAC9BrB,UAAU,EAAE;gBAAE8E,KAAK,EAAEM,KAAK,GAAG;cAAI,CAAE;cACnC7J,SAAS,EAAC,eAAe;cACzBoF,KAAK,EAAE;gBACLC,UAAU,EAAE,0BAA0B;gBACtCsC,MAAM,EAAE;cACV,CAAE;cAAA1H,QAAA,eAEFtF,OAAA;gBAAKqF,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEtF,OAAA;kBAAKqF,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCtF,OAAA;oBAAKqF,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBtF,OAAA;sBAAKqF,SAAS,EAAC,yDAAyD;sBAACoF,KAAK,EAAE;wBAC9EE,KAAK,EAAE,MAAM;wBACbG,MAAM,EAAE,MAAM;wBACdJ,UAAU,EAAE,mDAAmD;wBAC/DoB,YAAY,EAAE;sBAChB,CAAE;sBAAAxG,QAAA,eACAtF,OAAA,CAACpC,QAAQ;wBAACyH,SAAS,EAAC,YAAY;wBAACE,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3F,OAAA;oBAAAsF,QAAA,gBACEtF,OAAA;sBAAIqF,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAEoB,GAAG,CAACoC;oBAAS;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChE3F,OAAA;sBAAGqF,SAAS,EAAC,0BAA0B;sBAAAC,QAAA,EACpCoB,GAAG,CAAClB,QAAQ,CAAC+D,MAAM,GAAG,EAAE,GAAG7C,GAAG,CAAClB,QAAQ,CAACyJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGvI,GAAG,CAAClB;oBAAQ;sBAAAA,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC,eACJ3F,OAAA;sBAAOqF,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAEoB,GAAG,CAACsC;oBAAe;sBAAAxD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3F,OAAA;kBAAKqF,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCtF,OAAA;oBAAKqF,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAC3BR,cAAc,CAAC4B,GAAG,CAAC3B,MAAM,CAAC,EAC1B2B,GAAG,CAAC2C,IAAI,iBACPrJ,OAAA;sBAAKqF,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,GAAC,GAC5C,EAACoB,GAAG,CAAC2C,IAAI,CAACwF,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAArJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClB,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eAEN3F,OAAA;oBAAKqF,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BtF,OAAA,CAAC/C,MAAM;sBACLsP,OAAO,EAAC,eAAe;sBACvBhH,IAAI,EAAC,IAAI;sBACT6G,OAAO,EAAEA,CAAA,KAAMiB,cAAc,CAAC3G,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAAClB,QAAQ,CAAE;sBACpDiF,KAAK,EAAE;wBAAEqB,YAAY,EAAE;sBAAM,CAAE;sBAAAxG,QAAA,eAE/BtF,OAAA,CAAChC,QAAQ;wBAACuH,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eAET3F,OAAA,CAAC/C,MAAM;sBACLsP,OAAO,EAAC,eAAe;sBACvBhH,IAAI,EAAC,IAAI;sBACT6G,OAAO,EAAEA,CAAA,KAAMgB,SAAS,CAAC1G,GAAG,CAAE;sBAC9B+D,KAAK,EAAE;wBAAEqB,YAAY,EAAE;sBAAM,CAAE;sBAAAxG,QAAA,eAE/BtF,OAAA,CAAC9B,GAAG;wBAACqH,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eAET3F,OAAA,CAAC/C,MAAM;sBACLsP,OAAO,EAAC,eAAe;sBACvBhH,IAAI,EAAC,IAAI;sBACT6G,OAAO,EAAEA,CAAA,KAAMkB,UAAU,CAAC5G,GAAG,CAAE;sBAC/B+D,KAAK,EAAE;wBAAEqB,YAAY,EAAE;sBAAM,CAAE;sBAAAxG,QAAA,eAE/BtF,OAAA,CAAC/B,aAAa;wBAACsH,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB,CAAC,EAERe,GAAG,CAAC3B,MAAM,KAAK,QAAQ,iBACtB/E,OAAA,CAAC/C,MAAM;sBACLsP,OAAO,EAAC,SAAS;sBACjBhH,IAAI,EAAC,IAAI;sBACT6G,OAAO,EAAEA,CAAA,KAAMmB,YAAY,CAAC7G,GAAG,CAACI,EAAE,CAAE;sBACpC2D,KAAK,EAAE;wBAAEqB,YAAY,EAAE;sBAAM,CAAE;sBAAAxG,QAAA,eAE/BtF,OAAA,CAAClC,WAAW;wBAACyH,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAjFDe,GAAG,CAACI,EAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkFD,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN3F,OAAA,CAACvC,MAAM,CAAC+M,GAAG;YACTqB,OAAO,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAI,CAAE;YACpCH,OAAO,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAEyB,KAAK,EAAE;YAAE,CAAE;YAClChG,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAE5BtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBtF,OAAA;gBAAKqF,SAAS,EAAC,yDAAyD;gBAACoF,KAAK,EAAE;kBAC9EE,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdJ,UAAU,EAAE,mDAAmD;kBAC/DoB,YAAY,EAAE;gBAChB,CAAE;gBAAAxG,QAAA,eACAtF,OAAA,CAACrC,MAAM;kBAAC0H,SAAS,EAAC,YAAY;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3F,OAAA;cAAIqF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5D3F,OAAA;cAAGqF,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAsC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN3F,OAAA,CAACjD,GAAG;MAAC+R,EAAE,EAAE,CAAE;MAAAxJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,0BAA0B;QAACoF,KAAK,EAAE;UAChDC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCtF,OAAA,CAACjB,MAAM;cAACsG,SAAS,EAAC,MAAM;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAEvC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,eACRtF,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtF,OAAA;cAAKqF,SAAS,EAAC,iEAAiE;cAACoF,KAAK,EAAE;gBACtFC,UAAU,EAAE;cACd,CAAE;cAAApF,QAAA,gBACAtF,OAAA;gBAAKqF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAACoF,KAAK,EAAE;oBAC3BE,KAAK,EAAE,MAAM;oBACbG,MAAM,EAAE,MAAM;oBACdJ,UAAU,EAAE,mDAAmD;oBAC/DoB,YAAY,EAAE,MAAM;oBACpBqD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE;kBAClB,CAAE;kBAAA/J,QAAA,eACAtF,OAAA,CAACnC,KAAK;oBAACwH,SAAS,EAAC,YAAY;oBAACE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN3F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAIqF,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5C3F,OAAA;oBAAOqF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3F,OAAA;gBAAIqF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAEmE;cAAW;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eAEN3F,OAAA;cAAKqF,SAAS,EAAC,iEAAiE;cAACoF,KAAK,EAAE;gBACtFC,UAAU,EAAE;cACd,CAAE;cAAApF,QAAA,gBACAtF,OAAA;gBAAKqF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAACoF,KAAK,EAAE;oBAC3BE,KAAK,EAAE,MAAM;oBACbG,MAAM,EAAE,MAAM;oBACdJ,UAAU,EAAE,mDAAmD;oBAC/DoB,YAAY,EAAE,MAAM;oBACpBqD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE;kBAClB,CAAE;kBAAA/J,QAAA,eACAtF,OAAA,CAAC5B,OAAO;oBAACiH,SAAS,EAAC,YAAY;oBAACE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN3F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAIqF,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5C3F,OAAA;oBAAOqF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3F,OAAA;gBAAIqF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAE5E,YAAY,CAACiI,MAAM,CAAE2G,CAAc,IAAKA,CAAC,CAACC,QAAQ,CAAC,CAAChG;cAAM;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eAEN3F,OAAA;cAAKqF,SAAS,EAAC,iEAAiE;cAACoF,KAAK,EAAE;gBACtFC,UAAU,EAAE;cACd,CAAE;cAAApF,QAAA,gBACAtF,OAAA;gBAAKqF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAACoF,KAAK,EAAE;oBAC3BE,KAAK,EAAE,MAAM;oBACbG,MAAM,EAAE,MAAM;oBACdJ,UAAU,EAAE,mDAAmD;oBAC/DoB,YAAY,EAAE,MAAM;oBACpBqD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE;kBAClB,CAAE;kBAAA/J,QAAA,eACAtF,OAAA,CAAC3B,IAAI;oBAACgH,SAAS,EAAC,YAAY;oBAACE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACN3F,OAAA;kBAAAsF,QAAA,gBACEtF,OAAA;oBAAIqF,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAC;kBAAU;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC/C3F,OAAA;oBAAOqF,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3F,OAAA;gBAAIqF,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA6J,GAAA,GA9WMrC,YAA2B;AA+WjC,MAAMK,OAAsB,GAAGA,CAAC;EAC9BC,IAAI;EAAEzL,UAAU;EAAEC,aAAa;EAAEC,YAAY;EAAEC,eAAe;EAAEC,MAAM;EAAEC,SAAS;EACjF+K,SAAS;EAAEC,cAAc;EAAEC,UAAU;EAAEC,YAAY;EAAEzI,cAAc;EAAEc;AACvE,CAAC,KAAK;EACJ,oBACE5F,OAAA,CAAChD,IAAI;IAACqI,SAAS,EAAC,oBAAoB;IAACoF,KAAK,EAAE;MAC1CC,UAAU,EAAE,0BAA0B;MACtC2B,cAAc,EAAE,YAAY;MAC5BP,YAAY,EAAE;IAChB,CAAE;IAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;MAAC1J,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBAC9CtF,OAAA;QAAKqF,SAAS,EAAC,wDAAwD;QAAAC,QAAA,gBACrEtF,OAAA;UAAIqF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACrCtF,OAAA,CAACpC,QAAQ;YAACyH,SAAS,EAAC,MAAM;YAACE,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEzC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL3F,OAAA,CAAC9C,KAAK;UAAC+H,EAAE,EAAC,OAAO;UAAC+J,IAAI,EAAC,MAAM;UAAC3J,SAAS,EAAC,WAAW;UAAAC,QAAA,GAChDmI,IAAI,CAAClE,MAAM,EAAC,OACf;QAAA;UAAA/D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGN3F,OAAA,CAAClD,GAAG;QAACuI,SAAS,EAAC,KAAK;QAAAC,QAAA,gBAClBtF,OAAA,CAACjD,GAAG;UAAC4R,EAAE,EAAE,CAAE;UAAArJ,QAAA,eACTtF,OAAA,CAAC3C,UAAU;YAAAiI,QAAA,gBACTtF,OAAA,CAAC3C,UAAU,CAACoS,IAAI;cAAChF,KAAK,EAAE;gBAAEC,UAAU,EAAE,0BAA0B;gBAAEsC,MAAM,EAAE;cAAO,CAAE;cAAA1H,QAAA,eACjFtF,OAAA,CAACrB,MAAM;gBAAC0G,SAAS,EAAC,eAAe;gBAACE,IAAI,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eAClB3F,OAAA,CAAC7C,IAAI,CAACuS,OAAO;cACXC,WAAW,EAAC,gBAAgB;cAC5BrJ,KAAK,EAAEtE,UAAW;cAClB4N,QAAQ,EAAGC,CAAC,IAAK5N,aAAa,CAAC4N,CAAC,CAACC,MAAM,CAACxJ,KAAK,CAAE;cAC/CmE,KAAK,EAAE;gBACLC,UAAU,EAAE,0BAA0B;gBACtCsC,MAAM,EAAE,MAAM;gBACdV,KAAK,EAAE;cACT;YAAE;cAAA9G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN3F,OAAA,CAACjD,GAAG;UAAC4R,EAAE,EAAE,CAAE;UAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;YACVzJ,KAAK,EAAEpE,YAAa;YACpB0N,QAAQ,EAAGC,CAAC,IAAK1N,eAAe,CAAC0N,CAAC,CAACC,MAAM,CAACxJ,KAAK,CAAE;YACjDmE,KAAK,EAAE;cACLC,UAAU,EAAE,0BAA0B;cACtCsC,MAAM,EAAE,MAAM;cACdV,KAAK,EAAE;YACT,CAAE;YAAAhH,QAAA,gBAEFtF,OAAA;cAAQsG,KAAK,EAAC,KAAK;cAAAhB,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvC3F,OAAA;cAAQsG,KAAK,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C3F,OAAA;cAAQsG,KAAK,EAAC,aAAa;cAAAhB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACjD3F,OAAA;cAAQsG,KAAK,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3F,OAAA;cAAQsG,KAAK,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C3F,OAAA;cAAQsG,KAAK,EAAC,YAAY;cAAAhB,QAAA,EAAC;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C3F,OAAA;cAAQsG,KAAK,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5C3F,OAAA;cAAQsG,KAAK,EAAC,WAAW;cAAAhB,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAEN3F,OAAA,CAACjD,GAAG;UAAC4R,EAAE,EAAE,CAAE;UAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;YACVzJ,KAAK,EAAElE,MAAO;YACdwN,QAAQ,EAAGC,CAAC,IAAKxN,SAAS,CAACwN,CAAC,CAACC,MAAM,CAACxJ,KAAK,CAAE;YAC3CmE,KAAK,EAAE;cACLC,UAAU,EAAE,0BAA0B;cACtCsC,MAAM,EAAE,MAAM;cACdV,KAAK,EAAE;YACT,CAAE;YAAAhH,QAAA,gBAEFtF,OAAA;cAAQsG,KAAK,EAAC,SAAS;cAAAhB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC7C3F,OAAA;cAAQsG,KAAK,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C3F,OAAA;cAAQsG,KAAK,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC1C3F,OAAA;cAAQsG,KAAK,EAAC,UAAU;cAAAhB,QAAA,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;MAAAnH,QAAA,EACPmI,IAAI,CAAClE,MAAM,GAAG,CAAC,gBACdvJ,OAAA,CAAClD,GAAG;QAACuI,SAAS,EAAC,KAAK;QAAAC,QAAA,EACjBmI,IAAI,CAACd,GAAG,CAAC,CAACjG,GAAQ,EAAEwI,KAAa,kBAChClP,OAAA,CAACjD,GAAG;UAAc4R,EAAE,EAAE,CAAE;UAACG,EAAE,EAAE,CAAE;UAAAxJ,QAAA,eAC7BtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;YACTqB,OAAO,EAAE;cAAEjC,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAG,CAAE;YAC/BgB,OAAO,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAEM,CAAC,EAAE;YAAE,CAAE;YAC9BJ,UAAU,EAAE;cAAE8E,KAAK,EAAEM,KAAK,GAAG;YAAK,CAAE;YAAA5J,QAAA,eAEpCtF,OAAA,CAAChD,IAAI;cAACqI,SAAS,EAAC,gBAAgB;cAACoF,KAAK,EAAE;gBACtCC,UAAU,EAAE,0BAA0B;gBACtCoB,YAAY,EAAE;cAChB,CAAE;cAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;gBAAC1J,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,eAC9CtF,OAAA;kBAAKqF,SAAS,EAAC,kDAAkD;kBAAAC,QAAA,gBAC/DtF,OAAA;oBAAAsF,QAAA,gBACEtF,OAAA;sBAAIqF,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,EAAEoB,GAAG,CAACoC;oBAAS;sBAAAtD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAC3DC,gBAAgB,CAACc,GAAG,CAAC1D,QAAQ,CAAC;kBAAA;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,EACLb,cAAc,CAAC4B,GAAG,CAAC3B,MAAM,CAAC;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAEd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;gBAACpH,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACzBtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBtF,OAAA;oBAAKqF,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,gBACjEtF,OAAA,CAACpC,QAAQ;sBAAC2H,IAAI,EAAE,EAAG;sBAACF,SAAS,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtCe,GAAG,CAAClB,QAAQ,CAAC+D,MAAM,GAAG,EAAE,GAAG7C,GAAG,CAAClB,QAAQ,CAACyJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGvI,GAAG,CAAClB,QAAQ;kBAAA;oBAAAA,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzE,CAAC,eACN3F,OAAA;oBAAKqF,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClCtF,OAAA;sBAAAsF,QAAA,gBAAKtF,OAAA;wBAAAsF,QAAA,EAAQ;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC9D,SAAS;oBAAA;sBAAA4C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjD3F,OAAA;sBAAAsF,QAAA,gBAAKtF,OAAA;wBAAAsF,QAAA,EAAQ;sBAAO;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC7D,MAAM;oBAAA;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD3F,OAAA;sBAAAsF,QAAA,gBAAKtF,OAAA;wBAAAsF,QAAA,EAAQ;sBAAM;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC5D,SAAS;oBAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClD3F,OAAA;sBAAAsF,QAAA,gBAAKtF,OAAA;wBAAAsF,QAAA,EAAQ;sBAAK;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC3D,SAAS;oBAAA;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAELe,GAAG,CAAC2C,IAAI,iBACPrJ,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBtF,OAAA;oBAAMqF,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,GACvC,EAACoB,GAAG,CAAC2C,IAAI,CAACwF,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAArJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACN,eAED3F,OAAA;kBAAKqF,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACtC,IAAI0K,IAAI,CAACtJ,GAAG,CAACuJ,OAAO,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAA1K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eAEN3F,OAAA;kBAAKqF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCtF,OAAA,CAAC/C,MAAM;oBACLsP,OAAO,EAAC,eAAe;oBACvBhH,IAAI,EAAC,IAAI;oBACT6G,OAAO,EAAEA,CAAA,KAAMiB,cAAc,CAAC3G,GAAG,CAACI,EAAE,EAAEJ,GAAG,CAAClB,QAAQ,CAAE;oBACpDiF,KAAK,EAAE;sBAAEqB,YAAY,EAAE;oBAAM,CAAE;oBAAAxG,QAAA,eAE/BtF,OAAA,CAAChC,QAAQ;sBAACuH,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eAET3F,OAAA,CAAC/C,MAAM;oBACLsP,OAAO,EAAC,eAAe;oBACvBhH,IAAI,EAAC,IAAI;oBACT6G,OAAO,EAAEA,CAAA,KAAMgB,SAAS,CAAC1G,GAAG,CAAE;oBAC9B+D,KAAK,EAAE;sBAAEqB,YAAY,EAAE;oBAAM,CAAE;oBAAAxG,QAAA,eAE/BtF,OAAA,CAAC9B,GAAG;sBAACqH,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eAET3F,OAAA,CAAC/C,MAAM;oBACLsP,OAAO,EAAC,eAAe;oBACvBhH,IAAI,EAAC,IAAI;oBACT6G,OAAO,EAAEA,CAAA,KAAMkB,UAAU,CAAC5G,GAAG,CAAE;oBAC/B+D,KAAK,EAAE;sBAAEqB,YAAY,EAAE;oBAAM,CAAE;oBAAAxG,QAAA,eAE/BtF,OAAA,CAAC/B,aAAa;sBAACsH,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,EAERe,GAAG,CAAC3B,MAAM,KAAK,QAAQ,iBACtB/E,OAAA,CAAC/C,MAAM;oBACLsP,OAAO,EAAC,SAAS;oBACjBhH,IAAI,EAAC,IAAI;oBACT6G,OAAO,EAAEA,CAAA,KAAMmB,YAAY,CAAC7G,GAAG,CAACI,EAAE,CAAE;oBACpC2D,KAAK,EAAE;sBAAEqB,YAAY,EAAE;oBAAM,CAAE;oBAAAxG,QAAA,eAE/BtF,OAAA,CAAClC,WAAW;sBAACyH,IAAI,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CACT;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC,GAvFLe,GAAG,CAACI,EAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwFX,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN3F,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAI,CAAE;QACpCH,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEyB,KAAK,EAAE;QAAE,CAAE;QAClChG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAE5BtF,OAAA;UAAKqF,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBtF,OAAA;YAAKqF,SAAS,EAAC,yDAAyD;YAACoF,KAAK,EAAE;cAC9EE,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACdJ,UAAU,EAAE,mDAAmD;cAC/DoB,YAAY,EAAE;YAChB,CAAE;YAAAxG,QAAA,eACAtF,OAAA,CAACpC,QAAQ;cAACyH,SAAS,EAAC,YAAY;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN3F,OAAA;UAAIqF,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9D3F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE;IACb;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX,CAAC;;AAED;AAAAwK,GAAA,GAzMM3C,OAAsB;AA0M5B,MAAME,UAAyB,GAAGA,CAAC;EAAEC,OAAO;EAAE/M,gBAAgB;EAAEgN,gBAAgB;EAAEC;AAAe,CAAC,KAAK;EACrG,oBACE7N,OAAA,CAAClD,GAAG;IAACuI,SAAS,EAAC,KAAK;IAAAC,QAAA,EACjBqI,OAAO,CAAChB,GAAG,CAAC,CAACqB,MAAW,EAAEkB,KAAa,kBACtClP,OAAA,CAACjD,GAAG;MAAiB4R,EAAE,EAAE,CAAE;MAACG,EAAE,EAAE,CAAE;MAAAxJ,QAAA,eAChCtF,OAAA,CAACvC,MAAM,CAAC+M,GAAG;QACTqB,OAAO,EAAE;UAAEjC,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAG,CAAE;QAC/BgB,OAAO,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEM,CAAC,EAAE;QAAE,CAAE;QAC9BJ,UAAU,EAAE;UAAE8E,KAAK,EAAEM,KAAK,GAAG;QAAI,CAAE;QACnChD,UAAU,EAAE;UAAEhC,CAAC,EAAE,CAAC,CAAC;UAAEmB,KAAK,EAAE;QAAK,CAAE;QAAA/F,QAAA,eAEnCtF,OAAA,CAAChD,IAAI;UAACqI,SAAS,EAAC,0BAA0B;UAACoF,KAAK,EAAE;YAChDC,UAAU,EAAE,0BAA0B;YACtC2B,cAAc,EAAE,YAAY;YAC5BP,YAAY,EAAE;UAChB,CAAE;UAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;YAAC1J,SAAS,EAAC,yBAAyB;YAAAC,QAAA,eAC9CtF,OAAA;cAAKqF,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/DtF,OAAA;gBAAAsF,QAAA,gBACEtF,OAAA;kBAAIqF,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAE0I,MAAM,CAACoC;gBAAI;kBAAA5K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1D3F,OAAA;kBAAKqF,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DtF,OAAA,CAACP,QAAQ;oBAAC8F,IAAI,EAAE,EAAG;oBAACF,SAAS,EAAC;kBAAM;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtCqI,MAAM,CAACqC,QAAQ;gBAAA;kBAAA7K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3F,OAAA,CAACvC,MAAM,CAAC6S,MAAM;gBACZpE,UAAU,EAAE;kBAAEb,KAAK,EAAE;gBAAI,CAAE;gBAC3Bc,QAAQ,EAAE;kBAAEd,KAAK,EAAE;gBAAI,CAAE;gBACzBe,OAAO,EAAEA,CAAA,KAAMwB,gBAAgB,CAACI,MAAM,CAAClH,EAAE,CAAE;gBAC3CzB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eAErCtF,OAAA,CAACf,KAAK;kBACJsG,IAAI,EAAE,EAAG;kBACTF,SAAS,EAAEzE,gBAAgB,CAAC8H,QAAQ,CAACsF,MAAM,CAAClH,EAAE,CAAC,GAAG,aAAa,GAAG,eAAgB;kBAClFyJ,IAAI,EAAE3P,gBAAgB,CAAC8H,QAAQ,CAACsF,MAAM,CAAClH,EAAE,CAAC,GAAG,cAAc,GAAG;gBAAO;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,eAEd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;YAAAnH,QAAA,gBACRtF,OAAA;cAAKqF,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtF,OAAA;gBAAKqF,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrEtF,OAAA;kBAAKqF,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,gBACxCtF,OAAA,CAAC3B,IAAI;oBAACgH,SAAS,EAAC,mBAAmB;oBAACE,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChD3F,OAAA;oBAAMqF,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EACrC0I,MAAM,CAACwC,aAAa,CAAC3B,OAAO,CAAC,CAAC;kBAAC;oBAAArJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN3F,OAAA,CAAC9C,KAAK;kBACJ+H,EAAE,EAAE+I,MAAM,CAACvE,WAAW,IAAI,CAAC,GAAG,SAAS,GAAGuE,MAAM,CAACvE,WAAW,IAAI,EAAE,GAAG,SAAS,GAAG,QAAS;kBAC1FpE,SAAS,EAAC,WAAW;kBAAAC,QAAA,GAEpB0I,MAAM,CAACvE,WAAW,EAAC,OACtB;gBAAA;kBAAAjE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EAELqI,MAAM,CAACyC,QAAQ,iBACdzQ,OAAA;gBAAKqF,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBtF,OAAA;kBAAKqF,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpC0I,MAAM,CAACyC,QAAQ,CAACxB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACtC,GAAG,CAAC,CAAC+D,OAAe,EAAEC,GAAW,kBAC5D3Q,OAAA,CAAC9C,KAAK;oBAAW+H,EAAE,EAAC,OAAO;oBAAC+J,IAAI,EAAC,MAAM;oBAAC3J,SAAS,EAAC,WAAW;oBAAAC,QAAA,EAC1DoL;kBAAO,GADEC,GAAG;oBAAAnL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAER,CACR;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAEAqI,MAAM,CAAC4C,UAAU,iBAChB5Q,OAAA;gBAAKqF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvCtF,OAAA,CAACjC,UAAU;kBAACwH,IAAI,EAAE,EAAG;kBAACF,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxCqI,MAAM,CAAC4C,UAAU;cAAA;gBAAApL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACN,EAEAqI,MAAM,CAAC6C,YAAY,iBAClB7Q,OAAA;gBAAKqF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,gBACvCtF,OAAA,CAACnC,KAAK;kBAAC0H,IAAI,EAAE,EAAG;kBAACF,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACnCqI,MAAM,CAAC6C,YAAY;cAAA;gBAAArL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEN3F,OAAA;cAAKqF,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BtF,OAAA,CAAC/C,MAAM;gBACLsP,OAAO,EAAC,eAAe;gBACvBhH,IAAI,EAAC,IAAI;gBACT6G,OAAO,EAAEA,CAAA,KAAMyB,cAAc,CAACG,MAAM,CAAE;gBACtC3I,SAAS,EAAC,aAAa;gBACvBoF,KAAK,EAAE;kBAAEqB,YAAY,EAAE;gBAAO,CAAE;gBAAAxG,QAAA,gBAEhCtF,OAAA,CAAC9B,GAAG;kBAACqH,IAAI,EAAE,EAAG;kBAACF,SAAS,EAAC;gBAAM;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEpC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAERqI,MAAM,CAACnK,KAAK,iBACX7D,OAAA,CAAC/C,MAAM;gBACLsP,OAAO,EAAC,eAAe;gBACvBhH,IAAI,EAAC,IAAI;gBACToC,IAAI,EAAE,OAAOqG,MAAM,CAACnK,KAAK,EAAG;gBAC5B4G,KAAK,EAAE;kBAAEqB,YAAY,EAAE;gBAAO,CAAE;gBAAAxG,QAAA,eAEhCtF,OAAA,CAACT,KAAK;kBAACgG,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC,GAzGLqI,MAAM,CAAClH,EAAE;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA0Gd,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAmL,GAAA,GApHMpD,UAAyB;AAqH/B,MAAMI,UAAyB,GAAGA,CAAC;EAAEL,IAAI;EAAEL,SAAS;EAAEtI;AAAe,CAAC,KAAK;EACzE,oBACE9E,OAAA,CAAChD,IAAI;IAACqI,SAAS,EAAC,oBAAoB;IAACoF,KAAK,EAAE;MAC1CC,UAAU,EAAE,0BAA0B;MACtC2B,cAAc,EAAE,YAAY;MAC5BP,YAAY,EAAE;IAChB,CAAE;IAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;MAAC1J,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eAC9CtF,OAAA;QAAIqF,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACrCtF,OAAA,CAAC1B,OAAO;UAAC+G,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAExC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;MAAAnH,QAAA,EACPmI,IAAI,CAAClE,MAAM,GAAG,CAAC,gBACdvJ,OAAA;QAAKqF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BtF,OAAA,CAACzC,KAAK;UAAC8H,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACvCtF,OAAA;YAAAsF,QAAA,eACEtF,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAAsF,QAAA,EAAI;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnB3F,OAAA;gBAAAsF,QAAA,EAAI;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClB3F,OAAA;gBAAAsF,QAAA,EAAI;cAAM;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf3F,OAAA;gBAAAsF,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb3F,OAAA;gBAAAsF,QAAA,EAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb3F,OAAA;gBAAAsF,QAAA,EAAI;cAAO;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR3F,OAAA;YAAAsF,QAAA,EACGmI,IAAI,CAACd,GAAG,CAAEjG,GAAQ,iBACjB1G,OAAA;cAAAsF,QAAA,gBACEtF,OAAA;gBAAIqF,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEoB,GAAG,CAACoC;cAAS;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD3F,OAAA;gBAAAsF,QAAA,EAAKoB,GAAG,CAAClB,QAAQ,CAAC+D,MAAM,GAAG,EAAE,GAAG7C,GAAG,CAAClB,QAAQ,CAACyJ,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGvI,GAAG,CAAClB;cAAQ;gBAAAA,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtF3F,OAAA;gBAAAsF,QAAA,EAAKR,cAAc,CAAC4B,GAAG,CAAC3B,MAAM;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrC3F,OAAA;gBAAIqF,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EACrCoB,GAAG,CAAC2C,IAAI,GAAG,IAAI3C,GAAG,CAAC2C,IAAI,CAACwF,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;cAAG;gBAAArJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACL3F,OAAA;gBAAAsF,QAAA,EAAK,IAAI0K,IAAI,CAACtJ,GAAG,CAACuJ,OAAO,CAAC,CAACC,kBAAkB,CAAC;cAAC;gBAAA1K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrD3F,OAAA;gBAAAsF,QAAA,eACEtF,OAAA,CAAC/C,MAAM;kBACLsP,OAAO,EAAC,eAAe;kBACvBhH,IAAI,EAAC,IAAI;kBACT6G,OAAO,EAAEA,CAAA,KAAMgB,SAAS,CAAC1G,GAAG,CAAE;kBAAApB,QAAA,eAE9BtF,OAAA,CAAC9B,GAAG;oBAACqH,IAAI,EAAE;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAhBEe,GAAG,CAACI,EAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBX,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,gBAEN3F,OAAA;QAAKqF,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BtF,OAAA,CAAC1B,OAAO;UAAC+G,SAAS,EAAC,oBAAoB;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpD3F,OAAA;UAAIqF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9C3F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEX,CAAC;;AAED;AAAAoL,GAAA,GA/DMjD,UAAyB;AAgE/B,MAAMC,YAA2B,GAAGA,CAAC;EAAEJ,OAAO;EAAEC,gBAAgB;EAAEC;AAAe,CAAC,KAAK;EACrF,oBACE7N,OAAA;IAAAsF,QAAA,EACGqI,OAAO,CAACpE,MAAM,GAAG,CAAC,gBACjBvJ,OAAA,CAAC0N,UAAU;MACTC,OAAO,EAAEA,OAAQ;MACjB/M,gBAAgB,EAAE+M,OAAO,CAAChB,GAAG,CAAE2C,CAAM,IAAKA,CAAC,CAACxI,EAAE,CAAE;MAChD8G,gBAAgB,EAAEA,gBAAiB;MACnCC,cAAc,EAAEA;IAAe;MAAArI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,gBAEF3F,OAAA,CAAChD,IAAI;MAACqI,SAAS,EAAC,oBAAoB;MAACoF,KAAK,EAAE;QAC1CC,UAAU,EAAE,0BAA0B;QACtC2B,cAAc,EAAE,YAAY;QAC5BP,YAAY,EAAE;MAChB,CAAE;MAAAxG,QAAA,eACAtF,OAAA,CAAChD,IAAI,CAACyP,IAAI;QAACpH,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBACrCtF,OAAA,CAACf,KAAK;UAACoG,SAAS,EAAC,oBAAoB;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClD3F,OAAA;UAAIqF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChD3F,OAAA;UAAGqF,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAqD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EACP;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAAqL,GAAA,GA3BMjD,YAA2B;AA4BjC,MAAME,YAA2B,GAAGA,CAAC;EAAER,IAAI;EAAEvE;AAAW,CAAC,KAAK;EAC5D,MAAM+H,eAAe,GAAGxD,IAAI,CAACtE,MAAM,CAAC,CAAC+H,GAAQ,EAAExK,GAAQ,KAAK;IAC1D,MAAMyK,KAAK,GAAG,IAAInB,IAAI,CAACtJ,GAAG,CAACuJ,OAAO,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAAEiB,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAU,CAAC,CAAC;IACpGF,GAAG,CAACC,KAAK,CAAC,GAAG,CAACD,GAAG,CAACC,KAAK,CAAC,IAAI,CAAC,KAAKzK,GAAG,CAAC2C,IAAI,IAAI,CAAC,CAAC;IAChD,OAAO6H,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAMG,YAAY,GAAG5D,IAAI,CAACtE,MAAM,CAAC,CAAC+H,GAAQ,EAAExK,GAAQ,KAAK;IACvDwK,GAAG,CAACxK,GAAG,CAAC3B,MAAM,CAAC,GAAG,CAACmM,GAAG,CAACxK,GAAG,CAAC3B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;IAC5C,OAAOmM,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,oBACElR,OAAA,CAAClD,GAAG;IAACuI,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBtF,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,0BAA0B;QAACoF,KAAK,EAAE;UAChDC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCtF,OAAA,CAACnB,SAAS;cAACwG,SAAS,EAAC,MAAM;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAE1C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,gBACRtF,OAAA;YAAKqF,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBtF,OAAA;cAAIqF,SAAS,EAAC,sBAAsB;cAAAC,QAAA,GAAC,GAAC,EAAC4D,UAAU,CAAC2F,OAAO,CAAC,CAAC,CAAC;YAAA;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClE3F,OAAA;cAAGqF,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eAEN3F,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBY,MAAM,CAACC,OAAO,CAAC8K,eAAe,CAAC,CAAChC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACtC,GAAG,CAAC,CAAC,CAACwE,KAAK,EAAEG,MAAM,CAAgB,kBAC5EtR,OAAA;cAAiBqF,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC5EtF,OAAA;gBAAMqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAE6L;cAAK;gBAAA3L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3C3F,OAAA;gBAAKqF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAACoF,KAAK,EAAE;oBAAEE,KAAK,EAAE;kBAAQ,CAAE;kBAAArF,QAAA,eAC9CtF,OAAA,CAACxC,WAAW;oBACV+T,GAAG,EAAGD,MAAM,GAAG1G,IAAI,CAAC4G,GAAG,CAAC,GAAGtL,MAAM,CAACuL,MAAM,CAACR,eAAe,CAAC,CAACtE,GAAG,CAAC+E,CAAC,IAAIC,MAAM,CAACD,CAAC,CAAC,CAAC,CAAC,GAAI,GAAI;oBACtFjH,KAAK,EAAE;sBAAEK,MAAM,EAAE;oBAAM,CAAE;oBACzByB,OAAO,EAAC;kBAAS;oBAAA/G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN3F,OAAA;kBAAMqF,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,GAAC,GAAC,EAACgM,MAAM,CAACzC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAArJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnE,CAAC;YAAA,GAXEwL,KAAK;cAAA3L,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN3F,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,0BAA0B;QAACoF,KAAK,EAAE;UAChDC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCtF,OAAA,CAAClB,QAAQ;cAACuG,SAAS,EAAC,MAAM;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,2BAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,eACRtF,OAAA;YAAKqF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBY,MAAM,CAACC,OAAO,CAACkL,YAAY,CAAC,CAAC1E,GAAG,CAAC,CAAC,CAAC5H,MAAM,EAAE6M,KAAK,CAAgB,kBAC/D5R,OAAA;cAAkBqF,SAAS,EAAC,mDAAmD;cAAAC,QAAA,gBAC7EtF,OAAA;gBAAMqF,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAEP;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5C3F,OAAA;gBAAKqF,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACxCtF,OAAA;kBAAKqF,SAAS,EAAC,MAAM;kBAACoF,KAAK,EAAE;oBAAEE,KAAK,EAAE;kBAAQ,CAAE;kBAAArF,QAAA,eAC9CtF,OAAA,CAACxC,WAAW;oBACV+T,GAAG,EAAGK,KAAK,GAAGnE,IAAI,CAAClE,MAAM,GAAI,GAAI;oBACjCkB,KAAK,EAAE;sBAAEK,MAAM,EAAE;oBAAM,CAAE;oBACzByB,OAAO,EAAC;kBAAM;oBAAA/G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN3F,OAAA;kBAAMqF,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEsM;gBAAK;kBAAApM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GAXEZ,MAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYX,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAkM,GAAA,GA1FM5D,YAA2B;AA2FjC,MAAMC,WAA0B,GAAGA,CAAC;EAAEjL,YAAY;EAAEC;AAAgB,CAAC,KAAK;EACxE,oBACElD,OAAA,CAAClD,GAAG;IAACuI,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClBtF,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,oBAAoB;QAACoF,KAAK,EAAE;UAC1CC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCtF,OAAA,CAACtB,IAAI;cAAC2G,SAAS,EAAC,MAAM;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,yBAErC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,eACRtF,OAAA,CAAC7C,IAAI;YAAAmI,QAAA,gBACHtF,OAAA,CAAC7C,IAAI,CAAC2U,KAAK;cACTC,IAAI,EAAC,QAAQ;cACbjL,EAAE,EAAC,qBAAqB;cACxB4F,KAAK,EAAC,qBAAqB;cAC3BsF,OAAO,EAAE/O,YAAY,CAACE,aAAa,CAACC,KAAM;cAC1CwM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;gBAC/B,GAAGD,YAAY;gBACfE,aAAa,EAAE;kBAAE,GAAGF,YAAY,CAACE,aAAa;kBAAEC,KAAK,EAAEyM,CAAC,CAACC,MAAM,CAACkC;gBAAQ;cAC1E,CAAC,CAAE;cACH3M,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF3F,OAAA,CAAC7C,IAAI,CAAC2U,KAAK;cACTC,IAAI,EAAC,QAAQ;cACbjL,EAAE,EAAC,oBAAoB;cACvB4F,KAAK,EAAC,oBAAoB;cAC1BsF,OAAO,EAAE/O,YAAY,CAACE,aAAa,CAACE,IAAK;cACzCuM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;gBAC/B,GAAGD,YAAY;gBACfE,aAAa,EAAE;kBAAE,GAAGF,YAAY,CAACE,aAAa;kBAAEE,IAAI,EAAEwM,CAAC,CAACC,MAAM,CAACkC;gBAAQ;cACzE,CAAC,CAAE;cACH3M,SAAS,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACF3F,OAAA,CAAC7C,IAAI,CAAC2U,KAAK;cACTC,IAAI,EAAC,QAAQ;cACbjL,EAAE,EAAC,mBAAmB;cACtB4F,KAAK,EAAC,mBAAmB;cACzBsF,OAAO,EAAE/O,YAAY,CAACE,aAAa,CAACG,GAAI;cACxCsM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;gBAC/B,GAAGD,YAAY;gBACfE,aAAa,EAAE;kBAAE,GAAGF,YAAY,CAACE,aAAa;kBAAEG,GAAG,EAAEuM,CAAC,CAACC,MAAM,CAACkC;gBAAQ;cACxE,CAAC,CAAE;cACH3M,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAEN3F,OAAA,CAACjD,GAAG;MAAC4R,EAAE,EAAE,CAAE;MAAArJ,QAAA,eACTtF,OAAA,CAAChD,IAAI;QAACqI,SAAS,EAAC,oBAAoB;QAACoF,KAAK,EAAE;UAC1CC,UAAU,EAAE,0BAA0B;UACtC2B,cAAc,EAAE,YAAY;UAC5BP,YAAY,EAAE;QAChB,CAAE;QAAAxG,QAAA,gBACAtF,OAAA,CAAChD,IAAI,CAAC+R,MAAM;UAAC1J,SAAS,EAAC,yBAAyB;UAAAC,QAAA,eAC9CtF,OAAA;YAAIqF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACrCtF,OAAA,CAACzB,QAAQ;cAAC8G,SAAS,EAAC,MAAM;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAEzC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eACd3F,OAAA,CAAChD,IAAI,CAACyP,IAAI;UAAAnH,QAAA,eACRtF,OAAA,CAAC7C,IAAI;YAAAmI,QAAA,gBACHtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAC7M,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAErD,YAAY,CAACM,WAAW,CAACC,gBAAiB;gBACjDoM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;kBAC/B,GAAGD,YAAY;kBACfM,WAAW,EAAE;oBAAE,GAAGN,YAAY,CAACM,WAAW;oBAAEC,gBAAgB,EAAEqM,CAAC,CAACC,MAAM,CAACxJ;kBAAM;gBAC/E,CAAC,CAAE;gBACHmE,KAAK,EAAE;kBACLC,UAAU,EAAE,0BAA0B;kBACtCsC,MAAM,EAAE,MAAM;kBACdV,KAAK,EAAE;gBACT,CAAE;gBAAAhH,QAAA,gBAEFtF,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3F,OAAA;kBAAQsG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC3F,OAAA;kBAAQsG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEb3F,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAC7M,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAErD,YAAY,CAACM,WAAW,CAACE,gBAAiB;gBACjDmM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;kBAC/B,GAAGD,YAAY;kBACfM,WAAW,EAAE;oBAAE,GAAGN,YAAY,CAACM,WAAW;oBAAEE,gBAAgB,EAAEoM,CAAC,CAACC,MAAM,CAACxJ;kBAAM;gBAC/E,CAAC,CAAE;gBACHmE,KAAK,EAAE;kBACLC,UAAU,EAAE,0BAA0B;kBACtCsC,MAAM,EAAE,MAAM;kBACdV,KAAK,EAAE;gBACT,CAAE;gBAAAhH,QAAA,gBAEFtF,OAAA;kBAAQsG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEb3F,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAC7M,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAkB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClE3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAErD,YAAY,CAACM,WAAW,CAACG,gBAAiB;gBACjDkM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;kBAC/B,GAAGD,YAAY;kBACfM,WAAW,EAAE;oBAAE,GAAGN,YAAY,CAACM,WAAW;oBAAEG,gBAAgB,EAAEmM,CAAC,CAACC,MAAM,CAACxJ;kBAAM;gBAC/E,CAAC,CAAE;gBACHmE,KAAK,EAAE;kBACLC,UAAU,EAAE,0BAA0B;kBACtCsC,MAAM,EAAE,MAAM;kBACdV,KAAK,EAAE;gBACT,CAAE;gBAAAhH,QAAA,gBAEFtF,OAAA;kBAAQsG,KAAK,EAAC,IAAI;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9B3F,OAAA;kBAAQsG,KAAK,EAAC,IAAI;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9B3F,OAAA;kBAAQsG,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEb3F,OAAA,CAAC7C,IAAI,CAAC2U,KAAK;cACTC,IAAI,EAAC,QAAQ;cACbjL,EAAE,EAAC,cAAc;cACjB4F,KAAK,EAAC,+BAA+B;cACrCsF,OAAO,EAAE/O,YAAY,CAACM,WAAW,CAACI,iBAAkB;cACpDiM,QAAQ,EAAGC,CAAC,IAAK3M,eAAe,CAAC;gBAC/B,GAAGD,YAAY;gBACfM,WAAW,EAAE;kBAAE,GAAGN,YAAY,CAACM,WAAW;kBAAEI,iBAAiB,EAAEkM,CAAC,CAACC,MAAM,CAACkC;gBAAQ;cAClF,CAAC,CAAE;cACH3M,SAAS,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAwM,GAAA,GArJMjE,WAA0B;AAsJhC,MAAMC,WAA0B,GAAGA,CAAC;EAClCC,IAAI;EAAEC,MAAM;EAAE7L,UAAU;EAAEC,aAAa;EAAEf,YAAY;EAAEC,eAAe;EAAEjB,YAAY;EAAE4N;AACxF,CAAC,KAAK;EACJ,oBACEtO,OAAA,CAAC5C,KAAK;IAACgR,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC9I,IAAI,EAAC,IAAI;IAAC6M,QAAQ;IAAA9M,QAAA,gBACnDtF,OAAA,CAAC5C,KAAK,CAAC2R,MAAM;MAACsD,WAAW;MAAC5H,KAAK,EAAE;QAAEC,UAAU,EAAE,mDAAmD;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,eACnHtF,OAAA,CAAC5C,KAAK,CAACkV,KAAK;QAACjN,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACjCtF,OAAA,CAACrC,MAAM;UAAC0H,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACf3F,OAAA,CAAC5C,KAAK,CAACqP,IAAI;MAAChC,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAE4B,KAAK,EAAE;MAAO,CAAE;MAAAhH,QAAA,eAC1DtF,OAAA,CAAC7C,IAAI;QAAAmI,QAAA,gBACHtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;UAAC5M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;YAAA5M,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpC3F,OAAA,CAAC7C,IAAI,CAACuS,OAAO;YACXqC,IAAI,EAAC,MAAM;YACXQ,MAAM,EAAC,2CAA2C;YAClD3C,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAM2C,KAAK,GAAI3C,CAAC,CAACC,MAAM,CAAsB0C,KAAK;cAClD7Q,eAAe,CAAC6Q,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;YAC1C,CAAE;YACF/H,KAAK,EAAE;cAAEC,UAAU,EAAE,SAAS;cAAEsC,MAAM,EAAE,gBAAgB;cAAEV,KAAK,EAAE;YAAO;UAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACF3F,OAAA,CAAC7C,IAAI,CAACsS,IAAI;YAACpK,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAElC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEb3F,OAAA,CAAClD,GAAG;UAAAwI,QAAA,gBACFtF,OAAA,CAACjD,GAAG;YAAC4R,EAAE,EAAE,CAAE;YAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAA5M,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAE9D,UAAU,CAACI,SAAU;gBAC5BgN,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;kBAAE,GAAGA,IAAI;kBAAE3F,SAAS,EAAEiN,CAAC,CAACC,MAAM,CAACxJ;gBAAM,CAAC,CAAC,CAAE;gBACxFmE,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEsC,MAAM,EAAE,gBAAgB;kBAAEV,KAAK,EAAE;gBAAO,CAAE;gBAAAhH,QAAA,gBAE1EtF,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC3F,OAAA;kBAAQsG,KAAK,EAAC,SAAS;kBAAAhB,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC3F,OAAA;kBAAQsG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3F,OAAA,CAACjD,GAAG;YAAC4R,EAAE,EAAE,CAAE;YAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAA5M,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC3F,OAAA,CAAC7C,IAAI,CAACuS,OAAO;gBACXqC,IAAI,EAAC,QAAQ;gBACbU,GAAG,EAAC,GAAG;gBACPnM,KAAK,EAAE9D,UAAU,CAACK,MAAO;gBACzB+M,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;kBAAE,GAAGA,IAAI;kBAAE1F,MAAM,EAAE6P,QAAQ,CAAC7C,CAAC,CAACC,MAAM,CAACxJ,KAAK;gBAAE,CAAC,CAAC,CAAE;gBAC/FmE,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEsC,MAAM,EAAE,gBAAgB;kBAAEV,KAAK,EAAE;gBAAO;cAAE;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAAClD,GAAG;UAAAwI,QAAA,gBACFtF,OAAA,CAACjD,GAAG;YAAC4R,EAAE,EAAE,CAAE;YAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAA5M,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAE9D,UAAU,CAACM,SAAU;gBAC5B8M,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;kBAAE,GAAGA,IAAI;kBAAEzF,SAAS,EAAE+M,CAAC,CAACC,MAAM,CAACxJ;gBAAM,CAAC,CAAC,CAAE;gBACxFmE,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEsC,MAAM,EAAE,gBAAgB;kBAAEV,KAAK,EAAE;gBAAO,CAAE;gBAAAhH,QAAA,gBAE1EtF,OAAA;kBAAQsG,KAAK,EAAC,YAAY;kBAAAhB,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACN3F,OAAA,CAACjD,GAAG;YAAC4R,EAAE,EAAE,CAAE;YAAArJ,QAAA,eACTtF,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;cAAC5M,SAAS,EAAC,MAAM;cAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;gBAAA5M,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;gBACVzJ,KAAK,EAAE9D,UAAU,CAACO,SAAU;gBAC5B6M,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;kBAAE,GAAGA,IAAI;kBAAExF,SAAS,EAAE8M,CAAC,CAACC,MAAM,CAACxJ;gBAAM,CAAC,CAAC,CAAE;gBACxFmE,KAAK,EAAE;kBAAEC,UAAU,EAAE,SAAS;kBAAEsC,MAAM,EAAE,gBAAgB;kBAAEV,KAAK,EAAE;gBAAO,CAAE;gBAAAhH,QAAA,gBAE1EtF,OAAA;kBAAQsG,KAAK,EAAC,IAAI;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9B3F,OAAA;kBAAQsG,KAAK,EAAC,IAAI;kBAAAhB,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9B3F,OAAA;kBAAQsG,KAAK,EAAC,QAAQ;kBAAAhB,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC3F,OAAA;kBAAQsG,KAAK,EAAC,OAAO;kBAAAhB,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;UAAC5M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;YAAA5M,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjC3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;YACVzJ,KAAK,EAAE9D,UAAU,CAACQ,QAAS;YAC3B4M,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;cAAE,GAAGA,IAAI;cAAEvF,QAAQ,EAAE6M,CAAC,CAACC,MAAM,CAACxJ;YAAM,CAAC,CAAC,CAAE;YACvFmE,KAAK,EAAE;cAAEC,UAAU,EAAE,SAAS;cAAEsC,MAAM,EAAE,gBAAgB;cAAEV,KAAK,EAAE;YAAO,CAAE;YAAAhH,QAAA,gBAE1EtF,OAAA;cAAQsG,KAAK,EAAC,KAAK;cAAAhB,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC3F,OAAA;cAAQsG,KAAK,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC3F,OAAA;cAAQsG,KAAK,EAAC,MAAM;cAAAhB,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC3F,OAAA;cAAQsG,KAAK,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEb3F,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;UAAC5M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;YAAA5M,QAAA,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/C3F,OAAA,CAAC7C,IAAI,CAAC4S,MAAM;YACVzJ,KAAK,EAAE9D,UAAU,CAACG,sBAAuB;YACzCiN,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;cAAE,GAAGA,IAAI;cAAE5F,sBAAsB,EAAEkN,CAAC,CAACC,MAAM,CAACxJ;YAAM,CAAC,CAAC,CAAE;YACrGmE,KAAK,EAAE;cAAEC,UAAU,EAAE,SAAS;cAAEsC,MAAM,EAAE,gBAAgB;cAAEV,KAAK,EAAE;YAAO,CAAE;YAAAhH,QAAA,gBAE1EtF,OAAA;cAAQsG,KAAK,EAAC,EAAE;cAAAhB,QAAA,EAAC;YAA0B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EACnDjF,YAAY,CAACiM,GAAG,CAAEqB,MAAW,iBAC5BhO,OAAA;cAAwBsG,KAAK,EAAE0H,MAAM,CAAClH,EAAG;cAAAxB,QAAA,GACtC0I,MAAM,CAACoC,IAAI,EAAC,KAAG,EAACpC,MAAM,CAACvE,WAAW,EAAC,eACtC;YAAA,GAFauE,MAAM,CAAClH,EAAE;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEb3F,OAAA,CAAC7C,IAAI,CAAC8U,KAAK;UAAC5M,SAAS,EAAC,MAAM;UAAAC,QAAA,gBAC1BtF,OAAA,CAAC7C,IAAI,CAAC+U,KAAK;YAAA5M,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChC3F,OAAA,CAAC7C,IAAI,CAACuS,OAAO;YACXiD,EAAE,EAAC,UAAU;YACbC,IAAI,EAAE,CAAE;YACRjD,WAAW,EAAC,wCAAwC;YACpDrJ,KAAK,EAAE9D,UAAU,CAACE,OAAQ;YAC1BkN,QAAQ,EAAGC,CAAC,IAAKpN,aAAa,CAAE8F,IAAS,KAAM;cAAE,GAAGA,IAAI;cAAE7F,OAAO,EAAEmN,CAAC,CAACC,MAAM,CAACxJ;YAAM,CAAC,CAAC,CAAE;YACtFmE,KAAK,EAAE;cAAEC,UAAU,EAAE,SAAS;cAAEsC,MAAM,EAAE,gBAAgB;cAAEV,KAAK,EAAE;YAAO;UAAE;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACb3F,OAAA,CAAC5C,KAAK,CAACyV,MAAM;MAACpI,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,gBAC7DtF,OAAA,CAAC/C,MAAM;QAACsP,OAAO,EAAC,WAAW;QAACH,OAAO,EAAEiC,MAAO;QAAA/I,QAAA,EAAC;MAE7C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3F,OAAA,CAAC/C,MAAM;QACLmP,OAAO,EAAEkC,QAAS;QAClB9B,QAAQ,EAAE,CAAC9K,YAAa;QACxB+I,KAAK,EAAE;UAAEC,UAAU,EAAE,mDAAmD;UAAEsC,MAAM,EAAE;QAAO,CAAE;QAAA1H,QAAA,gBAE3FtF,OAAA,CAACrC,MAAM;UAAC0H,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEvC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;;AAED;AAAAmN,GAAA,GAtJM3E,WAA0B;AAuJhC,MAAMI,YAA2B,GAAGA,CAAC;EAAEH,IAAI;EAAEC,MAAM;EAAE3H,GAAG;EAAE5B,cAAc;EAAEc;AAAiB,CAAC,KAAK;EAC/F,oBACE5F,OAAA,CAAC5C,KAAK;IAACgR,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC9I,IAAI,EAAC,IAAI;IAAC6M,QAAQ;IAAA9M,QAAA,gBACnDtF,OAAA,CAAC5C,KAAK,CAAC2R,MAAM;MAACsD,WAAW;MAAC5H,KAAK,EAAE;QAAEC,UAAU,EAAE,mDAAmD;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,eACnHtF,OAAA,CAAC5C,KAAK,CAACkV,KAAK;QAACjN,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACjCtF,OAAA,CAAC9B,GAAG;UAACmH,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACf3F,OAAA,CAAC5C,KAAK,CAACqP,IAAI;MAAChC,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAE4B,KAAK,EAAE;MAAO,CAAE;MAAAhH,QAAA,EACzDoB,GAAG,iBACF1G,OAAA,CAAClD,GAAG;QAAAwI,QAAA,gBACFtF,OAAA,CAACjD,GAAG;UAAC4R,EAAE,EAAE,CAAE;UAAArJ,QAAA,gBACTtF,OAAA;YAAIqF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAACoC,SAAS;UAAA;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnD3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACb,cAAc,CAAC4B,GAAG,CAAC3B,MAAM,CAAC;UAAA;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5D3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACC,gBAAgB,CAACc,GAAG,CAAC1D,QAAQ,CAAC;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClE3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAClB,QAAQ;UAAA;YAAAA,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjD3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAACsC,eAAe;UAAA;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAC1De,GAAG,CAAC2C,IAAI,iBAAIrJ,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,eAAA3F,OAAA;cAAMqF,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAC,GAAC,EAACoB,GAAG,CAAC2C,IAAI,CAACwF,OAAO,CAAC,CAAC,CAAC;YAAA;cAAArJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvG3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIqK,IAAI,CAACtJ,GAAG,CAACuJ,OAAO,CAAC,CAAC8C,cAAc,CAAC,CAAC;UAAA;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EACxEe,GAAG,CAACsM,uBAAuB,iBAC1BhT,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC,IAAIqK,IAAI,CAACtJ,GAAG,CAACsM,uBAAuB,CAAC,CAACD,cAAc,CAAC,CAAC;UAAA;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CACtG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN3F,OAAA,CAACjD,GAAG;UAAC4R,EAAE,EAAE,CAAE;UAAArJ,QAAA,gBACTtF,OAAA;YAAIqF,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAoB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtD3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC9D,SAAS;UAAA;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7C3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC7D,MAAM;UAAA;YAAA2C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5C3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC5D,SAAS;UAAA;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9C3F,OAAA;YAAAsF,QAAA,gBAAGtF,OAAA;cAAAsF,QAAA,EAAQ;YAAW;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACe,GAAG,CAAC3D,SAAS;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClDe,GAAG,CAAChE,OAAO,iBACV1C,OAAA,CAAAE,SAAA;YAAAoF,QAAA,gBACEtF,OAAA;cAAIqF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAC;YAAO;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C3F,OAAA;cAAGqF,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEoB,GAAG,CAAChE;YAAO;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,eAC3C,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACS,CAAC,eACb3F,OAAA,CAAC5C,KAAK,CAACyV,MAAM;MAACpI,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,eAC7DtF,OAAA,CAAC/C,MAAM;QAACsP,OAAO,EAAC,WAAW;QAACH,OAAO,EAAEiC,MAAO;QAAA/I,QAAA,EAAC;MAE7C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;;AAED;AAAAsN,GAAA,GAlDM1E,YAA2B;AAmDjC,MAAMC,SAAwB,GAAGA,CAAC;EAChCJ,IAAI;EAAEC,MAAM;EAAE3H,GAAG;EAAE5E,QAAQ;EAAEF,WAAW;EAAEC,cAAc;EAAE4M;AAC5D,CAAC,KAAK;EACJ,oBACEzO,OAAA,CAAC5C,KAAK;IAACgR,IAAI,EAAEA,IAAK;IAACC,MAAM,EAAEA,MAAO;IAAC9I,IAAI,EAAC,IAAI;IAAC6M,QAAQ;IAAA9M,QAAA,gBACnDtF,OAAA,CAAC5C,KAAK,CAAC2R,MAAM;MAACsD,WAAW;MAAC5H,KAAK,EAAE;QAAEC,UAAU,EAAE,mDAAmD;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,eACnHtF,OAAA,CAAC5C,KAAK,CAACkV,KAAK;QAACjN,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACjCtF,OAAA,CAAC/B,aAAa;UAACoH,SAAS,EAAC,MAAM;UAACE,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,WACrC,EAACe,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEoC,SAAS;MAAA;QAAAtD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACf3F,OAAA,CAAC5C,KAAK,CAACqP,IAAI;MAAChC,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAE4B,KAAK,EAAE,MAAM;QAAExB,MAAM,EAAE,OAAO;QAAEoI,SAAS,EAAE;MAAO,CAAE;MAAA5N,QAAA,eAC9FtF,OAAA;QAAKqF,SAAS,EAAC,MAAM;QAAAC,QAAA,EAClBxD,QAAQ,CAAC6K,GAAG,CAAC,CAACwG,OAAY,EAAEjE,KAAa,kBACxClP,OAAA;UAAiBqF,SAAS,EAAE,QAAQ8N,OAAO,CAACC,aAAa,GAAG,UAAU,GAAG,YAAY,EAAG;UAAA9N,QAAA,gBACtFtF,OAAA;YAAKqF,SAAS,EAAE,8BACd8N,OAAO,CAACC,aAAa,GACjB,YAAY,GACZ,yBAAyB,EAC5B;YAAC3I,KAAK,EAAE;cACTC,UAAU,EAAEyI,OAAO,CAACC,aAAa,GAC7B,mDAAmD,GACnD;YACN,CAAE;YAAA9N,QAAA,EACC6N,OAAO,CAACE;UAAO;YAAA7N,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACN3F,OAAA;YAAKqF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC9B,IAAI0K,IAAI,CAACmD,OAAO,CAACG,SAAS,CAAC,CAACP,cAAc,CAAC;UAAC;YAAAvN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA,GAdEuJ,KAAK;UAAA1J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACb3F,OAAA,CAAC5C,KAAK,CAACyV,MAAM;MAACpI,KAAK,EAAE;QAAEC,UAAU,EAAE,SAAS;QAAEsC,MAAM,EAAE;MAAO,CAAE;MAAA1H,QAAA,eAC7DtF,OAAA,CAAC3C,UAAU;QAAAiI,QAAA,gBACTtF,OAAA,CAAC7C,IAAI,CAACuS,OAAO;UACXqC,IAAI,EAAC,MAAM;UACXpC,WAAW,EAAC,sBAAsB;UAClCrJ,KAAK,EAAE1E,WAAY;UACnBgO,QAAQ,EAAGC,CAAC,IAAKhO,cAAc,CAACgO,CAAC,CAACC,MAAM,CAACxJ,KAAK,CAAE;UAChDiN,UAAU,EAAG1D,CAAC,IAAKA,CAAC,CAACxJ,GAAG,KAAK,OAAO,IAAIoI,aAAa,CAAC,CAAE;UACxDhE,KAAK,EAAE;YAAEC,UAAU,EAAE,SAAS;YAAEsC,MAAM,EAAE,gBAAgB;YAAEV,KAAK,EAAE;UAAO;QAAE;UAAA9G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC,eACF3F,OAAA,CAAC/C,MAAM;UACLmP,OAAO,EAAEqC,aAAc;UACvBhE,KAAK,EAAE;YAAEC,UAAU,EAAE,mDAAmD;YAAEsC,MAAM,EAAE;UAAO,CAAE;UAAA1H,QAAA,eAE3FtF,OAAA,CAACV,IAAI;YAACiG,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEZ,CAAC;AAAC6N,GAAA,GArDIhF,SAAwB;AAuD9B,eAAerO,0BAA0B;AAAC,IAAAuO,EAAA,EAAAc,GAAA,EAAAW,GAAA,EAAAW,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAa,GAAA,EAAAM,GAAA,EAAAW,GAAA,EAAAG,GAAA,EAAAO,GAAA;AAAAC,YAAA,CAAA/E,EAAA;AAAA+E,YAAA,CAAAjE,GAAA;AAAAiE,YAAA,CAAAtD,GAAA;AAAAsD,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAzC,GAAA;AAAAyC,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}