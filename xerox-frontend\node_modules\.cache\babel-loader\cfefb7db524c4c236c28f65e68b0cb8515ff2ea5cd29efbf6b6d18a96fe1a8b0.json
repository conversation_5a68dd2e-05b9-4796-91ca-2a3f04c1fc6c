{"ast": null, "code": "\"use client\";\n\nimport { createContext } from 'react';\nconst LazyContext = createContext({\n  strict: false\n});\nexport { LazyContext };", "map": {"version": 3, "names": ["createContext", "LazyContext", "strict"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/context/LazyContext.mjs"], "sourcesContent": ["\"use client\";\nimport { createContext } from 'react';\n\nconst LazyContext = createContext({ strict: false });\n\nexport { LazyContext };\n"], "mappings": "AAAA,YAAY;;AACZ,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,WAAW,GAAGD,aAAa,CAAC;EAAEE,MAAM,EAAE;AAAM,CAAC,CAAC;AAEpD,SAASD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}