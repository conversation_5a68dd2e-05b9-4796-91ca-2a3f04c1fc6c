{"ast": null, "code": "import { useCallback } from 'react';\nimport useMounted from './useMounted';\n\n/**\n * `useSafeState` takes the return value of a `useState` hook and wraps the\n * setter to prevent updates onces the component has unmounted. Can used\n * with `useMergeState` and `useStateAsync` as well\n *\n * @param state The return value of a useStateHook\n *\n * ```ts\n * const [show, setShow] = useSafeState(useState(true));\n * ```\n */\n\nfunction useSafeState(state) {\n  const isMounted = useMounted();\n  return [state[0], useCallback(nextState => {\n    if (!isMounted()) return;\n    return state[1](nextState);\n  }, [isMounted, state[1]])];\n}\nexport default useSafeState;", "map": {"version": 3, "names": ["useCallback", "useMounted", "useSafeState", "state", "isMounted", "nextState"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useSafeState.js"], "sourcesContent": ["import { useCallback } from 'react';\nimport useMounted from './useMounted';\n\n/**\n * `useSafeState` takes the return value of a `useState` hook and wraps the\n * setter to prevent updates onces the component has unmounted. Can used\n * with `useMergeState` and `useStateAsync` as well\n *\n * @param state The return value of a useStateHook\n *\n * ```ts\n * const [show, setShow] = useSafeState(useState(true));\n * ```\n */\n\nfunction useSafeState(state) {\n  const isMounted = useMounted();\n  return [state[0], useCallback(nextState => {\n    if (!isMounted()) return;\n    return state[1](nextState);\n  }, [isMounted, state[1]])];\n}\nexport default useSafeState;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,OAAOC,UAAU,MAAM,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAMC,SAAS,GAAGH,UAAU,CAAC,CAAC;EAC9B,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,EAAEH,WAAW,CAACK,SAAS,IAAI;IACzC,IAAI,CAACD,SAAS,CAAC,CAAC,EAAE;IAClB,OAAOD,KAAK,CAAC,CAAC,CAAC,CAACE,SAAS,CAAC;EAC5B,CAAC,EAAE,CAACD,SAAS,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5B;AACA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}