﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XeroxModule.API", "XeroxModule.API\XeroxModule.API.csproj", "{5C075951-C722-4F20-B868-64D629B1C133}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XeroxModule.Core", "XeroxModule.Core\XeroxModule.Core.csproj", "{64F4F4EE-5453-4190-958C-CD718FDDFBED}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XeroxModule.Infrastructure", "XeroxModule.Infrastructure\XeroxModule.Infrastructure.csproj", "{4A3A02E2-479E-4CC5-855E-AFD225779706}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XeroxModule.Application", "XeroxModule.Application\XeroxModule.Application.csproj", "{B80DBBA3-690D-48AA-91A3-5FA351960430}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5C075951-C722-4F20-B868-64D629B1C133}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C075951-C722-4F20-B868-64D629B1C133}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C075951-C722-4F20-B868-64D629B1C133}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C075951-C722-4F20-B868-64D629B1C133}.Release|Any CPU.Build.0 = Release|Any CPU
		{64F4F4EE-5453-4190-958C-CD718FDDFBED}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64F4F4EE-5453-4190-958C-CD718FDDFBED}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64F4F4EE-5453-4190-958C-CD718FDDFBED}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64F4F4EE-5453-4190-958C-CD718FDDFBED}.Release|Any CPU.Build.0 = Release|Any CPU
		{4A3A02E2-479E-4CC5-855E-AFD225779706}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4A3A02E2-479E-4CC5-855E-AFD225779706}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4A3A02E2-479E-4CC5-855E-AFD225779706}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4A3A02E2-479E-4CC5-855E-AFD225779706}.Release|Any CPU.Build.0 = Release|Any CPU
		{B80DBBA3-690D-48AA-91A3-5FA351960430}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B80DBBA3-690D-48AA-91A3-5FA351960430}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B80DBBA3-690D-48AA-91A3-5FA351960430}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B80DBBA3-690D-48AA-91A3-5FA351960430}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
EndGlobal
