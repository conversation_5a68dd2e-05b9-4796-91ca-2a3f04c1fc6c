import React, { useState } from 'react';
import { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON>, Alert, Card } from 'react-bootstrap';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Mail, Lock, Eye, EyeOff, LogIn, Moon, Sun } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';

const SimpleAceternityLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const { isDarkMode, toggleTheme } = useTheme();
  const navigate = useNavigate();
  const location = useLocation();

  const message = location.state?.message;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      await login(email, password);
      navigate('/dashboard');
    } catch (error: any) {
      setError(error.response?.data?.message || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  // Professional color scheme
  const colors = {
    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue
    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray
    accent: isDarkMode ? '#10B981' : '#059669', // Emerald
    background: isDarkMode ? '#111827' : '#F9FAFB',
    surface: isDarkMode ? '#1F2937' : '#FFFFFF',
    text: isDarkMode ? '#F9FAFB' : '#111827',
    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'
  };

  const floatingShapes = Array.from({ length: 4 }, (_, i) => (
    <motion.div
      key={i}
      className="position-absolute rounded-circle"
      style={{
        background: `linear-gradient(135deg, ${colors.primary}20, ${colors.accent}20)`,
        width: `${Math.random() * 80 + 40}px`,
        height: `${Math.random() * 80 + 40}px`,
        left: `${Math.random() * 100}%`,
        top: `${Math.random() * 100}%`,
        opacity: 0.1,
        zIndex: 0
      }}
      animate={{
        x: [0, Math.random() * 50 - 25],
        y: [0, Math.random() * 50 - 25],
        rotate: [0, 360],
        scale: [1, 1.1, 1],
      }}
      transition={{
        duration: Math.random() * 15 + 10,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut"
      }}
    />
  ));

  return (
    <div 
      className="min-h-screen position-relative overflow-hidden d-flex align-items-center"
      style={{
        background: isDarkMode 
          ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)'
          : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)'
      }}
    >
      {/* Animated Background */}
      <div className="position-absolute w-100 h-100 overflow-hidden">
        {floatingShapes}
        <div className="position-absolute w-100 h-100" style={{
          background: isDarkMode
            ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'
            : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'
        }} />
      </div>

      {/* Theme Toggle Button */}
      <motion.button
        onClick={toggleTheme}
        className="position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center"
        style={{
          width: '50px',
          height: '50px',
          background: colors.surface,
          boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',
          color: colors.text,
          zIndex: 1000
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
      </motion.button>

      <Container className="position-relative" style={{ zIndex: 1 }}>
        <Row className="justify-content-center">
          <Col md={6} lg={5} xl={4}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {/* Header */}
              <div className="text-center mb-5">
                <motion.div
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="d-inline-flex align-items-center justify-content-center mb-4"
                  style={{
                    width: '80px',
                    height: '80px',
                    background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                    borderRadius: '20px',
                    boxShadow: `0 20px 40px ${colors.primary}30`
                  }}
                >
                  <LogIn className="text-white" size={40} />
                </motion.div>
                <h1 className="fw-bold mb-2" style={{ fontSize: '2.5rem', color: colors.text }}>
                  Welcome Back
                </h1>
                <p className="fs-5" style={{ color: colors.textSecondary }}>
                  Sign in to your Xerox Hub account
                </p>
              </div>

              {/* Login Card */}
              <Card 
                className="border-0 shadow-lg"
                style={{
                  background: colors.surface,
                  backdropFilter: 'blur(20px)',
                  borderRadius: '20px',
                  boxShadow: isDarkMode 
                    ? '0 25px 50px rgba(0, 0, 0, 0.3)' 
                    : '0 25px 50px rgba(0, 0, 0, 0.1)'
                }}
              >
                <Card.Body className="p-4">
                  {message && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-3"
                    >
                      <Alert 
                        variant="success" 
                        className="border-0 rounded-3"
                        style={{
                          background: `${colors.accent}20`,
                          color: colors.accent
                        }}
                      >
                        {message}
                      </Alert>
                    </motion.div>
                  )}

                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-3"
                    >
                      <Alert 
                        variant="danger" 
                        className="border-0 rounded-3"
                        style={{
                          background: isDarkMode ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.1)',
                          color: '#EF4444'
                        }}
                      >
                        {error}
                      </Alert>
                    </motion.div>
                  )}

                  <Form onSubmit={handleSubmit}>
                    <Form.Group className="mb-3">
                      <div className="position-relative">
                        <Mail 
                          className="position-absolute" 
                          size={20} 
                          style={{
                            left: '15px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            zIndex: 2,
                            color: colors.textSecondary
                          }} 
                        />
                        <Form.Control
                          type="email"
                          placeholder="Email Address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          className="ps-5 py-3 rounded-3 border-0"
                          style={{
                            background: isDarkMode ? '#374151' : '#F3F4F6',
                            color: colors.text,
                            fontSize: '16px'
                          }}
                        />
                      </div>
                    </Form.Group>

                    <Form.Group className="mb-4">
                      <div className="position-relative">
                        <Lock 
                          className="position-absolute" 
                          size={20} 
                          style={{
                            left: '15px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            zIndex: 2,
                            color: colors.textSecondary
                          }} 
                        />
                        <Form.Control
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          className="ps-5 pe-5 py-3 rounded-3 border-0"
                          style={{
                            background: isDarkMode ? '#374151' : '#F3F4F6',
                            color: colors.text,
                            fontSize: '16px'
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="position-absolute border-0 bg-transparent"
                          style={{
                            right: '15px',
                            top: '50%',
                            transform: 'translateY(-50%)',
                            zIndex: 2,
                            color: colors.textSecondary
                          }}
                        >
                          {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                        </button>
                      </div>
                    </Form.Group>

                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        type="submit"
                        disabled={loading}
                        className="w-100 py-3 rounded-3 border-0 fw-semibold fs-5"
                        style={{
                          background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,
                          boxShadow: `0 10px 30px ${colors.primary}30`,
                          transition: 'all 0.3s ease'
                        }}
                      >
                        {loading ? (
                          <div className="d-flex align-items-center justify-content-center">
                            <div className="spinner-border spinner-border-sm me-2" role="status" />
                            Signing In...
                          </div>
                        ) : (
                          <>
                            <LogIn size={20} className="me-2" />
                            Sign In
                          </>
                        )}
                      </Button>
                    </motion.div>
                  </Form>

                  <div className="text-center mt-4">
                    <p className="mb-0" style={{ color: colors.textSecondary }}>
                      Don't have an account?{' '}
                      <Link
                        to="/register"
                        className="fw-semibold text-decoration-none"
                        style={{
                          color: colors.primary,
                          transition: 'all 0.3s ease'
                        }}
                      >
                        Sign Up
                      </Link>
                    </p>
                  </div>
                </Card.Body>
              </Card>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default SimpleAceternityLogin;
