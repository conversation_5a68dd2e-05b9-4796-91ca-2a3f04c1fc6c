{"ast": null, "code": "import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\nfunction useRafInterval(fn, ms) {\n  let paused = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  let handle;\n  let start = new Date().getTime();\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  function loop() {\n    const current = new Date().getTime();\n    const delta = current - start;\n    if (pausedRef.current) return;\n    if (delta >= ms && fnRef.current) {\n      fnRef.current();\n      start = new Date().getTime();\n    }\n    cancelAnimationFrame(handle);\n    handle = requestAnimationFrame(loop);\n  }\n  useEffect(() => {\n    handle = requestAnimationFrame(loop);\n    return () => cancelAnimationFrame(handle);\n  }, []);\n}\nexport default useRafInterval;", "map": {"version": 3, "names": ["useEffect", "useCommittedRef", "useRafInterval", "fn", "ms", "paused", "arguments", "length", "undefined", "handle", "start", "Date", "getTime", "fnRef", "pausedRef", "loop", "current", "delta", "cancelAnimationFrame", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useRafInterval.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\nfunction useRafInterval(fn, ms, paused = false) {\n  let handle;\n  let start = new Date().getTime();\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  function loop() {\n    const current = new Date().getTime();\n    const delta = current - start;\n    if (pausedRef.current) return;\n    if (delta >= ms && fnRef.current) {\n      fnRef.current();\n      start = new Date().getTime();\n    }\n    cancelAnimationFrame(handle);\n    handle = requestAnimationFrame(loop);\n  }\n  useEffect(() => {\n    handle = requestAnimationFrame(loop);\n    return () => cancelAnimationFrame(handle);\n  }, []);\n}\nexport default useRafInterval;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAkB;EAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EAC5C,IAAIG,MAAM;EACV,IAAIC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAChC,MAAMC,KAAK,GAAGZ,eAAe,CAACE,EAAE,CAAC;EACjC;EACA;EACA,MAAMW,SAAS,GAAGb,eAAe,CAACI,MAAM,CAAC;EACzC,SAASU,IAAIA,CAAA,EAAG;IACd,MAAMC,OAAO,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACpC,MAAMK,KAAK,GAAGD,OAAO,GAAGN,KAAK;IAC7B,IAAII,SAAS,CAACE,OAAO,EAAE;IACvB,IAAIC,KAAK,IAAIb,EAAE,IAAIS,KAAK,CAACG,OAAO,EAAE;MAChCH,KAAK,CAACG,OAAO,CAAC,CAAC;MACfN,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC9B;IACAM,oBAAoB,CAACT,MAAM,CAAC;IAC5BA,MAAM,GAAGU,qBAAqB,CAACJ,IAAI,CAAC;EACtC;EACAf,SAAS,CAAC,MAAM;IACdS,MAAM,GAAGU,qBAAqB,CAACJ,IAAI,CAAC;IACpC,OAAO,MAAMG,oBAAoB,CAACT,MAAM,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;AACR;AACA,eAAeP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}