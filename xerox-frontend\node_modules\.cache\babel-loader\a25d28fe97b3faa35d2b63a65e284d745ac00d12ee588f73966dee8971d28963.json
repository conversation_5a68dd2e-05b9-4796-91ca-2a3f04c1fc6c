{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\SimpleAceternityLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Form, Button, Alert, Card } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Mail, Lock, Eye, EyeOff, LogIn, Moon, Sun } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SimpleAceternityLogin = () => {\n  _s();\n  var _location$state;\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const message = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.message;\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    try {\n      await login(email, password);\n      navigate('/dashboard');\n    } catch (error) {\n      var _error$response, _error$response$data;\n      setError(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed. Please check your credentials.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF',\n    // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151',\n    // Gray\n    accent: isDarkMode ? '#10B981' : '#059669',\n    // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n  const floatingShapes = Array.from({\n    length: 4\n  }, (_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"position-absolute rounded-circle\",\n    style: {\n      background: `linear-gradient(135deg, ${colors.primary}20, ${colors.accent}20)`,\n      width: `${Math.random() * 80 + 40}px`,\n      height: `${Math.random() * 80 + 40}px`,\n      left: `${Math.random() * 100}%`,\n      top: `${Math.random() * 100}%`,\n      opacity: 0.1,\n      zIndex: 0\n    },\n    animate: {\n      x: [0, Math.random() * 50 - 25],\n      y: [0, Math.random() * 50 - 25],\n      rotate: [0, 360],\n      scale: [1, 1.1, 1]\n    },\n    transition: {\n      duration: Math.random() * 15 + 10,\n      repeat: Infinity,\n      repeatType: \"reverse\",\n      ease: \"easeInOut\"\n    }\n  }, i, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen position-relative overflow-hidden d-flex align-items-center\",\n    style: {\n      background: isDarkMode ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)' : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute w-100 h-100 overflow-hidden\",\n      children: [floatingShapes, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute w-100 h-100\",\n        style: {\n          background: isDarkMode ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)' : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n      onClick: toggleTheme,\n      className: \"position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center\",\n      style: {\n        width: '50px',\n        height: '50px',\n        background: colors.surface,\n        boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',\n        color: colors.text,\n        zIndex: 1000\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 23\n      }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      className: \"position-relative\",\n      style: {\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          lg: 5,\n          xl: 4,\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-5\",\n              children: [/*#__PURE__*/_jsxDEV(motion.div, {\n                animate: {\n                  rotate: [0, 360],\n                  scale: [1, 1.1, 1]\n                },\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                },\n                className: \"d-inline-flex align-items-center justify-content-center mb-4\",\n                style: {\n                  width: '80px',\n                  height: '80px',\n                  background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                  borderRadius: '20px',\n                  boxShadow: `0 20px 40px ${colors.primary}30`\n                },\n                children: /*#__PURE__*/_jsxDEV(LogIn, {\n                  className: \"text-white\",\n                  size: 40\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"fw-bold mb-2\",\n                style: {\n                  fontSize: '2.5rem',\n                  color: colors.text\n                },\n                children: \"Welcome Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"fs-5\",\n                style: {\n                  color: colors.textSecondary\n                },\n                children: \"Sign in to your Xerox Hub account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              className: \"border-0 shadow-lg\",\n              style: {\n                background: colors.surface,\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                boxShadow: isDarkMode ? '0 25px 50px rgba(0, 0, 0, 0.3)' : '0 25px 50px rgba(0, 0, 0, 0.1)'\n              },\n              children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                className: \"p-4\",\n                children: [message && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: -10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"success\",\n                    className: \"border-0 rounded-3\",\n                    style: {\n                      background: `${colors.accent}20`,\n                      color: colors.accent\n                    },\n                    children: message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), error && /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: -10\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  className: \"mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Alert, {\n                    variant: \"danger\",\n                    className: \"border-0 rounded-3\",\n                    style: {\n                      background: isDarkMode ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.1)',\n                      color: '#EF4444'\n                    },\n                    children: error\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Form, {\n                  onSubmit: handleSubmit,\n                  children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(Mail, {\n                        className: \"position-absolute\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2,\n                          color: colors.textSecondary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 206,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: e => setEmail(e.target.value),\n                        required: true,\n                        className: \"ps-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: isDarkMode ? '#374151' : '#F3F4F6',\n                          color: colors.text,\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 217,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 205,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 204,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"position-relative\",\n                      children: [/*#__PURE__*/_jsxDEV(Lock, {\n                        className: \"position-absolute\",\n                        size: 20,\n                        style: {\n                          left: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2,\n                          color: colors.textSecondary\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                        type: showPassword ? 'text' : 'password',\n                        placeholder: \"Password\",\n                        value: password,\n                        onChange: e => setPassword(e.target.value),\n                        required: true,\n                        className: \"ps-5 pe-5 py-3 rounded-3 border-0\",\n                        style: {\n                          background: isDarkMode ? '#374151' : '#F3F4F6',\n                          color: colors.text,\n                          fontSize: '16px'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        type: \"button\",\n                        onClick: () => setShowPassword(!showPassword),\n                        className: \"position-absolute border-0 bg-transparent\",\n                        style: {\n                          right: '15px',\n                          top: '50%',\n                          transform: 'translateY(-50%)',\n                          zIndex: 2,\n                          color: colors.textSecondary\n                        },\n                        children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 43\n                        }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                          size: 20\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 271,\n                          columnNumber: 66\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                    whileHover: {\n                      scale: 1.02\n                    },\n                    whileTap: {\n                      scale: 0.98\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"submit\",\n                      disabled: loading,\n                      className: \"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\",\n                      style: {\n                        background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                        boxShadow: `0 10px 30px ${colors.primary}30`,\n                        transition: 'all 0.3s ease'\n                      },\n                      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex align-items-center justify-content-center\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"spinner-border spinner-border-sm me-2\",\n                          role: \"status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 292,\n                          columnNumber: 29\n                        }, this), \"Signing In...\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 27\n                      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(LogIn, {\n                          size: 20,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 297,\n                          columnNumber: 29\n                        }, this), \"Sign In\"]\n                      }, void 0, true)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center mt-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0\",\n                    style: {\n                      color: colors.textSecondary\n                    },\n                    children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                      to: \"/register\",\n                      className: \"fw-semibold text-decoration-none\",\n                      style: {\n                        color: colors.primary,\n                        transition: 'all 0.3s ease'\n                      },\n                      children: \"Sign Up\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleAceternityLogin, \"mwaBISpUELuAZxm3B5B+HdRkpWs=\", false, function () {\n  return [useAuth, useTheme, useNavigate, useLocation];\n});\n_c = SimpleAceternityLogin;\nexport default SimpleAceternityLogin;\nvar _c;\n$RefreshReg$(_c, \"SimpleAceternityLogin\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Card", "Link", "useNavigate", "useLocation", "motion", "Mail", "Lock", "Eye", "Eye<PERSON>ff", "LogIn", "Moon", "Sun", "useAuth", "useTheme", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SimpleAceternityLogin", "_s", "_location$state", "email", "setEmail", "password", "setPassword", "showPassword", "setShowPassword", "error", "setError", "loading", "setLoading", "login", "isDarkMode", "toggleTheme", "navigate", "location", "message", "state", "handleSubmit", "e", "preventDefault", "_error$response", "_error$response$data", "response", "data", "colors", "primary", "secondary", "accent", "background", "surface", "text", "textSecondary", "floatingShapes", "Array", "from", "length", "_", "i", "div", "className", "style", "width", "Math", "random", "height", "left", "top", "opacity", "zIndex", "animate", "x", "y", "rotate", "scale", "transition", "duration", "repeat", "Infinity", "repeatType", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "button", "onClick", "boxShadow", "color", "whileHover", "whileTap", "size", "md", "lg", "xl", "initial", "borderRadius", "fontSize", "<PERSON><PERSON>ilter", "Body", "variant", "onSubmit", "Group", "transform", "Control", "type", "placeholder", "value", "onChange", "target", "required", "right", "disabled", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/SimpleAceternityLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, <PERSON>, <PERSON>, Alert, Card } from 'react-bootstrap';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Mail, Lock, Eye, EyeOff, LogIn, Moon, Sun } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\n\nconst SimpleAceternityLogin: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [showPassword, setShowPassword] = useState(false);\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n  const { isDarkMode, toggleTheme } = useTheme();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const message = location.state?.message;\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      await login(email, password);\n      navigate('/dashboard');\n    } catch (error: any) {\n      setError(error.response?.data?.message || 'Login failed. Please check your credentials.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray\n    accent: isDarkMode ? '#10B981' : '#059669', // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n\n  const floatingShapes = Array.from({ length: 4 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"position-absolute rounded-circle\"\n      style={{\n        background: `linear-gradient(135deg, ${colors.primary}20, ${colors.accent}20)`,\n        width: `${Math.random() * 80 + 40}px`,\n        height: `${Math.random() * 80 + 40}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        opacity: 0.1,\n        zIndex: 0\n      }}\n      animate={{\n        x: [0, Math.random() * 50 - 25],\n        y: [0, Math.random() * 50 - 25],\n        rotate: [0, 360],\n        scale: [1, 1.1, 1],\n      }}\n      transition={{\n        duration: Math.random() * 15 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n        ease: \"easeInOut\"\n      }}\n    />\n  ));\n\n  return (\n    <div \n      className=\"min-h-screen position-relative overflow-hidden d-flex align-items-center\"\n      style={{\n        background: isDarkMode \n          ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)'\n          : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)'\n      }}\n    >\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100 overflow-hidden\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: isDarkMode\n            ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'\n            : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'\n        }} />\n      </div>\n\n      {/* Theme Toggle Button */}\n      <motion.button\n        onClick={toggleTheme}\n        className=\"position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center\"\n        style={{\n          width: '50px',\n          height: '50px',\n          background: colors.surface,\n          boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',\n          color: colors.text,\n          zIndex: 1000\n        }}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}\n      </motion.button>\n\n      <Container className=\"position-relative\" style={{ zIndex: 1 }}>\n        <Row className=\"justify-content-center\">\n          <Col md={6} lg={5} xl={4}>\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              {/* Header */}\n              <div className=\"text-center mb-5\">\n                <motion.div\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [1, 1.1, 1],\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className=\"d-inline-flex align-items-center justify-content-center mb-4\"\n                  style={{\n                    width: '80px',\n                    height: '80px',\n                    background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                    borderRadius: '20px',\n                    boxShadow: `0 20px 40px ${colors.primary}30`\n                  }}\n                >\n                  <LogIn className=\"text-white\" size={40} />\n                </motion.div>\n                <h1 className=\"fw-bold mb-2\" style={{ fontSize: '2.5rem', color: colors.text }}>\n                  Welcome Back\n                </h1>\n                <p className=\"fs-5\" style={{ color: colors.textSecondary }}>\n                  Sign in to your Xerox Hub account\n                </p>\n              </div>\n\n              {/* Login Card */}\n              <Card \n                className=\"border-0 shadow-lg\"\n                style={{\n                  background: colors.surface,\n                  backdropFilter: 'blur(20px)',\n                  borderRadius: '20px',\n                  boxShadow: isDarkMode \n                    ? '0 25px 50px rgba(0, 0, 0, 0.3)' \n                    : '0 25px 50px rgba(0, 0, 0, 0.1)'\n                }}\n              >\n                <Card.Body className=\"p-4\">\n                  {message && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"mb-3\"\n                    >\n                      <Alert \n                        variant=\"success\" \n                        className=\"border-0 rounded-3\"\n                        style={{\n                          background: `${colors.accent}20`,\n                          color: colors.accent\n                        }}\n                      >\n                        {message}\n                      </Alert>\n                    </motion.div>\n                  )}\n\n                  {error && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      className=\"mb-3\"\n                    >\n                      <Alert \n                        variant=\"danger\" \n                        className=\"border-0 rounded-3\"\n                        style={{\n                          background: isDarkMode ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.1)',\n                          color: '#EF4444'\n                        }}\n                      >\n                        {error}\n                      </Alert>\n                    </motion.div>\n                  )}\n\n                  <Form onSubmit={handleSubmit}>\n                    <Form.Group className=\"mb-3\">\n                      <div className=\"position-relative\">\n                        <Mail \n                          className=\"position-absolute\" \n                          size={20} \n                          style={{\n                            left: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2,\n                            color: colors.textSecondary\n                          }} \n                        />\n                        <Form.Control\n                          type=\"email\"\n                          placeholder=\"Email Address\"\n                          value={email}\n                          onChange={(e) => setEmail(e.target.value)}\n                          required\n                          className=\"ps-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: isDarkMode ? '#374151' : '#F3F4F6',\n                            color: colors.text,\n                            fontSize: '16px'\n                          }}\n                        />\n                      </div>\n                    </Form.Group>\n\n                    <Form.Group className=\"mb-4\">\n                      <div className=\"position-relative\">\n                        <Lock \n                          className=\"position-absolute\" \n                          size={20} \n                          style={{\n                            left: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2,\n                            color: colors.textSecondary\n                          }} \n                        />\n                        <Form.Control\n                          type={showPassword ? 'text' : 'password'}\n                          placeholder=\"Password\"\n                          value={password}\n                          onChange={(e) => setPassword(e.target.value)}\n                          required\n                          className=\"ps-5 pe-5 py-3 rounded-3 border-0\"\n                          style={{\n                            background: isDarkMode ? '#374151' : '#F3F4F6',\n                            color: colors.text,\n                            fontSize: '16px'\n                          }}\n                        />\n                        <button\n                          type=\"button\"\n                          onClick={() => setShowPassword(!showPassword)}\n                          className=\"position-absolute border-0 bg-transparent\"\n                          style={{\n                            right: '15px',\n                            top: '50%',\n                            transform: 'translateY(-50%)',\n                            zIndex: 2,\n                            color: colors.textSecondary\n                          }}\n                        >\n                          {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}\n                        </button>\n                      </div>\n                    </Form.Group>\n\n                    <motion.div\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Button\n                        type=\"submit\"\n                        disabled={loading}\n                        className=\"w-100 py-3 rounded-3 border-0 fw-semibold fs-5\"\n                        style={{\n                          background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                          boxShadow: `0 10px 30px ${colors.primary}30`,\n                          transition: 'all 0.3s ease'\n                        }}\n                      >\n                        {loading ? (\n                          <div className=\"d-flex align-items-center justify-content-center\">\n                            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\" />\n                            Signing In...\n                          </div>\n                        ) : (\n                          <>\n                            <LogIn size={20} className=\"me-2\" />\n                            Sign In\n                          </>\n                        )}\n                      </Button>\n                    </motion.div>\n                  </Form>\n\n                  <div className=\"text-center mt-4\">\n                    <p className=\"mb-0\" style={{ color: colors.textSecondary }}>\n                      Don't have an account?{' '}\n                      <Link\n                        to=\"/register\"\n                        className=\"fw-semibold text-decoration-none\"\n                        style={{\n                          color: colors.primary,\n                          transition: 'all 0.3s ease'\n                        }}\n                      >\n                        Sign Up\n                      </Link>\n                    </p>\n                  </div>\n                </Card.Body>\n              </Card>\n            </motion.div>\n          </Col>\n        </Row>\n      </Container>\n    </div>\n  );\n};\n\nexport default SimpleAceternityLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,iBAAiB;AAChF,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,GAAG,QAAQ,cAAc;AACxE,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,MAAMC,qBAA+B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC5C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEsC;EAAM,CAAC,GAAGnB,OAAO,CAAC,CAAC;EAC3B,MAAM;IAAEoB,UAAU;IAAEC;EAAY,CAAC,GAAGpB,QAAQ,CAAC,CAAC;EAC9C,MAAMqB,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAE9B,MAAMiC,OAAO,IAAAhB,eAAA,GAAGe,QAAQ,CAACE,KAAK,cAAAjB,eAAA,uBAAdA,eAAA,CAAgBgB,OAAO;EAEvC,MAAME,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBZ,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMC,KAAK,CAACV,KAAK,EAAEE,QAAQ,CAAC;MAC5BW,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOP,KAAU,EAAE;MAAA,IAAAc,eAAA,EAAAC,oBAAA;MACnBd,QAAQ,CAAC,EAAAa,eAAA,GAAAd,KAAK,CAACgB,QAAQ,cAAAF,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBG,IAAI,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBN,OAAO,KAAI,8CAA8C,CAAC;IAC3F,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMe,MAAM,GAAG;IACbC,OAAO,EAAEd,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7Ce,SAAS,EAAEf,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC/CgB,MAAM,EAAEhB,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC5CiB,UAAU,EAAEjB,UAAU,GAAG,SAAS,GAAG,SAAS;IAC9CkB,OAAO,EAAElB,UAAU,GAAG,SAAS,GAAG,SAAS;IAC3CmB,IAAI,EAAEnB,UAAU,GAAG,SAAS,GAAG,SAAS;IACxCoB,aAAa,EAAEpB,UAAU,GAAG,SAAS,GAAG;EAC1C,CAAC;EAED,MAAMqB,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,kBACpD3C,OAAA,CAACX,MAAM,CAACuD,GAAG;IAETC,SAAS,EAAC,kCAAkC;IAC5CC,KAAK,EAAE;MACLZ,UAAU,EAAE,2BAA2BJ,MAAM,CAACC,OAAO,OAAOD,MAAM,CAACG,MAAM,KAAK;MAC9Ec,KAAK,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACrCC,MAAM,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACtCE,IAAI,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC/BG,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC9BI,OAAO,EAAE,GAAG;MACZC,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAE;MACPC,CAAC,EAAE,CAAC,CAAC,EAAER,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MAC/BQ,CAAC,EAAE,CAAC,CAAC,EAAET,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MAC/BS,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;MAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAE;IACFC,UAAU,EAAE;MACVC,QAAQ,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACjCa,MAAM,EAAEC,QAAQ;MAChBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE;IACR;EAAE,GAtBGtB,CAAC;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAuBP,CACF,CAAC;EAEF,oBACErE,OAAA;IACE6C,SAAS,EAAC,0EAA0E;IACpFC,KAAK,EAAE;MACLZ,UAAU,EAAEjB,UAAU,GAClB,gEAAgE,GAChE;IACN,CAAE;IAAAqD,QAAA,gBAGFtE,OAAA;MAAK6C,SAAS,EAAC,+CAA+C;MAAAyB,QAAA,GAC3DhC,cAAc,eACftC,OAAA;QAAK6C,SAAS,EAAC,+BAA+B;QAACC,KAAK,EAAE;UACpDZ,UAAU,EAAEjB,UAAU,GAClB,kKAAkK,GAClK;QACN;MAAE;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNrE,OAAA,CAACX,MAAM,CAACkF,MAAM;MACZC,OAAO,EAAEtD,WAAY;MACrB2B,SAAS,EAAC,6GAA6G;MACvHC,KAAK,EAAE;QACLC,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACdhB,UAAU,EAAEJ,MAAM,CAACK,OAAO;QAC1BsC,SAAS,EAAExD,UAAU,GAAG,4BAA4B,GAAG,4BAA4B;QACnFyD,KAAK,EAAE5C,MAAM,CAACM,IAAI;QAClBkB,MAAM,EAAE;MACV,CAAE;MACFqB,UAAU,EAAE;QAAEhB,KAAK,EAAE;MAAI,CAAE;MAC3BiB,QAAQ,EAAE;QAAEjB,KAAK,EAAE;MAAI,CAAE;MAAAW,QAAA,EAExBrD,UAAU,gBAAGjB,OAAA,CAACJ,GAAG;QAACiF,IAAI,EAAE;MAAG;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACL,IAAI;QAACkF,IAAI,EAAE;MAAG;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEhBrE,OAAA,CAACrB,SAAS;MAACkE,SAAS,EAAC,mBAAmB;MAACC,KAAK,EAAE;QAAEQ,MAAM,EAAE;MAAE,CAAE;MAAAgB,QAAA,eAC5DtE,OAAA,CAACpB,GAAG;QAACiE,SAAS,EAAC,wBAAwB;QAAAyB,QAAA,eACrCtE,OAAA,CAACnB,GAAG;UAACiG,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAV,QAAA,eACvBtE,OAAA,CAACX,MAAM,CAACuD,GAAG;YACTqC,OAAO,EAAE;cAAE5B,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE;YAAG,CAAE;YAC/BF,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEI,CAAC,EAAE;YAAE,CAAE;YAC9BG,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAS,QAAA,gBAG9BtE,OAAA;cAAK6C,SAAS,EAAC,kBAAkB;cAAAyB,QAAA,gBAC/BtE,OAAA,CAACX,MAAM,CAACuD,GAAG;gBACTW,OAAO,EAAE;kBACPG,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;kBAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;gBACnB,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBE,IAAI,EAAE;gBACR,CAAE;gBACFpB,SAAS,EAAC,8DAA8D;gBACxEC,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdhB,UAAU,EAAE,2BAA2BJ,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;kBAC1EiD,YAAY,EAAE,MAAM;kBACpBT,SAAS,EAAE,eAAe3C,MAAM,CAACC,OAAO;gBAC1C,CAAE;gBAAAuC,QAAA,eAEFtE,OAAA,CAACN,KAAK;kBAACmD,SAAS,EAAC,YAAY;kBAACgC,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACbrE,OAAA;gBAAI6C,SAAS,EAAC,cAAc;gBAACC,KAAK,EAAE;kBAAEqC,QAAQ,EAAE,QAAQ;kBAAET,KAAK,EAAE5C,MAAM,CAACM;gBAAK,CAAE;gBAAAkC,QAAA,EAAC;cAEhF;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrE,OAAA;gBAAG6C,SAAS,EAAC,MAAM;gBAACC,KAAK,EAAE;kBAAE4B,KAAK,EAAE5C,MAAM,CAACO;gBAAc,CAAE;gBAAAiC,QAAA,EAAC;cAE5D;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNrE,OAAA,CAACf,IAAI;cACH4D,SAAS,EAAC,oBAAoB;cAC9BC,KAAK,EAAE;gBACLZ,UAAU,EAAEJ,MAAM,CAACK,OAAO;gBAC1BiD,cAAc,EAAE,YAAY;gBAC5BF,YAAY,EAAE,MAAM;gBACpBT,SAAS,EAAExD,UAAU,GACjB,gCAAgC,GAChC;cACN,CAAE;cAAAqD,QAAA,eAEFtE,OAAA,CAACf,IAAI,CAACoG,IAAI;gBAACxC,SAAS,EAAC,KAAK;gBAAAyB,QAAA,GACvBjD,OAAO,iBACNrB,OAAA,CAACX,MAAM,CAACuD,GAAG;kBACTqC,OAAO,EAAE;oBAAE5B,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCF,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,MAAM;kBAAAyB,QAAA,eAEhBtE,OAAA,CAAChB,KAAK;oBACJsG,OAAO,EAAC,SAAS;oBACjBzC,SAAS,EAAC,oBAAoB;oBAC9BC,KAAK,EAAE;sBACLZ,UAAU,EAAE,GAAGJ,MAAM,CAACG,MAAM,IAAI;sBAChCyC,KAAK,EAAE5C,MAAM,CAACG;oBAChB,CAAE;oBAAAqC,QAAA,EAEDjD;kBAAO;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACb,EAEAzD,KAAK,iBACJZ,OAAA,CAACX,MAAM,CAACuD,GAAG;kBACTqC,OAAO,EAAE;oBAAE5B,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCF,OAAO,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEI,CAAC,EAAE;kBAAE,CAAE;kBAC9BZ,SAAS,EAAC,MAAM;kBAAAyB,QAAA,eAEhBtE,OAAA,CAAChB,KAAK;oBACJsG,OAAO,EAAC,QAAQ;oBAChBzC,SAAS,EAAC,oBAAoB;oBAC9BC,KAAK,EAAE;sBACLZ,UAAU,EAAEjB,UAAU,GAAG,wBAAwB,GAAG,wBAAwB;sBAC5EyD,KAAK,EAAE;oBACT,CAAE;oBAAAJ,QAAA,EAED1D;kBAAK;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACb,eAEDrE,OAAA,CAAClB,IAAI;kBAACyG,QAAQ,EAAEhE,YAAa;kBAAA+C,QAAA,gBAC3BtE,OAAA,CAAClB,IAAI,CAAC0G,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAyB,QAAA,eAC1BtE,OAAA;sBAAK6C,SAAS,EAAC,mBAAmB;sBAAAyB,QAAA,gBAChCtE,OAAA,CAACV,IAAI;wBACHuD,SAAS,EAAC,mBAAmB;wBAC7BgC,IAAI,EAAE,EAAG;wBACT/B,KAAK,EAAE;0BACLK,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVqC,SAAS,EAAE,kBAAkB;0BAC7BnC,MAAM,EAAE,CAAC;0BACToB,KAAK,EAAE5C,MAAM,CAACO;wBAChB;sBAAE;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrE,OAAA,CAAClB,IAAI,CAAC4G,OAAO;wBACXC,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BC,KAAK,EAAEvF,KAAM;wBACbwF,QAAQ,EAAGtE,CAAC,IAAKjB,QAAQ,CAACiB,CAAC,CAACuE,MAAM,CAACF,KAAK,CAAE;wBAC1CG,QAAQ;wBACRnD,SAAS,EAAC,8BAA8B;wBACxCC,KAAK,EAAE;0BACLZ,UAAU,EAAEjB,UAAU,GAAG,SAAS,GAAG,SAAS;0BAC9CyD,KAAK,EAAE5C,MAAM,CAACM,IAAI;0BAClB+C,QAAQ,EAAE;wBACZ;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAEbrE,OAAA,CAAClB,IAAI,CAAC0G,KAAK;oBAAC3C,SAAS,EAAC,MAAM;oBAAAyB,QAAA,eAC1BtE,OAAA;sBAAK6C,SAAS,EAAC,mBAAmB;sBAAAyB,QAAA,gBAChCtE,OAAA,CAACT,IAAI;wBACHsD,SAAS,EAAC,mBAAmB;wBAC7BgC,IAAI,EAAE,EAAG;wBACT/B,KAAK,EAAE;0BACLK,IAAI,EAAE,MAAM;0BACZC,GAAG,EAAE,KAAK;0BACVqC,SAAS,EAAE,kBAAkB;0BAC7BnC,MAAM,EAAE,CAAC;0BACToB,KAAK,EAAE5C,MAAM,CAACO;wBAChB;sBAAE;wBAAA6B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrE,OAAA,CAAClB,IAAI,CAAC4G,OAAO;wBACXC,IAAI,EAAEjF,YAAY,GAAG,MAAM,GAAG,UAAW;wBACzCkF,WAAW,EAAC,UAAU;wBACtBC,KAAK,EAAErF,QAAS;wBAChBsF,QAAQ,EAAGtE,CAAC,IAAKf,WAAW,CAACe,CAAC,CAACuE,MAAM,CAACF,KAAK,CAAE;wBAC7CG,QAAQ;wBACRnD,SAAS,EAAC,mCAAmC;wBAC7CC,KAAK,EAAE;0BACLZ,UAAU,EAAEjB,UAAU,GAAG,SAAS,GAAG,SAAS;0BAC9CyD,KAAK,EAAE5C,MAAM,CAACM,IAAI;0BAClB+C,QAAQ,EAAE;wBACZ;sBAAE;wBAAAjB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrE,OAAA;wBACE2F,IAAI,EAAC,QAAQ;wBACbnB,OAAO,EAAEA,CAAA,KAAM7D,eAAe,CAAC,CAACD,YAAY,CAAE;wBAC9CmC,SAAS,EAAC,2CAA2C;wBACrDC,KAAK,EAAE;0BACLmD,KAAK,EAAE,MAAM;0BACb7C,GAAG,EAAE,KAAK;0BACVqC,SAAS,EAAE,kBAAkB;0BAC7BnC,MAAM,EAAE,CAAC;0BACToB,KAAK,EAAE5C,MAAM,CAACO;wBAChB,CAAE;wBAAAiC,QAAA,EAED5D,YAAY,gBAAGV,OAAA,CAACP,MAAM;0BAACoF,IAAI,EAAE;wBAAG;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAAGrE,OAAA,CAACR,GAAG;0BAACqF,IAAI,EAAE;wBAAG;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAEbrE,OAAA,CAACX,MAAM,CAACuD,GAAG;oBACT+B,UAAU,EAAE;sBAAEhB,KAAK,EAAE;oBAAK,CAAE;oBAC5BiB,QAAQ,EAAE;sBAAEjB,KAAK,EAAE;oBAAK,CAAE;oBAAAW,QAAA,eAE1BtE,OAAA,CAACjB,MAAM;sBACL4G,IAAI,EAAC,QAAQ;sBACbO,QAAQ,EAAEpF,OAAQ;sBAClB+B,SAAS,EAAC,gDAAgD;sBAC1DC,KAAK,EAAE;wBACLZ,UAAU,EAAE,2BAA2BJ,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;wBAC1EwC,SAAS,EAAE,eAAe3C,MAAM,CAACC,OAAO,IAAI;wBAC5C6B,UAAU,EAAE;sBACd,CAAE;sBAAAU,QAAA,EAEDxD,OAAO,gBACNd,OAAA;wBAAK6C,SAAS,EAAC,kDAAkD;wBAAAyB,QAAA,gBAC/DtE,OAAA;0BAAK6C,SAAS,EAAC,uCAAuC;0BAACsD,IAAI,EAAC;wBAAQ;0BAAAjC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,iBAEzE;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,gBAENrE,OAAA,CAAAE,SAAA;wBAAAoE,QAAA,gBACEtE,OAAA,CAACN,KAAK;0BAACmF,IAAI,EAAE,EAAG;0BAAChC,SAAS,EAAC;wBAAM;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,WAEtC;sBAAA,eAAE;oBACH;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACK;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC,eAEPrE,OAAA;kBAAK6C,SAAS,EAAC,kBAAkB;kBAAAyB,QAAA,eAC/BtE,OAAA;oBAAG6C,SAAS,EAAC,MAAM;oBAACC,KAAK,EAAE;sBAAE4B,KAAK,EAAE5C,MAAM,CAACO;oBAAc,CAAE;oBAAAiC,QAAA,GAAC,wBACpC,EAAC,GAAG,eAC1BtE,OAAA,CAACd,IAAI;sBACHkH,EAAE,EAAC,WAAW;sBACdvD,SAAS,EAAC,kCAAkC;sBAC5CC,KAAK,EAAE;wBACL4B,KAAK,EAAE5C,MAAM,CAACC,OAAO;wBACrB6B,UAAU,EAAE;sBACd,CAAE;sBAAAU,QAAA,EACH;oBAED;sBAAAJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjE,EAAA,CA/TID,qBAA+B;EAAA,QAMjBN,OAAO,EACWC,QAAQ,EAC3BX,WAAW,EACXC,WAAW;AAAA;AAAAiH,EAAA,GATxBlG,qBAA+B;AAiUrC,eAAeA,qBAAqB;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}