{"ast": null, "code": "import { isZeroValueString } from 'motion-utils';\nfunction isNone(value) {\n  if (typeof value === \"number\") {\n    return value === 0;\n  } else if (value !== null) {\n    return value === \"none\" || value === \"0\" || isZeroValueString(value);\n  } else {\n    return true;\n  }\n}\nexport { isNone };", "map": {"version": 3, "names": ["isZeroValueString", "isNone", "value"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/animation/keyframes/utils/is-none.mjs"], "sourcesContent": ["import { isZeroValueString } from 'motion-utils';\n\nfunction isNone(value) {\n    if (typeof value === \"number\") {\n        return value === 0;\n    }\n    else if (value !== null) {\n        return value === \"none\" || value === \"0\" || isZeroValueString(value);\n    }\n    else {\n        return true;\n    }\n}\n\nexport { isNone };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,cAAc;AAEhD,SAASC,MAAMA,CAACC,KAAK,EAAE;EACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOA,KAAK,KAAK,CAAC;EACtB,CAAC,MACI,IAAIA,KAAK,KAAK,IAAI,EAAE;IACrB,OAAOA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,GAAG,IAAIF,iBAAiB,CAACE,KAAK,CAAC;EACxE,CAAC,MACI;IACD,OAAO,IAAI;EACf;AACJ;AAEA,SAASD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}