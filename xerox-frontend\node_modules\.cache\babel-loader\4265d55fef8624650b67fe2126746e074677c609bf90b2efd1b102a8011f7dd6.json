{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\XeroxCenterDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst XeroxCenterDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [printJobs, setPrintJobs] = useState([]);\n  const [selectedJob, setSelectedJob] = useState(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n  useEffect(() => {\n    // Mock data - replace with actual API calls\n    setPrintJobs([{\n      id: 1,\n      jobNumber: 'JOB-001',\n      fileName: 'Assignment1.pdf',\n      status: 'Requested',\n      studentName: 'John Doe',\n      studentEmail: '<EMAIL>',\n      printType: 'Print',\n      copies: 5,\n      colorType: 'BlackWhite',\n      paperSize: 'A4',\n      remarks: 'Please print double-sided',\n      created: '2024-01-15T10:00:00'\n    }, {\n      id: 2,\n      jobNumber: 'JOB-002',\n      fileName: 'Thesis_Chapter1.docx',\n      status: 'InProgress',\n      cost: 45.00,\n      estimatedCompletionTime: '2024-01-15T16:00:00',\n      studentName: 'Jane Smith',\n      studentEmail: '<EMAIL>',\n      printType: 'Print',\n      copies: 1,\n      colorType: 'BlackWhite',\n      paperSize: 'A4',\n      created: '2024-01-14T09:00:00'\n    }, {\n      id: 3,\n      jobNumber: 'JOB-003',\n      fileName: 'Presentation.pptx',\n      status: 'WaitingConfirmation',\n      cost: 25.50,\n      studentName: 'Mike Johnson',\n      studentEmail: '<EMAIL>',\n      printType: 'Print',\n      copies: 10,\n      colorType: 'Color',\n      paperSize: 'A4',\n      created: '2024-01-15T08:30:00'\n    }]);\n  }, []);\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        variant: 'secondary',\n        icon: 'clock'\n      },\n      'UnderReview': {\n        variant: 'info',\n        icon: 'eye'\n      },\n      'Quoted': {\n        variant: 'warning',\n        icon: 'dollar-sign'\n      },\n      'WaitingConfirmation': {\n        variant: 'warning',\n        icon: 'hourglass-half'\n      },\n      'Confirmed': {\n        variant: 'primary',\n        icon: 'check'\n      },\n      'InProgress': {\n        variant: 'info',\n        icon: 'cog'\n      },\n      'Completed': {\n        variant: 'success',\n        icon: 'check-circle'\n      },\n      'Delivered': {\n        variant: 'success',\n        icon: 'truck'\n      },\n      'Rejected': {\n        variant: 'danger',\n        icon: 'times'\n      },\n      'Cancelled': {\n        variant: 'dark',\n        icon: 'ban'\n      }\n    };\n    const config = statusConfig[status] || {\n      variant: 'secondary',\n      icon: 'question'\n    };\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.variant,\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: `fas fa-${config.icon} me-1`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  };\n  const handleQuoteSubmit = () => {\n    if (selectedJob) {\n      const estimatedCompletion = new Date();\n      estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n\n      // Mock API call to update job with quote\n      console.log('Submitting quote:', {\n        jobId: selectedJob.id,\n        cost: parseFloat(quoteData.cost),\n        estimatedCompletion,\n        notes: quoteData.notes\n      });\n\n      // Update local state\n      setPrintJobs(prev => prev.map(job => job.id === selectedJob.id ? {\n        ...job,\n        status: 'Quoted',\n        cost: parseFloat(quoteData.cost),\n        estimatedCompletionTime: estimatedCompletion.toISOString()\n      } : job));\n    }\n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({\n      cost: '',\n      estimatedHours: '',\n      notes: ''\n    });\n  };\n  const handleStatusUpdate = (jobId, newStatus) => {\n    // Mock API call to update job status\n    console.log('Updating job status:', {\n      jobId,\n      newStatus\n    });\n    setPrintJobs(prev => prev.map(job => job.id === jobId ? {\n      ...job,\n      status: newStatus\n    } : job));\n  };\n  const filteredJobs = filterStatus === 'all' ? printJobs : printJobs.filter(job => job.status === filterStatus);\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    children: [/*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-store me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), \"Xerox Center Dashboard\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-primary\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-file-alt fa-2x text-primary mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Total Jobs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-primary\",\n              children: stats.total\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-warning\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-clock fa-2x text-warning mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-warning\",\n              children: stats.pending\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-info\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-cog fa-2x text-info mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"In Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-info\",\n              children: stats.inProgress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        md: 3,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"text-center border-left-success\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-dollar-sign fa-2x text-success mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-success\",\n              children: [\"$\", stats.revenue.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-tasks me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), \"Job Queue\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n          style: {\n            width: 'auto'\n          },\n          value: filterStatus,\n          onChange: e => setFilterStatus(e.target.value),\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all\",\n            children: \"All Jobs\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Requested\",\n            children: \"Requested\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"UnderReview\",\n            children: \"Under Review\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Quoted\",\n            children: \"Quoted\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"WaitingConfirmation\",\n            children: \"Waiting Confirmation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Confirmed\",\n            children: \"Confirmed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"InProgress\",\n            children: \"In Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"Completed\",\n            children: \"Completed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n        children: filteredJobs.length > 0 ? /*#__PURE__*/_jsxDEV(Table, {\n          responsive: true,\n          hover: true,\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Job #\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Student\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"File\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Cost\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: filteredJobs.map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: job.jobNumber\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: new Date(job.created).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: job.studentName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: job.studentEmail\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-pdf me-2 text-danger\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 23\n                }, this), job.fileName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"small\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Type:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 30\n                    }, this), \" \", job.printType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Copies:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 30\n                    }, this), \" \", job.copies]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Color:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 30\n                    }, this), \" \", job.colorType]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Size:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 30\n                    }, this), \" \", job.paperSize]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 25\n                  }, this), job.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Remarks:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 32\n                    }, this), \" \", job.remarks]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: getStatusBadge(job.status)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"btn-group-vertical\",\n                  role: \"group\",\n                  children: [job.status === 'Requested' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-primary\",\n                      size: \"sm\",\n                      onClick: () => {\n                        setSelectedJob(job);\n                        setShowQuoteModal(true);\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-dollar-sign me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 291,\n                        columnNumber: 31\n                      }, this), \"Quote\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"outline-danger\",\n                      size: \"sm\",\n                      onClick: () => handleStatusUpdate(job.id, 'Rejected'),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-times me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 31\n                      }, this), \"Reject\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true), job.status === 'Confirmed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-info\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-play me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 29\n                    }, this), \"Start\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 27\n                  }, this), job.status === 'InProgress' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Completed'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-check me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 29\n                    }, this), \"Complete\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 27\n                  }, this), job.status === 'Completed' && /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-success\",\n                    size: \"sm\",\n                    onClick: () => handleStatusUpdate(job.id, 'Delivered'),\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-truck me-1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 29\n                    }, this), \"Deliver\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 328,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outline-secondary\",\n                    size: \"sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-comment\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 279,\n                columnNumber: 21\n              }, this)]\n            }, job.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          variant: \"info\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-info-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 15\n          }, this), \"No jobs found for the selected filter.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showQuoteModal,\n      onHide: () => setShowQuoteModal(false),\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-dollar-sign me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), \"Provide Quote - \", selectedJob === null || selectedJob === void 0 ? void 0 : selectedJob.jobNumber]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        children: selectedJob && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 p-3 bg-light rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              children: \"Job Details:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"File:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.fileName, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 66\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Type:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.printType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Copies:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.copies]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Color:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.colorType, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 68\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Size:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.paperSize, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 67\n                }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Student:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 21\n                }, this), \" \", selectedJob.studentName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 17\n            }, this), selectedJob.remarks && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Remarks:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 21\n              }, this), \" \", selectedJob.remarks]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Cost ($)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(InputGroup, {\n                    children: [/*#__PURE__*/_jsxDEV(InputGroup.Text, {\n                      children: \"$\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"number\",\n                      step: \"0.01\",\n                      placeholder: \"0.00\",\n                      value: quoteData.cost,\n                      onChange: e => setQuoteData(prev => ({\n                        ...prev,\n                        cost: e.target.value\n                      })),\n                      required: true\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                  className: \"mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                    children: \"Estimated Hours\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                    type: \"number\",\n                    min: \"1\",\n                    placeholder: \"Hours to complete\",\n                    value: quoteData.estimatedHours,\n                    onChange: e => setQuoteData(prev => ({\n                      ...prev,\n                      estimatedHours: e.target.value\n                    })),\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 409,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                children: \"Notes (Optional)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                as: \"textarea\",\n                rows: 3,\n                placeholder: \"Any additional notes for the student...\",\n                value: quoteData.notes,\n                onChange: e => setQuoteData(prev => ({\n                  ...prev,\n                  notes: e.target.value\n                }))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"secondary\",\n          onClick: () => setShowQuoteModal(false),\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          onClick: handleQuoteSubmit,\n          disabled: !quoteData.cost || !quoteData.estimatedHours,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-paper-plane me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), \"Send Quote\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 5\n  }, this);\n};\n_s(XeroxCenterDashboard, \"VJHAFSPjU/jS1C7Ma9QlRu7N0Bo=\", false, function () {\n  return [useAuth];\n});\n_c = XeroxCenterDashboard;\nexport default XeroxCenterDashboard;\nvar _c;\n$RefreshReg$(_c, \"XeroxCenterDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Table", "Badge", "<PERSON><PERSON>", "Form", "Modal", "InputGroup", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "XeroxCenterDashboard", "_s", "user", "printJobs", "setPrintJobs", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>ob", "showQuoteModal", "setShowQuoteModal", "quoteData", "setQuoteData", "cost", "estimatedHours", "notes", "filterStatus", "setFilterStatus", "id", "jobNumber", "fileName", "status", "studentName", "studentEmail", "printType", "copies", "colorType", "paperSize", "remarks", "created", "estimatedCompletionTime", "getStatusBadge", "statusConfig", "variant", "icon", "config", "bg", "children", "className", "_jsxFileName", "lineNumber", "columnNumber", "handleQuoteSubmit", "estimatedCompletion", "Date", "setHours", "getHours", "parseInt", "console", "log", "jobId", "parseFloat", "prev", "map", "job", "toISOString", "handleStatusUpdate", "newStatus", "filteredJobs", "filter", "stats", "total", "length", "pending", "includes", "inProgress", "completed", "revenue", "reduce", "sum", "fluid", "username", "md", "Body", "toFixed", "Header", "Select", "style", "width", "value", "onChange", "e", "target", "responsive", "hover", "toLocaleDateString", "role", "size", "onClick", "show", "onHide", "closeButton", "Title", "Group", "Label", "Text", "Control", "type", "step", "placeholder", "required", "min", "as", "rows", "Footer", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/XeroxCenterDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, Col, Card, Button, Table, Badge, Alert, Form, Modal, InputGroup } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { printJobApi, xeroxCenterApi } from '../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  estimatedCompletionTime?: string;\n  studentName: string;\n  studentEmail: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  remarks?: string;\n  created: string;\n}\n\nconst XeroxCenterDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [selectedJob, setSelectedJob] = useState<PrintJob | null>(null);\n  const [showQuoteModal, setShowQuoteModal] = useState(false);\n  const [quoteData, setQuoteData] = useState({\n    cost: '',\n    estimatedHours: '',\n    notes: ''\n  });\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  useEffect(() => {\n    // Mock data - replace with actual API calls\n    setPrintJobs([\n      {\n        id: 1,\n        jobNumber: 'JOB-001',\n        fileName: 'Assignment1.pdf',\n        status: 'Requested',\n        studentName: 'John Doe',\n        studentEmail: '<EMAIL>',\n        printType: 'Print',\n        copies: 5,\n        colorType: 'BlackWhite',\n        paperSize: 'A4',\n        remarks: 'Please print double-sided',\n        created: '2024-01-15T10:00:00'\n      },\n      {\n        id: 2,\n        jobNumber: 'JOB-002',\n        fileName: 'Thesis_Chapter1.docx',\n        status: 'InProgress',\n        cost: 45.00,\n        estimatedCompletionTime: '2024-01-15T16:00:00',\n        studentName: 'Jane Smith',\n        studentEmail: '<EMAIL>',\n        printType: 'Print',\n        copies: 1,\n        colorType: 'BlackWhite',\n        paperSize: 'A4',\n        created: '2024-01-14T09:00:00'\n      },\n      {\n        id: 3,\n        jobNumber: 'JOB-003',\n        fileName: 'Presentation.pptx',\n        status: 'WaitingConfirmation',\n        cost: 25.50,\n        studentName: 'Mike Johnson',\n        studentEmail: '<EMAIL>',\n        printType: 'Print',\n        copies: 10,\n        colorType: 'Color',\n        paperSize: 'A4',\n        created: '2024-01-15T08:30:00'\n      }\n    ]);\n  }, []);\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig = {\n      'Requested': { variant: 'secondary', icon: 'clock' },\n      'UnderReview': { variant: 'info', icon: 'eye' },\n      'Quoted': { variant: 'warning', icon: 'dollar-sign' },\n      'WaitingConfirmation': { variant: 'warning', icon: 'hourglass-half' },\n      'Confirmed': { variant: 'primary', icon: 'check' },\n      'InProgress': { variant: 'info', icon: 'cog' },\n      'Completed': { variant: 'success', icon: 'check-circle' },\n      'Delivered': { variant: 'success', icon: 'truck' },\n      'Rejected': { variant: 'danger', icon: 'times' },\n      'Cancelled': { variant: 'dark', icon: 'ban' }\n    };\n\n    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'secondary', icon: 'question' };\n    \n    return (\n      <Badge bg={config.variant}>\n        <i className={`fas fa-${config.icon} me-1`}></i>\n        {status}\n      </Badge>\n    );\n  };\n\n  const handleQuoteSubmit = () => {\n    if (selectedJob) {\n      const estimatedCompletion = new Date();\n      estimatedCompletion.setHours(estimatedCompletion.getHours() + parseInt(quoteData.estimatedHours));\n      \n      // Mock API call to update job with quote\n      console.log('Submitting quote:', {\n        jobId: selectedJob.id,\n        cost: parseFloat(quoteData.cost),\n        estimatedCompletion,\n        notes: quoteData.notes\n      });\n      \n      // Update local state\n      setPrintJobs(prev => prev.map(job => \n        job.id === selectedJob.id \n          ? { ...job, status: 'Quoted', cost: parseFloat(quoteData.cost), estimatedCompletionTime: estimatedCompletion.toISOString() }\n          : job\n      ));\n    }\n    \n    setShowQuoteModal(false);\n    setSelectedJob(null);\n    setQuoteData({ cost: '', estimatedHours: '', notes: '' });\n  };\n\n  const handleStatusUpdate = (jobId: number, newStatus: string) => {\n    // Mock API call to update job status\n    console.log('Updating job status:', { jobId, newStatus });\n    \n    setPrintJobs(prev => prev.map(job => \n      job.id === jobId ? { ...job, status: newStatus } : job\n    ));\n  };\n\n  const filteredJobs = filterStatus === 'all' \n    ? printJobs \n    : printJobs.filter(job => job.status === filterStatus);\n\n  const stats = {\n    total: printJobs.length,\n    pending: printJobs.filter(job => ['Requested', 'UnderReview', 'Quoted', 'WaitingConfirmation'].includes(job.status)).length,\n    inProgress: printJobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length,\n    completed: printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length,\n    revenue: printJobs.reduce((sum, job) => sum + (job.cost || 0), 0)\n  };\n\n  return (\n    <Container fluid>\n      <Row className=\"mb-4\">\n        <Col>\n          <h2>\n            <i className=\"fas fa-store me-2\"></i>\n            Xerox Center Dashboard\n          </h2>\n          <p className=\"text-muted\">Welcome back, {user?.username}!</p>\n        </Col>\n      </Row>\n\n      {/* Statistics Cards */}\n      <Row className=\"mb-4\">\n        <Col md={3}>\n          <Card className=\"text-center border-left-primary\">\n            <Card.Body>\n              <i className=\"fas fa-file-alt fa-2x text-primary mb-2\"></i>\n              <h5>Total Jobs</h5>\n              <h3 className=\"text-primary\">{stats.total}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-warning\">\n            <Card.Body>\n              <i className=\"fas fa-clock fa-2x text-warning mb-2\"></i>\n              <h5>Pending</h5>\n              <h3 className=\"text-warning\">{stats.pending}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-info\">\n            <Card.Body>\n              <i className=\"fas fa-cog fa-2x text-info mb-2\"></i>\n              <h5>In Progress</h5>\n              <h3 className=\"text-info\">{stats.inProgress}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n        <Col md={3}>\n          <Card className=\"text-center border-left-success\">\n            <Card.Body>\n              <i className=\"fas fa-dollar-sign fa-2x text-success mb-2\"></i>\n              <h5>Revenue</h5>\n              <h3 className=\"text-success\">${stats.revenue.toFixed(2)}</h3>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* Job Queue */}\n      <Card>\n        <Card.Header className=\"d-flex justify-content-between align-items-center\">\n          <h5 className=\"mb-0\">\n            <i className=\"fas fa-tasks me-2\"></i>\n            Job Queue\n          </h5>\n          <Form.Select \n            style={{ width: 'auto' }}\n            value={filterStatus}\n            onChange={(e) => setFilterStatus(e.target.value)}\n          >\n            <option value=\"all\">All Jobs</option>\n            <option value=\"Requested\">Requested</option>\n            <option value=\"UnderReview\">Under Review</option>\n            <option value=\"Quoted\">Quoted</option>\n            <option value=\"WaitingConfirmation\">Waiting Confirmation</option>\n            <option value=\"Confirmed\">Confirmed</option>\n            <option value=\"InProgress\">In Progress</option>\n            <option value=\"Completed\">Completed</option>\n          </Form.Select>\n        </Card.Header>\n        <Card.Body>\n          {filteredJobs.length > 0 ? (\n            <Table responsive hover>\n              <thead>\n                <tr>\n                  <th>Job #</th>\n                  <th>Student</th>\n                  <th>File</th>\n                  <th>Details</th>\n                  <th>Status</th>\n                  <th>Cost</th>\n                  <th>Actions</th>\n                </tr>\n              </thead>\n              <tbody>\n                {filteredJobs.map(job => (\n                  <tr key={job.id}>\n                    <td>\n                      <strong>{job.jobNumber}</strong>\n                      <br />\n                      <small className=\"text-muted\">\n                        {new Date(job.created).toLocaleDateString()}\n                      </small>\n                    </td>\n                    <td>\n                      <div>\n                        <strong>{job.studentName}</strong>\n                        <br />\n                        <small className=\"text-muted\">{job.studentEmail}</small>\n                      </div>\n                    </td>\n                    <td>\n                      <i className=\"fas fa-file-pdf me-2 text-danger\"></i>\n                      {job.fileName}\n                    </td>\n                    <td>\n                      <div className=\"small\">\n                        <div><strong>Type:</strong> {job.printType}</div>\n                        <div><strong>Copies:</strong> {job.copies}</div>\n                        <div><strong>Color:</strong> {job.colorType}</div>\n                        <div><strong>Size:</strong> {job.paperSize}</div>\n                        {job.remarks && (\n                          <div><strong>Remarks:</strong> {job.remarks}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td>{getStatusBadge(job.status)}</td>\n                    <td>\n                      {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                    </td>\n                    <td>\n                      <div className=\"btn-group-vertical\" role=\"group\">\n                        {job.status === 'Requested' && (\n                          <>\n                            <Button \n                              variant=\"outline-primary\" \n                              size=\"sm\"\n                              onClick={() => {\n                                setSelectedJob(job);\n                                setShowQuoteModal(true);\n                              }}\n                            >\n                              <i className=\"fas fa-dollar-sign me-1\"></i>\n                              Quote\n                            </Button>\n                            <Button \n                              variant=\"outline-danger\" \n                              size=\"sm\"\n                              onClick={() => handleStatusUpdate(job.id, 'Rejected')}\n                            >\n                              <i className=\"fas fa-times me-1\"></i>\n                              Reject\n                            </Button>\n                          </>\n                        )}\n                        \n                        {job.status === 'Confirmed' && (\n                          <Button \n                            variant=\"outline-info\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                          >\n                            <i className=\"fas fa-play me-1\"></i>\n                            Start\n                          </Button>\n                        )}\n                        \n                        {job.status === 'InProgress' && (\n                          <Button \n                            variant=\"outline-success\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Completed')}\n                          >\n                            <i className=\"fas fa-check me-1\"></i>\n                            Complete\n                          </Button>\n                        )}\n                        \n                        {job.status === 'Completed' && (\n                          <Button \n                            variant=\"outline-success\" \n                            size=\"sm\"\n                            onClick={() => handleStatusUpdate(job.id, 'Delivered')}\n                          >\n                            <i className=\"fas fa-truck me-1\"></i>\n                            Deliver\n                          </Button>\n                        )}\n                        \n                        <Button variant=\"outline-secondary\" size=\"sm\">\n                          <i className=\"fas fa-comment\"></i>\n                        </Button>\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </Table>\n          ) : (\n            <Alert variant=\"info\">\n              <i className=\"fas fa-info-circle me-2\"></i>\n              No jobs found for the selected filter.\n            </Alert>\n          )}\n        </Card.Body>\n      </Card>\n\n      {/* Quote Modal */}\n      <Modal show={showQuoteModal} onHide={() => setShowQuoteModal(false)}>\n        <Modal.Header closeButton>\n          <Modal.Title>\n            <i className=\"fas fa-dollar-sign me-2\"></i>\n            Provide Quote - {selectedJob?.jobNumber}\n          </Modal.Title>\n        </Modal.Header>\n        <Modal.Body>\n          {selectedJob && (\n            <>\n              <div className=\"mb-3 p-3 bg-light rounded\">\n                <h6>Job Details:</h6>\n                <div className=\"row\">\n                  <div className=\"col-6\">\n                    <strong>File:</strong> {selectedJob.fileName}<br />\n                    <strong>Type:</strong> {selectedJob.printType}<br />\n                    <strong>Copies:</strong> {selectedJob.copies}\n                  </div>\n                  <div className=\"col-6\">\n                    <strong>Color:</strong> {selectedJob.colorType}<br />\n                    <strong>Size:</strong> {selectedJob.paperSize}<br />\n                    <strong>Student:</strong> {selectedJob.studentName}\n                  </div>\n                </div>\n                {selectedJob.remarks && (\n                  <div className=\"mt-2\">\n                    <strong>Remarks:</strong> {selectedJob.remarks}\n                  </div>\n                )}\n              </div>\n\n              <Form>\n                <Row>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Cost ($)</Form.Label>\n                      <InputGroup>\n                        <InputGroup.Text>$</InputGroup.Text>\n                        <Form.Control\n                          type=\"number\"\n                          step=\"0.01\"\n                          placeholder=\"0.00\"\n                          value={quoteData.cost}\n                          onChange={(e) => setQuoteData(prev => ({ ...prev, cost: e.target.value }))}\n                          required\n                        />\n                      </InputGroup>\n                    </Form.Group>\n                  </Col>\n                  <Col md={6}>\n                    <Form.Group className=\"mb-3\">\n                      <Form.Label>Estimated Hours</Form.Label>\n                      <Form.Control\n                        type=\"number\"\n                        min=\"1\"\n                        placeholder=\"Hours to complete\"\n                        value={quoteData.estimatedHours}\n                        onChange={(e) => setQuoteData(prev => ({ ...prev, estimatedHours: e.target.value }))}\n                        required\n                      />\n                    </Form.Group>\n                  </Col>\n                </Row>\n\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Notes (Optional)</Form.Label>\n                  <Form.Control\n                    as=\"textarea\"\n                    rows={3}\n                    placeholder=\"Any additional notes for the student...\"\n                    value={quoteData.notes}\n                    onChange={(e) => setQuoteData(prev => ({ ...prev, notes: e.target.value }))}\n                  />\n                </Form.Group>\n              </Form>\n            </>\n          )}\n        </Modal.Body>\n        <Modal.Footer>\n          <Button variant=\"secondary\" onClick={() => setShowQuoteModal(false)}>\n            Cancel\n          </Button>\n          <Button \n            variant=\"primary\" \n            onClick={handleQuoteSubmit}\n            disabled={!quoteData.cost || !quoteData.estimatedHours}\n          >\n            <i className=\"fas fa-paper-plane me-2\"></i>\n            Send Quote\n          </Button>\n        </Modal.Footer>\n      </Modal>\n    </Container>\n  );\n};\n\nexport default XeroxCenterDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,iBAAiB;AACjH,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAoBlD,MAAMC,oBAA8B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3C,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACQ,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAkB,IAAI,CAAC;EACrE,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC;IACzC6B,IAAI,EAAE,EAAE;IACRC,cAAc,EAAE,EAAE;IAClBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd;IACAqB,YAAY,CAAC,CACX;MACEY,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,iBAAiB;MAC3BC,MAAM,EAAE,WAAW;MACnBC,WAAW,EAAE,UAAU;MACvBC,YAAY,EAAE,yBAAyB;MACvCC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,IAAI;MACfC,OAAO,EAAE,2BAA2B;MACpCC,OAAO,EAAE;IACX,CAAC,EACD;MACEX,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,sBAAsB;MAChCC,MAAM,EAAE,YAAY;MACpBR,IAAI,EAAE,KAAK;MACXiB,uBAAuB,EAAE,qBAAqB;MAC9CR,WAAW,EAAE,YAAY;MACzBC,YAAY,EAAE,2BAA2B;MACzCC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE;IACX,CAAC,EACD;MACEX,EAAE,EAAE,CAAC;MACLC,SAAS,EAAE,SAAS;MACpBC,QAAQ,EAAE,mBAAmB;MAC7BC,MAAM,EAAE,qBAAqB;MAC7BR,IAAI,EAAE,KAAK;MACXS,WAAW,EAAE,cAAc;MAC3BC,YAAY,EAAE,6BAA6B;MAC3CC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,IAAI;MACfE,OAAO,EAAE;IACX,CAAC,CACF,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,cAAc,GAAIV,MAAc,IAAK;IACzC,MAAMW,YAAY,GAAG;MACnB,WAAW,EAAE;QAAEC,OAAO,EAAE,WAAW;QAAEC,IAAI,EAAE;MAAQ,CAAC;MACpD,aAAa,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC/C,QAAQ,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAc,CAAC;MACrD,qBAAqB,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAiB,CAAC;MACrE,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,YAAY,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM,CAAC;MAC9C,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAe,CAAC;MACzD,WAAW,EAAE;QAAED,OAAO,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAClD,UAAU,EAAE;QAAED,OAAO,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAQ,CAAC;MAChD,WAAW,EAAE;QAAED,OAAO,EAAE,MAAM;QAAEC,IAAI,EAAE;MAAM;IAC9C,CAAC;IAED,MAAMC,MAAM,GAAGH,YAAY,CAACX,MAAM,CAA8B,IAAI;MAAEY,OAAO,EAAE,WAAW;MAAEC,IAAI,EAAE;IAAW,CAAC;IAE9G,oBACEnC,OAAA,CAACP,KAAK;MAAC4C,EAAE,EAAED,MAAM,CAACF,OAAQ;MAAAI,QAAA,gBACxBtC,OAAA;QAAGuC,SAAS,EAAE,UAAUH,MAAM,CAACD,IAAI;MAAQ;QAAAd,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC/CpB,MAAM;IAAA;MAAAD,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAInC,WAAW,EAAE;MACf,MAAMoC,mBAAmB,GAAG,IAAIC,IAAI,CAAC,CAAC;MACtCD,mBAAmB,CAACE,QAAQ,CAACF,mBAAmB,CAACG,QAAQ,CAAC,CAAC,GAAGC,QAAQ,CAACpC,SAAS,CAACG,cAAc,CAAC,CAAC;;MAEjG;MACAkC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;QAC/BC,KAAK,EAAE3C,WAAW,CAACW,EAAE;QACrBL,IAAI,EAAEsC,UAAU,CAACxC,SAAS,CAACE,IAAI,CAAC;QAChC8B,mBAAmB;QACnB5B,KAAK,EAAEJ,SAAS,CAACI;MACnB,CAAC,CAAC;;MAEF;MACAT,YAAY,CAAC8C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,GAAG,IAC/BA,GAAG,CAACpC,EAAE,KAAKX,WAAW,CAACW,EAAE,GACrB;QAAE,GAAGoC,GAAG;QAAEjC,MAAM,EAAE,QAAQ;QAAER,IAAI,EAAEsC,UAAU,CAACxC,SAAS,CAACE,IAAI,CAAC;QAAEiB,uBAAuB,EAAEa,mBAAmB,CAACY,WAAW,CAAC;MAAE,CAAC,GAC1HD,GACN,CAAC,CAAC;IACJ;IAEA5C,iBAAiB,CAAC,KAAK,CAAC;IACxBF,cAAc,CAAC,IAAI,CAAC;IACpBI,YAAY,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,cAAc,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAG,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMyC,kBAAkB,GAAGA,CAACN,KAAa,EAAEO,SAAiB,KAAK;IAC/D;IACAT,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAAEC,KAAK;MAAEO;IAAU,CAAC,CAAC;IAEzDnD,YAAY,CAAC8C,IAAI,IAAIA,IAAI,CAACC,GAAG,CAACC,GAAG,IAC/BA,GAAG,CAACpC,EAAE,KAAKgC,KAAK,GAAG;MAAE,GAAGI,GAAG;MAAEjC,MAAM,EAAEoC;IAAU,CAAC,GAAGH,GACrD,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,YAAY,GAAG1C,YAAY,KAAK,KAAK,GACvCX,SAAS,GACTA,SAAS,CAACsD,MAAM,CAACL,GAAG,IAAIA,GAAG,CAACjC,MAAM,KAAKL,YAAY,CAAC;EAExD,MAAM4C,KAAK,GAAG;IACZC,KAAK,EAAExD,SAAS,CAACyD,MAAM;IACvBC,OAAO,EAAE1D,SAAS,CAACsD,MAAM,CAACL,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAACU,QAAQ,CAACV,GAAG,CAACjC,MAAM,CAAC,CAAC,CAACyC,MAAM;IAC3HG,UAAU,EAAE5D,SAAS,CAACsD,MAAM,CAACL,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACU,QAAQ,CAACV,GAAG,CAACjC,MAAM,CAAC,CAAC,CAACyC,MAAM;IAC5FI,SAAS,EAAE7D,SAAS,CAACsD,MAAM,CAACL,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACU,QAAQ,CAACV,GAAG,CAACjC,MAAM,CAAC,CAAC,CAACyC,MAAM;IAC1FK,OAAO,EAAE9D,SAAS,CAAC+D,MAAM,CAAC,CAACC,GAAG,EAAEf,GAAG,KAAKe,GAAG,IAAIf,GAAG,CAACzC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC;EAClE,CAAC;EAED,oBACEd,OAAA,CAACb,SAAS;IAACoF,KAAK;IAAAjC,QAAA,gBACdtC,OAAA,CAACZ,GAAG;MAACmD,SAAS,EAAC,MAAM;MAAAD,QAAA,eACnBtC,OAAA,CAACX,GAAG;QAAAiD,QAAA,gBACFtC,OAAA;UAAAsC,QAAA,gBACEtC,OAAA;YAAGuC,SAAS,EAAC;UAAmB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0BAEvC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1C,OAAA;UAAGuC,SAAS,EAAC,YAAY;UAAAD,QAAA,GAAC,gBAAc,EAACjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,QAAQ,EAAC,GAAC;QAAA;UAAAnD,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D;IAAC;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA,CAACZ,GAAG;MAACmD,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBtC,OAAA,CAACX,GAAG;QAACoF,EAAE,EAAE,CAAE;QAAAnC,QAAA,eACTtC,OAAA,CAACV,IAAI;UAACiD,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/CtC,OAAA,CAACV,IAAI,CAACoF,IAAI;YAAApC,QAAA,gBACRtC,OAAA;cAAGuC,SAAS,EAAC;YAAyC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3D1C,OAAA;cAAAsC,QAAA,EAAI;YAAU;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnB1C,OAAA;cAAIuC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEuB,KAAK,CAACC;YAAK;cAAAzC,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoF,EAAE,EAAE,CAAE;QAAAnC,QAAA,eACTtC,OAAA,CAACV,IAAI;UAACiD,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/CtC,OAAA,CAACV,IAAI,CAACoF,IAAI;YAAApC,QAAA,gBACRtC,OAAA;cAAGuC,SAAS,EAAC;YAAsC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxD1C,OAAA;cAAAsC,QAAA,EAAI;YAAO;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB1C,OAAA;cAAIuC,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAEuB,KAAK,CAACG;YAAO;cAAA3C,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoF,EAAE,EAAE,CAAE;QAAAnC,QAAA,eACTtC,OAAA,CAACV,IAAI;UAACiD,SAAS,EAAC,8BAA8B;UAAAD,QAAA,eAC5CtC,OAAA,CAACV,IAAI,CAACoF,IAAI;YAAApC,QAAA,gBACRtC,OAAA;cAAGuC,SAAS,EAAC;YAAiC;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD1C,OAAA;cAAAsC,QAAA,EAAI;YAAW;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpB1C,OAAA;cAAIuC,SAAS,EAAC,WAAW;cAAAD,QAAA,EAAEuB,KAAK,CAACK;YAAU;cAAA7C,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1C,OAAA,CAACX,GAAG;QAACoF,EAAE,EAAE,CAAE;QAAAnC,QAAA,eACTtC,OAAA,CAACV,IAAI;UAACiD,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eAC/CtC,OAAA,CAACV,IAAI,CAACoF,IAAI;YAAApC,QAAA,gBACRtC,OAAA;cAAGuC,SAAS,EAAC;YAA4C;cAAAlB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9D1C,OAAA;cAAAsC,QAAA,EAAI;YAAO;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChB1C,OAAA;cAAIuC,SAAS,EAAC,cAAc;cAAAD,QAAA,GAAC,GAAC,EAACuB,KAAK,CAACO,OAAO,CAACO,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAtD,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA,CAACV,IAAI;MAAAgD,QAAA,gBACHtC,OAAA,CAACV,IAAI,CAACsF,MAAM;QAACrC,SAAS,EAAC,mDAAmD;QAAAD,QAAA,gBACxEtC,OAAA;UAAIuC,SAAS,EAAC,MAAM;UAAAD,QAAA,gBAClBtC,OAAA;YAAGuC,SAAS,EAAC;UAAmB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,aAEvC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1C,OAAA,CAACL,IAAI,CAACkF,MAAM;UACVC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAE;UACzBC,KAAK,EAAE/D,YAAa;UACpBgE,QAAQ,EAAGC,CAAC,IAAKhE,eAAe,CAACgE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAAA1C,QAAA,gBAEjDtC,OAAA;YAAQgF,KAAK,EAAC,KAAK;YAAA1C,QAAA,EAAC;UAAQ;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrC1C,OAAA;YAAQgF,KAAK,EAAC,WAAW;YAAA1C,QAAA,EAAC;UAAS;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C1C,OAAA;YAAQgF,KAAK,EAAC,aAAa;YAAA1C,QAAA,EAAC;UAAY;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjD1C,OAAA;YAAQgF,KAAK,EAAC,QAAQ;YAAA1C,QAAA,EAAC;UAAM;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtC1C,OAAA;YAAQgF,KAAK,EAAC,qBAAqB;YAAA1C,QAAA,EAAC;UAAoB;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACjE1C,OAAA;YAAQgF,KAAK,EAAC,WAAW;YAAA1C,QAAA,EAAC;UAAS;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5C1C,OAAA;YAAQgF,KAAK,EAAC,YAAY;YAAA1C,QAAA,EAAC;UAAW;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC/C1C,OAAA;YAAQgF,KAAK,EAAC,WAAW;YAAA1C,QAAA,EAAC;UAAS;YAAAjB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACd1C,OAAA,CAACV,IAAI,CAACoF,IAAI;QAAApC,QAAA,EACPqB,YAAY,CAACI,MAAM,GAAG,CAAC,gBACtB/D,OAAA,CAACR,KAAK;UAAC4F,UAAU;UAACC,KAAK;UAAA/C,QAAA,gBACrBtC,OAAA;YAAAsC,QAAA,eACEtC,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAAsC,QAAA,EAAI;cAAK;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACd1C,OAAA;gBAAAsC,QAAA,EAAI;cAAO;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB1C,OAAA;gBAAAsC,QAAA,EAAI;cAAI;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb1C,OAAA;gBAAAsC,QAAA,EAAI;cAAO;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChB1C,OAAA;gBAAAsC,QAAA,EAAI;cAAM;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACf1C,OAAA;gBAAAsC,QAAA,EAAI;cAAI;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACb1C,OAAA;gBAAAsC,QAAA,EAAI;cAAO;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR1C,OAAA;YAAAsC,QAAA,EACGqB,YAAY,CAACL,GAAG,CAACC,GAAG,iBACnBvD,OAAA;cAAAsC,QAAA,gBACEtC,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAAsC,QAAA,EAASiB,GAAG,CAACnC;gBAAS;kBAAAC,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eAChC1C,OAAA;kBAAAqB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACN1C,OAAA;kBAAOuC,SAAS,EAAC,YAAY;kBAAAD,QAAA,EAC1B,IAAIO,IAAI,CAACU,GAAG,CAACzB,OAAO,CAAC,CAACwD,kBAAkB,CAAC;gBAAC;kBAAAjE,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACL1C,OAAA;gBAAAsC,QAAA,eACEtC,OAAA;kBAAAsC,QAAA,gBACEtC,OAAA;oBAAAsC,QAAA,EAASiB,GAAG,CAAChC;kBAAW;oBAAAF,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eAClC1C,OAAA;oBAAAqB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACN1C,OAAA;oBAAOuC,SAAS,EAAC,YAAY;oBAAAD,QAAA,EAAEiB,GAAG,CAAC/B;kBAAY;oBAAAH,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL1C,OAAA;gBAAAsC,QAAA,gBACEtC,OAAA;kBAAGuC,SAAS,EAAC;gBAAkC;kBAAAlB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACnDa,GAAG,CAAClC,QAAQ;cAAA;gBAAAA,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,eACL1C,OAAA;gBAAAsC,QAAA,eACEtC,OAAA;kBAAKuC,SAAS,EAAC,OAAO;kBAAAD,QAAA,gBACpBtC,OAAA;oBAAAsC,QAAA,gBAAKtC,OAAA;sBAAAsC,QAAA,EAAQ;oBAAK;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,GAAG,CAAC9B,SAAS;kBAAA;oBAAAJ,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjD1C,OAAA;oBAAAsC,QAAA,gBAAKtC,OAAA;sBAAAsC,QAAA,EAAQ;oBAAO;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,GAAG,CAAC7B,MAAM;kBAAA;oBAAAL,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChD1C,OAAA;oBAAAsC,QAAA,gBAAKtC,OAAA;sBAAAsC,QAAA,EAAQ;oBAAM;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,GAAG,CAAC5B,SAAS;kBAAA;oBAAAN,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClD1C,OAAA;oBAAAsC,QAAA,gBAAKtC,OAAA;sBAAAsC,QAAA,EAAQ;oBAAK;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,GAAG,CAAC3B,SAAS;kBAAA;oBAAAP,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EAChDa,GAAG,CAAC1B,OAAO,iBACV7B,OAAA;oBAAAsC,QAAA,gBAAKtC,OAAA;sBAAAsC,QAAA,EAAQ;oBAAQ;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,KAAC,EAACa,GAAG,CAAC1B,OAAO;kBAAA;oBAAAR,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACL1C,OAAA;gBAAAsC,QAAA,EAAKN,cAAc,CAACuB,GAAG,CAACjC,MAAM;cAAC;gBAAAD,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrC1C,OAAA;gBAAAsC,QAAA,EACGiB,GAAG,CAACzC,IAAI,GAAG,IAAIyC,GAAG,CAACzC,IAAI,CAAC6D,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;cAAG;gBAAAtD,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACL1C,OAAA;gBAAAsC,QAAA,eACEtC,OAAA;kBAAKuC,SAAS,EAAC,oBAAoB;kBAACgD,IAAI,EAAC,OAAO;kBAAAjD,QAAA,GAC7CiB,GAAG,CAACjC,MAAM,KAAK,WAAW,iBACzBtB,OAAA,CAAAE,SAAA;oBAAAoC,QAAA,gBACEtC,OAAA,CAACT,MAAM;sBACL2C,OAAO,EAAC,iBAAiB;sBACzBsD,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAM;wBACbhF,cAAc,CAAC8C,GAAG,CAAC;wBACnB5C,iBAAiB,CAAC,IAAI,CAAC;sBACzB,CAAE;sBAAA2B,QAAA,gBAEFtC,OAAA;wBAAGuC,SAAS,EAAC;sBAAyB;wBAAAlB,QAAA,EAAAmB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,SAE7C;oBAAA;sBAAArB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACT1C,OAAA,CAACT,MAAM;sBACL2C,OAAO,EAAC,gBAAgB;sBACxBsD,IAAI,EAAC,IAAI;sBACTC,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACF,GAAG,CAACpC,EAAE,EAAE,UAAU,CAAE;sBAAAmB,QAAA,gBAEtDtC,OAAA;wBAAGuC,SAAS,EAAC;sBAAmB;wBAAAlB,QAAA,EAAAmB,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,UAEvC;oBAAA;sBAAArB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA,eACT,CACH,EAEAa,GAAG,CAACjC,MAAM,KAAK,WAAW,iBACzBtB,OAAA,CAACT,MAAM;oBACL2C,OAAO,EAAC,cAAc;oBACtBsD,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACF,GAAG,CAACpC,EAAE,EAAE,YAAY,CAAE;oBAAAmB,QAAA,gBAExDtC,OAAA;sBAAGuC,SAAS,EAAC;oBAAkB;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,SAEtC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAa,GAAG,CAACjC,MAAM,KAAK,YAAY,iBAC1BtB,OAAA,CAACT,MAAM;oBACL2C,OAAO,EAAC,iBAAiB;oBACzBsD,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACF,GAAG,CAACpC,EAAE,EAAE,WAAW,CAAE;oBAAAmB,QAAA,gBAEvDtC,OAAA;sBAAGuC,SAAS,EAAC;oBAAmB;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,YAEvC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,EAEAa,GAAG,CAACjC,MAAM,KAAK,WAAW,iBACzBtB,OAAA,CAACT,MAAM;oBACL2C,OAAO,EAAC,iBAAiB;oBACzBsD,IAAI,EAAC,IAAI;oBACTC,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAACF,GAAG,CAACpC,EAAE,EAAE,WAAW,CAAE;oBAAAmB,QAAA,gBAEvDtC,OAAA;sBAAGuC,SAAS,EAAC;oBAAmB;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,WAEvC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT,eAED1C,OAAA,CAACT,MAAM;oBAAC2C,OAAO,EAAC,mBAAmB;oBAACsD,IAAI,EAAC,IAAI;oBAAAlD,QAAA,eAC3CtC,OAAA;sBAAGuC,SAAS,EAAC;oBAAgB;sBAAAlB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAjGEa,GAAG,CAACpC,EAAE;cAAAE,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkGX,CACL;UAAC;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAER1C,OAAA,CAACN,KAAK;UAACwC,OAAO,EAAC,MAAM;UAAAI,QAAA,gBACnBtC,OAAA;YAAGuC,SAAS,EAAC;UAAyB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,0CAE7C;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGP1C,OAAA,CAACJ,KAAK;MAAC8F,IAAI,EAAEhF,cAAe;MAACiF,MAAM,EAAEA,CAAA,KAAMhF,iBAAiB,CAAC,KAAK,CAAE;MAAA2B,QAAA,gBAClEtC,OAAA,CAACJ,KAAK,CAACgF,MAAM;QAACgB,WAAW;QAAAtD,QAAA,eACvBtC,OAAA,CAACJ,KAAK,CAACiG,KAAK;UAAAvD,QAAA,gBACVtC,OAAA;YAAGuC,SAAS,EAAC;UAAyB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAC3B,EAAClC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEY,SAAS;QAAA;UAAAC,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B;MAAC;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACf1C,OAAA,CAACJ,KAAK,CAAC8E,IAAI;QAAApC,QAAA,EACR9B,WAAW,iBACVR,OAAA,CAAAE,SAAA;UAAAoC,QAAA,gBACEtC,OAAA;YAAKuC,SAAS,EAAC,2BAA2B;YAAAD,QAAA,gBACxCtC,OAAA;cAAAsC,QAAA,EAAI;YAAY;cAAAjB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrB1C,OAAA;cAAKuC,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClBtC,OAAA;gBAAKuC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBtC,OAAA;kBAAAsC,QAAA,EAAQ;gBAAK;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACa,QAAQ,eAACrB,OAAA;kBAAAqB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnD1C,OAAA;kBAAAsC,QAAA,EAAQ;gBAAK;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACiB,SAAS,eAACzB,OAAA;kBAAAqB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpD1C,OAAA;kBAAAsC,QAAA,EAAQ;gBAAO;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACkB,MAAM;cAAA;gBAAAL,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN1C,OAAA;gBAAKuC,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBACpBtC,OAAA;kBAAAsC,QAAA,EAAQ;gBAAM;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACmB,SAAS,eAAC3B,OAAA;kBAAAqB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrD1C,OAAA;kBAAAsC,QAAA,EAAQ;gBAAK;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACoB,SAAS,eAAC5B,OAAA;kBAAAqB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACpD1C,OAAA;kBAAAsC,QAAA,EAAQ;gBAAQ;kBAAAjB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACe,WAAW;cAAA;gBAAAF,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLlC,WAAW,CAACqB,OAAO,iBAClB7B,OAAA;cAAKuC,SAAS,EAAC,MAAM;cAAAD,QAAA,gBACnBtC,OAAA;gBAAAsC,QAAA,EAAQ;cAAQ;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAAClC,WAAW,CAACqB,OAAO;YAAA;cAAAR,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACN;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAEN1C,OAAA,CAACL,IAAI;YAAA2C,QAAA,gBACHtC,OAAA,CAACZ,GAAG;cAAAkD,QAAA,gBACFtC,OAAA,CAACX,GAAG;gBAACoF,EAAE,EAAE,CAAE;gBAAAnC,QAAA,eACTtC,OAAA,CAACL,IAAI,CAACmG,KAAK;kBAACvD,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BtC,OAAA,CAACL,IAAI,CAACoG,KAAK;oBAAAzD,QAAA,EAAC;kBAAQ;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACjC1C,OAAA,CAACH,UAAU;oBAAAyC,QAAA,gBACTtC,OAAA,CAACH,UAAU,CAACmG,IAAI;sBAAA1D,QAAA,EAAC;oBAAC;sBAAAjB,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiB,CAAC,eACpC1C,OAAA,CAACL,IAAI,CAACsG,OAAO;sBACXC,IAAI,EAAC,QAAQ;sBACbC,IAAI,EAAC,MAAM;sBACXC,WAAW,EAAC,MAAM;sBAClBpB,KAAK,EAAEpE,SAAS,CAACE,IAAK;sBACtBmE,QAAQ,EAAGC,CAAC,IAAKrE,YAAY,CAACwC,IAAI,KAAK;wBAAE,GAAGA,IAAI;wBAAEvC,IAAI,EAAEoE,CAAC,CAACC,MAAM,CAACH;sBAAM,CAAC,CAAC,CAAE;sBAC3EqB,QAAQ;oBAAA;sBAAAhF,QAAA,EAAAmB,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAArB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN1C,OAAA,CAACX,GAAG;gBAACoF,EAAE,EAAE,CAAE;gBAAAnC,QAAA,eACTtC,OAAA,CAACL,IAAI,CAACmG,KAAK;kBAACvD,SAAS,EAAC,MAAM;kBAAAD,QAAA,gBAC1BtC,OAAA,CAACL,IAAI,CAACoG,KAAK;oBAAAzD,QAAA,EAAC;kBAAe;oBAAAjB,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACxC1C,OAAA,CAACL,IAAI,CAACsG,OAAO;oBACXC,IAAI,EAAC,QAAQ;oBACbI,GAAG,EAAC,GAAG;oBACPF,WAAW,EAAC,mBAAmB;oBAC/BpB,KAAK,EAAEpE,SAAS,CAACG,cAAe;oBAChCkE,QAAQ,EAAGC,CAAC,IAAKrE,YAAY,CAACwC,IAAI,KAAK;sBAAE,GAAGA,IAAI;sBAAEtC,cAAc,EAAEmE,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAC,CAAE;oBACrFqB,QAAQ;kBAAA;oBAAAhF,QAAA,EAAAmB,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAArB,QAAA,EAAAmB,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ;cAAC;gBAAArB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1C,OAAA,CAACL,IAAI,CAACmG,KAAK;cAACvD,SAAS,EAAC,MAAM;cAAAD,QAAA,gBAC1BtC,OAAA,CAACL,IAAI,CAACoG,KAAK;gBAAAzD,QAAA,EAAC;cAAgB;gBAAAjB,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACzC1C,OAAA,CAACL,IAAI,CAACsG,OAAO;gBACXM,EAAE,EAAC,UAAU;gBACbC,IAAI,EAAE,CAAE;gBACRJ,WAAW,EAAC,yCAAyC;gBACrDpB,KAAK,EAAEpE,SAAS,CAACI,KAAM;gBACvBiE,QAAQ,EAAGC,CAAC,IAAKrE,YAAY,CAACwC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAErC,KAAK,EAAEkE,CAAC,CAACC,MAAM,CAACH;gBAAM,CAAC,CAAC;cAAE;gBAAA3D,QAAA,EAAAmB,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAArB,QAAA,EAAAmB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAArB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA,eACP;MACH;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eACb1C,OAAA,CAACJ,KAAK,CAAC6G,MAAM;QAAAnE,QAAA,gBACXtC,OAAA,CAACT,MAAM;UAAC2C,OAAO,EAAC,WAAW;UAACuD,OAAO,EAAEA,CAAA,KAAM9E,iBAAiB,CAAC,KAAK,CAAE;UAAA2B,QAAA,EAAC;QAErE;UAAAjB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1C,OAAA,CAACT,MAAM;UACL2C,OAAO,EAAC,SAAS;UACjBuD,OAAO,EAAE9C,iBAAkB;UAC3B+D,QAAQ,EAAE,CAAC9F,SAAS,CAACE,IAAI,IAAI,CAACF,SAAS,CAACG,cAAe;UAAAuB,QAAA,gBAEvDtC,OAAA;YAAGuC,SAAS,EAAC;UAAyB;YAAAlB,QAAA,EAAAmB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,cAE7C;QAAA;UAAArB,QAAA,EAAAmB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAArB,QAAA,EAAAmB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAArB,QAAA,EAAAmB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAArB,QAAA,EAAAmB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACtC,EAAA,CA5aID,oBAA8B;EAAA,QACjBL,OAAO;AAAA;AAAA6G,EAAA,GADpBxG,oBAA8B;AA8apC,eAAeA,oBAAoB;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}