{"ast": null, "code": "import { useEffect, useRef } from 'react';\n\n/**\n * Store the last of some value. Tracked via a `Ref` only updating it\n * after the component renders.\n *\n * Helpful if you need to compare a prop value to it's previous value during render.\n *\n * ```ts\n * function Component(props) {\n *   const lastProps = usePrevious(props)\n *\n *   if (lastProps.foo !== props.foo)\n *     resetValueFromProps(props.foo)\n * }\n * ```\n *\n * @param value the value to track\n */\nexport default function usePrevious(value) {\n  const ref = useRef(null);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n}", "map": {"version": 3, "names": ["useEffect", "useRef", "usePrevious", "value", "ref", "current"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/usePrevious.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n/**\n * Store the last of some value. Tracked via a `Ref` only updating it\n * after the component renders.\n *\n * Helpful if you need to compare a prop value to it's previous value during render.\n *\n * ```ts\n * function Component(props) {\n *   const lastProps = usePrevious(props)\n *\n *   if (lastProps.foo !== props.foo)\n *     resetValueFromProps(props.foo)\n * }\n * ```\n *\n * @param value the value to track\n */\nexport default function usePrevious(value) {\n  const ref = useRef(null);\n  useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n}"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,MAAMC,GAAG,GAAGH,MAAM,CAAC,IAAI,CAAC;EACxBD,SAAS,CAAC,MAAM;IACdI,GAAG,CAACC,OAAO,GAAGF,KAAK;EACrB,CAAC,CAAC;EACF,OAAOC,GAAG,CAACC,OAAO;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}