{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\SimpleAceternityStudentDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { FileText, Clock, CheckCircle, DollarSign, Download, MessageCircle, Eye, Upload, Activity, Printer, History, Settings, User, BarChart3, RefreshC<PERSON>, Pie<PERSON><PERSON>, Heart } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleAceternityStudentDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [printJobs, setPrintJobs] = useState([]);\n  const [xeroxCenters, setXeroxCenters] = useState([]);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF',\n    // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151',\n    // Gray\n    accent: isDarkMode ? '#10B981' : '#059669',\n    // Emerald\n    warning: isDarkMode ? '#F59E0B' : '#D97706',\n    // Amber\n    danger: isDarkMode ? '#EF4444' : '#DC2626',\n    // Red\n    success: isDarkMode ? '#10B981' : '#059669',\n    // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchData = async () => {\n    setIsRefreshing(true);\n    try {\n      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([printJobApi.getStudentJobs(), xeroxCenterApi.getAll()]);\n      setPrintJobs(printJobsResponse.data || []);\n      setXeroxCenters(xeroxCentersResponse.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setPrintJobs([]);\n      setXeroxCenters([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        bg: 'secondary',\n        icon: Clock\n      },\n      'UnderReview': {\n        bg: 'info',\n        icon: Eye\n      },\n      'Quoted': {\n        bg: 'warning',\n        icon: DollarSign\n      },\n      'Confirmed': {\n        bg: 'info',\n        icon: CheckCircle\n      },\n      'InProgress': {\n        bg: 'primary',\n        icon: Activity\n      },\n      'Completed': {\n        bg: 'success',\n        icon: CheckCircle\n      },\n      'Delivered': {\n        bg: 'success',\n        icon: CheckCircle\n      }\n    };\n    const config = statusConfig[status] || {\n      bg: 'secondary',\n      icon: Clock\n    };\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      bg: config.bg,\n      className: \"d-flex align-items-center gap-1 px-3 py-2\",\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this);\n  };\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  // Floating shapes for background\n  const floatingShapes = Array.from({\n    length: 6\n  }, (_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"position-absolute rounded-circle\",\n    style: {\n      background: `linear-gradient(135deg, ${i % 2 === 0 ? '#667eea' : '#764ba2'}, ${i % 2 === 0 ? '#764ba2' : '#667eea'})`,\n      width: `${Math.random() * 60 + 20}px`,\n      height: `${Math.random() * 60 + 20}px`,\n      left: `${Math.random() * 100}%`,\n      top: `${Math.random() * 100}%`,\n      opacity: 0.1,\n      zIndex: 0\n    },\n    animate: {\n      x: [0, Math.random() * 100 - 50],\n      y: [0, Math.random() * 100 - 50],\n      rotate: [0, 360],\n      scale: [1, 1.2, 1]\n    },\n    transition: {\n      duration: Math.random() * 20 + 10,\n      repeat: Infinity,\n      repeatType: \"reverse\",\n      ease: \"easeInOut\"\n    }\n  }, i, false, {\n    fileName: _jsxFileName,\n    lineNumber: 150,\n    columnNumber: 5\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen position-relative\",\n    style: {\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute w-100 h-100 overflow-hidden\",\n      children: [floatingShapes, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute w-100 h-100\",\n        style: {\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"position-relative py-4\",\n      style: {\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        variants: containerVariants,\n        initial: \"hidden\",\n        animate: \"visible\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          variants: itemVariants,\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: [0, 360],\n              scale: [1, 1.1, 1]\n            },\n            transition: {\n              duration: 4,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"d-inline-flex align-items-center justify-content-center mb-3\",\n            style: {\n              width: '80px',\n              height: '80px',\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              borderRadius: '20px',\n              boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n            },\n            children: /*#__PURE__*/_jsxDEV(User, {\n              className: \"text-white\",\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-white fw-bold mb-2\",\n            style: {\n              fontSize: '2.5rem'\n            },\n            children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.username, \"!\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-white-50 fs-5 mb-4\",\n            children: \"Manage your printing jobs and explore xerox centers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-center gap-3 flex-wrap\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"lg\",\n                className: \"px-5 py-3 rounded-4 border-0 fw-semibold\",\n                style: {\n                  background: 'rgba(255, 255, 255, 0.2)',\n                  backdropFilter: 'blur(20px)',\n                  color: '#fff',\n                  boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Upload, {\n                  className: \"me-2\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this), \"Upload Files\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outline-light\",\n                size: \"lg\",\n                onClick: fetchData,\n                disabled: isRefreshing,\n                className: \"px-4 py-3 rounded-4 fw-semibold\",\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `me-2 ${isRefreshing ? 'spin' : ''}`,\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), \"Refresh\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          variants: itemVariants,\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"border-0 shadow-lg\",\n            style: {\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-2\",\n              children: /*#__PURE__*/_jsxDEV(Nav, {\n                variant: \"pills\",\n                className: \"justify-content-center flex-wrap\",\n                children: [{\n                  key: 'dashboard',\n                  label: 'Dashboard',\n                  icon: BarChart3\n                }, {\n                  key: 'jobs',\n                  label: 'My Jobs',\n                  icon: FileText\n                }, {\n                  key: 'centers',\n                  label: 'Xerox Centers',\n                  icon: Printer\n                }, {\n                  key: 'history',\n                  label: 'History',\n                  icon: History\n                }, {\n                  key: 'favorites',\n                  label: 'Favorites',\n                  icon: Heart\n                }, {\n                  key: 'analytics',\n                  label: 'Analytics',\n                  icon: PieChart\n                }, {\n                  key: 'settings',\n                  label: 'Settings',\n                  icon: Settings\n                }].map(tab => {\n                  const IconComponent = tab.icon;\n                  return /*#__PURE__*/_jsxDEV(Nav.Item, {\n                    className: \"m-1\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === tab.key,\n                        onClick: () => setActiveTab(tab.key),\n                        className: `px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${activeTab === tab.key ? 'text-primary' : 'text-white-50'}`,\n                        style: {\n                          background: activeTab === tab.key ? 'rgba(255, 255, 255, 0.9)' : 'transparent',\n                          border: 'none',\n                          transition: 'all 0.3s ease',\n                          boxShadow: activeTab === tab.key ? '0 10px 30px rgba(255, 255, 255, 0.2)' : 'none'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 18,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 299,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"d-none d-md-inline\",\n                          children: tab.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 300,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 25\n                    }, this)\n                  }, tab.key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-4\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FileText, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 341,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-white-50 mb-1\",\n                      children: \"Total Jobs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 344,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold text-white\",\n                      children: printJobs.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.1\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Clock, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 365,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-white-50 mb-1\",\n                      children: \"In Progress\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold text-white\",\n                      children: inProgressJobs\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 375,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.2\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 401,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 395,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-white-50 mb-1\",\n                      children: \"Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold text-white\",\n                      children: completedJobs\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 425,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"text-white-50 mb-1\",\n                      children: \"Total Spent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold text-white\",\n                      children: [\"$\", totalSpent.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 435,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              lg: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"border-0 shadow-lg\",\n                style: {\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  backdropFilter: 'blur(20px)',\n                  borderRadius: '20px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"border-0 bg-transparent\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center justify-content-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"fw-bold mb-0 text-white\",\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        className: \"me-2\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 451,\n                        columnNumber: 27\n                      }, this), \"Recent Jobs\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 450,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      bg: \"light\",\n                      text: \"dark\",\n                      className: \"px-3 py-2\",\n                      children: [printJobs.length, \" total\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: printJobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"table-responsive\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      className: \"table-dark table-hover\",\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Job Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 465,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"File Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 466,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Status\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 467,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Cost\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 468,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Center\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 469,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Actions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 470,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 464,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 463,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: printJobs.slice(0, 10).map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"fw-semibold\",\n                            children: job.jobNumber\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 476,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 477,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: getStatusBadge(job.status)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 478,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"text-success fw-semibold\",\n                            children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 479,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: job.xeroxCenterName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 482,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex gap-1\",\n                              children: [/*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-light\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/_jsxDEV(Download, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 486,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 485,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-light\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/_jsxDEV(Eye, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 489,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 488,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-light\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 492,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 491,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 484,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 483,\n                            columnNumber: 35\n                          }, this)]\n                        }, job.id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 475,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 462,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.9\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    className: \"text-center py-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '80px',\n                          height: '80px',\n                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                          borderRadius: '20px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Upload, {\n                          className: \"text-white\",\n                          size: 40\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 514,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 508,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"fw-semibold mb-2 text-white\",\n                      children: \"No jobs yet\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-white-50 mb-4\",\n                      children: \"Upload your first file to get started!\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 502,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), activeTab !== 'dashboard' && /*#__PURE__*/_jsxDEV(Card, {\n            className: \"border-0 shadow-lg\",\n            style: {\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"text-center py-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-white mb-3\",\n                children: [activeTab.charAt(0).toUpperCase() + activeTab.slice(1), \" Tab\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-white-50\",\n                children: \"This tab is under development. Coming soon!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 15\n          }, this)]\n        }, activeTab, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 178,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleAceternityStudentDashboard, \"7wpd9sVXetnntBrEVIv3uotH+Lk=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = SimpleAceternityStudentDashboard;\nexport default SimpleAceternityStudentDashboard;\nvar _c;\n$RefreshReg$(_c, \"SimpleAceternityStudentDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Nav", "Table", "motion", "FileText", "Clock", "CheckCircle", "DollarSign", "Download", "MessageCircle", "Eye", "Upload", "Activity", "Printer", "History", "Settings", "User", "BarChart3", "RefreshCw", "<PERSON><PERSON><PERSON>", "Heart", "useAuth", "useTheme", "printJobApi", "xeroxCenterApi", "jsxDEV", "_jsxDEV", "SimpleAceternityStudentDashboard", "_s", "user", "isDarkMode", "toggleTheme", "activeTab", "setActiveTab", "printJobs", "setPrintJobs", "xeroxCenters", "setXeroxCenters", "isRefreshing", "setIsRefreshing", "colors", "primary", "secondary", "accent", "warning", "danger", "success", "background", "surface", "text", "textSecondary", "fetchData", "interval", "setInterval", "clearInterval", "printJobsResponse", "xeroxCentersResponse", "Promise", "all", "getStudentJobs", "getAll", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "bg", "icon", "config", "IconComponent", "className", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "totalSpent", "reduce", "sum", "job", "cost", "inProgressJobs", "filter", "includes", "length", "completedJobs", "containerVariants", "hidden", "opacity", "visible", "transition", "duration", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "floatingShapes", "Array", "from", "_", "i", "div", "style", "width", "Math", "random", "height", "left", "top", "zIndex", "animate", "x", "rotate", "scale", "repeat", "Infinity", "repeatType", "ease", "minHeight", "fluid", "variants", "initial", "borderRadius", "boxShadow", "fontSize", "username", "whileHover", "whileTap", "<PERSON><PERSON>ilter", "color", "variant", "onClick", "disabled", "Body", "key", "label", "map", "tab", "<PERSON><PERSON>", "Link", "active", "border", "md", "delay", "toFixed", "lg", "Header", "slice", "jobNumber", "xeroxCenterName", "id", "char<PERSON>t", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/SimpleAceternityStudentDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport {\n  FileText,\n  Clock,\n  CheckCircle,\n  DollarSign,\n  Download,\n  MessageCircle,\n  Eye,\n  Upload,\n  Activity,\n  Printer,\n  Star,\n  History,\n  Settings,\n  User,\n  BarChart3,\n  RefreshCw,\n  PieChart,\n  Heart,\n  Moon,\n  Sun,\n  TrendingUp\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { printJobApi, xeroxCenterApi } from '../../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  xeroxCenterName: string;\n  created: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  priority: string;\n}\n\ninterface XeroxCenter {\n  id: number;\n  name: string;\n  location: string;\n  pendingJobs: number;\n  averageRating: number;\n  isActive: boolean;\n}\n\nconst SimpleAceternityStudentDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { isDarkMode, toggleTheme } = useTheme();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [printJobs, setPrintJobs] = useState<PrintJob[]>([]);\n  const [xeroxCenters, setXeroxCenters] = useState<XeroxCenter[]>([]);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray\n    accent: isDarkMode ? '#10B981' : '#059669', // Emerald\n    warning: isDarkMode ? '#F59E0B' : '#D97706', // Amber\n    danger: isDarkMode ? '#EF4444' : '#DC2626', // Red\n    success: isDarkMode ? '#10B981' : '#059669', // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n\n  useEffect(() => {\n    fetchData();\n    const interval = setInterval(fetchData, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchData = async () => {\n    setIsRefreshing(true);\n    try {\n      const [printJobsResponse, xeroxCentersResponse] = await Promise.all([\n        printJobApi.getStudentJobs(),\n        xeroxCenterApi.getAll()\n      ]);\n      \n      setPrintJobs(printJobsResponse.data || []);\n      setXeroxCenters(xeroxCentersResponse.data || []);\n    } catch (error) {\n      console.error('Error fetching data:', error);\n      setPrintJobs([]);\n      setXeroxCenters([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig: { [key: string]: { bg: string; icon: any } } = {\n      'Requested': { bg: 'secondary', icon: Clock },\n      'UnderReview': { bg: 'info', icon: Eye },\n      'Quoted': { bg: 'warning', icon: DollarSign },\n      'Confirmed': { bg: 'info', icon: CheckCircle },\n      'InProgress': { bg: 'primary', icon: Activity },\n      'Completed': { bg: 'success', icon: CheckCircle },\n      'Delivered': { bg: 'success', icon: CheckCircle }\n    };\n\n    const config = statusConfig[status] || { bg: 'secondary', icon: Clock };\n    const IconComponent = config.icon;\n    \n    return (\n      <Badge bg={config.bg} className=\"d-flex align-items-center gap-1 px-3 py-2\">\n        <IconComponent size={14} />\n        {status}\n      </Badge>\n    );\n  };\n\n  const totalSpent = printJobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const inProgressJobs = printJobs.filter(job => ['InProgress', 'Confirmed', 'UnderReview'].includes(job.status)).length;\n  const completedJobs = printJobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  // Floating shapes for background\n  const floatingShapes = Array.from({ length: 6 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"position-absolute rounded-circle\"\n      style={{\n        background: `linear-gradient(135deg, ${i % 2 === 0 ? '#667eea' : '#764ba2'}, ${i % 2 === 0 ? '#764ba2' : '#667eea'})`,\n        width: `${Math.random() * 60 + 20}px`,\n        height: `${Math.random() * 60 + 20}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        opacity: 0.1,\n        zIndex: 0\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n        scale: [1, 1.2, 1],\n      }}\n      transition={{\n        duration: Math.random() * 20 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n        ease: \"easeInOut\"\n      }}\n    />\n  ));\n\n  return (\n    <div className=\"min-h-screen position-relative\" style={{\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      minHeight: '100vh'\n    }}>\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100 overflow-hidden\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%)',\n        }} />\n      </div>\n\n      <Container fluid className=\"position-relative py-4\" style={{ zIndex: 1 }}>\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Header */}\n          <motion.div variants={itemVariants} className=\"text-center mb-5\">\n            <motion.div\n              animate={{\n                rotate: [0, 360],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"d-inline-flex align-items-center justify-content-center mb-3\"\n              style={{\n                width: '80px',\n                height: '80px',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                borderRadius: '20px',\n                boxShadow: '0 20px 40px rgba(102, 126, 234, 0.3)'\n              }}\n            >\n              <User className=\"text-white\" size={40} />\n            </motion.div>\n            <h1 className=\"text-white fw-bold mb-2\" style={{ fontSize: '2.5rem' }}>\n              Welcome back, {user?.username}!\n            </h1>\n            <p className=\"text-white-50 fs-5 mb-4\">\n              Manage your printing jobs and explore xerox centers\n            </p>\n            \n            <div className=\"d-flex align-items-center justify-content-center gap-3 flex-wrap\">\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  size=\"lg\"\n                  className=\"px-5 py-3 rounded-4 border-0 fw-semibold\"\n                  style={{\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    backdropFilter: 'blur(20px)',\n                    color: '#fff',\n                    boxShadow: '0 10px 30px rgba(255, 255, 255, 0.1)'\n                  }}\n                >\n                  <Upload className=\"me-2\" size={20} />\n                  Upload Files\n                </Button>\n              </motion.div>\n              \n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  variant=\"outline-light\"\n                  size=\"lg\"\n                  onClick={fetchData}\n                  disabled={isRefreshing}\n                  className=\"px-4 py-3 rounded-4 fw-semibold\"\n                >\n                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />\n                  Refresh\n                </Button>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Navigation Tabs */}\n          <motion.div variants={itemVariants} className=\"mb-4\">\n            <Card className=\"border-0 shadow-lg\" style={{\n              background: 'rgba(255, 255, 255, 0.1)',\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px'\n            }}>\n              <Card.Body className=\"p-2\">\n                <Nav variant=\"pills\" className=\"justify-content-center flex-wrap\">\n                  {[\n                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },\n                    { key: 'jobs', label: 'My Jobs', icon: FileText },\n                    { key: 'centers', label: 'Xerox Centers', icon: Printer },\n                    { key: 'history', label: 'History', icon: History },\n                    { key: 'favorites', label: 'Favorites', icon: Heart },\n                    { key: 'analytics', label: 'Analytics', icon: PieChart },\n                    { key: 'settings', label: 'Settings', icon: Settings }\n                  ].map(tab => {\n                    const IconComponent = tab.icon;\n                    return (\n                      <Nav.Item key={tab.key} className=\"m-1\">\n                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                          <Nav.Link\n                            active={activeTab === tab.key}\n                            onClick={() => setActiveTab(tab.key)}\n                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center ${\n                              activeTab === tab.key\n                                ? 'text-primary'\n                                : 'text-white-50'\n                            }`}\n                            style={{\n                              background: activeTab === tab.key \n                                ? 'rgba(255, 255, 255, 0.9)' \n                                : 'transparent',\n                              border: 'none',\n                              transition: 'all 0.3s ease',\n                              boxShadow: activeTab === tab.key \n                                ? '0 10px 30px rgba(255, 255, 255, 0.2)' \n                                : 'none'\n                            }}\n                          >\n                            <IconComponent size={18} className=\"me-2\" />\n                            <span className=\"d-none d-md-inline\">{tab.label}</span>\n                          </Nav.Link>\n                        </motion.div>\n                      </Nav.Item>\n                    );\n                  })}\n                </Nav>\n              </Card.Body>\n            </Card>\n          </motion.div>\n\n          {/* Tab Content */}\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {activeTab === 'dashboard' && (\n              <Row className=\"g-4\">\n                {/* Statistics Cards */}\n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                      background: 'rgba(255, 255, 255, 0.1)',\n                      backdropFilter: 'blur(20px)',\n                      borderRadius: '20px'\n                    }}>\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '60px',\n                            height: '60px',\n                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                            borderRadius: '15px'\n                          }}>\n                            <FileText className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"text-white-50 mb-1\">Total Jobs</h6>\n                        <h2 className=\"fw-bold text-white\">{printJobs.length}</h2>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.1 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                      background: 'rgba(255, 255, 255, 0.1)',\n                      backdropFilter: 'blur(20px)',\n                      borderRadius: '20px'\n                    }}>\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '60px',\n                            height: '60px',\n                            background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',\n                            borderRadius: '15px'\n                          }}>\n                            <Clock className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"text-white-50 mb-1\">In Progress</h6>\n                        <h2 className=\"fw-bold text-white\">{inProgressJobs}</h2>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.2 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                      background: 'rgba(255, 255, 255, 0.1)',\n                      backdropFilter: 'blur(20px)',\n                      borderRadius: '20px'\n                    }}>\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '60px',\n                            height: '60px',\n                            background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',\n                            borderRadius: '15px'\n                          }}>\n                            <CheckCircle className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"text-white-50 mb-1\">Completed</h6>\n                        <h2 className=\"fw-bold text-white\">{completedJobs}</h2>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.3 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card className=\"h-100 border-0 shadow-lg\" style={{ \n                      background: 'rgba(255, 255, 255, 0.1)',\n                      backdropFilter: 'blur(20px)',\n                      borderRadius: '20px'\n                    }}>\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                            width: '60px',\n                            height: '60px',\n                            background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',\n                            borderRadius: '15px'\n                          }}>\n                            <DollarSign className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"text-white-50 mb-1\">Total Spent</h6>\n                        <h2 className=\"fw-bold text-white\">${totalSpent.toFixed(2)}</h2>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n\n                {/* Recent Jobs Table */}\n                <Col lg={12}>\n                  <Card className=\"border-0 shadow-lg\" style={{ \n                    background: 'rgba(255, 255, 255, 0.1)',\n                    backdropFilter: 'blur(20px)',\n                    borderRadius: '20px'\n                  }}>\n                    <Card.Header className=\"border-0 bg-transparent\">\n                      <div className=\"d-flex align-items-center justify-content-between\">\n                        <h4 className=\"fw-bold mb-0 text-white\">\n                          <Activity className=\"me-2\" size={20} />\n                          Recent Jobs\n                        </h4>\n                        <Badge bg=\"light\" text=\"dark\" className=\"px-3 py-2\">\n                          {printJobs.length} total\n                        </Badge>\n                      </div>\n                    </Card.Header>\n                    <Card.Body>\n                      {printJobs.length > 0 ? (\n                        <div className=\"table-responsive\">\n                          <Table className=\"table-dark table-hover\">\n                            <thead>\n                              <tr>\n                                <th>Job Number</th>\n                                <th>File Name</th>\n                                <th>Status</th>\n                                <th>Cost</th>\n                                <th>Center</th>\n                                <th>Actions</th>\n                              </tr>\n                            </thead>\n                            <tbody>\n                              {printJobs.slice(0, 10).map((job: PrintJob) => (\n                                <tr key={job.id}>\n                                  <td className=\"fw-semibold\">{job.jobNumber}</td>\n                                  <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>\n                                  <td>{getStatusBadge(job.status)}</td>\n                                  <td className=\"text-success fw-semibold\">\n                                    {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                                  </td>\n                                  <td>{job.xeroxCenterName}</td>\n                                  <td>\n                                    <div className=\"d-flex gap-1\">\n                                      <Button variant=\"outline-light\" size=\"sm\">\n                                        <Download size={14} />\n                                      </Button>\n                                      <Button variant=\"outline-light\" size=\"sm\">\n                                        <Eye size={14} />\n                                      </Button>\n                                      <Button variant=\"outline-light\" size=\"sm\">\n                                        <MessageCircle size={14} />\n                                      </Button>\n                                    </div>\n                                  </td>\n                                </tr>\n                              ))}\n                            </tbody>\n                          </Table>\n                        </div>\n                      ) : (\n                        <motion.div\n                          initial={{ opacity: 0, scale: 0.9 }}\n                          animate={{ opacity: 1, scale: 1 }}\n                          className=\"text-center py-5\"\n                        >\n                          <div className=\"mb-4\">\n                            <div className=\"d-inline-flex align-items-center justify-content-center\" style={{\n                              width: '80px',\n                              height: '80px',\n                              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                              borderRadius: '20px'\n                            }}>\n                              <Upload className=\"text-white\" size={40} />\n                            </div>\n                          </div>\n                          <h5 className=\"fw-semibold mb-2 text-white\">No jobs yet</h5>\n                          <p className=\"text-white-50 mb-4\">Upload your first file to get started!</p>\n                        </motion.div>\n                      )}\n                    </Card.Body>\n                  </Card>\n                </Col>\n              </Row>\n            )}\n\n            {/* Other tabs content can be added here */}\n            {activeTab !== 'dashboard' && (\n              <Card className=\"border-0 shadow-lg\" style={{ \n                background: 'rgba(255, 255, 255, 0.1)',\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px'\n              }}>\n                <Card.Body className=\"text-center py-5\">\n                  <h4 className=\"text-white mb-3\">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Tab</h4>\n                  <p className=\"text-white-50\">This tab is under development. Coming soon!</p>\n                </Card.Body>\n              </Card>\n            )}\n          </motion.div>\n        </motion.div>\n      </Container>\n    </div>\n  );\n};\n\nexport default SimpleAceternityStudentDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AACtF,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EACVC,QAAQ,EACRC,aAAa,EACbC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,OAAO,EAEPC,OAAO,EACPC,QAAQ,EACRC,IAAI,EACJC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,KAAK,QAIA,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,WAAW,EAAEC,cAAc,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0BjE,MAAMC,gCAA0C,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvD,MAAM;IAAEC;EAAK,CAAC,GAAGR,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAES,UAAU;IAAEC;EAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC;EAC9C,MAAM,CAACU,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACyC,SAAS,EAAEC,YAAY,CAAC,GAAG1C,QAAQ,CAAa,EAAE,CAAC;EAC1D,MAAM,CAAC2C,YAAY,EAAEC,eAAe,CAAC,GAAG5C,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC6C,YAAY,EAAEC,eAAe,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM+C,MAAM,GAAG;IACbC,OAAO,EAAEX,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7CY,SAAS,EAAEZ,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC/Ca,MAAM,EAAEb,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC5Cc,OAAO,EAAEd,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7Ce,MAAM,EAAEf,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC5CgB,OAAO,EAAEhB,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7CiB,UAAU,EAAEjB,UAAU,GAAG,SAAS,GAAG,SAAS;IAC9CkB,OAAO,EAAElB,UAAU,GAAG,SAAS,GAAG,SAAS;IAC3CmB,IAAI,EAAEnB,UAAU,GAAG,SAAS,GAAG,SAAS;IACxCoB,aAAa,EAAEpB,UAAU,GAAG,SAAS,GAAG;EAC1C,CAAC;EAEDpC,SAAS,CAAC,MAAM;IACdyD,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BZ,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAM,CAACgB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClEnC,WAAW,CAACoC,cAAc,CAAC,CAAC,EAC5BnC,cAAc,CAACoC,MAAM,CAAC,CAAC,CACxB,CAAC;MAEFzB,YAAY,CAACoB,iBAAiB,CAACM,IAAI,IAAI,EAAE,CAAC;MAC1CxB,eAAe,CAACmB,oBAAoB,CAACK,IAAI,IAAI,EAAE,CAAC;IAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C3B,YAAY,CAAC,EAAE,CAAC;MAChBE,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMyB,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAA0D,GAAG;MACjE,WAAW,EAAE;QAAEC,EAAE,EAAE,WAAW;QAAEC,IAAI,EAAE/D;MAAM,CAAC;MAC7C,aAAa,EAAE;QAAE8D,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE1D;MAAI,CAAC;MACxC,QAAQ,EAAE;QAAEyD,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE7D;MAAW,CAAC;MAC7C,WAAW,EAAE;QAAE4D,EAAE,EAAE,MAAM;QAAEC,IAAI,EAAE9D;MAAY,CAAC;MAC9C,YAAY,EAAE;QAAE6D,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAExD;MAAS,CAAC;MAC/C,WAAW,EAAE;QAAEuD,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE9D;MAAY,CAAC;MACjD,WAAW,EAAE;QAAE6D,EAAE,EAAE,SAAS;QAAEC,IAAI,EAAE9D;MAAY;IAClD,CAAC;IAED,MAAM+D,MAAM,GAAGH,YAAY,CAACD,MAAM,CAAC,IAAI;MAAEE,EAAE,EAAE,WAAW;MAAEC,IAAI,EAAE/D;IAAM,CAAC;IACvE,MAAMiE,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACE1C,OAAA,CAAC1B,KAAK;MAACmE,EAAE,EAAEE,MAAM,CAACF,EAAG;MAACI,SAAS,EAAC,2CAA2C;MAAAC,QAAA,gBACzE9C,OAAA,CAAC4C,aAAa;QAACG,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1BZ,MAAM;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,UAAU,GAAG5C,SAAS,CAAC6C,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAACC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E,MAAMC,cAAc,GAAGjD,SAAS,CAACkD,MAAM,CAACH,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAACI,QAAQ,CAACJ,GAAG,CAAChB,MAAM,CAAC,CAAC,CAACqB,MAAM;EACtH,MAAMC,aAAa,GAAGrD,SAAS,CAACkD,MAAM,CAACH,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACI,QAAQ,CAACJ,GAAG,CAAChB,MAAM,CAAC,CAAC,CAACqB,MAAM;EAErG,MAAME,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBN,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEM,CAAC,EAAE;IAAG,CAAC;IAC7BL,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVM,CAAC,EAAE,CAAC;MACJJ,UAAU,EAAE;QAAEC,QAAQ,EAAE;MAAI;IAC9B;EACF,CAAC;;EAED;EACA,MAAMI,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEb,MAAM,EAAE;EAAE,CAAC,EAAE,CAACc,CAAC,EAAEC,CAAC,kBACpD3E,OAAA,CAACvB,MAAM,CAACmG,GAAG;IAET/B,SAAS,EAAC,kCAAkC;IAC5CgC,KAAK,EAAE;MACLxD,UAAU,EAAE,2BAA2BsD,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,KAAKA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,SAAS,GAAG;MACrHG,KAAK,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACrCC,MAAM,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACtCE,IAAI,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC/BG,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC9BhB,OAAO,EAAE,GAAG;MACZoB,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAE;MACPC,CAAC,EAAE,CAAC,CAAC,EAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCV,CAAC,EAAE,CAAC,CAAC,EAAES,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCO,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;MAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAE;IACFtB,UAAU,EAAE;MACVC,QAAQ,EAAEY,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACjCS,MAAM,EAAEC,QAAQ;MAChBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE;IACR;EAAE,GAtBGjB,CAAC;IAAA3B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAuBP,CACF,CAAC;EAEF,oBACEnD,OAAA;IAAK6C,SAAS,EAAC,gCAAgC;IAACgC,KAAK,EAAE;MACrDxD,UAAU,EAAE,mDAAmD;MAC/DwE,SAAS,EAAE;IACb,CAAE;IAAA/C,QAAA,gBAEA9C,OAAA;MAAK6C,SAAS,EAAC,+CAA+C;MAAAC,QAAA,GAC3DyB,cAAc,eACfvE,OAAA;QAAK6C,SAAS,EAAC,+BAA+B;QAACgC,KAAK,EAAE;UACpDxD,UAAU,EAAE;QACd;MAAE;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENnD,OAAA,CAAC/B,SAAS;MAAC6H,KAAK;MAACjD,SAAS,EAAC,wBAAwB;MAACgC,KAAK,EAAE;QAAEO,MAAM,EAAE;MAAE,CAAE;MAAAtC,QAAA,eACvE9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;QACTmB,QAAQ,EAAEjC,iBAAkB;QAC5BkC,OAAO,EAAC,QAAQ;QAChBX,OAAO,EAAC,SAAS;QAAAvC,QAAA,gBAGjB9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;UAACmB,QAAQ,EAAE1B,YAAa;UAACxB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC9D9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;YACTS,OAAO,EAAE;cACPE,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;cAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACnB,CAAE;YACFtB,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXsB,MAAM,EAAEC,QAAQ;cAChBE,IAAI,EAAE;YACR,CAAE;YACF/C,SAAS,EAAC,8DAA8D;YACxEgC,KAAK,EAAE;cACLC,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACd5D,UAAU,EAAE,mDAAmD;cAC/D4E,YAAY,EAAE,MAAM;cACpBC,SAAS,EAAE;YACb,CAAE;YAAApD,QAAA,eAEF9C,OAAA,CAACV,IAAI;cAACuD,SAAS,EAAC,YAAY;cAACE,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACbnD,OAAA;YAAI6C,SAAS,EAAC,yBAAyB;YAACgC,KAAK,EAAE;cAAEsB,QAAQ,EAAE;YAAS,CAAE;YAAArD,QAAA,GAAC,gBACvD,EAAC3C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,QAAQ,EAAC,GAChC;UAAA;YAAApD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLnD,OAAA;YAAG6C,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJnD,OAAA;YAAK6C,SAAS,EAAC,kEAAkE;YAAAC,QAAA,gBAC/E9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;cAACyB,UAAU,EAAE;gBAAEb,KAAK,EAAE;cAAK,CAAE;cAACc,QAAQ,EAAE;gBAAEd,KAAK,EAAE;cAAK,CAAE;cAAA1C,QAAA,eACjE9C,OAAA,CAAC3B,MAAM;gBACL0E,IAAI,EAAC,IAAI;gBACTF,SAAS,EAAC,0CAA0C;gBACpDgC,KAAK,EAAE;kBACLxD,UAAU,EAAE,0BAA0B;kBACtCkF,cAAc,EAAE,YAAY;kBAC5BC,KAAK,EAAE,MAAM;kBACbN,SAAS,EAAE;gBACb,CAAE;gBAAApD,QAAA,gBAEF9C,OAAA,CAACf,MAAM;kBAAC4D,SAAS,EAAC,MAAM;kBAACE,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEvC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEbnD,OAAA,CAACvB,MAAM,CAACmG,GAAG;cAACyB,UAAU,EAAE;gBAAEb,KAAK,EAAE;cAAK,CAAE;cAACc,QAAQ,EAAE;gBAAEd,KAAK,EAAE;cAAK,CAAE;cAAA1C,QAAA,eACjE9C,OAAA,CAAC3B,MAAM;gBACLoI,OAAO,EAAC,eAAe;gBACvB1D,IAAI,EAAC,IAAI;gBACT2D,OAAO,EAAEjF,SAAU;gBACnBkF,QAAQ,EAAE/F,YAAa;gBACvBiC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAE3C9C,OAAA,CAACR,SAAS;kBAACqD,SAAS,EAAE,QAAQjC,YAAY,GAAG,MAAM,GAAG,EAAE,EAAG;kBAACmC,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbnD,OAAA,CAACvB,MAAM,CAACmG,GAAG;UAACmB,QAAQ,EAAE1B,YAAa;UAACxB,SAAS,EAAC,MAAM;UAAAC,QAAA,eAClD9C,OAAA,CAAC5B,IAAI;YAACyE,SAAS,EAAC,oBAAoB;YAACgC,KAAK,EAAE;cAC1CxD,UAAU,EAAE,0BAA0B;cACtCkF,cAAc,EAAE,YAAY;cAC5BN,YAAY,EAAE;YAChB,CAAE;YAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;cAAC/D,SAAS,EAAC,KAAK;cAAAC,QAAA,eACxB9C,OAAA,CAACzB,GAAG;gBAACkI,OAAO,EAAC,OAAO;gBAAC5D,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAC9D,CACC;kBAAE+D,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAEpE,IAAI,EAAEnD;gBAAU,CAAC,EACzD;kBAAEsH,GAAG,EAAE,MAAM;kBAAEC,KAAK,EAAE,SAAS;kBAAEpE,IAAI,EAAEhE;gBAAS,CAAC,EACjD;kBAAEmI,GAAG,EAAE,SAAS;kBAAEC,KAAK,EAAE,eAAe;kBAAEpE,IAAI,EAAEvD;gBAAQ,CAAC,EACzD;kBAAE0H,GAAG,EAAE,SAAS;kBAAEC,KAAK,EAAE,SAAS;kBAAEpE,IAAI,EAAEtD;gBAAQ,CAAC,EACnD;kBAAEyH,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAEpE,IAAI,EAAEhD;gBAAM,CAAC,EACrD;kBAAEmH,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAEpE,IAAI,EAAEjD;gBAAS,CAAC,EACxD;kBAAEoH,GAAG,EAAE,UAAU;kBAAEC,KAAK,EAAE,UAAU;kBAAEpE,IAAI,EAAErD;gBAAS,CAAC,CACvD,CAAC0H,GAAG,CAACC,GAAG,IAAI;kBACX,MAAMpE,aAAa,GAAGoE,GAAG,CAACtE,IAAI;kBAC9B,oBACE1C,OAAA,CAACzB,GAAG,CAAC0I,IAAI;oBAAepE,SAAS,EAAC,KAAK;oBAAAC,QAAA,eACrC9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;sBAACyB,UAAU,EAAE;wBAAEb,KAAK,EAAE;sBAAK,CAAE;sBAACc,QAAQ,EAAE;wBAAEd,KAAK,EAAE;sBAAK,CAAE;sBAAA1C,QAAA,eACjE9C,OAAA,CAACzB,GAAG,CAAC2I,IAAI;wBACPC,MAAM,EAAE7G,SAAS,KAAK0G,GAAG,CAACH,GAAI;wBAC9BH,OAAO,EAAEA,CAAA,KAAMnG,YAAY,CAACyG,GAAG,CAACH,GAAG,CAAE;wBACrChE,SAAS,EAAE,6DACTvC,SAAS,KAAK0G,GAAG,CAACH,GAAG,GACjB,cAAc,GACd,eAAe,EAClB;wBACHhC,KAAK,EAAE;0BACLxD,UAAU,EAAEf,SAAS,KAAK0G,GAAG,CAACH,GAAG,GAC7B,0BAA0B,GAC1B,aAAa;0BACjBO,MAAM,EAAE,MAAM;0BACdlD,UAAU,EAAE,eAAe;0BAC3BgC,SAAS,EAAE5F,SAAS,KAAK0G,GAAG,CAACH,GAAG,GAC5B,sCAAsC,GACtC;wBACN,CAAE;wBAAA/D,QAAA,gBAEF9C,OAAA,CAAC4C,aAAa;0BAACG,IAAI,EAAE,EAAG;0BAACF,SAAS,EAAC;wBAAM;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5CnD,OAAA;0BAAM6C,SAAS,EAAC,oBAAoB;0BAAAC,QAAA,EAAEkE,GAAG,CAACF;wBAAK;0BAAA9D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GAxBA6D,GAAG,CAACH,GAAG;oBAAA7D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBZ,CAAC;gBAEf,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGbnD,OAAA,CAACvB,MAAM,CAACmG,GAAG;UAEToB,OAAO,EAAE;YAAEhC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/Be,OAAO,EAAE;YAAErB,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAC9BJ,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAArB,QAAA,GAE7BxC,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAC9B,GAAG;YAAC2E,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAElB9C,OAAA,CAAC7B,GAAG;cAACkJ,EAAE,EAAE,CAAE;cAAAvE,QAAA,eACT9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;gBACToB,OAAO,EAAE;kBAAEhC,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAI,CAAE;gBACpCH,OAAO,EAAE;kBAAErB,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAE,CAAE;gBAClCtB,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAC9BkC,UAAU,EAAE;kBAAE/B,CAAC,EAAE,CAAC,CAAC;kBAAEkB,KAAK,EAAE;gBAAK,CAAE;gBAAA1C,QAAA,eAEnC9C,OAAA,CAAC5B,IAAI;kBAACyE,SAAS,EAAC,0BAA0B;kBAACgC,KAAK,EAAE;oBAChDxD,UAAU,EAAE,0BAA0B;oBACtCkF,cAAc,EAAE,YAAY;oBAC5BN,YAAY,EAAE;kBAChB,CAAE;kBAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;oBAAC/D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBACpC9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBAAK6C,SAAS,EAAC,yDAAyD;wBAACgC,KAAK,EAAE;0BAC9EC,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACd5D,UAAU,EAAE,mDAAmD;0BAC/D4E,YAAY,EAAE;wBAChB,CAAE;wBAAAnD,QAAA,eACA9C,OAAA,CAACtB,QAAQ;0BAACmE,SAAS,EAAC,YAAY;0BAACE,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAClDnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAEtC,SAAS,CAACoD;oBAAM;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAAC7B,GAAG;cAACkJ,EAAE,EAAE,CAAE;cAAAvE,QAAA,eACT9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;gBACToB,OAAO,EAAE;kBAAEhC,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAI,CAAE;gBACpCH,OAAO,EAAE;kBAAErB,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAE,CAAE;gBAClCtB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmD,KAAK,EAAE;gBAAI,CAAE;gBAC1CjB,UAAU,EAAE;kBAAE/B,CAAC,EAAE,CAAC,CAAC;kBAAEkB,KAAK,EAAE;gBAAK,CAAE;gBAAA1C,QAAA,eAEnC9C,OAAA,CAAC5B,IAAI;kBAACyE,SAAS,EAAC,0BAA0B;kBAACgC,KAAK,EAAE;oBAChDxD,UAAU,EAAE,0BAA0B;oBACtCkF,cAAc,EAAE,YAAY;oBAC5BN,YAAY,EAAE;kBAChB,CAAE;kBAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;oBAAC/D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBACpC9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBAAK6C,SAAS,EAAC,yDAAyD;wBAACgC,KAAK,EAAE;0BAC9EC,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACd5D,UAAU,EAAE,mDAAmD;0BAC/D4E,YAAY,EAAE;wBAChB,CAAE;wBAAAnD,QAAA,eACA9C,OAAA,CAACrB,KAAK;0BAACkE,SAAS,EAAC,YAAY;0BAACE,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAEW;oBAAc;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAAC7B,GAAG;cAACkJ,EAAE,EAAE,CAAE;cAAAvE,QAAA,eACT9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;gBACToB,OAAO,EAAE;kBAAEhC,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAI,CAAE;gBACpCH,OAAO,EAAE;kBAAErB,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAE,CAAE;gBAClCtB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmD,KAAK,EAAE;gBAAI,CAAE;gBAC1CjB,UAAU,EAAE;kBAAE/B,CAAC,EAAE,CAAC,CAAC;kBAAEkB,KAAK,EAAE;gBAAK,CAAE;gBAAA1C,QAAA,eAEnC9C,OAAA,CAAC5B,IAAI;kBAACyE,SAAS,EAAC,0BAA0B;kBAACgC,KAAK,EAAE;oBAChDxD,UAAU,EAAE,0BAA0B;oBACtCkF,cAAc,EAAE,YAAY;oBAC5BN,YAAY,EAAE;kBAChB,CAAE;kBAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;oBAAC/D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBACpC9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBAAK6C,SAAS,EAAC,yDAAyD;wBAACgC,KAAK,EAAE;0BAC9EC,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACd5D,UAAU,EAAE,mDAAmD;0BAC/D4E,YAAY,EAAE;wBAChB,CAAE;wBAAAnD,QAAA,eACA9C,OAAA,CAACpB,WAAW;0BAACiE,SAAS,EAAC,YAAY;0BAACE,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjDnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAEe;oBAAa;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENnD,OAAA,CAAC7B,GAAG;cAACkJ,EAAE,EAAE,CAAE;cAAAvE,QAAA,eACT9C,OAAA,CAACvB,MAAM,CAACmG,GAAG;gBACToB,OAAO,EAAE;kBAAEhC,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAI,CAAE;gBACpCH,OAAO,EAAE;kBAAErB,OAAO,EAAE,CAAC;kBAAEwB,KAAK,EAAE;gBAAE,CAAE;gBAClCtB,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmD,KAAK,EAAE;gBAAI,CAAE;gBAC1CjB,UAAU,EAAE;kBAAE/B,CAAC,EAAE,CAAC,CAAC;kBAAEkB,KAAK,EAAE;gBAAK,CAAE;gBAAA1C,QAAA,eAEnC9C,OAAA,CAAC5B,IAAI;kBAACyE,SAAS,EAAC,0BAA0B;kBAACgC,KAAK,EAAE;oBAChDxD,UAAU,EAAE,0BAA0B;oBACtCkF,cAAc,EAAE,YAAY;oBAC5BN,YAAY,EAAE;kBAChB,CAAE;kBAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;oBAAC/D,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBACpC9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBAAK6C,SAAS,EAAC,yDAAyD;wBAACgC,KAAK,EAAE;0BAC9EC,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACd5D,UAAU,EAAE,mDAAmD;0BAC/D4E,YAAY,EAAE;wBAChB,CAAE;wBAAAnD,QAAA,eACA9C,OAAA,CAACnB,UAAU;0BAACgE,SAAS,EAAC,YAAY;0BAACE,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACnDnD,OAAA;sBAAI6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,GAAC,GAAC,EAACM,UAAU,CAACmE,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNnD,OAAA,CAAC7B,GAAG;cAACqJ,EAAE,EAAE,EAAG;cAAA1E,QAAA,eACV9C,OAAA,CAAC5B,IAAI;gBAACyE,SAAS,EAAC,oBAAoB;gBAACgC,KAAK,EAAE;kBAC1CxD,UAAU,EAAE,0BAA0B;kBACtCkF,cAAc,EAAE,YAAY;kBAC5BN,YAAY,EAAE;gBAChB,CAAE;gBAAAnD,QAAA,gBACA9C,OAAA,CAAC5B,IAAI,CAACqJ,MAAM;kBAAC5E,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eAC9C9C,OAAA;oBAAK6C,SAAS,EAAC,mDAAmD;oBAAAC,QAAA,gBAChE9C,OAAA;sBAAI6C,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,gBACrC9C,OAAA,CAACd,QAAQ;wBAAC2D,SAAS,EAAC,MAAM;wBAACE,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAEzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLnD,OAAA,CAAC1B,KAAK;sBAACmE,EAAE,EAAC,OAAO;sBAAClB,IAAI,EAAC,MAAM;sBAACsB,SAAS,EAAC,WAAW;sBAAAC,QAAA,GAChDtC,SAAS,CAACoD,MAAM,EAAC,QACpB;oBAAA;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACdnD,OAAA,CAAC5B,IAAI,CAACwI,IAAI;kBAAA9D,QAAA,EACPtC,SAAS,CAACoD,MAAM,GAAG,CAAC,gBACnB5D,OAAA;oBAAK6C,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,eAC/B9C,OAAA,CAACxB,KAAK;sBAACqE,SAAS,EAAC,wBAAwB;sBAAAC,QAAA,gBACvC9C,OAAA;wBAAA8C,QAAA,eACE9C,OAAA;0BAAA8C,QAAA,gBACE9C,OAAA;4BAAA8C,QAAA,EAAI;0BAAU;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBnD,OAAA;4BAAA8C,QAAA,EAAI;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAClBnD,OAAA;4BAAA8C,QAAA,EAAI;0BAAM;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACfnD,OAAA;4BAAA8C,QAAA,EAAI;0BAAI;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbnD,OAAA;4BAAA8C,QAAA,EAAI;0BAAM;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACfnD,OAAA;4BAAA8C,QAAA,EAAI;0BAAO;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRnD,OAAA;wBAAA8C,QAAA,EACGtC,SAAS,CAACkH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACX,GAAG,CAAExD,GAAa,iBACxCvD,OAAA;0BAAA8C,QAAA,gBACE9C,OAAA;4BAAI6C,SAAS,EAAC,aAAa;4BAAAC,QAAA,EAAES,GAAG,CAACoE;0BAAS;4BAAA3E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChDnD,OAAA;4BAAA8C,QAAA,EAAKS,GAAG,CAACP,QAAQ,CAACY,MAAM,GAAG,EAAE,GAAGL,GAAG,CAACP,QAAQ,CAAC0E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGnE,GAAG,CAACP;0BAAQ;4BAAAA,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtFnD,OAAA;4BAAA8C,QAAA,EAAKR,cAAc,CAACiB,GAAG,CAAChB,MAAM;0BAAC;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrCnD,OAAA;4BAAI6C,SAAS,EAAC,0BAA0B;4BAAAC,QAAA,EACrCS,GAAG,CAACC,IAAI,GAAG,IAAID,GAAG,CAACC,IAAI,CAAC+D,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;0BAAG;4BAAAvE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC,CAAC,eACLnD,OAAA;4BAAA8C,QAAA,EAAKS,GAAG,CAACqE;0BAAe;4BAAA5E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC9BnD,OAAA;4BAAA8C,QAAA,eACE9C,OAAA;8BAAK6C,SAAS,EAAC,cAAc;8BAAAC,QAAA,gBAC3B9C,OAAA,CAAC3B,MAAM;gCAACoI,OAAO,EAAC,eAAe;gCAAC1D,IAAI,EAAC,IAAI;gCAAAD,QAAA,eACvC9C,OAAA,CAAClB,QAAQ;kCAACiE,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAChB,CAAC,eACTnD,OAAA,CAAC3B,MAAM;gCAACoI,OAAO,EAAC,eAAe;gCAAC1D,IAAI,EAAC,IAAI;gCAAAD,QAAA,eACvC9C,OAAA,CAAChB,GAAG;kCAAC+D,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACX,CAAC,eACTnD,OAAA,CAAC3B,MAAM;gCAACoI,OAAO,EAAC,eAAe;gCAAC1D,IAAI,EAAC,IAAI;gCAAAD,QAAA,eACvC9C,OAAA,CAACjB,aAAa;kCAACgE,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrB,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA,GApBEI,GAAG,CAACsE,EAAE;0BAAA7E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAqBX,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENnD,OAAA,CAACvB,MAAM,CAACmG,GAAG;oBACToB,OAAO,EAAE;sBAAEhC,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAI,CAAE;oBACpCH,OAAO,EAAE;sBAAErB,OAAO,EAAE,CAAC;sBAAEwB,KAAK,EAAE;oBAAE,CAAE;oBAClC3C,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAE5B9C,OAAA;sBAAK6C,SAAS,EAAC,MAAM;sBAAAC,QAAA,eACnB9C,OAAA;wBAAK6C,SAAS,EAAC,yDAAyD;wBAACgC,KAAK,EAAE;0BAC9EC,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACd5D,UAAU,EAAE,mDAAmD;0BAC/D4E,YAAY,EAAE;wBAChB,CAAE;wBAAAnD,QAAA,eACA9C,OAAA,CAACf,MAAM;0BAAC4D,SAAS,EAAC,YAAY;0BAACE,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNnD,OAAA;sBAAI6C,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5DnD,OAAA;sBAAG6C,SAAS,EAAC,oBAAoB;sBAAAC,QAAA,EAAC;oBAAsC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA7C,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAC5B,IAAI;YAACyE,SAAS,EAAC,oBAAoB;YAACgC,KAAK,EAAE;cAC1CxD,UAAU,EAAE,0BAA0B;cACtCkF,cAAc,EAAE,YAAY;cAC5BN,YAAY,EAAE;YAChB,CAAE;YAAAnD,QAAA,eACA9C,OAAA,CAAC5B,IAAI,CAACwI,IAAI;cAAC/D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBACrC9C,OAAA;gBAAI6C,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,GAAExC,SAAS,CAACwH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGzH,SAAS,CAACoH,KAAK,CAAC,CAAC,CAAC,EAAC,MAAI;cAAA;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjGnD,OAAA;gBAAG6C,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAA2C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA,GAlOI7C,SAAS;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmOJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAACjD,EAAA,CA1eID,gCAA0C;EAAA,QAC7BN,OAAO,EACYC,QAAQ;AAAA;AAAAoI,EAAA,GAFxC/H,gCAA0C;AA4ehD,eAAeA,gCAAgC;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}