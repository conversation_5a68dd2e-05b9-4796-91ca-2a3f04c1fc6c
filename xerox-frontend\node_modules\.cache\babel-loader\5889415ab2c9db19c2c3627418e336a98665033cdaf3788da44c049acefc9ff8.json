{"ast": null, "code": "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n      placement: placement\n    })) : offset,\n    skidding = _ref[0],\n    distance = _ref[1];\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\nfunction offset(_ref2) {\n  var state = _ref2.state,\n    options = _ref2.options,\n    name = _ref2.name;\n  var _options$offset = options.offset,\n    offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n    x = _data$state$placement.x,\n    y = _data$state$placement.y;\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "map": {"version": 3, "names": ["getBasePlacement", "top", "left", "right", "placements", "distanceAndSkiddingToXY", "placement", "rects", "offset", "basePlacement", "invertDistance", "indexOf", "_ref", "Object", "assign", "skidding", "distance", "x", "y", "_ref2", "state", "options", "name", "_options$offset", "data", "reduce", "acc", "_data$state$placement", "modifiersData", "popperOffsets", "enabled", "phase", "requires", "fn"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@popperjs/core/lib/modifiers/offset.js"], "sourcesContent": ["import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,UAAU,QAAQ,aAAa,CAAC,CAAC;;AAE5D,OAAO,SAASC,uBAAuBA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAE;EAChE,IAAIC,aAAa,GAAGT,gBAAgB,CAACM,SAAS,CAAC;EAC/C,IAAII,cAAc,GAAG,CAACR,IAAI,EAAED,GAAG,CAAC,CAACU,OAAO,CAACF,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAErE,IAAIG,IAAI,GAAG,OAAOJ,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACK,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEP,KAAK,EAAE;MACxED,SAAS,EAAEA;IACb,CAAC,CAAC,CAAC,GAAGE,MAAM;IACRO,QAAQ,GAAGH,IAAI,CAAC,CAAC,CAAC;IAClBI,QAAQ,GAAGJ,IAAI,CAAC,CAAC,CAAC;EAEtBG,QAAQ,GAAGA,QAAQ,IAAI,CAAC;EACxBC,QAAQ,GAAG,CAACA,QAAQ,IAAI,CAAC,IAAIN,cAAc;EAC3C,OAAO,CAACR,IAAI,EAAEC,KAAK,CAAC,CAACQ,OAAO,CAACF,aAAa,CAAC,IAAI,CAAC,GAAG;IACjDQ,CAAC,EAAED,QAAQ;IACXE,CAAC,EAAEH;EACL,CAAC,GAAG;IACFE,CAAC,EAAEF,QAAQ;IACXG,CAAC,EAAEF;EACL,CAAC;AACH;AAEA,SAASR,MAAMA,CAACW,KAAK,EAAE;EACrB,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACnBC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,IAAI,GAAGH,KAAK,CAACG,IAAI;EACrB,IAAIC,eAAe,GAAGF,OAAO,CAACb,MAAM;IAChCA,MAAM,GAAGe,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,eAAe;EAClE,IAAIC,IAAI,GAAGpB,UAAU,CAACqB,MAAM,CAAC,UAAUC,GAAG,EAAEpB,SAAS,EAAE;IACrDoB,GAAG,CAACpB,SAAS,CAAC,GAAGD,uBAAuB,CAACC,SAAS,EAAEc,KAAK,CAACb,KAAK,EAAEC,MAAM,CAAC;IACxE,OAAOkB,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,IAAIC,qBAAqB,GAAGH,IAAI,CAACJ,KAAK,CAACd,SAAS,CAAC;IAC7CW,CAAC,GAAGU,qBAAqB,CAACV,CAAC;IAC3BC,CAAC,GAAGS,qBAAqB,CAACT,CAAC;EAE/B,IAAIE,KAAK,CAACQ,aAAa,CAACC,aAAa,IAAI,IAAI,EAAE;IAC7CT,KAAK,CAACQ,aAAa,CAACC,aAAa,CAACZ,CAAC,IAAIA,CAAC;IACxCG,KAAK,CAACQ,aAAa,CAACC,aAAa,CAACX,CAAC,IAAIA,CAAC;EAC1C;EAEAE,KAAK,CAACQ,aAAa,CAACN,IAAI,CAAC,GAAGE,IAAI;AAClC,CAAC,CAAC;;AAGF,eAAe;EACbF,IAAI,EAAE,QAAQ;EACdQ,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,MAAM;EACbC,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC3BC,EAAE,EAAEzB;AACN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}