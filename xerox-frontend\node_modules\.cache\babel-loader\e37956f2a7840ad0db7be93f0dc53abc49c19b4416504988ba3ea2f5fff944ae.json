{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { resolveElements } from '../../utils/resolve-elements.mjs';\nfunction setupGesture(elementOrSelector, options) {\n  const elements = resolveElements(elementOrSelector);\n  const gestureAbortController = new AbortController();\n  const eventOptions = _objectSpread(_objectSpread({\n    passive: true\n  }, options), {}, {\n    signal: gestureAbortController.signal\n  });\n  const cancel = () => gestureAbortController.abort();\n  return [elements, eventOptions, cancel];\n}\nexport { setupGesture };", "map": {"version": 3, "names": ["resolveElements", "setupGesture", "elementOrSelector", "options", "elements", "gestureAbortController", "AbortController", "eventOptions", "_objectSpread", "passive", "signal", "cancel", "abort"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs"], "sourcesContent": ["import { resolveElements } from '../../utils/resolve-elements.mjs';\n\nfunction setupGesture(elementOrSelector, options) {\n    const elements = resolveElements(elementOrSelector);\n    const gestureAbortController = new AbortController();\n    const eventOptions = {\n        passive: true,\n        ...options,\n        signal: gestureAbortController.signal,\n    };\n    const cancel = () => gestureAbortController.abort();\n    return [elements, eventOptions, cancel];\n}\n\nexport { setupGesture };\n"], "mappings": ";AAAA,SAASA,eAAe,QAAQ,kCAAkC;AAElE,SAASC,YAAYA,CAACC,iBAAiB,EAAEC,OAAO,EAAE;EAC9C,MAAMC,QAAQ,GAAGJ,eAAe,CAACE,iBAAiB,CAAC;EACnD,MAAMG,sBAAsB,GAAG,IAAIC,eAAe,CAAC,CAAC;EACpD,MAAMC,YAAY,GAAAC,aAAA,CAAAA,aAAA;IACdC,OAAO,EAAE;EAAI,GACVN,OAAO;IACVO,MAAM,EAAEL,sBAAsB,CAACK;EAAM,EACxC;EACD,MAAMC,MAAM,GAAGA,CAAA,KAAMN,sBAAsB,CAACO,KAAK,CAAC,CAAC;EACnD,OAAO,CAACR,QAAQ,EAAEG,YAAY,EAAEI,MAAM,CAAC;AAC3C;AAEA,SAASV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}