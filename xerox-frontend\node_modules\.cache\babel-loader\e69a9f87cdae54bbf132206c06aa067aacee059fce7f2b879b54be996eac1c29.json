{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Xerox Module\\\\xerox-frontend\\\\src\\\\components\\\\ui\\\\SimpleAceternityXeroxDashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { FileText, Clock, CheckCircle, DollarSign, MessageCircle, Eye, Activity, Users, Star, Settings, Building, BarChart3, RefreshCw, PieChart, TrendingUp, Moon, Sun, Play, Truck } from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { printJobApi } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleAceternityXeroxDashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    isDarkMode,\n    toggleTheme\n  } = useTheme();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [jobs, setJobs] = useState([]);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF',\n    // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151',\n    // Gray\n    accent: isDarkMode ? '#10B981' : '#059669',\n    // Emerald\n    warning: isDarkMode ? '#F59E0B' : '#D97706',\n    // Amber\n    danger: isDarkMode ? '#EF4444' : '#DC2626',\n    // Red\n    success: isDarkMode ? '#10B981' : '#059669',\n    // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data || []);\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n  const getStatusBadge = status => {\n    const statusConfig = {\n      'Requested': {\n        bg: colors.secondary + '20',\n        color: colors.secondary,\n        icon: Clock\n      },\n      'UnderReview': {\n        bg: colors.primary + '20',\n        color: colors.primary,\n        icon: Eye\n      },\n      'Quoted': {\n        bg: colors.warning + '20',\n        color: colors.warning,\n        icon: DollarSign\n      },\n      'Confirmed': {\n        bg: colors.accent + '20',\n        color: colors.accent,\n        icon: CheckCircle\n      },\n      'InProgress': {\n        bg: colors.primary + '20',\n        color: colors.primary,\n        icon: Activity\n      },\n      'Completed': {\n        bg: colors.success + '20',\n        color: colors.success,\n        icon: CheckCircle\n      },\n      'Delivered': {\n        bg: colors.success + '20',\n        color: colors.success,\n        icon: Truck\n      }\n    };\n    const config = statusConfig[status] || {\n      bg: colors.secondary + '20',\n      color: colors.secondary,\n      icon: Clock\n    };\n    const IconComponent = config.icon;\n    return /*#__PURE__*/_jsxDEV(Badge, {\n      className: \"d-flex align-items-center gap-1 px-3 py-2\",\n      style: {\n        background: config.bg,\n        color: config.color,\n        border: 'none'\n      },\n      children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n        size: 14\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), status]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this);\n  };\n  const handleStatusUpdate = async (jobId, newStatus) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n\n  // Calculate statistics\n  const totalJobs = jobs.length;\n  const pendingJobs = jobs.filter(job => ['Requested', 'UnderReview'].includes(job.status)).length;\n  const inProgressJobs = jobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length;\n  const completedJobs = jobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const totalRevenue = jobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n  const floatingShapes = Array.from({\n    length: 6\n  }, (_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"position-absolute rounded-circle\",\n    style: {\n      background: `linear-gradient(135deg, ${colors.primary}15, ${colors.accent}15)`,\n      width: `${Math.random() * 60 + 20}px`,\n      height: `${Math.random() * 60 + 20}px`,\n      left: `${Math.random() * 100}%`,\n      top: `${Math.random() * 100}%`,\n      opacity: 0.3,\n      zIndex: 0\n    },\n    animate: {\n      x: [0, Math.random() * 100 - 50],\n      y: [0, Math.random() * 100 - 50],\n      rotate: [0, 360],\n      scale: [1, 1.2, 1]\n    },\n    transition: {\n      duration: Math.random() * 20 + 10,\n      repeat: Infinity,\n      repeatType: \"reverse\",\n      ease: \"easeInOut\"\n    }\n  }, i, false, {\n    fileName: _jsxFileName,\n    lineNumber: 134,\n    columnNumber: 5\n  }, this));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen position-relative\",\n    style: {\n      background: isDarkMode ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)' : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-absolute w-100 h-100 overflow-hidden\",\n      children: [floatingShapes, /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"position-absolute w-100 h-100\",\n        style: {\n          background: isDarkMode ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)' : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n      onClick: toggleTheme,\n      className: \"position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center\",\n      style: {\n        width: '50px',\n        height: '50px',\n        background: colors.surface,\n        boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',\n        color: colors.text,\n        zIndex: 1000\n      },\n      whileHover: {\n        scale: 1.1\n      },\n      whileTap: {\n        scale: 0.9\n      },\n      children: isDarkMode ? /*#__PURE__*/_jsxDEV(Sun, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 23\n      }, this) : /*#__PURE__*/_jsxDEV(Moon, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 43\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: \"position-relative py-4\",\n      style: {\n        zIndex: 1\n      },\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.6\n        },\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5\n          },\n          className: \"text-center mb-5\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            animate: {\n              rotate: [0, 360],\n              scale: [1, 1.1, 1]\n            },\n            transition: {\n              duration: 4,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            },\n            className: \"d-inline-flex align-items-center justify-content-center mb-3\",\n            style: {\n              width: '80px',\n              height: '80px',\n              background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n              borderRadius: '20px',\n              boxShadow: `0 20px 40px ${colors.primary}30`\n            },\n            children: /*#__PURE__*/_jsxDEV(Building, {\n              className: \"text-white\",\n              size: 40\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"fw-bold mb-2\",\n            style: {\n              fontSize: '2.5rem',\n              color: colors.text\n            },\n            children: \"Xerox Center Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"fs-5 mb-4\",\n            style: {\n              color: colors.textSecondary\n            },\n            children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"fw-semibold\",\n              children: user === null || user === void 0 ? void 0 : user.username\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 29\n            }, this), \"! Manage your printing business\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex align-items-center justify-content-center gap-3 flex-wrap\",\n            children: [/*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"lg\",\n                onClick: fetchJobs,\n                disabled: isRefreshing,\n                className: \"px-4 py-3 rounded-4 fw-semibold border-0\",\n                style: {\n                  background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                  color: '#fff'\n                },\n                children: [/*#__PURE__*/_jsxDEV(RefreshCw, {\n                  className: `me-2 ${isRefreshing ? 'spin' : ''}`,\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), \"Refresh\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Badge, {\n              className: \"px-3 py-2 fs-6\",\n              style: {\n                background: colors.surface,\n                color: colors.text,\n                boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.2)' : '0 4px 20px rgba(0,0,0,0.1)'\n              },\n              children: [\"Last updated: \", new Date().toLocaleTimeString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.5,\n            delay: 0.1\n          },\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"border-0 shadow-lg\",\n            style: {\n              background: colors.surface,\n              backdropFilter: 'blur(20px)',\n              borderRadius: '20px',\n              boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"p-2\",\n              children: /*#__PURE__*/_jsxDEV(Nav, {\n                variant: \"pills\",\n                className: \"justify-content-center flex-wrap\",\n                children: [{\n                  key: 'dashboard',\n                  label: 'Dashboard',\n                  icon: BarChart3\n                }, {\n                  key: 'jobs',\n                  label: 'Job Queue',\n                  icon: FileText\n                }, {\n                  key: 'analytics',\n                  label: 'Analytics',\n                  icon: PieChart\n                }, {\n                  key: 'customers',\n                  label: 'Customers',\n                  icon: Users\n                }, {\n                  key: 'settings',\n                  label: 'Settings',\n                  icon: Settings\n                }].map(tab => {\n                  const IconComponent = tab.icon;\n                  return /*#__PURE__*/_jsxDEV(Nav.Item, {\n                    className: \"m-1\",\n                    children: /*#__PURE__*/_jsxDEV(motion.div, {\n                      whileHover: {\n                        scale: 1.05\n                      },\n                      whileTap: {\n                        scale: 0.95\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Nav.Link, {\n                        active: activeTab === tab.key,\n                        onClick: () => setActiveTab(tab.key),\n                        className: `px-4 py-3 rounded-3 fw-semibold d-flex align-items-center`,\n                        style: {\n                          background: activeTab === tab.key ? `linear-gradient(135deg, ${colors.primary}, ${colors.accent})` : 'transparent',\n                          border: 'none',\n                          transition: 'all 0.3s ease',\n                          color: activeTab === tab.key ? '#fff' : colors.textSecondary,\n                          boxShadow: activeTab === tab.key ? `0 10px 30px ${colors.primary}20` : 'none'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 18,\n                          className: \"me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 315,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"d-none d-md-inline\",\n                          children: tab.label\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 316,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 298,\n                      columnNumber: 25\n                    }, this)\n                  }, tab.key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 23\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: [activeTab === 'dashboard' && /*#__PURE__*/_jsxDEV(Row, {\n            className: \"g-4\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: colors.surface,\n                    borderRadius: '20px',\n                    boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FileText, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 363,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"mb-1\",\n                      style: {\n                        color: colors.textSecondary\n                      },\n                      children: \"Total Jobs\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 366,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: totalJobs\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 367,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: colors.success\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                        size: 12,\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 369,\n                        columnNumber: 27\n                      }, this), \"+12% vs last month\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 368,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.1\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: colors.surface,\n                    borderRadius: '20px',\n                    boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: `linear-gradient(135deg, ${colors.warning}, #F59E0B)`,\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(Clock, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 403,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 394,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 393,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"mb-1\",\n                      style: {\n                        color: colors.textSecondary\n                      },\n                      children: \"Pending\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 406,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: pendingJobs\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: colors.warning\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        size: 12,\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 409,\n                        columnNumber: 27\n                      }, this), \"Needs attention\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.2\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: colors.surface,\n                    borderRadius: '20px',\n                    boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: `linear-gradient(135deg, ${colors.success}, #10B981)`,\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 443,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"mb-1\",\n                      style: {\n                        color: colors.textSecondary\n                      },\n                      children: \"Completed\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 446,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: completedJobs\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: colors.success\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Star, {\n                        size: 12,\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 449,\n                        columnNumber: 27\n                      }, this), \"Finished jobs\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 3,\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  opacity: 0,\n                  scale: 0.9\n                },\n                animate: {\n                  opacity: 1,\n                  scale: 1\n                },\n                transition: {\n                  duration: 0.5,\n                  delay: 0.3\n                },\n                whileHover: {\n                  y: -5,\n                  scale: 1.02\n                },\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  className: \"h-100 border-0 shadow-lg\",\n                  style: {\n                    background: colors.surface,\n                    borderRadius: '20px',\n                    boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Card.Body, {\n                    className: \"text-center p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '60px',\n                          height: '60px',\n                          background: `linear-gradient(135deg, ${colors.accent}, #059669)`,\n                          borderRadius: '15px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(DollarSign, {\n                          className: \"text-white\",\n                          size: 24\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 483,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 474,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 473,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n                      className: \"mb-1\",\n                      style: {\n                        color: colors.textSecondary\n                      },\n                      children: \"Revenue\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"fw-bold\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: [\"$\", totalRevenue.toFixed(2)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      style: {\n                        color: colors.accent\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(TrendingUp, {\n                        size: 12,\n                        className: \"me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 489,\n                        columnNumber: 27\n                      }, this), \"This month\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 472,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              lg: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"border-0 shadow-lg\",\n                style: {\n                  background: colors.surface,\n                  borderRadius: '20px',\n                  boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n                  className: \"border-0 bg-transparent\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"d-flex align-items-center justify-content-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"fw-bold mb-0\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Activity, {\n                        className: \"me-2\",\n                        size: 20\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 510,\n                        columnNumber: 27\n                      }, this), \"Recent Jobs\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 509,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                      className: \"px-3 py-2\",\n                      style: {\n                        background: `${colors.primary}20`,\n                        color: colors.primary\n                      },\n                      children: [jobs.length, \" total\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 507,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n                  children: jobs.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"table-responsive\",\n                    children: /*#__PURE__*/_jsxDEV(Table, {\n                      className: isDarkMode ? 'table-dark' : '',\n                      hover: true,\n                      children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                        children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Job Number\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 530,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Student\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 531,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"File Name\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 532,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Status\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 533,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Cost\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 534,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                            children: \"Actions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 535,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 529,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 528,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                        children: jobs.slice(0, 10).map(job => /*#__PURE__*/_jsxDEV(\"tr\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"fw-semibold\",\n                            children: job.jobNumber\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 541,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: job.studentName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 542,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 543,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: getStatusBadge(job.status)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            className: \"fw-semibold\",\n                            style: {\n                              color: colors.success\n                            },\n                            children: job.cost ? `$${job.cost.toFixed(2)}` : '-'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 545,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex gap-1\",\n                              children: [/*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-primary\",\n                                size: \"sm\",\n                                style: {\n                                  borderRadius: '8px'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(Eye, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 555,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 550,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-success\",\n                                size: \"sm\",\n                                style: {\n                                  borderRadius: '8px'\n                                },\n                                onClick: () => handleStatusUpdate(job.id, 'InProgress'),\n                                children: /*#__PURE__*/_jsxDEV(Play, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 563,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 557,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                                variant: \"outline-info\",\n                                size: \"sm\",\n                                style: {\n                                  borderRadius: '8px'\n                                },\n                                children: /*#__PURE__*/_jsxDEV(MessageCircle, {\n                                  size: 14\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 570,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 565,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 549,\n                              columnNumber: 37\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 548,\n                            columnNumber: 35\n                          }, this)]\n                        }, job.id, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 540,\n                          columnNumber: 33\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 538,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.9\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    className: \"text-center py-5\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-inline-flex align-items-center justify-content-center\",\n                        style: {\n                          width: '80px',\n                          height: '80px',\n                          background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                          borderRadius: '20px'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(FileText, {\n                          className: \"text-white\",\n                          size: 40\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 595,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 586,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 585,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                      className: \"fw-semibold mb-2\",\n                      style: {\n                        color: colors.text\n                      },\n                      children: \"No jobs yet\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 598,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        color: colors.textSecondary\n                      },\n                      children: \"Jobs will appear here when students submit them.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 599,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 580,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 524,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 499,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 15\n          }, this), activeTab !== 'dashboard' && /*#__PURE__*/_jsxDEV(Card, {\n            className: \"border-0 shadow-lg\",\n            style: {\n              background: colors.surface,\n              borderRadius: '20px',\n              boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n            },\n            children: /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"text-center py-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mb-3\",\n                style: {\n                  color: colors.text\n                },\n                children: [activeTab.charAt(0).toUpperCase() + activeTab.slice(1), \" Tab\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: colors.textSecondary\n                },\n                children: \"This tab is under development. Coming soon!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this)]\n        }, activeTab, true, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleAceternityXeroxDashboard, \"w0s7TsMfcJ+UZDjXKovRd+BZ3fI=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = SimpleAceternityXeroxDashboard;\nexport default SimpleAceternityXeroxDashboard;\nvar _c;\n$RefreshReg$(_c, \"SimpleAceternityXeroxDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Container", "Row", "Col", "Card", "<PERSON><PERSON>", "Badge", "Nav", "Table", "motion", "FileText", "Clock", "CheckCircle", "DollarSign", "MessageCircle", "Eye", "Activity", "Users", "Star", "Settings", "Building", "BarChart3", "RefreshCw", "<PERSON><PERSON><PERSON>", "TrendingUp", "Moon", "Sun", "Play", "Truck", "useAuth", "useTheme", "printJobApi", "jsxDEV", "_jsxDEV", "SimpleAceternityXeroxDashboard", "_s", "user", "isDarkMode", "toggleTheme", "activeTab", "setActiveTab", "jobs", "setJobs", "isRefreshing", "setIsRefreshing", "colors", "primary", "secondary", "accent", "warning", "danger", "success", "background", "surface", "text", "textSecondary", "fetchJobs", "interval", "setInterval", "clearInterval", "response", "getXeroxCenterJobs", "data", "error", "console", "getStatusBadge", "status", "statusConfig", "bg", "color", "icon", "config", "IconComponent", "className", "style", "border", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleStatusUpdate", "jobId", "newStatus", "updateJobStatus", "totalJobs", "length", "pendingJobs", "filter", "job", "includes", "inProgressJobs", "completedJobs", "totalRevenue", "reduce", "sum", "cost", "floatingShapes", "Array", "from", "_", "i", "div", "width", "Math", "random", "height", "left", "top", "opacity", "zIndex", "animate", "x", "y", "rotate", "scale", "transition", "duration", "repeat", "Infinity", "repeatType", "ease", "minHeight", "button", "onClick", "boxShadow", "whileHover", "whileTap", "fluid", "initial", "borderRadius", "fontSize", "username", "disabled", "Date", "toLocaleTimeString", "delay", "<PERSON><PERSON>ilter", "Body", "variant", "key", "label", "map", "tab", "<PERSON><PERSON>", "Link", "active", "md", "toFixed", "lg", "Header", "hover", "slice", "jobNumber", "studentName", "id", "char<PERSON>t", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/src/components/ui/SimpleAceternityXeroxDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Container, Row, Col, Card, Button, Badge, Nav, Table } from 'react-bootstrap';\nimport { motion } from 'framer-motion';\nimport { \n  FileText, \n  Clock, \n  CheckCircle, \n  DollarSign, \n  Download, \n  MessageCircle, \n  Eye, \n  Activity,\n  Users,\n  Printer,\n  Star,\n  Settings,\n  Building,\n  BarChart3,\n  RefreshCw,\n  PieChart,\n  TrendingUp,\n  Moon,\n  Sun,\n  Play,\n  Truck,\n  X\n} from 'lucide-react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { useTheme } from '../../contexts/ThemeContext';\nimport { printJobApi } from '../../services/api';\n\ninterface PrintJob {\n  id: number;\n  jobNumber: string;\n  fileName: string;\n  status: string;\n  cost?: number;\n  studentName: string;\n  studentEmail: string;\n  created: string;\n  printType: string;\n  copies: number;\n  colorType: string;\n  paperSize: string;\n  priority: string;\n}\n\nconst SimpleAceternityXeroxDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const { isDarkMode, toggleTheme } = useTheme();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [jobs, setJobs] = useState<PrintJob[]>([]);\n  const [isRefreshing, setIsRefreshing] = useState(false);\n\n  // Professional color scheme\n  const colors = {\n    primary: isDarkMode ? '#3B82F6' : '#1E40AF', // Blue\n    secondary: isDarkMode ? '#6B7280' : '#374151', // Gray\n    accent: isDarkMode ? '#10B981' : '#059669', // Emerald\n    warning: isDarkMode ? '#F59E0B' : '#D97706', // Amber\n    danger: isDarkMode ? '#EF4444' : '#DC2626', // Red\n    success: isDarkMode ? '#10B981' : '#059669', // Emerald\n    background: isDarkMode ? '#111827' : '#F9FAFB',\n    surface: isDarkMode ? '#1F2937' : '#FFFFFF',\n    text: isDarkMode ? '#F9FAFB' : '#111827',\n    textSecondary: isDarkMode ? '#9CA3AF' : '#6B7280'\n  };\n\n  useEffect(() => {\n    fetchJobs();\n    const interval = setInterval(fetchJobs, 30000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchJobs = async () => {\n    setIsRefreshing(true);\n    try {\n      const response = await printJobApi.getXeroxCenterJobs();\n      setJobs(response.data || []);\n    } catch (error) {\n      console.error('Error fetching jobs:', error);\n      setJobs([]);\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  const getStatusBadge = (status: string) => {\n    const statusConfig: { [key: string]: { bg: string; color: string; icon: any } } = {\n      'Requested': { bg: colors.secondary + '20', color: colors.secondary, icon: Clock },\n      'UnderReview': { bg: colors.primary + '20', color: colors.primary, icon: Eye },\n      'Quoted': { bg: colors.warning + '20', color: colors.warning, icon: DollarSign },\n      'Confirmed': { bg: colors.accent + '20', color: colors.accent, icon: CheckCircle },\n      'InProgress': { bg: colors.primary + '20', color: colors.primary, icon: Activity },\n      'Completed': { bg: colors.success + '20', color: colors.success, icon: CheckCircle },\n      'Delivered': { bg: colors.success + '20', color: colors.success, icon: Truck }\n    };\n\n    const config = statusConfig[status] || { bg: colors.secondary + '20', color: colors.secondary, icon: Clock };\n    const IconComponent = config.icon;\n    \n    return (\n      <Badge \n        className=\"d-flex align-items-center gap-1 px-3 py-2\"\n        style={{\n          background: config.bg,\n          color: config.color,\n          border: 'none'\n        }}\n      >\n        <IconComponent size={14} />\n        {status}\n      </Badge>\n    );\n  };\n\n  const handleStatusUpdate = async (jobId: number, newStatus: string) => {\n    try {\n      await printJobApi.updateJobStatus(jobId, newStatus);\n      fetchJobs();\n    } catch (error) {\n      console.error('Error updating job status:', error);\n    }\n  };\n\n  // Calculate statistics\n  const totalJobs = jobs.length;\n  const pendingJobs = jobs.filter(job => ['Requested', 'UnderReview'].includes(job.status)).length;\n  const inProgressJobs = jobs.filter(job => ['Confirmed', 'InProgress'].includes(job.status)).length;\n  const completedJobs = jobs.filter(job => ['Completed', 'Delivered'].includes(job.status)).length;\n  const totalRevenue = jobs.reduce((sum, job) => sum + (job.cost || 0), 0);\n\n  const floatingShapes = Array.from({ length: 6 }, (_, i) => (\n    <motion.div\n      key={i}\n      className=\"position-absolute rounded-circle\"\n      style={{\n        background: `linear-gradient(135deg, ${colors.primary}15, ${colors.accent}15)`,\n        width: `${Math.random() * 60 + 20}px`,\n        height: `${Math.random() * 60 + 20}px`,\n        left: `${Math.random() * 100}%`,\n        top: `${Math.random() * 100}%`,\n        opacity: 0.3,\n        zIndex: 0\n      }}\n      animate={{\n        x: [0, Math.random() * 100 - 50],\n        y: [0, Math.random() * 100 - 50],\n        rotate: [0, 360],\n        scale: [1, 1.2, 1],\n      }}\n      transition={{\n        duration: Math.random() * 20 + 10,\n        repeat: Infinity,\n        repeatType: \"reverse\",\n        ease: \"easeInOut\"\n      }}\n    />\n  ));\n\n  return (\n    <div \n      className=\"min-h-screen position-relative\"\n      style={{\n        background: isDarkMode \n          ? 'linear-gradient(135deg, #111827 0%, #1F2937 50%, #374151 100%)'\n          : 'linear-gradient(135deg, #F9FAFB 0%, #E5E7EB 50%, #D1D5DB 100%)',\n        minHeight: '100vh'\n      }}\n    >\n      {/* Animated Background */}\n      <div className=\"position-absolute w-100 h-100 overflow-hidden\">\n        {floatingShapes}\n        <div className=\"position-absolute w-100 h-100\" style={{\n          background: isDarkMode\n            ? 'radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)'\n            : 'radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.05) 0%, transparent 50%)'\n        }} />\n      </div>\n\n      {/* Theme Toggle Button */}\n      <motion.button\n        onClick={toggleTheme}\n        className=\"position-fixed top-0 end-0 m-4 btn border-0 rounded-circle d-flex align-items-center justify-content-center\"\n        style={{\n          width: '50px',\n          height: '50px',\n          background: colors.surface,\n          boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.3)' : '0 4px 20px rgba(0,0,0,0.1)',\n          color: colors.text,\n          zIndex: 1000\n        }}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n      >\n        {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}\n      </motion.button>\n\n      <Container fluid className=\"position-relative py-4\" style={{ zIndex: 1 }}>\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.6 }}\n        >\n          {/* Header */}\n          <motion.div \n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            className=\"text-center mb-5\"\n          >\n            <motion.div\n              animate={{\n                rotate: [0, 360],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 4,\n                repeat: Infinity,\n                ease: \"easeInOut\"\n              }}\n              className=\"d-inline-flex align-items-center justify-content-center mb-3\"\n              style={{\n                width: '80px',\n                height: '80px',\n                background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                borderRadius: '20px',\n                boxShadow: `0 20px 40px ${colors.primary}30`\n              }}\n            >\n              <Building className=\"text-white\" size={40} />\n            </motion.div>\n            <h1 className=\"fw-bold mb-2\" style={{ fontSize: '2.5rem', color: colors.text }}>\n              Xerox Center Dashboard\n            </h1>\n            <p className=\"fs-5 mb-4\" style={{ color: colors.textSecondary }}>\n              Welcome back, <span className=\"fw-semibold\">{user?.username}</span>! Manage your printing business\n            </p>\n            \n            <div className=\"d-flex align-items-center justify-content-center gap-3 flex-wrap\">\n              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                <Button\n                  size=\"lg\"\n                  onClick={fetchJobs}\n                  disabled={isRefreshing}\n                  className=\"px-4 py-3 rounded-4 fw-semibold border-0\"\n                  style={{\n                    background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                    color: '#fff'\n                  }}\n                >\n                  <RefreshCw className={`me-2 ${isRefreshing ? 'spin' : ''}`} size={20} />\n                  Refresh\n                </Button>\n              </motion.div>\n              \n              <Badge \n                className=\"px-3 py-2 fs-6\"\n                style={{\n                  background: colors.surface,\n                  color: colors.text,\n                  boxShadow: isDarkMode ? '0 4px 20px rgba(0,0,0,0.2)' : '0 4px 20px rgba(0,0,0,0.1)'\n                }}\n              >\n                Last updated: {new Date().toLocaleTimeString()}\n              </Badge>\n            </div>\n          </motion.div>\n\n          {/* Navigation Tabs */}\n          <motion.div \n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            className=\"mb-4\"\n          >\n            <Card \n              className=\"border-0 shadow-lg\"\n              style={{\n                background: colors.surface,\n                backdropFilter: 'blur(20px)',\n                borderRadius: '20px',\n                boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n              }}\n            >\n              <Card.Body className=\"p-2\">\n                <Nav variant=\"pills\" className=\"justify-content-center flex-wrap\">\n                  {[\n                    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },\n                    { key: 'jobs', label: 'Job Queue', icon: FileText },\n                    { key: 'analytics', label: 'Analytics', icon: PieChart },\n                    { key: 'customers', label: 'Customers', icon: Users },\n                    { key: 'settings', label: 'Settings', icon: Settings }\n                  ].map(tab => {\n                    const IconComponent = tab.icon;\n                    return (\n                      <Nav.Item key={tab.key} className=\"m-1\">\n                        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>\n                          <Nav.Link\n                            active={activeTab === tab.key}\n                            onClick={() => setActiveTab(tab.key)}\n                            className={`px-4 py-3 rounded-3 fw-semibold d-flex align-items-center`}\n                            style={{\n                              background: activeTab === tab.key \n                                ? `linear-gradient(135deg, ${colors.primary}, ${colors.accent})` \n                                : 'transparent',\n                              border: 'none',\n                              transition: 'all 0.3s ease',\n                              color: activeTab === tab.key ? '#fff' : colors.textSecondary,\n                              boxShadow: activeTab === tab.key \n                                ? `0 10px 30px ${colors.primary}20` \n                                : 'none'\n                            }}\n                          >\n                            <IconComponent size={18} className=\"me-2\" />\n                            <span className=\"d-none d-md-inline\">{tab.label}</span>\n                          </Nav.Link>\n                        </motion.div>\n                      </Nav.Item>\n                    );\n                  })}\n                </Nav>\n              </Card.Body>\n            </Card>\n          </motion.div>\n\n          {/* Tab Content */}\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {activeTab === 'dashboard' && (\n              <Row className=\"g-4\">\n                {/* Statistics Cards */}\n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card \n                      className=\"h-100 border-0 shadow-lg\"\n                      style={{ \n                        background: colors.surface,\n                        borderRadius: '20px',\n                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                      }}\n                    >\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div \n                            className=\"d-inline-flex align-items-center justify-content-center\"\n                            style={{\n                              width: '60px',\n                              height: '60px',\n                              background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                              borderRadius: '15px'\n                            }}\n                          >\n                            <FileText className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"mb-1\" style={{ color: colors.textSecondary }}>Total Jobs</h6>\n                        <h2 className=\"fw-bold\" style={{ color: colors.text }}>{totalJobs}</h2>\n                        <small style={{ color: colors.success }}>\n                          <TrendingUp size={12} className=\"me-1\" />\n                          +12% vs last month\n                        </small>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.1 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card \n                      className=\"h-100 border-0 shadow-lg\"\n                      style={{ \n                        background: colors.surface,\n                        borderRadius: '20px',\n                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                      }}\n                    >\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div \n                            className=\"d-inline-flex align-items-center justify-content-center\"\n                            style={{\n                              width: '60px',\n                              height: '60px',\n                              background: `linear-gradient(135deg, ${colors.warning}, #F59E0B)`,\n                              borderRadius: '15px'\n                            }}\n                          >\n                            <Clock className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"mb-1\" style={{ color: colors.textSecondary }}>Pending</h6>\n                        <h2 className=\"fw-bold\" style={{ color: colors.text }}>{pendingJobs}</h2>\n                        <small style={{ color: colors.warning }}>\n                          <Activity size={12} className=\"me-1\" />\n                          Needs attention\n                        </small>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.2 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card \n                      className=\"h-100 border-0 shadow-lg\"\n                      style={{ \n                        background: colors.surface,\n                        borderRadius: '20px',\n                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                      }}\n                    >\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div \n                            className=\"d-inline-flex align-items-center justify-content-center\"\n                            style={{\n                              width: '60px',\n                              height: '60px',\n                              background: `linear-gradient(135deg, ${colors.success}, #10B981)`,\n                              borderRadius: '15px'\n                            }}\n                          >\n                            <CheckCircle className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"mb-1\" style={{ color: colors.textSecondary }}>Completed</h6>\n                        <h2 className=\"fw-bold\" style={{ color: colors.text }}>{completedJobs}</h2>\n                        <small style={{ color: colors.success }}>\n                          <Star size={12} className=\"me-1\" />\n                          Finished jobs\n                        </small>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n                \n                <Col md={3}>\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: 0.3 }}\n                    whileHover={{ y: -5, scale: 1.02 }}\n                  >\n                    <Card \n                      className=\"h-100 border-0 shadow-lg\"\n                      style={{ \n                        background: colors.surface,\n                        borderRadius: '20px',\n                        boxShadow: isDarkMode ? '0 10px 30px rgba(0,0,0,0.2)' : '0 10px 30px rgba(0,0,0,0.1)'\n                      }}\n                    >\n                      <Card.Body className=\"text-center p-4\">\n                        <div className=\"mb-3\">\n                          <div \n                            className=\"d-inline-flex align-items-center justify-content-center\"\n                            style={{\n                              width: '60px',\n                              height: '60px',\n                              background: `linear-gradient(135deg, ${colors.accent}, #059669)`,\n                              borderRadius: '15px'\n                            }}\n                          >\n                            <DollarSign className=\"text-white\" size={24} />\n                          </div>\n                        </div>\n                        <h6 className=\"mb-1\" style={{ color: colors.textSecondary }}>Revenue</h6>\n                        <h2 className=\"fw-bold\" style={{ color: colors.text }}>${totalRevenue.toFixed(2)}</h2>\n                        <small style={{ color: colors.accent }}>\n                          <TrendingUp size={12} className=\"me-1\" />\n                          This month\n                        </small>\n                      </Card.Body>\n                    </Card>\n                  </motion.div>\n                </Col>\n\n                {/* Recent Jobs Table */}\n                <Col lg={12}>\n                  <Card \n                    className=\"border-0 shadow-lg\"\n                    style={{ \n                      background: colors.surface,\n                      borderRadius: '20px',\n                      boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n                    }}\n                  >\n                    <Card.Header className=\"border-0 bg-transparent\">\n                      <div className=\"d-flex align-items-center justify-content-between\">\n                        <h4 className=\"fw-bold mb-0\" style={{ color: colors.text }}>\n                          <Activity className=\"me-2\" size={20} />\n                          Recent Jobs\n                        </h4>\n                        <Badge \n                          className=\"px-3 py-2\"\n                          style={{\n                            background: `${colors.primary}20`,\n                            color: colors.primary\n                          }}\n                        >\n                          {jobs.length} total\n                        </Badge>\n                      </div>\n                    </Card.Header>\n                    <Card.Body>\n                      {jobs.length > 0 ? (\n                        <div className=\"table-responsive\">\n                          <Table className={isDarkMode ? 'table-dark' : ''} hover>\n                            <thead>\n                              <tr>\n                                <th>Job Number</th>\n                                <th>Student</th>\n                                <th>File Name</th>\n                                <th>Status</th>\n                                <th>Cost</th>\n                                <th>Actions</th>\n                              </tr>\n                            </thead>\n                            <tbody>\n                              {jobs.slice(0, 10).map((job: PrintJob) => (\n                                <tr key={job.id}>\n                                  <td className=\"fw-semibold\">{job.jobNumber}</td>\n                                  <td>{job.studentName}</td>\n                                  <td>{job.fileName.length > 30 ? job.fileName.slice(0, 30) + '...' : job.fileName}</td>\n                                  <td>{getStatusBadge(job.status)}</td>\n                                  <td className=\"fw-semibold\" style={{ color: colors.success }}>\n                                    {job.cost ? `$${job.cost.toFixed(2)}` : '-'}\n                                  </td>\n                                  <td>\n                                    <div className=\"d-flex gap-1\">\n                                      <Button \n                                        variant=\"outline-primary\" \n                                        size=\"sm\"\n                                        style={{ borderRadius: '8px' }}\n                                      >\n                                        <Eye size={14} />\n                                      </Button>\n                                      <Button \n                                        variant=\"outline-success\" \n                                        size=\"sm\"\n                                        style={{ borderRadius: '8px' }}\n                                        onClick={() => handleStatusUpdate(job.id, 'InProgress')}\n                                      >\n                                        <Play size={14} />\n                                      </Button>\n                                      <Button \n                                        variant=\"outline-info\" \n                                        size=\"sm\"\n                                        style={{ borderRadius: '8px' }}\n                                      >\n                                        <MessageCircle size={14} />\n                                      </Button>\n                                    </div>\n                                  </td>\n                                </tr>\n                              ))}\n                            </tbody>\n                          </Table>\n                        </div>\n                      ) : (\n                        <motion.div\n                          initial={{ opacity: 0, scale: 0.9 }}\n                          animate={{ opacity: 1, scale: 1 }}\n                          className=\"text-center py-5\"\n                        >\n                          <div className=\"mb-4\">\n                            <div \n                              className=\"d-inline-flex align-items-center justify-content-center\"\n                              style={{\n                                width: '80px',\n                                height: '80px',\n                                background: `linear-gradient(135deg, ${colors.primary}, ${colors.accent})`,\n                                borderRadius: '20px'\n                              }}\n                            >\n                              <FileText className=\"text-white\" size={40} />\n                            </div>\n                          </div>\n                          <h5 className=\"fw-semibold mb-2\" style={{ color: colors.text }}>No jobs yet</h5>\n                          <p style={{ color: colors.textSecondary }}>Jobs will appear here when students submit them.</p>\n                        </motion.div>\n                      )}\n                    </Card.Body>\n                  </Card>\n                </Col>\n              </Row>\n            )}\n\n            {/* Other tabs content */}\n            {activeTab !== 'dashboard' && (\n              <Card \n                className=\"border-0 shadow-lg\"\n                style={{ \n                  background: colors.surface,\n                  borderRadius: '20px',\n                  boxShadow: isDarkMode ? '0 25px 50px rgba(0,0,0,0.2)' : '0 25px 50px rgba(0,0,0,0.1)'\n                }}\n              >\n                <Card.Body className=\"text-center py-5\">\n                  <h4 className=\"mb-3\" style={{ color: colors.text }}>\n                    {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Tab\n                  </h4>\n                  <p style={{ color: colors.textSecondary }}>\n                    This tab is under development. Coming soon!\n                  </p>\n                </Card.Body>\n              </Card>\n            )}\n          </motion.div>\n        </motion.div>\n      </Container>\n    </div>\n  );\n};\n\nexport default SimpleAceternityXeroxDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAEC,KAAK,QAAQ,iBAAiB;AACtF,SAASC,MAAM,QAAQ,eAAe;AACtC,SACEC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,UAAU,EAEVC,aAAa,EACbC,GAAG,EACHC,QAAQ,EACRC,KAAK,EAELC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,IAAI,EACJC,KAAK,QAEA,cAAc;AACrB,SAASC,OAAO,QAAQ,4BAA4B;AACpD,SAASC,QAAQ,QAAQ,6BAA6B;AACtD,SAASC,WAAW,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBjD,MAAMC,8BAAwC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEQ,UAAU;IAAEC;EAAY,CAAC,GAAGR,QAAQ,CAAC,CAAC;EAC9C,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAa,EAAE,CAAC;EAChD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM8C,MAAM,GAAG;IACbC,OAAO,EAAET,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7CU,SAAS,EAAEV,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC/CW,MAAM,EAAEX,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC5CY,OAAO,EAAEZ,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7Ca,MAAM,EAAEb,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC5Cc,OAAO,EAAEd,UAAU,GAAG,SAAS,GAAG,SAAS;IAAE;IAC7Ce,UAAU,EAAEf,UAAU,GAAG,SAAS,GAAG,SAAS;IAC9CgB,OAAO,EAAEhB,UAAU,GAAG,SAAS,GAAG,SAAS;IAC3CiB,IAAI,EAAEjB,UAAU,GAAG,SAAS,GAAG,SAAS;IACxCkB,aAAa,EAAElB,UAAU,GAAG,SAAS,GAAG;EAC1C,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACdwD,SAAS,CAAC,CAAC;IACX,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,KAAK,CAAC;IAC9C,OAAO,MAAMG,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5BZ,eAAe,CAAC,IAAI,CAAC;IACrB,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAM7B,WAAW,CAAC8B,kBAAkB,CAAC,CAAC;MACvDnB,OAAO,CAACkB,QAAQ,CAACE,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CrB,OAAO,CAAC,EAAE,CAAC;IACb,CAAC,SAAS;MACRE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,MAAMqB,cAAc,GAAIC,MAAc,IAAK;IACzC,MAAMC,YAAyE,GAAG;MAChF,WAAW,EAAE;QAAEC,EAAE,EAAEvB,MAAM,CAACE,SAAS,GAAG,IAAI;QAAEsB,KAAK,EAAExB,MAAM,CAACE,SAAS;QAAEuB,IAAI,EAAE3D;MAAM,CAAC;MAClF,aAAa,EAAE;QAAEyD,EAAE,EAAEvB,MAAM,CAACC,OAAO,GAAG,IAAI;QAAEuB,KAAK,EAAExB,MAAM,CAACC,OAAO;QAAEwB,IAAI,EAAEvD;MAAI,CAAC;MAC9E,QAAQ,EAAE;QAAEqD,EAAE,EAAEvB,MAAM,CAACI,OAAO,GAAG,IAAI;QAAEoB,KAAK,EAAExB,MAAM,CAACI,OAAO;QAAEqB,IAAI,EAAEzD;MAAW,CAAC;MAChF,WAAW,EAAE;QAAEuD,EAAE,EAAEvB,MAAM,CAACG,MAAM,GAAG,IAAI;QAAEqB,KAAK,EAAExB,MAAM,CAACG,MAAM;QAAEsB,IAAI,EAAE1D;MAAY,CAAC;MAClF,YAAY,EAAE;QAAEwD,EAAE,EAAEvB,MAAM,CAACC,OAAO,GAAG,IAAI;QAAEuB,KAAK,EAAExB,MAAM,CAACC,OAAO;QAAEwB,IAAI,EAAEtD;MAAS,CAAC;MAClF,WAAW,EAAE;QAAEoD,EAAE,EAAEvB,MAAM,CAACM,OAAO,GAAG,IAAI;QAAEkB,KAAK,EAAExB,MAAM,CAACM,OAAO;QAAEmB,IAAI,EAAE1D;MAAY,CAAC;MACpF,WAAW,EAAE;QAAEwD,EAAE,EAAEvB,MAAM,CAACM,OAAO,GAAG,IAAI;QAAEkB,KAAK,EAAExB,MAAM,CAACM,OAAO;QAAEmB,IAAI,EAAE1C;MAAM;IAC/E,CAAC;IAED,MAAM2C,MAAM,GAAGJ,YAAY,CAACD,MAAM,CAAC,IAAI;MAAEE,EAAE,EAAEvB,MAAM,CAACE,SAAS,GAAG,IAAI;MAAEsB,KAAK,EAAExB,MAAM,CAACE,SAAS;MAAEuB,IAAI,EAAE3D;IAAM,CAAC;IAC5G,MAAM6D,aAAa,GAAGD,MAAM,CAACD,IAAI;IAEjC,oBACErC,OAAA,CAAC3B,KAAK;MACJmE,SAAS,EAAC,2CAA2C;MACrDC,KAAK,EAAE;QACLtB,UAAU,EAAEmB,MAAM,CAACH,EAAE;QACrBC,KAAK,EAAEE,MAAM,CAACF,KAAK;QACnBM,MAAM,EAAE;MACV,CAAE;MAAAC,QAAA,gBAEF3C,OAAA,CAACuC,aAAa;QAACK,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC1Bf,MAAM;IAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CAAOC,KAAa,EAAEC,SAAiB,KAAK;IACrE,IAAI;MACF,MAAMrD,WAAW,CAACsD,eAAe,CAACF,KAAK,EAAEC,SAAS,CAAC;MACnD5B,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMuB,SAAS,GAAG7C,IAAI,CAAC8C,MAAM;EAC7B,MAAMC,WAAW,GAAG/C,IAAI,CAACgD,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACqB,MAAM;EAChG,MAAMK,cAAc,GAAGnD,IAAI,CAACgD,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACqB,MAAM;EAClG,MAAMM,aAAa,GAAGpD,IAAI,CAACgD,MAAM,CAACC,GAAG,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACD,GAAG,CAACxB,MAAM,CAAC,CAAC,CAACqB,MAAM;EAChG,MAAMO,YAAY,GAAGrD,IAAI,CAACsD,MAAM,CAAC,CAACC,GAAG,EAAEN,GAAG,KAAKM,GAAG,IAAIN,GAAG,CAACO,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAExE,MAAMC,cAAc,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEb,MAAM,EAAE;EAAE,CAAC,EAAE,CAACc,CAAC,EAAEC,CAAC,kBACpDrE,OAAA,CAACxB,MAAM,CAAC8F,GAAG;IAET9B,SAAS,EAAC,kCAAkC;IAC5CC,KAAK,EAAE;MACLtB,UAAU,EAAE,2BAA2BP,MAAM,CAACC,OAAO,OAAOD,MAAM,CAACG,MAAM,KAAK;MAC9EwD,KAAK,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACrCC,MAAM,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI;MACtCE,IAAI,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC/BG,GAAG,EAAE,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;MAC9BI,OAAO,EAAE,GAAG;MACZC,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAE;MACPC,CAAC,EAAE,CAAC,CAAC,EAAER,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCQ,CAAC,EAAE,CAAC,CAAC,EAAET,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MAChCS,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;MAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;IACnB,CAAE;IACFC,UAAU,EAAE;MACVC,QAAQ,EAAEb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;MACjCa,MAAM,EAAEC,QAAQ;MAChBC,UAAU,EAAE,SAAS;MACrBC,IAAI,EAAE;IACR;EAAE,GAtBGpB,CAAC;IAAAxB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAuBP,CACF,CAAC;EAEF,oBACEhD,OAAA;IACEwC,SAAS,EAAC,gCAAgC;IAC1CC,KAAK,EAAE;MACLtB,UAAU,EAAEf,UAAU,GAClB,gEAAgE,GAChE,gEAAgE;MACpEsF,SAAS,EAAE;IACb,CAAE;IAAA/C,QAAA,gBAGF3C,OAAA;MAAKwC,SAAS,EAAC,+CAA+C;MAAAG,QAAA,GAC3DsB,cAAc,eACfjE,OAAA;QAAKwC,SAAS,EAAC,+BAA+B;QAACC,KAAK,EAAE;UACpDtB,UAAU,EAAEf,UAAU,GAClB,kKAAkK,GAClK;QACN;MAAE;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNhD,OAAA,CAACxB,MAAM,CAACmH,MAAM;MACZC,OAAO,EAAEvF,WAAY;MACrBmC,SAAS,EAAC,6GAA6G;MACvHC,KAAK,EAAE;QACL8B,KAAK,EAAE,MAAM;QACbG,MAAM,EAAE,MAAM;QACdvD,UAAU,EAAEP,MAAM,CAACQ,OAAO;QAC1ByE,SAAS,EAAEzF,UAAU,GAAG,4BAA4B,GAAG,4BAA4B;QACnFgC,KAAK,EAAExB,MAAM,CAACS,IAAI;QAClByD,MAAM,EAAE;MACV,CAAE;MACFgB,UAAU,EAAE;QAAEX,KAAK,EAAE;MAAI,CAAE;MAC3BY,QAAQ,EAAE;QAAEZ,KAAK,EAAE;MAAI,CAAE;MAAAxC,QAAA,EAExBvC,UAAU,gBAAGJ,OAAA,CAACP,GAAG;QAACmD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGhD,OAAA,CAACR,IAAI;QAACoD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEhBhD,OAAA,CAAChC,SAAS;MAACgI,KAAK;MAACxD,SAAS,EAAC,wBAAwB;MAACC,KAAK,EAAE;QAAEqC,MAAM,EAAE;MAAE,CAAE;MAAAnC,QAAA,eACvE3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;QACT2B,OAAO,EAAE;UAAEpB,OAAO,EAAE;QAAE,CAAE;QACxBE,OAAO,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QACxBO,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAA1C,QAAA,gBAG9B3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;UACT2B,OAAO,EAAE;YAAEpB,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAG,CAAE;UAC/BF,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9B7C,SAAS,EAAC,kBAAkB;UAAAG,QAAA,gBAE5B3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;YACTS,OAAO,EAAE;cACPG,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;cAChBC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;YACnB,CAAE;YACFC,UAAU,EAAE;cACVC,QAAQ,EAAE,CAAC;cACXC,MAAM,EAAEC,QAAQ;cAChBE,IAAI,EAAE;YACR,CAAE;YACFjD,SAAS,EAAC,8DAA8D;YACxEC,KAAK,EAAE;cACL8B,KAAK,EAAE,MAAM;cACbG,MAAM,EAAE,MAAM;cACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;cAC1EmF,YAAY,EAAE,MAAM;cACpBL,SAAS,EAAE,eAAejF,MAAM,CAACC,OAAO;YAC1C,CAAE;YAAA8B,QAAA,eAEF3C,OAAA,CAACb,QAAQ;cAACqD,SAAS,EAAC,YAAY;cAACI,IAAI,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC,eACbhD,OAAA;YAAIwC,SAAS,EAAC,cAAc;YAACC,KAAK,EAAE;cAAE0D,QAAQ,EAAE,QAAQ;cAAE/D,KAAK,EAAExB,MAAM,CAACS;YAAK,CAAE;YAAAsB,QAAA,EAAC;UAEhF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLhD,OAAA;YAAGwC,SAAS,EAAC,WAAW;YAACC,KAAK,EAAE;cAAEL,KAAK,EAAExB,MAAM,CAACU;YAAc,CAAE;YAAAqB,QAAA,GAAC,gBACjD,eAAA3C,OAAA;cAAMwC,SAAS,EAAC,aAAa;cAAAG,QAAA,EAAExC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG;YAAQ;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,mCACrE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJhD,OAAA;YAAKwC,SAAS,EAAC,kEAAkE;YAAAG,QAAA,gBAC/E3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;cAACwB,UAAU,EAAE;gBAAEX,KAAK,EAAE;cAAK,CAAE;cAACY,QAAQ,EAAE;gBAAEZ,KAAK,EAAE;cAAK,CAAE;cAAAxC,QAAA,eACjE3C,OAAA,CAAC5B,MAAM;gBACLwE,IAAI,EAAC,IAAI;gBACTgD,OAAO,EAAErE,SAAU;gBACnB8E,QAAQ,EAAE3F,YAAa;gBACvB8B,SAAS,EAAC,0CAA0C;gBACpDC,KAAK,EAAE;kBACLtB,UAAU,EAAE,2BAA2BP,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;kBAC1EqB,KAAK,EAAE;gBACT,CAAE;gBAAAO,QAAA,gBAEF3C,OAAA,CAACX,SAAS;kBAACmD,SAAS,EAAE,QAAQ9B,YAAY,GAAG,MAAM,GAAG,EAAE,EAAG;kBAACkC,IAAI,EAAE;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WAE1E;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEbhD,OAAA,CAAC3B,KAAK;cACJmE,SAAS,EAAC,gBAAgB;cAC1BC,KAAK,EAAE;gBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;gBAC1BgB,KAAK,EAAExB,MAAM,CAACS,IAAI;gBAClBwE,SAAS,EAAEzF,UAAU,GAAG,4BAA4B,GAAG;cACzD,CAAE;cAAAuC,QAAA,GACH,gBACe,EAAC,IAAI2D,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbhD,OAAA,CAACxB,MAAM,CAAC8F,GAAG;UACT2B,OAAO,EAAE;YAAEpB,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAG,CAAE;UAC/BF,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEmB,KAAK,EAAE;UAAI,CAAE;UAC1ChE,SAAS,EAAC,MAAM;UAAAG,QAAA,eAEhB3C,OAAA,CAAC7B,IAAI;YACHqE,SAAS,EAAC,oBAAoB;YAC9BC,KAAK,EAAE;cACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;cAC1BqF,cAAc,EAAE,YAAY;cAC5BP,YAAY,EAAE,MAAM;cACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;YAC1D,CAAE;YAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;cAAClE,SAAS,EAAC,KAAK;cAAAG,QAAA,eACxB3C,OAAA,CAAC1B,GAAG;gBAACqI,OAAO,EAAC,OAAO;gBAACnE,SAAS,EAAC,kCAAkC;gBAAAG,QAAA,EAC9D,CACC;kBAAEiE,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAExE,IAAI,EAAEjD;gBAAU,CAAC,EACzD;kBAAEwH,GAAG,EAAE,MAAM;kBAAEC,KAAK,EAAE,WAAW;kBAAExE,IAAI,EAAE5D;gBAAS,CAAC,EACnD;kBAAEmI,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAExE,IAAI,EAAE/C;gBAAS,CAAC,EACxD;kBAAEsH,GAAG,EAAE,WAAW;kBAAEC,KAAK,EAAE,WAAW;kBAAExE,IAAI,EAAErD;gBAAM,CAAC,EACrD;kBAAE4H,GAAG,EAAE,UAAU;kBAAEC,KAAK,EAAE,UAAU;kBAAExE,IAAI,EAAEnD;gBAAS,CAAC,CACvD,CAAC4H,GAAG,CAACC,GAAG,IAAI;kBACX,MAAMxE,aAAa,GAAGwE,GAAG,CAAC1E,IAAI;kBAC9B,oBACErC,OAAA,CAAC1B,GAAG,CAAC0I,IAAI;oBAAexE,SAAS,EAAC,KAAK;oBAAAG,QAAA,eACrC3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;sBAACwB,UAAU,EAAE;wBAAEX,KAAK,EAAE;sBAAK,CAAE;sBAACY,QAAQ,EAAE;wBAAEZ,KAAK,EAAE;sBAAK,CAAE;sBAAAxC,QAAA,eACjE3C,OAAA,CAAC1B,GAAG,CAAC2I,IAAI;wBACPC,MAAM,EAAE5G,SAAS,KAAKyG,GAAG,CAACH,GAAI;wBAC9BhB,OAAO,EAAEA,CAAA,KAAMrF,YAAY,CAACwG,GAAG,CAACH,GAAG,CAAE;wBACrCpE,SAAS,EAAE,2DAA4D;wBACvEC,KAAK,EAAE;0BACLtB,UAAU,EAAEb,SAAS,KAAKyG,GAAG,CAACH,GAAG,GAC7B,2BAA2BhG,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG,GAC9D,aAAa;0BACjB2B,MAAM,EAAE,MAAM;0BACd0C,UAAU,EAAE,eAAe;0BAC3BhD,KAAK,EAAE9B,SAAS,KAAKyG,GAAG,CAACH,GAAG,GAAG,MAAM,GAAGhG,MAAM,CAACU,aAAa;0BAC5DuE,SAAS,EAAEvF,SAAS,KAAKyG,GAAG,CAACH,GAAG,GAC5B,eAAehG,MAAM,CAACC,OAAO,IAAI,GACjC;wBACN,CAAE;wBAAA8B,QAAA,gBAEF3C,OAAA,CAACuC,aAAa;0BAACK,IAAI,EAAE,EAAG;0BAACJ,SAAS,EAAC;wBAAM;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5ChD,OAAA;0BAAMwC,SAAS,EAAC,oBAAoB;0BAAAG,QAAA,EAAEoE,GAAG,CAACF;wBAAK;0BAAAhE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC,GArBA+D,GAAG,CAACH,GAAG;oBAAA/D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAsBZ,CAAC;gBAEf,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAGbhD,OAAA,CAACxB,MAAM,CAAC8F,GAAG;UAET2B,OAAO,EAAE;YAAEpB,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAG,CAAE;UAC/BF,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEI,CAAC,EAAE;UAAE,CAAE;UAC9BG,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAA1C,QAAA,GAE7BrC,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAC/B,GAAG;YAACuE,SAAS,EAAC,KAAK;YAAAG,QAAA,gBAElB3C,OAAA,CAAC9B,GAAG;cAACiJ,EAAE,EAAE,CAAE;cAAAxE,QAAA,eACT3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;gBACT2B,OAAO,EAAE;kBAAEpB,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAC9BS,UAAU,EAAE;kBAAEb,CAAC,EAAE,CAAC,CAAC;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAAAxC,QAAA,eAEnC3C,OAAA,CAAC7B,IAAI;kBACHqE,SAAS,EAAC,0BAA0B;kBACpCC,KAAK,EAAE;oBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;oBAC1B8E,YAAY,EAAE,MAAM;oBACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;kBAC1D,CAAE;kBAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;oBAAClE,SAAS,EAAC,iBAAiB;oBAAAG,QAAA,gBACpC3C,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAG,QAAA,eACnB3C,OAAA;wBACEwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACL8B,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;0BAC1EmF,YAAY,EAAE;wBAChB,CAAE;wBAAAvD,QAAA,eAEF3C,OAAA,CAACvB,QAAQ;0BAAC+D,SAAS,EAAC,YAAY;0BAACI,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhD,OAAA;sBAAIwC,SAAS,EAAC,MAAM;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACU;sBAAc,CAAE;sBAAAqB,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC5EhD,OAAA;sBAAIwC,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,EAAEU;oBAAS;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvEhD,OAAA;sBAAOyC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACM;sBAAQ,CAAE;sBAAAyB,QAAA,gBACtC3C,OAAA,CAACT,UAAU;wBAACqD,IAAI,EAAE,EAAG;wBAACJ,SAAS,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,sBAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhD,OAAA,CAAC9B,GAAG;cAACiJ,EAAE,EAAE,CAAE;cAAAxE,QAAA,eACT3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;gBACT2B,OAAO,EAAE;kBAAEpB,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmB,KAAK,EAAE;gBAAI,CAAE;gBAC1CV,UAAU,EAAE;kBAAEb,CAAC,EAAE,CAAC,CAAC;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAAAxC,QAAA,eAEnC3C,OAAA,CAAC7B,IAAI;kBACHqE,SAAS,EAAC,0BAA0B;kBACpCC,KAAK,EAAE;oBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;oBAC1B8E,YAAY,EAAE,MAAM;oBACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;kBAC1D,CAAE;kBAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;oBAAClE,SAAS,EAAC,iBAAiB;oBAAAG,QAAA,gBACpC3C,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAG,QAAA,eACnB3C,OAAA;wBACEwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACL8B,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACI,OAAO,YAAY;0BACjEkF,YAAY,EAAE;wBAChB,CAAE;wBAAAvD,QAAA,eAEF3C,OAAA,CAACtB,KAAK;0BAAC8D,SAAS,EAAC,YAAY;0BAACI,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhD,OAAA;sBAAIwC,SAAS,EAAC,MAAM;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACU;sBAAc,CAAE;sBAAAqB,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEhD,OAAA;sBAAIwC,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,EAAEY;oBAAW;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACzEhD,OAAA;sBAAOyC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACI;sBAAQ,CAAE;sBAAA2B,QAAA,gBACtC3C,OAAA,CAACjB,QAAQ;wBAAC6D,IAAI,EAAE,EAAG;wBAACJ,SAAS,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,mBAEzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhD,OAAA,CAAC9B,GAAG;cAACiJ,EAAE,EAAE,CAAE;cAAAxE,QAAA,eACT3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;gBACT2B,OAAO,EAAE;kBAAEpB,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmB,KAAK,EAAE;gBAAI,CAAE;gBAC1CV,UAAU,EAAE;kBAAEb,CAAC,EAAE,CAAC,CAAC;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAAAxC,QAAA,eAEnC3C,OAAA,CAAC7B,IAAI;kBACHqE,SAAS,EAAC,0BAA0B;kBACpCC,KAAK,EAAE;oBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;oBAC1B8E,YAAY,EAAE,MAAM;oBACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;kBAC1D,CAAE;kBAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;oBAAClE,SAAS,EAAC,iBAAiB;oBAAAG,QAAA,gBACpC3C,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAG,QAAA,eACnB3C,OAAA;wBACEwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACL8B,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACM,OAAO,YAAY;0BACjEgF,YAAY,EAAE;wBAChB,CAAE;wBAAAvD,QAAA,eAEF3C,OAAA,CAACrB,WAAW;0BAAC6D,SAAS,EAAC,YAAY;0BAACI,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhD,OAAA;sBAAIwC,SAAS,EAAC,MAAM;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACU;sBAAc,CAAE;sBAAAqB,QAAA,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3EhD,OAAA;sBAAIwC,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,EAAEiB;oBAAa;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3EhD,OAAA;sBAAOyC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACM;sBAAQ,CAAE;sBAAAyB,QAAA,gBACtC3C,OAAA,CAACf,IAAI;wBAAC2D,IAAI,EAAE,EAAG;wBAACJ,SAAS,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,iBAErC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhD,OAAA,CAAC9B,GAAG;cAACiJ,EAAE,EAAE,CAAE;cAAAxE,QAAA,eACT3C,OAAA,CAACxB,MAAM,CAAC8F,GAAG;gBACT2B,OAAO,EAAE;kBAAEpB,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAI,CAAE;gBACpCJ,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAEM,KAAK,EAAE;gBAAE,CAAE;gBAClCC,UAAU,EAAE;kBAAEC,QAAQ,EAAE,GAAG;kBAAEmB,KAAK,EAAE;gBAAI,CAAE;gBAC1CV,UAAU,EAAE;kBAAEb,CAAC,EAAE,CAAC,CAAC;kBAAEE,KAAK,EAAE;gBAAK,CAAE;gBAAAxC,QAAA,eAEnC3C,OAAA,CAAC7B,IAAI;kBACHqE,SAAS,EAAC,0BAA0B;kBACpCC,KAAK,EAAE;oBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;oBAC1B8E,YAAY,EAAE,MAAM;oBACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;kBAC1D,CAAE;kBAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;oBAAClE,SAAS,EAAC,iBAAiB;oBAAAG,QAAA,gBACpC3C,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAG,QAAA,eACnB3C,OAAA;wBACEwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACL8B,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACG,MAAM,YAAY;0BAChEmF,YAAY,EAAE;wBAChB,CAAE;wBAAAvD,QAAA,eAEF3C,OAAA,CAACpB,UAAU;0BAAC4D,SAAS,EAAC,YAAY;0BAACI,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhD,OAAA;sBAAIwC,SAAS,EAAC,MAAM;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACU;sBAAc,CAAE;sBAAAqB,QAAA,EAAC;oBAAO;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACzEhD,OAAA;sBAAIwC,SAAS,EAAC,SAAS;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,GAAC,GAAC,EAACkB,YAAY,CAACuD,OAAO,CAAC,CAAC,CAAC;oBAAA;sBAAAvE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtFhD,OAAA;sBAAOyC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACG;sBAAO,CAAE;sBAAA4B,QAAA,gBACrC3C,OAAA,CAACT,UAAU;wBAACqD,IAAI,EAAE,EAAG;wBAACJ,SAAS,EAAC;sBAAM;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,cAE3C;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGNhD,OAAA,CAAC9B,GAAG;cAACmJ,EAAE,EAAE,EAAG;cAAA1E,QAAA,eACV3C,OAAA,CAAC7B,IAAI;gBACHqE,SAAS,EAAC,oBAAoB;gBAC9BC,KAAK,EAAE;kBACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;kBAC1B8E,YAAY,EAAE,MAAM;kBACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;gBAC1D,CAAE;gBAAAuC,QAAA,gBAEF3C,OAAA,CAAC7B,IAAI,CAACmJ,MAAM;kBAAC9E,SAAS,EAAC,yBAAyB;kBAAAG,QAAA,eAC9C3C,OAAA;oBAAKwC,SAAS,EAAC,mDAAmD;oBAAAG,QAAA,gBAChE3C,OAAA;sBAAIwC,SAAS,EAAC,cAAc;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,gBACzD3C,OAAA,CAACjB,QAAQ;wBAACyD,SAAS,EAAC,MAAM;wBAACI,IAAI,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAEzC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACLhD,OAAA,CAAC3B,KAAK;sBACJmE,SAAS,EAAC,WAAW;sBACrBC,KAAK,EAAE;wBACLtB,UAAU,EAAE,GAAGP,MAAM,CAACC,OAAO,IAAI;wBACjCuB,KAAK,EAAExB,MAAM,CAACC;sBAChB,CAAE;sBAAA8B,QAAA,GAEDnC,IAAI,CAAC8C,MAAM,EAAC,QACf;oBAAA;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACdhD,OAAA,CAAC7B,IAAI,CAACuI,IAAI;kBAAA/D,QAAA,EACPnC,IAAI,CAAC8C,MAAM,GAAG,CAAC,gBACdtD,OAAA;oBAAKwC,SAAS,EAAC,kBAAkB;oBAAAG,QAAA,eAC/B3C,OAAA,CAACzB,KAAK;sBAACiE,SAAS,EAAEpC,UAAU,GAAG,YAAY,GAAG,EAAG;sBAACmH,KAAK;sBAAA5E,QAAA,gBACrD3C,OAAA;wBAAA2C,QAAA,eACE3C,OAAA;0BAAA2C,QAAA,gBACE3C,OAAA;4BAAA2C,QAAA,EAAI;0BAAU;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnBhD,OAAA;4BAAA2C,QAAA,EAAI;0BAAO;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChBhD,OAAA;4BAAA2C,QAAA,EAAI;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAClBhD,OAAA;4BAAA2C,QAAA,EAAI;0BAAM;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACfhD,OAAA;4BAAA2C,QAAA,EAAI;0BAAI;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACbhD,OAAA;4BAAA2C,QAAA,EAAI;0BAAO;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACd;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACRhD,OAAA;wBAAA2C,QAAA,EACGnC,IAAI,CAACgH,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACV,GAAG,CAAErD,GAAa,iBACnCzD,OAAA;0BAAA2C,QAAA,gBACE3C,OAAA;4BAAIwC,SAAS,EAAC,aAAa;4BAAAG,QAAA,EAAEc,GAAG,CAACgE;0BAAS;4BAAA5E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAChDhD,OAAA;4BAAA2C,QAAA,EAAKc,GAAG,CAACiE;0BAAW;4BAAA7E,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eAC1BhD,OAAA;4BAAA2C,QAAA,EAAKc,GAAG,CAACZ,QAAQ,CAACS,MAAM,GAAG,EAAE,GAAGG,GAAG,CAACZ,QAAQ,CAAC2E,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG/D,GAAG,CAACZ;0BAAQ;4BAAAA,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACtFhD,OAAA;4BAAA2C,QAAA,EAAKX,cAAc,CAACyB,GAAG,CAACxB,MAAM;0BAAC;4BAAAY,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACrChD,OAAA;4BAAIwC,SAAS,EAAC,aAAa;4BAACC,KAAK,EAAE;8BAAEL,KAAK,EAAExB,MAAM,CAACM;4BAAQ,CAAE;4BAAAyB,QAAA,EAC1Dc,GAAG,CAACO,IAAI,GAAG,IAAIP,GAAG,CAACO,IAAI,CAACoD,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG;0BAAG;4BAAAvE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzC,CAAC,eACLhD,OAAA;4BAAA2C,QAAA,eACE3C,OAAA;8BAAKwC,SAAS,EAAC,cAAc;8BAAAG,QAAA,gBAC3B3C,OAAA,CAAC5B,MAAM;gCACLuI,OAAO,EAAC,iBAAiB;gCACzB/D,IAAI,EAAC,IAAI;gCACTH,KAAK,EAAE;kCAAEyD,YAAY,EAAE;gCAAM,CAAE;gCAAAvD,QAAA,eAE/B3C,OAAA,CAAClB,GAAG;kCAAC8D,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACX,CAAC,eACThD,OAAA,CAAC5B,MAAM;gCACLuI,OAAO,EAAC,iBAAiB;gCACzB/D,IAAI,EAAC,IAAI;gCACTH,KAAK,EAAE;kCAAEyD,YAAY,EAAE;gCAAM,CAAE;gCAC/BN,OAAO,EAAEA,CAAA,KAAM3C,kBAAkB,CAACQ,GAAG,CAACkE,EAAE,EAAE,YAAY,CAAE;gCAAAhF,QAAA,eAExD3C,OAAA,CAACN,IAAI;kCAACkD,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACZ,CAAC,eACThD,OAAA,CAAC5B,MAAM;gCACLuI,OAAO,EAAC,cAAc;gCACtB/D,IAAI,EAAC,IAAI;gCACTH,KAAK,EAAE;kCAAEyD,YAAY,EAAE;gCAAM,CAAE;gCAAAvD,QAAA,eAE/B3C,OAAA,CAACnB,aAAa;kCAAC+D,IAAI,EAAE;gCAAG;kCAAAC,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACrB,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACN;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACJ,CAAC;wBAAA,GAjCES,GAAG,CAACkE,EAAE;0BAAA9E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAkCX,CACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENhD,OAAA,CAACxB,MAAM,CAAC8F,GAAG;oBACT2B,OAAO,EAAE;sBAAEpB,OAAO,EAAE,CAAC;sBAAEM,KAAK,EAAE;oBAAI,CAAE;oBACpCJ,OAAO,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEM,KAAK,EAAE;oBAAE,CAAE;oBAClC3C,SAAS,EAAC,kBAAkB;oBAAAG,QAAA,gBAE5B3C,OAAA;sBAAKwC,SAAS,EAAC,MAAM;sBAAAG,QAAA,eACnB3C,OAAA;wBACEwC,SAAS,EAAC,yDAAyD;wBACnEC,KAAK,EAAE;0BACL8B,KAAK,EAAE,MAAM;0BACbG,MAAM,EAAE,MAAM;0BACdvD,UAAU,EAAE,2BAA2BP,MAAM,CAACC,OAAO,KAAKD,MAAM,CAACG,MAAM,GAAG;0BAC1EmF,YAAY,EAAE;wBAChB,CAAE;wBAAAvD,QAAA,eAEF3C,OAAA,CAACvB,QAAQ;0BAAC+D,SAAS,EAAC,YAAY;0BAACI,IAAI,EAAE;wBAAG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACNhD,OAAA;sBAAIwC,SAAS,EAAC,kBAAkB;sBAACC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACS;sBAAK,CAAE;sBAAAsB,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChFhD,OAAA;sBAAGyC,KAAK,EAAE;wBAAEL,KAAK,EAAExB,MAAM,CAACU;sBAAc,CAAE;sBAAAqB,QAAA,EAAC;oBAAgD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACb;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA1C,SAAS,KAAK,WAAW,iBACxBN,OAAA,CAAC7B,IAAI;YACHqE,SAAS,EAAC,oBAAoB;YAC9BC,KAAK,EAAE;cACLtB,UAAU,EAAEP,MAAM,CAACQ,OAAO;cAC1B8E,YAAY,EAAE,MAAM;cACpBL,SAAS,EAAEzF,UAAU,GAAG,6BAA6B,GAAG;YAC1D,CAAE;YAAAuC,QAAA,eAEF3C,OAAA,CAAC7B,IAAI,CAACuI,IAAI;cAAClE,SAAS,EAAC,kBAAkB;cAAAG,QAAA,gBACrC3C,OAAA;gBAAIwC,SAAS,EAAC,MAAM;gBAACC,KAAK,EAAE;kBAAEL,KAAK,EAAExB,MAAM,CAACS;gBAAK,CAAE;gBAAAsB,QAAA,GAChDrC,SAAS,CAACsH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvH,SAAS,CAACkH,KAAK,CAAC,CAAC,CAAC,EAAC,MAC1D;cAAA;gBAAA3E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLhD,OAAA;gBAAGyC,KAAK,EAAE;kBAAEL,KAAK,EAAExB,MAAM,CAACU;gBAAc,CAAE;gBAAAqB,QAAA,EAAC;cAE3C;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CACP;QAAA,GA1SI1C,SAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2SJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;AAAC9C,EAAA,CAzkBID,8BAAwC;EAAA,QAC3BL,OAAO,EACYC,QAAQ;AAAA;AAAAiI,EAAA,GAFxC7H,8BAAwC;AA2kB9C,eAAeA,8BAA8B;AAAC,IAAA6H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}