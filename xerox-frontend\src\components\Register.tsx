import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert, Spinner, Nav } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth, StudentRegistrationData, XeroxCenterRegistrationData } from '../contexts/AuthContext';

const Register: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'student' | 'xerox'>('student');
  const [studentData, setStudentData] = useState<StudentRegistrationData>({
    username: '',
    email: '',
    password: '',
    studentNumber: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    department: '',
    year: undefined
  });
  const [xeroxData, setXeroxData] = useState<XeroxCenterRegistrationData>({
    username: '',
    email: '',
    password: '',
    xeroxCenterName: '',
    location: '',
    contactPerson: '',
    phoneNumber: '',
    description: ''
  });

  const { registerStudent, registerXeroxCenter, isLoading, error, user } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const handleStudentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await registerStudent(studentData);
    if (success) {
      navigate('/dashboard');
    }
  };

  const handleXeroxSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await registerXeroxCenter(xeroxData);
    if (success) {
      navigate('/dashboard');
    }
  };

  const handleStudentChange = (field: keyof StudentRegistrationData, value: string | number) => {
    setStudentData(prev => ({ ...prev, [field]: value }));
  };

  const handleXeroxChange = (field: keyof XeroxCenterRegistrationData, value: string) => {
    setXeroxData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Container className="mt-5">
      <Row className="justify-content-center">
        <Col md={8} lg={6}>
          <Card className="shadow">
            <Card.Body className="p-4">
              <div className="text-center mb-4">
                <i className="fas fa-user-plus fa-3x text-primary mb-3"></i>
                <h2 className="h4 text-gray-900 mb-4">Create Account</h2>
              </div>

              <Nav variant="tabs" className="mb-4">
                <Nav.Item>
                  <Nav.Link 
                    active={activeTab === 'student'} 
                    onClick={() => setActiveTab('student')}
                  >
                    <i className="fas fa-graduation-cap me-2"></i>
                    Student
                  </Nav.Link>
                </Nav.Item>
                <Nav.Item>
                  <Nav.Link 
                    active={activeTab === 'xerox'} 
                    onClick={() => setActiveTab('xerox')}
                  >
                    <i className="fas fa-store me-2"></i>
                    Xerox Center
                  </Nav.Link>
                </Nav.Item>
              </Nav>

              {error && (
                <Alert variant="danger" className="mb-3">
                  <i className="fas fa-exclamation-triangle me-2"></i>
                  {error}
                </Alert>
              )}

              {activeTab === 'student' ? (
                <Form onSubmit={handleStudentSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Username</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter username"
                          value={studentData.username}
                          onChange={(e) => handleStudentChange('username', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Student Number</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter student number"
                          value={studentData.studentNumber}
                          onChange={(e) => handleStudentChange('studentNumber', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Email Address</Form.Label>
                    <Form.Control
                      type="email"
                      placeholder="Enter email"
                      value={studentData.email}
                      onChange={(e) => handleStudentChange('email', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      placeholder="Password"
                      value={studentData.password}
                      onChange={(e) => handleStudentChange('password', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>First Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="First name"
                          value={studentData.firstName}
                          onChange={(e) => handleStudentChange('firstName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Last Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Last name"
                          value={studentData.lastName}
                          onChange={(e) => handleStudentChange('lastName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Department</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Department (optional)"
                          value={studentData.department}
                          onChange={(e) => handleStudentChange('department', e.target.value)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Year</Form.Label>
                        <Form.Control
                          type="number"
                          placeholder="Year (optional)"
                          value={studentData.year || ''}
                          onChange={(e) => handleStudentChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Phone Number</Form.Label>
                    <Form.Control
                      type="tel"
                      placeholder="Phone number (optional)"
                      value={studentData.phoneNumber}
                      onChange={(e) => handleStudentChange('phoneNumber', e.target.value)}
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button 
                    variant="primary" 
                    type="submit" 
                    className="w-100 mb-3"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-user-plus me-2"></i>
                        Register as Student
                      </>
                    )}
                  </Button>
                </Form>
              ) : (
                <Form onSubmit={handleXeroxSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Username</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Enter username"
                          value={xeroxData.username}
                          onChange={(e) => handleXeroxChange('username', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Xerox Center Name</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Center name"
                          value={xeroxData.xeroxCenterName}
                          onChange={(e) => handleXeroxChange('xeroxCenterName', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Email Address</Form.Label>
                    <Form.Control
                      type="email"
                      placeholder="Enter email"
                      value={xeroxData.email}
                      onChange={(e) => handleXeroxChange('email', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Password</Form.Label>
                    <Form.Control
                      type="password"
                      placeholder="Password"
                      value={xeroxData.password}
                      onChange={(e) => handleXeroxChange('password', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Location</Form.Label>
                    <Form.Control
                      type="text"
                      placeholder="Center location"
                      value={xeroxData.location}
                      onChange={(e) => handleXeroxChange('location', e.target.value)}
                      required
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Contact Person</Form.Label>
                        <Form.Control
                          type="text"
                          placeholder="Contact person (optional)"
                          value={xeroxData.contactPerson}
                          onChange={(e) => handleXeroxChange('contactPerson', e.target.value)}
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Phone Number</Form.Label>
                        <Form.Control
                          type="tel"
                          placeholder="Phone number"
                          value={xeroxData.phoneNumber}
                          onChange={(e) => handleXeroxChange('phoneNumber', e.target.value)}
                          required
                          disabled={isLoading}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      placeholder="Center description (optional)"
                      value={xeroxData.description}
                      onChange={(e) => handleXeroxChange('description', e.target.value)}
                      disabled={isLoading}
                    />
                  </Form.Group>

                  <Button 
                    variant="primary" 
                    type="submit" 
                    className="w-100 mb-3"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Spinner
                          as="span"
                          animation="border"
                          size="sm"
                          role="status"
                          aria-hidden="true"
                          className="me-2"
                        />
                        Creating Account...
                      </>
                    ) : (
                      <>
                        <i className="fas fa-store me-2"></i>
                        Register as Xerox Center
                      </>
                    )}
                  </Button>
                </Form>
              )}

              <hr />

              <div className="text-center">
                <Link to="/login" className="small">
                  Already have an account? Login!
                </Link>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default Register;
