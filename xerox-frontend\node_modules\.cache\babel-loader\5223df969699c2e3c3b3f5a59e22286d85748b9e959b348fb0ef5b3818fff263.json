{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { statsBuffer, isSVGElement, isSVGSVGElement, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, frame, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\nconst metrics = {\n  nodes: 0,\n  calculatedTargetDeltas: 0,\n  calculatedProjections: 0\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = {\n  visibility: \"hidden\"\n};\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n  const {\n    latestValues\n  } = visualElement;\n  // Record the distorting transform and then temporarily set it to 0\n  if (latestValues[key]) {\n    values[key] = latestValues[key];\n    visualElement.setStaticValue(key, 0);\n    if (sharedAnimationValues) {\n      sharedAnimationValues[key] = 0;\n    }\n  }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n  projectionNode.hasCheckedOptimisedAppear = true;\n  if (projectionNode.root === projectionNode) return;\n  const {\n    visualElement\n  } = projectionNode.options;\n  if (!visualElement) return;\n  const appearId = getOptimisedAppearId(visualElement);\n  if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n    const {\n      layout,\n      layoutId\n    } = projectionNode.options;\n    window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n  }\n  const {\n    parent\n  } = projectionNode;\n  if (parent && !parent.hasCheckedOptimisedAppear) {\n    cancelTreeOptimisedTransformAnimations(parent);\n  }\n}\nfunction createProjectionNode(_ref) {\n  let {\n    attachResizeListener,\n    defaultParent,\n    measureScroll,\n    checkIsScrollRoot,\n    resetTransform\n  } = _ref;\n  return class ProjectionNode {\n    constructor() {\n      let latestValues = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      let parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultParent === null || defaultParent === void 0 ? void 0 : defaultParent();\n      /**\n       * A unique ID generated for every projection node.\n       */\n      this.id = id++;\n      /**\n       * An id that represents a unique session instigated by startUpdate.\n       */\n      this.animationId = 0;\n      /**\n       * A Set containing all this component's children. This is used to iterate\n       * through the children.\n       *\n       * TODO: This could be faster to iterate as a flat array stored on the root node.\n       */\n      this.children = new Set();\n      /**\n       * Options for the node. We use this to configure what kind of layout animations\n       * we should perform (if any).\n       */\n      this.options = {};\n      /**\n       * We use this to detect when its safe to shut down part of a projection tree.\n       * We have to keep projecting children for scale correction and relative projection\n       * until all their parents stop performing layout animations.\n       */\n      this.isTreeAnimating = false;\n      this.isAnimationBlocked = false;\n      /**\n       * Flag to true if we think this layout has been changed. We can't always know this,\n       * currently we set it to true every time a component renders, or if it has a layoutDependency\n       * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n       * and if one node is dirtied, they all are.\n       */\n      this.isLayoutDirty = false;\n      /**\n       * Flag to true if we think the projection calculations for this node needs\n       * recalculating as a result of an updated transform or layout animation.\n       */\n      this.isProjectionDirty = false;\n      /**\n       * Flag to true if the layout *or* transform has changed. This then gets propagated\n       * throughout the projection tree, forcing any element below to recalculate on the next frame.\n       */\n      this.isSharedProjectionDirty = false;\n      /**\n       * Flag transform dirty. This gets propagated throughout the whole tree but is only\n       * respected by shared nodes.\n       */\n      this.isTransformDirty = false;\n      /**\n       * Block layout updates for instant layout transitions throughout the tree.\n       */\n      this.updateManuallyBlocked = false;\n      this.updateBlockedByResize = false;\n      /**\n       * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n       * call.\n       */\n      this.isUpdating = false;\n      /**\n       * If this is an SVG element we currently disable projection transforms\n       */\n      this.isSVG = false;\n      /**\n       * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n       * its projection styles.\n       */\n      this.needsReset = false;\n      /**\n       * Flags whether this node should have its transform reset prior to measuring.\n       */\n      this.shouldResetTransform = false;\n      /**\n       * Store whether this node has been checked for optimised appear animations. As\n       * effects fire bottom-up, and we want to look up the tree for appear animations,\n       * this makes sure we only check each path once, stopping at nodes that\n       * have already been checked.\n       */\n      this.hasCheckedOptimisedAppear = false;\n      /**\n       * An object representing the calculated contextual/accumulated/tree scale.\n       * This will be used to scale calculcated projection transforms, as these are\n       * calculated in screen-space but need to be scaled for elements to layoutly\n       * make it to their calculated destinations.\n       *\n       * TODO: Lazy-init\n       */\n      this.treeScale = {\n        x: 1,\n        y: 1\n      };\n      /**\n       *\n       */\n      this.eventHandlers = new Map();\n      this.hasTreeAnimated = false;\n      // Note: Currently only running on root node\n      this.updateScheduled = false;\n      this.scheduleUpdate = () => this.update();\n      this.projectionUpdateScheduled = false;\n      this.checkUpdateFailed = () => {\n        if (this.isUpdating) {\n          this.isUpdating = false;\n          this.clearAllSnapshots();\n        }\n      };\n      /**\n       * This is a multi-step process as shared nodes might be of different depths. Nodes\n       * are sorted by depth order, so we need to resolve the entire tree before moving to\n       * the next step.\n       */\n      this.updateProjection = () => {\n        this.projectionUpdateScheduled = false;\n        /**\n         * Reset debug counts. Manually resetting rather than creating a new\n         * object each frame.\n         */\n        if (statsBuffer.value) {\n          metrics.nodes = metrics.calculatedTargetDeltas = metrics.calculatedProjections = 0;\n        }\n        this.nodes.forEach(propagateDirtyNodes);\n        this.nodes.forEach(resolveTargetDelta);\n        this.nodes.forEach(calcProjection);\n        this.nodes.forEach(cleanDirtyNodes);\n        if (statsBuffer.addProjectionMetrics) {\n          statsBuffer.addProjectionMetrics(metrics);\n        }\n      };\n      /**\n       * Frame calculations\n       */\n      this.resolvedRelativeTargetAt = 0.0;\n      this.hasProjected = false;\n      this.isVisible = true;\n      this.animationProgress = 0;\n      /**\n       * Shared layout\n       */\n      // TODO Only running on root node\n      this.sharedNodes = new Map();\n      this.latestValues = latestValues;\n      this.root = parent ? parent.root || parent : this;\n      this.path = parent ? [...parent.path, parent] : [];\n      this.parent = parent;\n      this.depth = parent ? parent.depth + 1 : 0;\n      for (let i = 0; i < this.path.length; i++) {\n        this.path[i].shouldResetTransform = true;\n      }\n      if (this.root === this) this.nodes = new FlatTree();\n    }\n    addEventListener(name, handler) {\n      if (!this.eventHandlers.has(name)) {\n        this.eventHandlers.set(name, new SubscriptionManager());\n      }\n      return this.eventHandlers.get(name).add(handler);\n    }\n    notifyListeners(name) {\n      const subscriptionManager = this.eventHandlers.get(name);\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      subscriptionManager && subscriptionManager.notify(...args);\n    }\n    hasListeners(name) {\n      return this.eventHandlers.has(name);\n    }\n    /**\n     * Lifecycles\n     */\n    mount(instance) {\n      if (this.instance) return;\n      this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n      this.instance = instance;\n      const {\n        layoutId,\n        layout,\n        visualElement\n      } = this.options;\n      if (visualElement && !visualElement.current) {\n        visualElement.mount(instance);\n      }\n      this.root.nodes.add(this);\n      this.parent && this.parent.children.add(this);\n      if (this.root.hasTreeAnimated && (layout || layoutId)) {\n        this.isLayoutDirty = true;\n      }\n      if (attachResizeListener) {\n        let cancelDelay;\n        const resizeUnblockUpdate = () => this.root.updateBlockedByResize = false;\n        attachResizeListener(instance, () => {\n          this.root.updateBlockedByResize = true;\n          cancelDelay && cancelDelay();\n          cancelDelay = delay(resizeUnblockUpdate, 250);\n          if (globalProjectionState.hasAnimatedSinceResize) {\n            globalProjectionState.hasAnimatedSinceResize = false;\n            this.nodes.forEach(finishAnimation);\n          }\n        });\n      }\n      if (layoutId) {\n        this.root.registerSharedNode(layoutId, this);\n      }\n      // Only register the handler if it requires layout animation\n      if (this.options.animate !== false && visualElement && (layoutId || layout)) {\n        this.addEventListener(\"didUpdate\", _ref2 => {\n          let {\n            delta,\n            hasLayoutChanged,\n            hasRelativeLayoutChanged,\n            layout: newLayout\n          } = _ref2;\n          if (this.isTreeAnimationBlocked()) {\n            this.target = undefined;\n            this.relativeTarget = undefined;\n            return;\n          }\n          // TODO: Check here if an animation exists\n          const layoutTransition = this.options.transition || visualElement.getDefaultTransition() || defaultLayoutTransition;\n          const {\n            onLayoutAnimationStart,\n            onLayoutAnimationComplete\n          } = visualElement.getProps();\n          /**\n           * The target layout of the element might stay the same,\n           * but its position relative to its parent has changed.\n           */\n          const hasTargetChanged = !this.targetLayout || !boxEqualsRounded(this.targetLayout, newLayout);\n          /*\n           * Note: Disabled to fix relative animations always triggering new\n           * layout animations. If this causes further issues, we can try\n           * a different approach to detecting relative target changes.\n           */\n          // || hasRelativeLayoutChanged\n          /**\n           * If the layout hasn't seemed to have changed, it might be that the\n           * element is visually in the same place in the document but its position\n           * relative to its parent has indeed changed. So here we check for that.\n           */\n          const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n          if (this.options.layoutRoot || this.resumeFrom || hasOnlyRelativeTargetChanged || hasLayoutChanged && (hasTargetChanged || !this.currentAnimation)) {\n            if (this.resumeFrom) {\n              this.resumingFrom = this.resumeFrom;\n              this.resumingFrom.resumingFrom = undefined;\n            }\n            const animationOptions = _objectSpread(_objectSpread({}, getValueTransition(layoutTransition, \"layout\")), {}, {\n              onPlay: onLayoutAnimationStart,\n              onComplete: onLayoutAnimationComplete\n            });\n            if (visualElement.shouldReduceMotion || this.options.layoutRoot) {\n              animationOptions.delay = 0;\n              animationOptions.type = false;\n            }\n            this.startAnimation(animationOptions);\n            /**\n             * Set animation origin after starting animation to avoid layout jump\n             * caused by stopping previous layout animation\n             */\n            this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n          } else {\n            /**\n             * If the layout hasn't changed and we have an animation that hasn't started yet,\n             * finish it immediately. Otherwise it will be animating from a location\n             * that was probably never commited to screen and look like a jumpy box.\n             */\n            if (!hasLayoutChanged) {\n              finishAnimation(this);\n            }\n            if (this.isLead() && this.options.onExitComplete) {\n              this.options.onExitComplete();\n            }\n          }\n          this.targetLayout = newLayout;\n        });\n      }\n    }\n    unmount() {\n      this.options.layoutId && this.willUpdate();\n      this.root.nodes.remove(this);\n      const stack = this.getStack();\n      stack && stack.remove(this);\n      this.parent && this.parent.children.delete(this);\n      this.instance = undefined;\n      this.eventHandlers.clear();\n      cancelFrame(this.updateProjection);\n    }\n    // only on the root\n    blockUpdate() {\n      this.updateManuallyBlocked = true;\n    }\n    unblockUpdate() {\n      this.updateManuallyBlocked = false;\n    }\n    isUpdateBlocked() {\n      return this.updateManuallyBlocked || this.updateBlockedByResize;\n    }\n    isTreeAnimationBlocked() {\n      return this.isAnimationBlocked || this.parent && this.parent.isTreeAnimationBlocked() || false;\n    }\n    // Note: currently only running on root node\n    startUpdate() {\n      if (this.isUpdateBlocked()) return;\n      this.isUpdating = true;\n      this.nodes && this.nodes.forEach(resetSkewAndRotation);\n      this.animationId++;\n    }\n    getTransformTemplate() {\n      const {\n        visualElement\n      } = this.options;\n      return visualElement && visualElement.getProps().transformTemplate;\n    }\n    willUpdate() {\n      let shouldNotifyListeners = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      this.root.hasTreeAnimated = true;\n      if (this.root.isUpdateBlocked()) {\n        this.options.onExitComplete && this.options.onExitComplete();\n        return;\n      }\n      /**\n       * If we're running optimised appear animations then these must be\n       * cancelled before measuring the DOM. This is so we can measure\n       * the true layout of the element rather than the WAAPI animation\n       * which will be unaffected by the resetSkewAndRotate step.\n       *\n       * Note: This is a DOM write. Worst case scenario is this is sandwiched\n       * between other snapshot reads which will cause unnecessary style recalculations.\n       * This has to happen here though, as we don't yet know which nodes will need\n       * snapshots in startUpdate(), but we only want to cancel optimised animations\n       * if a layout animation measurement is actually going to be affected by them.\n       */\n      if (window.MotionCancelOptimisedAnimation && !this.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(this);\n      }\n      !this.root.isUpdating && this.root.startUpdate();\n      if (this.isLayoutDirty) return;\n      this.isLayoutDirty = true;\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        node.shouldResetTransform = true;\n        node.updateScroll(\"snapshot\");\n        if (node.options.layoutRoot) {\n          node.willUpdate(false);\n        }\n      }\n      const {\n        layoutId,\n        layout\n      } = this.options;\n      if (layoutId === undefined && !layout) return;\n      const transformTemplate = this.getTransformTemplate();\n      this.prevTransformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      this.updateSnapshot();\n      shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n    }\n    update() {\n      this.updateScheduled = false;\n      const updateWasBlocked = this.isUpdateBlocked();\n      // When doing an instant transition, we skip the layout update,\n      // but should still clean up the measurements so that the next\n      // snapshot could be taken correctly.\n      if (updateWasBlocked) {\n        this.unblockUpdate();\n        this.clearAllSnapshots();\n        this.nodes.forEach(clearMeasurements);\n        return;\n      }\n      if (!this.isUpdating) {\n        this.nodes.forEach(clearIsLayoutDirty);\n      }\n      this.isUpdating = false;\n      /**\n       * Write\n       */\n      this.nodes.forEach(resetTransformStyle);\n      /**\n       * Read ==================\n       */\n      // Update layout measurements of updated children\n      this.nodes.forEach(updateLayout);\n      /**\n       * Write\n       */\n      // Notify listeners that the layout is updated\n      this.nodes.forEach(notifyLayoutUpdate);\n      this.clearAllSnapshots();\n      /**\n       * Manually flush any pending updates. Ideally\n       * we could leave this to the following requestAnimationFrame but this seems\n       * to leave a flash of incorrectly styled content.\n       */\n      const now = time.now();\n      frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n      frameData.timestamp = now;\n      frameData.isProcessing = true;\n      frameSteps.update.process(frameData);\n      frameSteps.preRender.process(frameData);\n      frameSteps.render.process(frameData);\n      frameData.isProcessing = false;\n    }\n    didUpdate() {\n      if (!this.updateScheduled) {\n        this.updateScheduled = true;\n        microtask.read(this.scheduleUpdate);\n      }\n    }\n    clearAllSnapshots() {\n      this.nodes.forEach(clearSnapshot);\n      this.sharedNodes.forEach(removeLeadSnapshots);\n    }\n    scheduleUpdateProjection() {\n      if (!this.projectionUpdateScheduled) {\n        this.projectionUpdateScheduled = true;\n        frame.preRender(this.updateProjection, false, true);\n      }\n    }\n    scheduleCheckAfterUnmount() {\n      /**\n       * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n       * we manually call didUpdate to give a chance to the siblings to animate.\n       * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n       */\n      frame.postRender(() => {\n        if (this.isLayoutDirty) {\n          this.root.didUpdate();\n        } else {\n          this.root.checkUpdateFailed();\n        }\n      });\n    }\n    /**\n     * Update measurements\n     */\n    updateSnapshot() {\n      if (this.snapshot || !this.instance) return;\n      this.snapshot = this.measure();\n      if (this.snapshot && !calcLength(this.snapshot.measuredBox.x) && !calcLength(this.snapshot.measuredBox.y)) {\n        this.snapshot = undefined;\n      }\n    }\n    updateLayout() {\n      if (!this.instance) return;\n      // TODO: Incorporate into a forwarded scroll offset\n      this.updateScroll();\n      if (!(this.options.alwaysMeasureLayout && this.isLead()) && !this.isLayoutDirty) {\n        return;\n      }\n      /**\n       * When a node is mounted, it simply resumes from the prevLead's\n       * snapshot instead of taking a new one, but the ancestors scroll\n       * might have updated while the prevLead is unmounted. We need to\n       * update the scroll again to make sure the layout we measure is\n       * up to date.\n       */\n      if (this.resumeFrom && !this.resumeFrom.instance) {\n        for (let i = 0; i < this.path.length; i++) {\n          const node = this.path[i];\n          node.updateScroll();\n        }\n      }\n      const prevLayout = this.layout;\n      this.layout = this.measure(false);\n      this.layoutCorrected = createBox();\n      this.isLayoutDirty = false;\n      this.projectionDelta = undefined;\n      this.notifyListeners(\"measure\", this.layout.layoutBox);\n      const {\n        visualElement\n      } = this.options;\n      visualElement && visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n    }\n    updateScroll() {\n      let phase = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"measure\";\n      let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n      if (this.scroll && this.scroll.animationId === this.root.animationId && this.scroll.phase === phase) {\n        needsMeasurement = false;\n      }\n      if (needsMeasurement && this.instance) {\n        const isRoot = checkIsScrollRoot(this.instance);\n        this.scroll = {\n          animationId: this.root.animationId,\n          phase,\n          isRoot,\n          offset: measureScroll(this.instance),\n          wasRoot: this.scroll ? this.scroll.isRoot : isRoot\n        };\n      }\n    }\n    resetTransform() {\n      if (!resetTransform) return;\n      const isResetRequested = this.isLayoutDirty || this.shouldResetTransform || this.options.alwaysMeasureLayout;\n      const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n      const transformTemplate = this.getTransformTemplate();\n      const transformTemplateValue = transformTemplate ? transformTemplate(this.latestValues, \"\") : undefined;\n      const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n      if (isResetRequested && this.instance && (hasProjection || hasTransform(this.latestValues) || transformTemplateHasChanged)) {\n        resetTransform(this.instance, transformTemplateValue);\n        this.shouldResetTransform = false;\n        this.scheduleRender();\n      }\n    }\n    measure() {\n      let removeTransform = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      const pageBox = this.measurePageBox();\n      let layoutBox = this.removeElementScroll(pageBox);\n      /**\n       * Measurements taken during the pre-render stage\n       * still have transforms applied so we remove them\n       * via calculation.\n       */\n      if (removeTransform) {\n        layoutBox = this.removeTransform(layoutBox);\n      }\n      roundBox(layoutBox);\n      return {\n        animationId: this.root.animationId,\n        measuredBox: pageBox,\n        layoutBox,\n        latestValues: {},\n        source: this.id\n      };\n    }\n    measurePageBox() {\n      var _this$scroll;\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return createBox();\n      const box = visualElement.measureViewportBox();\n      const wasInScrollRoot = ((_this$scroll = this.scroll) === null || _this$scroll === void 0 ? void 0 : _this$scroll.wasRoot) || this.path.some(checkNodeWasScrollRoot);\n      if (!wasInScrollRoot) {\n        // Remove viewport scroll to give page-relative coordinates\n        const {\n          scroll\n        } = this.root;\n        if (scroll) {\n          translateAxis(box.x, scroll.offset.x);\n          translateAxis(box.y, scroll.offset.y);\n        }\n      }\n      return box;\n    }\n    removeElementScroll(box) {\n      var _this$scroll2;\n      const boxWithoutScroll = createBox();\n      copyBoxInto(boxWithoutScroll, box);\n      if ((_this$scroll2 = this.scroll) !== null && _this$scroll2 !== void 0 && _this$scroll2.wasRoot) {\n        return boxWithoutScroll;\n      }\n      /**\n       * Performance TODO: Keep a cumulative scroll offset down the tree\n       * rather than loop back up the path.\n       */\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        const {\n          scroll,\n          options\n        } = node;\n        if (node !== this.root && scroll && options.layoutScroll) {\n          /**\n           * If this is a new scroll root, we want to remove all previous scrolls\n           * from the viewport box.\n           */\n          if (scroll.wasRoot) {\n            copyBoxInto(boxWithoutScroll, box);\n          }\n          translateAxis(boxWithoutScroll.x, scroll.offset.x);\n          translateAxis(boxWithoutScroll.y, scroll.offset.y);\n        }\n      }\n      return boxWithoutScroll;\n    }\n    applyTransform(box) {\n      let transformOnly = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      const withTransforms = createBox();\n      copyBoxInto(withTransforms, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!transformOnly && node.options.layoutScroll && node.scroll && node !== node.root) {\n          transformBox(withTransforms, {\n            x: -node.scroll.offset.x,\n            y: -node.scroll.offset.y\n          });\n        }\n        if (!hasTransform(node.latestValues)) continue;\n        transformBox(withTransforms, node.latestValues);\n      }\n      if (hasTransform(this.latestValues)) {\n        transformBox(withTransforms, this.latestValues);\n      }\n      return withTransforms;\n    }\n    removeTransform(box) {\n      const boxWithoutTransform = createBox();\n      copyBoxInto(boxWithoutTransform, box);\n      for (let i = 0; i < this.path.length; i++) {\n        const node = this.path[i];\n        if (!node.instance) continue;\n        if (!hasTransform(node.latestValues)) continue;\n        hasScale(node.latestValues) && node.updateSnapshot();\n        const sourceBox = createBox();\n        const nodeBox = node.measurePageBox();\n        copyBoxInto(sourceBox, nodeBox);\n        removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n      }\n      if (hasTransform(this.latestValues)) {\n        removeBoxTransforms(boxWithoutTransform, this.latestValues);\n      }\n      return boxWithoutTransform;\n    }\n    setTargetDelta(delta) {\n      this.targetDelta = delta;\n      this.root.scheduleUpdateProjection();\n      this.isProjectionDirty = true;\n    }\n    setOptions(options) {\n      this.options = _objectSpread(_objectSpread(_objectSpread({}, this.options), options), {}, {\n        crossfade: options.crossfade !== undefined ? options.crossfade : true\n      });\n    }\n    clearMeasurements() {\n      this.scroll = undefined;\n      this.layout = undefined;\n      this.snapshot = undefined;\n      this.prevTransformTemplateValue = undefined;\n      this.targetDelta = undefined;\n      this.target = undefined;\n      this.isLayoutDirty = false;\n    }\n    forceRelativeParentToResolveTarget() {\n      if (!this.relativeParent) return;\n      /**\n       * If the parent target isn't up-to-date, force it to update.\n       * This is an unfortunate de-optimisation as it means any updating relative\n       * projection will cause all the relative parents to recalculate back\n       * up the tree.\n       */\n      if (this.relativeParent.resolvedRelativeTargetAt !== frameData.timestamp) {\n        this.relativeParent.resolveTargetDelta(true);\n      }\n    }\n    resolveTargetDelta() {\n      var _this$parent;\n      let forceRecalculation = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      /**\n       * Once the dirty status of nodes has been spread through the tree, we also\n       * need to check if we have a shared node of a different depth that has itself\n       * been dirtied.\n       */\n      const lead = this.getLead();\n      this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n      this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n      this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      /**\n       * We don't use transform for this step of processing so we don't\n       * need to check whether any nodes have changed transform.\n       */\n      const canSkip = !(forceRecalculation || isShared && this.isSharedProjectionDirty || this.isProjectionDirty || (_this$parent = this.parent) !== null && _this$parent !== void 0 && _this$parent.isProjectionDirty || this.attemptToResolveRelativeTarget || this.root.updateBlockedByResize);\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If we have no layout, we can't perform projection, so early return\n       */\n      if (!this.layout || !(layout || layoutId)) return;\n      this.resolvedRelativeTargetAt = frameData.timestamp;\n      /**\n       * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n       * a relativeParent. This will allow a component to perform scale correction\n       * even if no animation has started.\n       */\n      if (!this.targetDelta && !this.relativeTarget) {\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && relativeParent.layout && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * If we have no relative target or no target delta our target isn't valid\n       * for this frame.\n       */\n      if (!this.relativeTarget && !this.targetDelta) return;\n      /**\n       * Lazy-init target data structure\n       */\n      if (!this.target) {\n        this.target = createBox();\n        this.targetWithTransforms = createBox();\n      }\n      /**\n       * If we've got a relative box for this component, resolve it into a target relative to the parent.\n       */\n      if (this.relativeTarget && this.relativeTargetOrigin && this.relativeParent && this.relativeParent.target) {\n        this.forceRelativeParentToResolveTarget();\n        calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n        /**\n         * If we've only got a targetDelta, resolve it into a target\n         */\n      } else if (this.targetDelta) {\n        if (Boolean(this.resumingFrom)) {\n          // TODO: This is creating a new object every frame\n          this.target = this.applyTransform(this.layout.layoutBox);\n        } else {\n          copyBoxInto(this.target, this.layout.layoutBox);\n        }\n        applyBoxDelta(this.target, this.targetDelta);\n      } else {\n        /**\n         * If no target, use own layout as target\n         */\n        copyBoxInto(this.target, this.layout.layoutBox);\n      }\n      /**\n       * If we've been told to attempt to resolve a relative target, do so.\n       */\n      if (this.attemptToResolveRelativeTarget) {\n        this.attemptToResolveRelativeTarget = false;\n        const relativeParent = this.getClosestProjectingParent();\n        if (relativeParent && Boolean(relativeParent.resumingFrom) === Boolean(this.resumingFrom) && !relativeParent.options.layoutScroll && relativeParent.target && this.animationProgress !== 1) {\n          this.relativeParent = relativeParent;\n          this.forceRelativeParentToResolveTarget();\n          this.relativeTarget = createBox();\n          this.relativeTargetOrigin = createBox();\n          calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n          copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n        } else {\n          this.relativeParent = this.relativeTarget = undefined;\n        }\n      }\n      /**\n       * Increase debug counter for resolved target deltas\n       */\n      if (statsBuffer.value) {\n        metrics.calculatedTargetDeltas++;\n      }\n    }\n    getClosestProjectingParent() {\n      if (!this.parent || hasScale(this.parent.latestValues) || has2DTranslate(this.parent.latestValues)) {\n        return undefined;\n      }\n      if (this.parent.isProjecting()) {\n        return this.parent;\n      } else {\n        return this.parent.getClosestProjectingParent();\n      }\n    }\n    isProjecting() {\n      return Boolean((this.relativeTarget || this.targetDelta || this.options.layoutRoot) && this.layout);\n    }\n    calcProjection() {\n      var _this$parent2;\n      const lead = this.getLead();\n      const isShared = Boolean(this.resumingFrom) || this !== lead;\n      let canSkip = true;\n      /**\n       * If this is a normal layout animation and neither this node nor its nearest projecting\n       * is dirty then we can't skip.\n       */\n      if (this.isProjectionDirty || (_this$parent2 = this.parent) !== null && _this$parent2 !== void 0 && _this$parent2.isProjectionDirty) {\n        canSkip = false;\n      }\n      /**\n       * If this is a shared layout animation and this node's shared projection is dirty then\n       * we can't skip.\n       */\n      if (isShared && (this.isSharedProjectionDirty || this.isTransformDirty)) {\n        canSkip = false;\n      }\n      /**\n       * If we have resolved the target this frame we must recalculate the\n       * projection to ensure it visually represents the internal calculations.\n       */\n      if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n        canSkip = false;\n      }\n      if (canSkip) return;\n      const {\n        layout,\n        layoutId\n      } = this.options;\n      /**\n       * If this section of the tree isn't animating we can\n       * delete our target sources for the following frame.\n       */\n      this.isTreeAnimating = Boolean(this.parent && this.parent.isTreeAnimating || this.currentAnimation || this.pendingAnimation);\n      if (!this.isTreeAnimating) {\n        this.targetDelta = this.relativeTarget = undefined;\n      }\n      if (!this.layout || !(layout || layoutId)) return;\n      /**\n       * Reset the corrected box with the latest values from box, as we're then going\n       * to perform mutative operations on it.\n       */\n      copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n      /**\n       * Record previous tree scales before updating.\n       */\n      const prevTreeScaleX = this.treeScale.x;\n      const prevTreeScaleY = this.treeScale.y;\n      /**\n       * Apply all the parent deltas to this box to produce the corrected box. This\n       * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n       */\n      applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n      /**\n       * If this layer needs to perform scale correction but doesn't have a target,\n       * use the layout as the target.\n       */\n      if (lead.layout && !lead.target && (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n        lead.target = lead.layout.layoutBox;\n        lead.targetWithTransforms = createBox();\n      }\n      const {\n        target\n      } = lead;\n      if (!target) {\n        /**\n         * If we don't have a target to project into, but we were previously\n         * projecting, we want to remove the stored transform and schedule\n         * a render to ensure the elements reflect the removed transform.\n         */\n        if (this.prevProjectionDelta) {\n          this.createProjectionDeltas();\n          this.scheduleRender();\n        }\n        return;\n      }\n      if (!this.projectionDelta || !this.prevProjectionDelta) {\n        this.createProjectionDeltas();\n      } else {\n        copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n        copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n      }\n      /**\n       * Update the delta between the corrected box and the target box before user-set transforms were applied.\n       * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n       * for our layout reprojection, but still allow them to be scaled correctly by the user.\n       * It might be that to simplify this we may want to accept that user-set scale is also corrected\n       * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n       * to allow people to choose whether these styles are corrected based on just the\n       * layout reprojection or the final bounding box.\n       */\n      calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n      if (this.treeScale.x !== prevTreeScaleX || this.treeScale.y !== prevTreeScaleY || !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) || !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n        this.hasProjected = true;\n        this.scheduleRender();\n        this.notifyListeners(\"projectionUpdate\", target);\n      }\n      /**\n       * Increase debug counter for recalculated projections\n       */\n      if (statsBuffer.value) {\n        metrics.calculatedProjections++;\n      }\n    }\n    hide() {\n      this.isVisible = false;\n      // TODO: Schedule render\n    }\n    show() {\n      this.isVisible = true;\n      // TODO: Schedule render\n    }\n    scheduleRender() {\n      var _this$options$visualE;\n      let notifyAll = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n      (_this$options$visualE = this.options.visualElement) === null || _this$options$visualE === void 0 || _this$options$visualE.scheduleRender();\n      if (notifyAll) {\n        const stack = this.getStack();\n        stack && stack.scheduleRender();\n      }\n      if (this.resumingFrom && !this.resumingFrom.instance) {\n        this.resumingFrom = undefined;\n      }\n    }\n    createProjectionDeltas() {\n      this.prevProjectionDelta = createDelta();\n      this.projectionDelta = createDelta();\n      this.projectionDeltaWithTransform = createDelta();\n    }\n    setAnimationOrigin(delta) {\n      let hasOnlyRelativeTargetChanged = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n      const snapshot = this.snapshot;\n      const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n      const mixedValues = _objectSpread({}, this.latestValues);\n      const targetDelta = createDelta();\n      if (!this.relativeParent || !this.relativeParent.options.layoutRoot) {\n        this.relativeTarget = this.relativeTargetOrigin = undefined;\n      }\n      this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n      const relativeLayout = createBox();\n      const snapshotSource = snapshot ? snapshot.source : undefined;\n      const layoutSource = this.layout ? this.layout.source : undefined;\n      const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n      const stack = this.getStack();\n      const isOnlyMember = !stack || stack.members.length <= 1;\n      const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation && !isOnlyMember && this.options.crossfade === true && !this.path.some(hasOpacityCrossfade));\n      this.animationProgress = 0;\n      let prevRelativeTarget;\n      this.mixTargetDelta = latest => {\n        const progress = latest / 1000;\n        mixAxisDelta(targetDelta.x, delta.x, progress);\n        mixAxisDelta(targetDelta.y, delta.y, progress);\n        this.setTargetDelta(targetDelta);\n        if (this.relativeTarget && this.relativeTargetOrigin && this.layout && this.relativeParent && this.relativeParent.layout) {\n          calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n          mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n          /**\n           * If this is an unchanged relative target we can consider the\n           * projection not dirty.\n           */\n          if (prevRelativeTarget && boxEquals(this.relativeTarget, prevRelativeTarget)) {\n            this.isProjectionDirty = false;\n          }\n          if (!prevRelativeTarget) prevRelativeTarget = createBox();\n          copyBoxInto(prevRelativeTarget, this.relativeTarget);\n        }\n        if (isSharedLayoutAnimation) {\n          this.animationValues = mixedValues;\n          mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n        }\n        this.root.scheduleUpdateProjection();\n        this.scheduleRender();\n        this.animationProgress = progress;\n      };\n      this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n    }\n    startAnimation(options) {\n      var _this$currentAnimatio, _this$resumingFrom;\n      this.notifyListeners(\"animationStart\");\n      (_this$currentAnimatio = this.currentAnimation) === null || _this$currentAnimatio === void 0 || _this$currentAnimatio.stop();\n      (_this$resumingFrom = this.resumingFrom) === null || _this$resumingFrom === void 0 || (_this$resumingFrom = _this$resumingFrom.currentAnimation) === null || _this$resumingFrom === void 0 || _this$resumingFrom.stop();\n      if (this.pendingAnimation) {\n        cancelFrame(this.pendingAnimation);\n        this.pendingAnimation = undefined;\n      }\n      /**\n       * Start the animation in the next frame to have a frame with progress 0,\n       * where the target is the same as when the animation started, so we can\n       * calculate the relative positions correctly for instant transitions.\n       */\n      this.pendingAnimation = frame.update(() => {\n        globalProjectionState.hasAnimatedSinceResize = true;\n        activeAnimations.layout++;\n        this.motionValue || (this.motionValue = motionValue(0));\n        this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], _objectSpread(_objectSpread({}, options), {}, {\n          velocity: 0,\n          isSync: true,\n          onUpdate: latest => {\n            this.mixTargetDelta(latest);\n            options.onUpdate && options.onUpdate(latest);\n          },\n          onStop: () => {\n            activeAnimations.layout--;\n          },\n          onComplete: () => {\n            activeAnimations.layout--;\n            options.onComplete && options.onComplete();\n            this.completeAnimation();\n          }\n        }));\n        if (this.resumingFrom) {\n          this.resumingFrom.currentAnimation = this.currentAnimation;\n        }\n        this.pendingAnimation = undefined;\n      });\n    }\n    completeAnimation() {\n      if (this.resumingFrom) {\n        this.resumingFrom.currentAnimation = undefined;\n        this.resumingFrom.preserveOpacity = undefined;\n      }\n      const stack = this.getStack();\n      stack && stack.exitAnimationComplete();\n      this.resumingFrom = this.currentAnimation = this.animationValues = undefined;\n      this.notifyListeners(\"animationComplete\");\n    }\n    finishAnimation() {\n      if (this.currentAnimation) {\n        this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n        this.currentAnimation.stop();\n      }\n      this.completeAnimation();\n    }\n    applyTransformsToTarget() {\n      const lead = this.getLead();\n      let {\n        targetWithTransforms,\n        target,\n        layout,\n        latestValues\n      } = lead;\n      if (!targetWithTransforms || !target || !layout) return;\n      /**\n       * If we're only animating position, and this element isn't the lead element,\n       * then instead of projecting into the lead box we instead want to calculate\n       * a new target that aligns the two boxes but maintains the layout shape.\n       */\n      if (this !== lead && this.layout && layout && shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n        target = this.target || createBox();\n        const xLength = calcLength(this.layout.layoutBox.x);\n        target.x.min = lead.target.x.min;\n        target.x.max = target.x.min + xLength;\n        const yLength = calcLength(this.layout.layoutBox.y);\n        target.y.min = lead.target.y.min;\n        target.y.max = target.y.min + yLength;\n      }\n      copyBoxInto(targetWithTransforms, target);\n      /**\n       * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n       * This is the final box that we will then project into by calculating a transform delta and\n       * applying it to the corrected box.\n       */\n      transformBox(targetWithTransforms, latestValues);\n      /**\n       * Update the delta between the corrected box and the final target box, after\n       * user-set transforms are applied to it. This will be used by the renderer to\n       * create a transform style that will reproject the element from its layout layout\n       * into the desired bounding box.\n       */\n      calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n    }\n    registerSharedNode(layoutId, node) {\n      if (!this.sharedNodes.has(layoutId)) {\n        this.sharedNodes.set(layoutId, new NodeStack());\n      }\n      const stack = this.sharedNodes.get(layoutId);\n      stack.add(node);\n      const config = node.options.initialPromotionConfig;\n      node.promote({\n        transition: config ? config.transition : undefined,\n        preserveFollowOpacity: config && config.shouldPreserveFollowOpacity ? config.shouldPreserveFollowOpacity(node) : undefined\n      });\n    }\n    isLead() {\n      const stack = this.getStack();\n      return stack ? stack.lead === this : true;\n    }\n    getLead() {\n      var _this$getStack;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? ((_this$getStack = this.getStack()) === null || _this$getStack === void 0 ? void 0 : _this$getStack.lead) || this : this;\n    }\n    getPrevLead() {\n      var _this$getStack2;\n      const {\n        layoutId\n      } = this.options;\n      return layoutId ? (_this$getStack2 = this.getStack()) === null || _this$getStack2 === void 0 ? void 0 : _this$getStack2.prevLead : undefined;\n    }\n    getStack() {\n      const {\n        layoutId\n      } = this.options;\n      if (layoutId) return this.root.sharedNodes.get(layoutId);\n    }\n    promote() {\n      let {\n        needsReset,\n        transition,\n        preserveFollowOpacity\n      } = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      const stack = this.getStack();\n      if (stack) stack.promote(this, preserveFollowOpacity);\n      if (needsReset) {\n        this.projectionDelta = undefined;\n        this.needsReset = true;\n      }\n      if (transition) this.setOptions({\n        transition\n      });\n    }\n    relegate() {\n      const stack = this.getStack();\n      if (stack) {\n        return stack.relegate(this);\n      } else {\n        return false;\n      }\n    }\n    resetSkewAndRotation() {\n      const {\n        visualElement\n      } = this.options;\n      if (!visualElement) return;\n      // If there's no detected skew or rotation values, we can early return without a forced render.\n      let hasDistortingTransform = false;\n      /**\n       * An unrolled check for rotation values. Most elements don't have any rotation and\n       * skipping the nested loop and new object creation is 50% faster.\n       */\n      const {\n        latestValues\n      } = visualElement;\n      if (latestValues.z || latestValues.rotate || latestValues.rotateX || latestValues.rotateY || latestValues.rotateZ || latestValues.skewX || latestValues.skewY) {\n        hasDistortingTransform = true;\n      }\n      // If there's no distorting values, we don't need to do any more.\n      if (!hasDistortingTransform) return;\n      const resetValues = {};\n      if (latestValues.z) {\n        resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n      }\n      // Check the skew and rotate value of all axes and reset to 0\n      for (let i = 0; i < transformAxes.length; i++) {\n        resetDistortingTransform(\"rotate\".concat(transformAxes[i]), visualElement, resetValues, this.animationValues);\n        resetDistortingTransform(\"skew\".concat(transformAxes[i]), visualElement, resetValues, this.animationValues);\n      }\n      // Force a render of this element to apply the transform with all skews and rotations\n      // set to 0.\n      visualElement.render();\n      // Put back all the values we reset\n      for (const key in resetValues) {\n        visualElement.setStaticValue(key, resetValues[key]);\n        if (this.animationValues) {\n          this.animationValues[key] = resetValues[key];\n        }\n      }\n      // Schedule a render for the next frame. This ensures we won't visually\n      // see the element with the reset rotate value applied.\n      visualElement.scheduleRender();\n    }\n    getProjectionStyles(styleProp) {\n      if (!this.instance || this.isSVG) return undefined;\n      if (!this.isVisible) {\n        return hiddenVisibility;\n      }\n      const styles = {\n        visibility: \"\"\n      };\n      const transformTemplate = this.getTransformTemplate();\n      if (this.needsReset) {\n        this.needsReset = false;\n        styles.opacity = \"\";\n        styles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        styles.transform = transformTemplate ? transformTemplate(this.latestValues, \"\") : \"none\";\n        return styles;\n      }\n      const lead = this.getLead();\n      if (!this.projectionDelta || !this.layout || !lead.target) {\n        const emptyStyles = {};\n        if (this.options.layoutId) {\n          emptyStyles.opacity = this.latestValues.opacity !== undefined ? this.latestValues.opacity : 1;\n          emptyStyles.pointerEvents = resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\";\n        }\n        if (this.hasProjected && !hasTransform(this.latestValues)) {\n          emptyStyles.transform = transformTemplate ? transformTemplate({}, \"\") : \"none\";\n          this.hasProjected = false;\n        }\n        return emptyStyles;\n      }\n      const valuesToRender = lead.animationValues || lead.latestValues;\n      this.applyTransformsToTarget();\n      styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n      if (transformTemplate) {\n        styles.transform = transformTemplate(valuesToRender, styles.transform);\n      }\n      const {\n        x,\n        y\n      } = this.projectionDelta;\n      styles.transformOrigin = \"\".concat(x.origin * 100, \"% \").concat(y.origin * 100, \"% 0\");\n      if (lead.animationValues) {\n        var _ref3, _valuesToRender$opaci;\n        /**\n         * If the lead component is animating, assign this either the entering/leaving\n         * opacity\n         */\n        styles.opacity = lead === this ? (_ref3 = (_valuesToRender$opaci = valuesToRender.opacity) !== null && _valuesToRender$opaci !== void 0 ? _valuesToRender$opaci : this.latestValues.opacity) !== null && _ref3 !== void 0 ? _ref3 : 1 : this.preserveOpacity ? this.latestValues.opacity : valuesToRender.opacityExit;\n      } else {\n        /**\n         * Or we're not animating at all, set the lead component to its layout\n         * opacity and other components to hidden.\n         */\n        styles.opacity = lead === this ? valuesToRender.opacity !== undefined ? valuesToRender.opacity : \"\" : valuesToRender.opacityExit !== undefined ? valuesToRender.opacityExit : 0;\n      }\n      /**\n       * Apply scale correction\n       */\n      for (const key in scaleCorrectors) {\n        if (valuesToRender[key] === undefined) continue;\n        const {\n          correct,\n          applyTo,\n          isCSSVariable\n        } = scaleCorrectors[key];\n        /**\n         * Only apply scale correction to the value if we have an\n         * active projection transform. Otherwise these values become\n         * vulnerable to distortion if the element changes size without\n         * a corresponding layout animation.\n         */\n        const corrected = styles.transform === \"none\" ? valuesToRender[key] : correct(valuesToRender[key], lead);\n        if (applyTo) {\n          const num = applyTo.length;\n          for (let i = 0; i < num; i++) {\n            styles[applyTo[i]] = corrected;\n          }\n        } else {\n          // If this is a CSS variable, set it directly on the instance.\n          // Replacing this function from creating styles to setting them\n          // would be a good place to remove per frame object creation\n          if (isCSSVariable) {\n            this.options.visualElement.renderState.vars[key] = corrected;\n          } else {\n            styles[key] = corrected;\n          }\n        }\n      }\n      /**\n       * Disable pointer events on follow components. This is to ensure\n       * that if a follow component covers a lead component it doesn't block\n       * pointer events on the lead.\n       */\n      if (this.options.layoutId) {\n        styles.pointerEvents = lead === this ? resolveMotionValue(styleProp === null || styleProp === void 0 ? void 0 : styleProp.pointerEvents) || \"\" : \"none\";\n      }\n      return styles;\n    }\n    clearSnapshot() {\n      this.resumeFrom = this.snapshot = undefined;\n    }\n    // Only run on root\n    resetTree() {\n      this.root.nodes.forEach(node => {\n        var _node$currentAnimatio;\n        return (_node$currentAnimatio = node.currentAnimation) === null || _node$currentAnimatio === void 0 ? void 0 : _node$currentAnimatio.stop();\n      });\n      this.root.nodes.forEach(clearMeasurements);\n      this.root.sharedNodes.clear();\n    }\n  };\n}\nfunction updateLayout(node) {\n  node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n  var _node$resumeFrom;\n  const snapshot = ((_node$resumeFrom = node.resumeFrom) === null || _node$resumeFrom === void 0 ? void 0 : _node$resumeFrom.snapshot) || node.snapshot;\n  if (node.isLead() && node.layout && snapshot && node.hasListeners(\"didUpdate\")) {\n    const {\n      layoutBox: layout,\n      measuredBox: measuredLayout\n    } = node.layout;\n    const {\n      animationType\n    } = node.options;\n    const isShared = snapshot.source !== node.layout.source;\n    // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n    // animations for instance if layout=\"size\" and an element has only changed position\n    if (animationType === \"size\") {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(axisSnapshot);\n        axisSnapshot.min = layout[axis].min;\n        axisSnapshot.max = axisSnapshot.min + length;\n      });\n    } else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n      eachAxis(axis => {\n        const axisSnapshot = isShared ? snapshot.measuredBox[axis] : snapshot.layoutBox[axis];\n        const length = calcLength(layout[axis]);\n        axisSnapshot.max = axisSnapshot.min + length;\n        /**\n         * Ensure relative target gets resized and rerendererd\n         */\n        if (node.relativeTarget && !node.currentAnimation) {\n          node.isProjectionDirty = true;\n          node.relativeTarget[axis].max = node.relativeTarget[axis].min + length;\n        }\n      });\n    }\n    const layoutDelta = createDelta();\n    calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n    const visualDelta = createDelta();\n    if (isShared) {\n      calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n    } else {\n      calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n    }\n    const hasLayoutChanged = !isDeltaZero(layoutDelta);\n    let hasRelativeLayoutChanged = false;\n    if (!node.resumeFrom) {\n      const relativeParent = node.getClosestProjectingParent();\n      /**\n       * If the relativeParent is itself resuming from a different element then\n       * the relative snapshot is not relavent\n       */\n      if (relativeParent && !relativeParent.resumeFrom) {\n        const {\n          snapshot: parentSnapshot,\n          layout: parentLayout\n        } = relativeParent;\n        if (parentSnapshot && parentLayout) {\n          const relativeSnapshot = createBox();\n          calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n          const relativeLayout = createBox();\n          calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n          if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n            hasRelativeLayoutChanged = true;\n          }\n          if (relativeParent.options.layoutRoot) {\n            node.relativeTarget = relativeLayout;\n            node.relativeTargetOrigin = relativeSnapshot;\n            node.relativeParent = relativeParent;\n          }\n        }\n      }\n    }\n    node.notifyListeners(\"didUpdate\", {\n      layout,\n      snapshot,\n      delta: visualDelta,\n      layoutDelta,\n      hasLayoutChanged,\n      hasRelativeLayoutChanged\n    });\n  } else if (node.isLead()) {\n    const {\n      onExitComplete\n    } = node.options;\n    onExitComplete && onExitComplete();\n  }\n  /**\n   * Clearing transition\n   * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n   * and why we need it at all\n   */\n  node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n  /**\n   * Increase debug counter for nodes encountered this frame\n   */\n  if (statsBuffer.value) {\n    metrics.nodes++;\n  }\n  if (!node.parent) return;\n  /**\n   * If this node isn't projecting, propagate isProjectionDirty. It will have\n   * no performance impact but it will allow the next child that *is* projecting\n   * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n   * correcting.\n   */\n  if (!node.isProjecting()) {\n    node.isProjectionDirty = node.parent.isProjectionDirty;\n  }\n  /**\n   * Propagate isSharedProjectionDirty and isTransformDirty\n   * throughout the whole tree. A future revision can take another look at\n   * this but for safety we still recalcualte shared nodes.\n   */\n  node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty || node.parent.isProjectionDirty || node.parent.isSharedProjectionDirty));\n  node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n  node.isProjectionDirty = node.isSharedProjectionDirty = node.isTransformDirty = false;\n}\nfunction clearSnapshot(node) {\n  node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n  node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n  node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n  const {\n    visualElement\n  } = node.options;\n  if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n    visualElement.notify(\"BeforeLayoutMeasure\");\n  }\n  node.resetTransform();\n}\nfunction finishAnimation(node) {\n  node.finishAnimation();\n  node.targetDelta = node.relativeTarget = node.target = undefined;\n  node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n  node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n  node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n  node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n  stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n  output.translate = mixNumber(delta.translate, 0, p);\n  output.scale = mixNumber(delta.scale, 1, p);\n  output.origin = delta.origin;\n  output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n  output.min = mixNumber(from.min, to.min, p);\n  output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n  mixAxis(output.x, from.x, to.x, p);\n  mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n  return node.animationValues && node.animationValues.opacityExit !== undefined;\n}\nconst defaultLayoutTransition = {\n  duration: 0.45,\n  ease: [0.4, 0, 0.1, 1]\n};\nconst userAgentContains = string => typeof navigator !== \"undefined\" && navigator.userAgent && navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\") ? Math.round : noop;\nfunction roundAxis(axis) {\n  // Round to the nearest .5 pixels to support subpixel layouts\n  axis.min = roundPoint(axis.min);\n  axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n  roundAxis(box.x);\n  roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n  return animationType === \"position\" || animationType === \"preserve-aspect\" && !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2);\n}\nfunction checkNodeWasScrollRoot(node) {\n  var _node$scroll;\n  return node !== node.root && ((_node$scroll = node.scroll) === null || _node$scroll === void 0 ? void 0 : _node$scroll.wasRoot);\n}\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };", "map": {"version": 3, "names": ["statsBuffer", "isSVGElement", "isSVGSVGElement", "getValueTransition", "cancelFrame", "time", "frameData", "frameSteps", "microtask", "frame", "activeAnimations", "motionValue", "mixNumber", "SubscriptionManager", "clamp", "noop", "animateSingleValue", "getOptimisedAppearId", "FlatTree", "delay", "resolveMotionValue", "mixValues", "copyBoxInto", "copyAxisDeltaInto", "translateAxis", "transformBox", "applyBoxDelta", "applyTreeDeltas", "calcLength", "calcRelativePosition", "calcRelativeBox", "calcBoxDelta", "isNear", "removeBoxTransforms", "createBox", "create<PERSON><PERSON><PERSON>", "boxEqualsRounded", "isDeltaZero", "axisDeltaEquals", "aspectRatio", "boxEquals", "NodeStack", "scaleCorrectors", "buildProjectionTransform", "eachAxis", "hasTransform", "hasScale", "has2DTranslate", "globalProjectionState", "metrics", "nodes", "calculatedTargetDeltas", "calculatedProjections", "transformAxes", "hiddenVisibility", "visibility", "animationTarget", "id", "resetDistortingTransform", "key", "visualElement", "values", "sharedAnimationValues", "latestValues", "setStaticValue", "cancelTreeOptimisedTransformAnimations", "projectionNode", "hasCheckedOptimisedAppear", "root", "options", "appearId", "window", "MotionHasOptimisedAnimation", "layout", "layoutId", "MotionCancelOptimisedAnimation", "parent", "createProjectionNode", "_ref", "attachResizeListener", "defaultParent", "measureScroll", "checkIsScrollRoot", "resetTransform", "ProjectionNode", "constructor", "arguments", "length", "undefined", "animationId", "children", "Set", "isTreeAnimating", "isAnimationBlocked", "isLayoutDirty", "isProjectionDirty", "isSharedProjectionDirty", "isTransformDirty", "updateManuallyBlocked", "updateBlockedByResize", "isUpdating", "isSVG", "needsReset", "shouldResetTransform", "treeScale", "x", "y", "eventHandlers", "Map", "hasTreeAnimated", "updateScheduled", "scheduleUpdate", "update", "projectionUpdateScheduled", "checkUpdateFailed", "clearAllSnapshots", "updateProjection", "value", "for<PERSON>ach", "propagateDirtyNodes", "resolveTargetDel<PERSON>", "calcProjection", "cleanDirtyNodes", "addProjectionMetrics", "resolvedRelativeTargetAt", "hasProjected", "isVisible", "animationProgress", "sharedNodes", "path", "depth", "i", "addEventListener", "name", "handler", "has", "set", "get", "add", "notifyListeners", "subscriptionManager", "_len", "args", "Array", "_key", "notify", "hasListeners", "mount", "instance", "current", "cancelDelay", "resizeUnblockUpdate", "hasAnimatedSinceResize", "finishAnimation", "registerSharedNode", "animate", "_ref2", "delta", "hasLayoutChanged", "hasRelativeLayoutChanged", "newLayout", "isTreeAnimationBlocked", "target", "<PERSON><PERSON><PERSON><PERSON>", "layoutTransition", "transition", "getDefaultTransition", "defaultLayoutTransition", "onLayoutAnimationStart", "onLayoutAnimationComplete", "getProps", "hasTargetChanged", "targetLayout", "hasOnlyRelativeTargetChanged", "layoutRoot", "resumeFrom", "currentAnimation", "resumingFrom", "animationOptions", "_objectSpread", "onPlay", "onComplete", "shouldReduceMotion", "type", "startAnimation", "setAnimationOrigin", "isLead", "onExitComplete", "unmount", "willUpdate", "remove", "stack", "getStack", "delete", "clear", "blockUpdate", "unblockUpdate", "isUpdateBlocked", "startUpdate", "resetSkewAndRotation", "getTransformTemplate", "transformTemplate", "shouldNotifyListeners", "node", "updateScroll", "prevTransformTemplateValue", "updateSnapshot", "updateWasBlocked", "clearMeasurements", "clearIsLayoutDirty", "resetTransformStyle", "updateLayout", "notifyLayoutUpdate", "now", "timestamp", "isProcessing", "process", "preRender", "render", "didUpdate", "read", "clearSnapshot", "removeLeadSnapshots", "scheduleUpdateProjection", "scheduleCheckAfterUnmount", "postRender", "snapshot", "measure", "measuredBox", "alwaysMeasureLayout", "prevLayout", "layoutCorrected", "projectionDel<PERSON>", "layoutBox", "phase", "needsMeasurement", "Boolean", "layoutScroll", "scroll", "isRoot", "offset", "wasRoot", "isResetRequested", "hasProjection", "transformTemplateValue", "transformTemplateHasChanged", "scheduleRender", "removeTransform", "pageBox", "measurePageBox", "removeElementScroll", "roundBox", "source", "_this$scroll", "box", "measureViewportBox", "wasInScrollRoot", "some", "checkNodeWasScrollRoot", "_this$scroll2", "boxWithoutScroll", "applyTransform", "transformOnly", "withTransforms", "boxWithoutTransform", "sourceBox", "nodeBox", "set<PERSON>argetD<PERSON><PERSON>", "targetDel<PERSON>", "setOptions", "crossfade", "forceRelativeParentToResolveTarget", "relativeParent", "_this$parent", "forceRecalculation", "lead", "getLead", "isShared", "canSkip", "attemptToResolveRelativeTarget", "getClosestProjectingParent", "relativeTarget<PERSON><PERSON>in", "targetWithTransforms", "isProjecting", "_this$parent2", "pendingAnimation", "prevTreeScaleX", "prevTreeScaleY", "prevProjectionDelta", "createProjectionDeltas", "hide", "show", "_this$options$visualE", "notifyAll", "projectionDeltaWithTransform", "snapshotLatestValues", "mixedValues", "relativeLayout", "snapshotSource", "layoutSource", "isSharedLayoutAnimation", "isOnlyMember", "members", "shouldCrossfadeOpacity", "hasOpacityCrossfade", "prevRelativeTarget", "mixTargetDelta", "latest", "progress", "mixAxisDelta", "mixBox", "animationValues", "_this$currentAnimatio", "_this$resumingFrom", "stop", "velocity", "isSync", "onUpdate", "onStop", "completeAnimation", "preserveOpacity", "exitAnimationComplete", "applyTransformsToTarget", "shouldAnimatePositionOnly", "animationType", "xLength", "min", "max", "y<PERSON><PERSON><PERSON>", "config", "initialPromotionConfig", "promote", "preserveFollowOpacity", "shouldPreserveFollowOpacity", "_this$getStack", "getPrevLead", "_this$getStack2", "prevLead", "relegate", "hasDistortingTransform", "z", "rotate", "rotateX", "rotateY", "rotateZ", "skewX", "skewY", "resetValues", "concat", "getProjectionStyles", "styleProp", "styles", "opacity", "pointerEvents", "transform", "emptyStyles", "valuesToRender", "transform<PERSON><PERSON>in", "origin", "_ref3", "_valuesToRender$opaci", "opacityExit", "correct", "applyTo", "isCSSVariable", "corrected", "num", "renderState", "vars", "resetTree", "_node$currentAnimatio", "_node$resumeFrom", "measuredLayout", "axis", "axisSnapshot", "<PERSON><PERSON><PERSON><PERSON>", "visualD<PERSON><PERSON>", "parentSnapshot", "parentLayout", "relativeSnapshot", "onBeforeLayoutMeasure", "removeLeadSnapshot", "output", "p", "translate", "scale", "originPoint", "mixAxis", "from", "to", "duration", "ease", "userAgentContains", "string", "navigator", "userAgent", "toLowerCase", "includes", "roundPoint", "Math", "round", "roundAxis", "_node$scroll"], "sources": ["C:/Users/<USER>/Desktop/Xerox Module/xerox-frontend/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs"], "sourcesContent": ["import { statsBuffer, isSVGElement, isSVGSVGElement, getValueTransition, cancelFrame, time, frameData, frameSteps, microtask, frame, activeAnimations, motionValue, mixNumber } from 'motion-dom';\nimport { SubscriptionManager, clamp, noop } from 'motion-utils';\nimport { animateSingleValue } from '../../animation/animate/single-value.mjs';\nimport { getOptimisedAppearId } from '../../animation/optimized-appear/get-appear-id.mjs';\nimport { FlatTree } from '../../render/utils/flat-tree.mjs';\nimport { delay } from '../../utils/delay.mjs';\nimport { resolveMotionValue } from '../../value/utils/resolve-motion-value.mjs';\nimport { mixValues } from '../animation/mix-values.mjs';\nimport { copyBoxInto, copyAxisDeltaInto } from '../geometry/copy.mjs';\nimport { translateAxis, transformBox, applyBoxDelta, applyTreeDeltas } from '../geometry/delta-apply.mjs';\nimport { calcLength, calcRelativePosition, calcRelativeBox, calcBoxDelta, isNear } from '../geometry/delta-calc.mjs';\nimport { removeBoxTransforms } from '../geometry/delta-remove.mjs';\nimport { createBox, createDelta } from '../geometry/models.mjs';\nimport { boxEqualsRounded, isDeltaZero, axisDeltaEquals, aspectRatio, boxEquals } from '../geometry/utils.mjs';\nimport { NodeStack } from '../shared/stack.mjs';\nimport { scaleCorrectors } from '../styles/scale-correction.mjs';\nimport { buildProjectionTransform } from '../styles/transform.mjs';\nimport { eachAxis } from '../utils/each-axis.mjs';\nimport { hasTransform, hasScale, has2DTranslate } from '../utils/has-transform.mjs';\nimport { globalProjectionState } from './state.mjs';\n\nconst metrics = {\n    nodes: 0,\n    calculatedTargetDeltas: 0,\n    calculatedProjections: 0,\n};\nconst transformAxes = [\"\", \"X\", \"Y\", \"Z\"];\nconst hiddenVisibility = { visibility: \"hidden\" };\n/**\n * We use 1000 as the animation target as 0-1000 maps better to pixels than 0-1\n * which has a noticeable difference in spring animations\n */\nconst animationTarget = 1000;\nlet id = 0;\nfunction resetDistortingTransform(key, visualElement, values, sharedAnimationValues) {\n    const { latestValues } = visualElement;\n    // Record the distorting transform and then temporarily set it to 0\n    if (latestValues[key]) {\n        values[key] = latestValues[key];\n        visualElement.setStaticValue(key, 0);\n        if (sharedAnimationValues) {\n            sharedAnimationValues[key] = 0;\n        }\n    }\n}\nfunction cancelTreeOptimisedTransformAnimations(projectionNode) {\n    projectionNode.hasCheckedOptimisedAppear = true;\n    if (projectionNode.root === projectionNode)\n        return;\n    const { visualElement } = projectionNode.options;\n    if (!visualElement)\n        return;\n    const appearId = getOptimisedAppearId(visualElement);\n    if (window.MotionHasOptimisedAnimation(appearId, \"transform\")) {\n        const { layout, layoutId } = projectionNode.options;\n        window.MotionCancelOptimisedAnimation(appearId, \"transform\", frame, !(layout || layoutId));\n    }\n    const { parent } = projectionNode;\n    if (parent && !parent.hasCheckedOptimisedAppear) {\n        cancelTreeOptimisedTransformAnimations(parent);\n    }\n}\nfunction createProjectionNode({ attachResizeListener, defaultParent, measureScroll, checkIsScrollRoot, resetTransform, }) {\n    return class ProjectionNode {\n        constructor(latestValues = {}, parent = defaultParent?.()) {\n            /**\n             * A unique ID generated for every projection node.\n             */\n            this.id = id++;\n            /**\n             * An id that represents a unique session instigated by startUpdate.\n             */\n            this.animationId = 0;\n            /**\n             * A Set containing all this component's children. This is used to iterate\n             * through the children.\n             *\n             * TODO: This could be faster to iterate as a flat array stored on the root node.\n             */\n            this.children = new Set();\n            /**\n             * Options for the node. We use this to configure what kind of layout animations\n             * we should perform (if any).\n             */\n            this.options = {};\n            /**\n             * We use this to detect when its safe to shut down part of a projection tree.\n             * We have to keep projecting children for scale correction and relative projection\n             * until all their parents stop performing layout animations.\n             */\n            this.isTreeAnimating = false;\n            this.isAnimationBlocked = false;\n            /**\n             * Flag to true if we think this layout has been changed. We can't always know this,\n             * currently we set it to true every time a component renders, or if it has a layoutDependency\n             * if that has changed between renders. Additionally, components can be grouped by LayoutGroup\n             * and if one node is dirtied, they all are.\n             */\n            this.isLayoutDirty = false;\n            /**\n             * Flag to true if we think the projection calculations for this node needs\n             * recalculating as a result of an updated transform or layout animation.\n             */\n            this.isProjectionDirty = false;\n            /**\n             * Flag to true if the layout *or* transform has changed. This then gets propagated\n             * throughout the projection tree, forcing any element below to recalculate on the next frame.\n             */\n            this.isSharedProjectionDirty = false;\n            /**\n             * Flag transform dirty. This gets propagated throughout the whole tree but is only\n             * respected by shared nodes.\n             */\n            this.isTransformDirty = false;\n            /**\n             * Block layout updates for instant layout transitions throughout the tree.\n             */\n            this.updateManuallyBlocked = false;\n            this.updateBlockedByResize = false;\n            /**\n             * Set to true between the start of the first `willUpdate` call and the end of the `didUpdate`\n             * call.\n             */\n            this.isUpdating = false;\n            /**\n             * If this is an SVG element we currently disable projection transforms\n             */\n            this.isSVG = false;\n            /**\n             * Flag to true (during promotion) if a node doing an instant layout transition needs to reset\n             * its projection styles.\n             */\n            this.needsReset = false;\n            /**\n             * Flags whether this node should have its transform reset prior to measuring.\n             */\n            this.shouldResetTransform = false;\n            /**\n             * Store whether this node has been checked for optimised appear animations. As\n             * effects fire bottom-up, and we want to look up the tree for appear animations,\n             * this makes sure we only check each path once, stopping at nodes that\n             * have already been checked.\n             */\n            this.hasCheckedOptimisedAppear = false;\n            /**\n             * An object representing the calculated contextual/accumulated/tree scale.\n             * This will be used to scale calculcated projection transforms, as these are\n             * calculated in screen-space but need to be scaled for elements to layoutly\n             * make it to their calculated destinations.\n             *\n             * TODO: Lazy-init\n             */\n            this.treeScale = { x: 1, y: 1 };\n            /**\n             *\n             */\n            this.eventHandlers = new Map();\n            this.hasTreeAnimated = false;\n            // Note: Currently only running on root node\n            this.updateScheduled = false;\n            this.scheduleUpdate = () => this.update();\n            this.projectionUpdateScheduled = false;\n            this.checkUpdateFailed = () => {\n                if (this.isUpdating) {\n                    this.isUpdating = false;\n                    this.clearAllSnapshots();\n                }\n            };\n            /**\n             * This is a multi-step process as shared nodes might be of different depths. Nodes\n             * are sorted by depth order, so we need to resolve the entire tree before moving to\n             * the next step.\n             */\n            this.updateProjection = () => {\n                this.projectionUpdateScheduled = false;\n                /**\n                 * Reset debug counts. Manually resetting rather than creating a new\n                 * object each frame.\n                 */\n                if (statsBuffer.value) {\n                    metrics.nodes =\n                        metrics.calculatedTargetDeltas =\n                            metrics.calculatedProjections =\n                                0;\n                }\n                this.nodes.forEach(propagateDirtyNodes);\n                this.nodes.forEach(resolveTargetDelta);\n                this.nodes.forEach(calcProjection);\n                this.nodes.forEach(cleanDirtyNodes);\n                if (statsBuffer.addProjectionMetrics) {\n                    statsBuffer.addProjectionMetrics(metrics);\n                }\n            };\n            /**\n             * Frame calculations\n             */\n            this.resolvedRelativeTargetAt = 0.0;\n            this.hasProjected = false;\n            this.isVisible = true;\n            this.animationProgress = 0;\n            /**\n             * Shared layout\n             */\n            // TODO Only running on root node\n            this.sharedNodes = new Map();\n            this.latestValues = latestValues;\n            this.root = parent ? parent.root || parent : this;\n            this.path = parent ? [...parent.path, parent] : [];\n            this.parent = parent;\n            this.depth = parent ? parent.depth + 1 : 0;\n            for (let i = 0; i < this.path.length; i++) {\n                this.path[i].shouldResetTransform = true;\n            }\n            if (this.root === this)\n                this.nodes = new FlatTree();\n        }\n        addEventListener(name, handler) {\n            if (!this.eventHandlers.has(name)) {\n                this.eventHandlers.set(name, new SubscriptionManager());\n            }\n            return this.eventHandlers.get(name).add(handler);\n        }\n        notifyListeners(name, ...args) {\n            const subscriptionManager = this.eventHandlers.get(name);\n            subscriptionManager && subscriptionManager.notify(...args);\n        }\n        hasListeners(name) {\n            return this.eventHandlers.has(name);\n        }\n        /**\n         * Lifecycles\n         */\n        mount(instance) {\n            if (this.instance)\n                return;\n            this.isSVG = isSVGElement(instance) && !isSVGSVGElement(instance);\n            this.instance = instance;\n            const { layoutId, layout, visualElement } = this.options;\n            if (visualElement && !visualElement.current) {\n                visualElement.mount(instance);\n            }\n            this.root.nodes.add(this);\n            this.parent && this.parent.children.add(this);\n            if (this.root.hasTreeAnimated && (layout || layoutId)) {\n                this.isLayoutDirty = true;\n            }\n            if (attachResizeListener) {\n                let cancelDelay;\n                const resizeUnblockUpdate = () => (this.root.updateBlockedByResize = false);\n                attachResizeListener(instance, () => {\n                    this.root.updateBlockedByResize = true;\n                    cancelDelay && cancelDelay();\n                    cancelDelay = delay(resizeUnblockUpdate, 250);\n                    if (globalProjectionState.hasAnimatedSinceResize) {\n                        globalProjectionState.hasAnimatedSinceResize = false;\n                        this.nodes.forEach(finishAnimation);\n                    }\n                });\n            }\n            if (layoutId) {\n                this.root.registerSharedNode(layoutId, this);\n            }\n            // Only register the handler if it requires layout animation\n            if (this.options.animate !== false &&\n                visualElement &&\n                (layoutId || layout)) {\n                this.addEventListener(\"didUpdate\", ({ delta, hasLayoutChanged, hasRelativeLayoutChanged, layout: newLayout, }) => {\n                    if (this.isTreeAnimationBlocked()) {\n                        this.target = undefined;\n                        this.relativeTarget = undefined;\n                        return;\n                    }\n                    // TODO: Check here if an animation exists\n                    const layoutTransition = this.options.transition ||\n                        visualElement.getDefaultTransition() ||\n                        defaultLayoutTransition;\n                    const { onLayoutAnimationStart, onLayoutAnimationComplete, } = visualElement.getProps();\n                    /**\n                     * The target layout of the element might stay the same,\n                     * but its position relative to its parent has changed.\n                     */\n                    const hasTargetChanged = !this.targetLayout ||\n                        !boxEqualsRounded(this.targetLayout, newLayout);\n                    /*\n                     * Note: Disabled to fix relative animations always triggering new\n                     * layout animations. If this causes further issues, we can try\n                     * a different approach to detecting relative target changes.\n                     */\n                    // || hasRelativeLayoutChanged\n                    /**\n                     * If the layout hasn't seemed to have changed, it might be that the\n                     * element is visually in the same place in the document but its position\n                     * relative to its parent has indeed changed. So here we check for that.\n                     */\n                    const hasOnlyRelativeTargetChanged = !hasLayoutChanged && hasRelativeLayoutChanged;\n                    if (this.options.layoutRoot ||\n                        this.resumeFrom ||\n                        hasOnlyRelativeTargetChanged ||\n                        (hasLayoutChanged &&\n                            (hasTargetChanged || !this.currentAnimation))) {\n                        if (this.resumeFrom) {\n                            this.resumingFrom = this.resumeFrom;\n                            this.resumingFrom.resumingFrom = undefined;\n                        }\n                        const animationOptions = {\n                            ...getValueTransition(layoutTransition, \"layout\"),\n                            onPlay: onLayoutAnimationStart,\n                            onComplete: onLayoutAnimationComplete,\n                        };\n                        if (visualElement.shouldReduceMotion ||\n                            this.options.layoutRoot) {\n                            animationOptions.delay = 0;\n                            animationOptions.type = false;\n                        }\n                        this.startAnimation(animationOptions);\n                        /**\n                         * Set animation origin after starting animation to avoid layout jump\n                         * caused by stopping previous layout animation\n                         */\n                        this.setAnimationOrigin(delta, hasOnlyRelativeTargetChanged);\n                    }\n                    else {\n                        /**\n                         * If the layout hasn't changed and we have an animation that hasn't started yet,\n                         * finish it immediately. Otherwise it will be animating from a location\n                         * that was probably never commited to screen and look like a jumpy box.\n                         */\n                        if (!hasLayoutChanged) {\n                            finishAnimation(this);\n                        }\n                        if (this.isLead() && this.options.onExitComplete) {\n                            this.options.onExitComplete();\n                        }\n                    }\n                    this.targetLayout = newLayout;\n                });\n            }\n        }\n        unmount() {\n            this.options.layoutId && this.willUpdate();\n            this.root.nodes.remove(this);\n            const stack = this.getStack();\n            stack && stack.remove(this);\n            this.parent && this.parent.children.delete(this);\n            this.instance = undefined;\n            this.eventHandlers.clear();\n            cancelFrame(this.updateProjection);\n        }\n        // only on the root\n        blockUpdate() {\n            this.updateManuallyBlocked = true;\n        }\n        unblockUpdate() {\n            this.updateManuallyBlocked = false;\n        }\n        isUpdateBlocked() {\n            return this.updateManuallyBlocked || this.updateBlockedByResize;\n        }\n        isTreeAnimationBlocked() {\n            return (this.isAnimationBlocked ||\n                (this.parent && this.parent.isTreeAnimationBlocked()) ||\n                false);\n        }\n        // Note: currently only running on root node\n        startUpdate() {\n            if (this.isUpdateBlocked())\n                return;\n            this.isUpdating = true;\n            this.nodes && this.nodes.forEach(resetSkewAndRotation);\n            this.animationId++;\n        }\n        getTransformTemplate() {\n            const { visualElement } = this.options;\n            return visualElement && visualElement.getProps().transformTemplate;\n        }\n        willUpdate(shouldNotifyListeners = true) {\n            this.root.hasTreeAnimated = true;\n            if (this.root.isUpdateBlocked()) {\n                this.options.onExitComplete && this.options.onExitComplete();\n                return;\n            }\n            /**\n             * If we're running optimised appear animations then these must be\n             * cancelled before measuring the DOM. This is so we can measure\n             * the true layout of the element rather than the WAAPI animation\n             * which will be unaffected by the resetSkewAndRotate step.\n             *\n             * Note: This is a DOM write. Worst case scenario is this is sandwiched\n             * between other snapshot reads which will cause unnecessary style recalculations.\n             * This has to happen here though, as we don't yet know which nodes will need\n             * snapshots in startUpdate(), but we only want to cancel optimised animations\n             * if a layout animation measurement is actually going to be affected by them.\n             */\n            if (window.MotionCancelOptimisedAnimation &&\n                !this.hasCheckedOptimisedAppear) {\n                cancelTreeOptimisedTransformAnimations(this);\n            }\n            !this.root.isUpdating && this.root.startUpdate();\n            if (this.isLayoutDirty)\n                return;\n            this.isLayoutDirty = true;\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                node.shouldResetTransform = true;\n                node.updateScroll(\"snapshot\");\n                if (node.options.layoutRoot) {\n                    node.willUpdate(false);\n                }\n            }\n            const { layoutId, layout } = this.options;\n            if (layoutId === undefined && !layout)\n                return;\n            const transformTemplate = this.getTransformTemplate();\n            this.prevTransformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            this.updateSnapshot();\n            shouldNotifyListeners && this.notifyListeners(\"willUpdate\");\n        }\n        update() {\n            this.updateScheduled = false;\n            const updateWasBlocked = this.isUpdateBlocked();\n            // When doing an instant transition, we skip the layout update,\n            // but should still clean up the measurements so that the next\n            // snapshot could be taken correctly.\n            if (updateWasBlocked) {\n                this.unblockUpdate();\n                this.clearAllSnapshots();\n                this.nodes.forEach(clearMeasurements);\n                return;\n            }\n            if (!this.isUpdating) {\n                this.nodes.forEach(clearIsLayoutDirty);\n            }\n            this.isUpdating = false;\n            /**\n             * Write\n             */\n            this.nodes.forEach(resetTransformStyle);\n            /**\n             * Read ==================\n             */\n            // Update layout measurements of updated children\n            this.nodes.forEach(updateLayout);\n            /**\n             * Write\n             */\n            // Notify listeners that the layout is updated\n            this.nodes.forEach(notifyLayoutUpdate);\n            this.clearAllSnapshots();\n            /**\n             * Manually flush any pending updates. Ideally\n             * we could leave this to the following requestAnimationFrame but this seems\n             * to leave a flash of incorrectly styled content.\n             */\n            const now = time.now();\n            frameData.delta = clamp(0, 1000 / 60, now - frameData.timestamp);\n            frameData.timestamp = now;\n            frameData.isProcessing = true;\n            frameSteps.update.process(frameData);\n            frameSteps.preRender.process(frameData);\n            frameSteps.render.process(frameData);\n            frameData.isProcessing = false;\n        }\n        didUpdate() {\n            if (!this.updateScheduled) {\n                this.updateScheduled = true;\n                microtask.read(this.scheduleUpdate);\n            }\n        }\n        clearAllSnapshots() {\n            this.nodes.forEach(clearSnapshot);\n            this.sharedNodes.forEach(removeLeadSnapshots);\n        }\n        scheduleUpdateProjection() {\n            if (!this.projectionUpdateScheduled) {\n                this.projectionUpdateScheduled = true;\n                frame.preRender(this.updateProjection, false, true);\n            }\n        }\n        scheduleCheckAfterUnmount() {\n            /**\n             * If the unmounting node is in a layoutGroup and did trigger a willUpdate,\n             * we manually call didUpdate to give a chance to the siblings to animate.\n             * Otherwise, cleanup all snapshots to prevents future nodes from reusing them.\n             */\n            frame.postRender(() => {\n                if (this.isLayoutDirty) {\n                    this.root.didUpdate();\n                }\n                else {\n                    this.root.checkUpdateFailed();\n                }\n            });\n        }\n        /**\n         * Update measurements\n         */\n        updateSnapshot() {\n            if (this.snapshot || !this.instance)\n                return;\n            this.snapshot = this.measure();\n            if (this.snapshot &&\n                !calcLength(this.snapshot.measuredBox.x) &&\n                !calcLength(this.snapshot.measuredBox.y)) {\n                this.snapshot = undefined;\n            }\n        }\n        updateLayout() {\n            if (!this.instance)\n                return;\n            // TODO: Incorporate into a forwarded scroll offset\n            this.updateScroll();\n            if (!(this.options.alwaysMeasureLayout && this.isLead()) &&\n                !this.isLayoutDirty) {\n                return;\n            }\n            /**\n             * When a node is mounted, it simply resumes from the prevLead's\n             * snapshot instead of taking a new one, but the ancestors scroll\n             * might have updated while the prevLead is unmounted. We need to\n             * update the scroll again to make sure the layout we measure is\n             * up to date.\n             */\n            if (this.resumeFrom && !this.resumeFrom.instance) {\n                for (let i = 0; i < this.path.length; i++) {\n                    const node = this.path[i];\n                    node.updateScroll();\n                }\n            }\n            const prevLayout = this.layout;\n            this.layout = this.measure(false);\n            this.layoutCorrected = createBox();\n            this.isLayoutDirty = false;\n            this.projectionDelta = undefined;\n            this.notifyListeners(\"measure\", this.layout.layoutBox);\n            const { visualElement } = this.options;\n            visualElement &&\n                visualElement.notify(\"LayoutMeasure\", this.layout.layoutBox, prevLayout ? prevLayout.layoutBox : undefined);\n        }\n        updateScroll(phase = \"measure\") {\n            let needsMeasurement = Boolean(this.options.layoutScroll && this.instance);\n            if (this.scroll &&\n                this.scroll.animationId === this.root.animationId &&\n                this.scroll.phase === phase) {\n                needsMeasurement = false;\n            }\n            if (needsMeasurement && this.instance) {\n                const isRoot = checkIsScrollRoot(this.instance);\n                this.scroll = {\n                    animationId: this.root.animationId,\n                    phase,\n                    isRoot,\n                    offset: measureScroll(this.instance),\n                    wasRoot: this.scroll ? this.scroll.isRoot : isRoot,\n                };\n            }\n        }\n        resetTransform() {\n            if (!resetTransform)\n                return;\n            const isResetRequested = this.isLayoutDirty ||\n                this.shouldResetTransform ||\n                this.options.alwaysMeasureLayout;\n            const hasProjection = this.projectionDelta && !isDeltaZero(this.projectionDelta);\n            const transformTemplate = this.getTransformTemplate();\n            const transformTemplateValue = transformTemplate\n                ? transformTemplate(this.latestValues, \"\")\n                : undefined;\n            const transformTemplateHasChanged = transformTemplateValue !== this.prevTransformTemplateValue;\n            if (isResetRequested &&\n                this.instance &&\n                (hasProjection ||\n                    hasTransform(this.latestValues) ||\n                    transformTemplateHasChanged)) {\n                resetTransform(this.instance, transformTemplateValue);\n                this.shouldResetTransform = false;\n                this.scheduleRender();\n            }\n        }\n        measure(removeTransform = true) {\n            const pageBox = this.measurePageBox();\n            let layoutBox = this.removeElementScroll(pageBox);\n            /**\n             * Measurements taken during the pre-render stage\n             * still have transforms applied so we remove them\n             * via calculation.\n             */\n            if (removeTransform) {\n                layoutBox = this.removeTransform(layoutBox);\n            }\n            roundBox(layoutBox);\n            return {\n                animationId: this.root.animationId,\n                measuredBox: pageBox,\n                layoutBox,\n                latestValues: {},\n                source: this.id,\n            };\n        }\n        measurePageBox() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return createBox();\n            const box = visualElement.measureViewportBox();\n            const wasInScrollRoot = this.scroll?.wasRoot || this.path.some(checkNodeWasScrollRoot);\n            if (!wasInScrollRoot) {\n                // Remove viewport scroll to give page-relative coordinates\n                const { scroll } = this.root;\n                if (scroll) {\n                    translateAxis(box.x, scroll.offset.x);\n                    translateAxis(box.y, scroll.offset.y);\n                }\n            }\n            return box;\n        }\n        removeElementScroll(box) {\n            const boxWithoutScroll = createBox();\n            copyBoxInto(boxWithoutScroll, box);\n            if (this.scroll?.wasRoot) {\n                return boxWithoutScroll;\n            }\n            /**\n             * Performance TODO: Keep a cumulative scroll offset down the tree\n             * rather than loop back up the path.\n             */\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                const { scroll, options } = node;\n                if (node !== this.root && scroll && options.layoutScroll) {\n                    /**\n                     * If this is a new scroll root, we want to remove all previous scrolls\n                     * from the viewport box.\n                     */\n                    if (scroll.wasRoot) {\n                        copyBoxInto(boxWithoutScroll, box);\n                    }\n                    translateAxis(boxWithoutScroll.x, scroll.offset.x);\n                    translateAxis(boxWithoutScroll.y, scroll.offset.y);\n                }\n            }\n            return boxWithoutScroll;\n        }\n        applyTransform(box, transformOnly = false) {\n            const withTransforms = createBox();\n            copyBoxInto(withTransforms, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!transformOnly &&\n                    node.options.layoutScroll &&\n                    node.scroll &&\n                    node !== node.root) {\n                    transformBox(withTransforms, {\n                        x: -node.scroll.offset.x,\n                        y: -node.scroll.offset.y,\n                    });\n                }\n                if (!hasTransform(node.latestValues))\n                    continue;\n                transformBox(withTransforms, node.latestValues);\n            }\n            if (hasTransform(this.latestValues)) {\n                transformBox(withTransforms, this.latestValues);\n            }\n            return withTransforms;\n        }\n        removeTransform(box) {\n            const boxWithoutTransform = createBox();\n            copyBoxInto(boxWithoutTransform, box);\n            for (let i = 0; i < this.path.length; i++) {\n                const node = this.path[i];\n                if (!node.instance)\n                    continue;\n                if (!hasTransform(node.latestValues))\n                    continue;\n                hasScale(node.latestValues) && node.updateSnapshot();\n                const sourceBox = createBox();\n                const nodeBox = node.measurePageBox();\n                copyBoxInto(sourceBox, nodeBox);\n                removeBoxTransforms(boxWithoutTransform, node.latestValues, node.snapshot ? node.snapshot.layoutBox : undefined, sourceBox);\n            }\n            if (hasTransform(this.latestValues)) {\n                removeBoxTransforms(boxWithoutTransform, this.latestValues);\n            }\n            return boxWithoutTransform;\n        }\n        setTargetDelta(delta) {\n            this.targetDelta = delta;\n            this.root.scheduleUpdateProjection();\n            this.isProjectionDirty = true;\n        }\n        setOptions(options) {\n            this.options = {\n                ...this.options,\n                ...options,\n                crossfade: options.crossfade !== undefined ? options.crossfade : true,\n            };\n        }\n        clearMeasurements() {\n            this.scroll = undefined;\n            this.layout = undefined;\n            this.snapshot = undefined;\n            this.prevTransformTemplateValue = undefined;\n            this.targetDelta = undefined;\n            this.target = undefined;\n            this.isLayoutDirty = false;\n        }\n        forceRelativeParentToResolveTarget() {\n            if (!this.relativeParent)\n                return;\n            /**\n             * If the parent target isn't up-to-date, force it to update.\n             * This is an unfortunate de-optimisation as it means any updating relative\n             * projection will cause all the relative parents to recalculate back\n             * up the tree.\n             */\n            if (this.relativeParent.resolvedRelativeTargetAt !==\n                frameData.timestamp) {\n                this.relativeParent.resolveTargetDelta(true);\n            }\n        }\n        resolveTargetDelta(forceRecalculation = false) {\n            /**\n             * Once the dirty status of nodes has been spread through the tree, we also\n             * need to check if we have a shared node of a different depth that has itself\n             * been dirtied.\n             */\n            const lead = this.getLead();\n            this.isProjectionDirty || (this.isProjectionDirty = lead.isProjectionDirty);\n            this.isTransformDirty || (this.isTransformDirty = lead.isTransformDirty);\n            this.isSharedProjectionDirty || (this.isSharedProjectionDirty = lead.isSharedProjectionDirty);\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            /**\n             * We don't use transform for this step of processing so we don't\n             * need to check whether any nodes have changed transform.\n             */\n            const canSkip = !(forceRecalculation ||\n                (isShared && this.isSharedProjectionDirty) ||\n                this.isProjectionDirty ||\n                this.parent?.isProjectionDirty ||\n                this.attemptToResolveRelativeTarget ||\n                this.root.updateBlockedByResize);\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If we have no layout, we can't perform projection, so early return\n             */\n            if (!this.layout || !(layout || layoutId))\n                return;\n            this.resolvedRelativeTargetAt = frameData.timestamp;\n            /**\n             * If we don't have a targetDelta but do have a layout, we can attempt to resolve\n             * a relativeParent. This will allow a component to perform scale correction\n             * even if no animation has started.\n             */\n            if (!this.targetDelta && !this.relativeTarget) {\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    relativeParent.layout &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.layout.layoutBox, relativeParent.layout.layoutBox);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * If we have no relative target or no target delta our target isn't valid\n             * for this frame.\n             */\n            if (!this.relativeTarget && !this.targetDelta)\n                return;\n            /**\n             * Lazy-init target data structure\n             */\n            if (!this.target) {\n                this.target = createBox();\n                this.targetWithTransforms = createBox();\n            }\n            /**\n             * If we've got a relative box for this component, resolve it into a target relative to the parent.\n             */\n            if (this.relativeTarget &&\n                this.relativeTargetOrigin &&\n                this.relativeParent &&\n                this.relativeParent.target) {\n                this.forceRelativeParentToResolveTarget();\n                calcRelativeBox(this.target, this.relativeTarget, this.relativeParent.target);\n                /**\n                 * If we've only got a targetDelta, resolve it into a target\n                 */\n            }\n            else if (this.targetDelta) {\n                if (Boolean(this.resumingFrom)) {\n                    // TODO: This is creating a new object every frame\n                    this.target = this.applyTransform(this.layout.layoutBox);\n                }\n                else {\n                    copyBoxInto(this.target, this.layout.layoutBox);\n                }\n                applyBoxDelta(this.target, this.targetDelta);\n            }\n            else {\n                /**\n                 * If no target, use own layout as target\n                 */\n                copyBoxInto(this.target, this.layout.layoutBox);\n            }\n            /**\n             * If we've been told to attempt to resolve a relative target, do so.\n             */\n            if (this.attemptToResolveRelativeTarget) {\n                this.attemptToResolveRelativeTarget = false;\n                const relativeParent = this.getClosestProjectingParent();\n                if (relativeParent &&\n                    Boolean(relativeParent.resumingFrom) ===\n                        Boolean(this.resumingFrom) &&\n                    !relativeParent.options.layoutScroll &&\n                    relativeParent.target &&\n                    this.animationProgress !== 1) {\n                    this.relativeParent = relativeParent;\n                    this.forceRelativeParentToResolveTarget();\n                    this.relativeTarget = createBox();\n                    this.relativeTargetOrigin = createBox();\n                    calcRelativePosition(this.relativeTargetOrigin, this.target, relativeParent.target);\n                    copyBoxInto(this.relativeTarget, this.relativeTargetOrigin);\n                }\n                else {\n                    this.relativeParent = this.relativeTarget = undefined;\n                }\n            }\n            /**\n             * Increase debug counter for resolved target deltas\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedTargetDeltas++;\n            }\n        }\n        getClosestProjectingParent() {\n            if (!this.parent ||\n                hasScale(this.parent.latestValues) ||\n                has2DTranslate(this.parent.latestValues)) {\n                return undefined;\n            }\n            if (this.parent.isProjecting()) {\n                return this.parent;\n            }\n            else {\n                return this.parent.getClosestProjectingParent();\n            }\n        }\n        isProjecting() {\n            return Boolean((this.relativeTarget ||\n                this.targetDelta ||\n                this.options.layoutRoot) &&\n                this.layout);\n        }\n        calcProjection() {\n            const lead = this.getLead();\n            const isShared = Boolean(this.resumingFrom) || this !== lead;\n            let canSkip = true;\n            /**\n             * If this is a normal layout animation and neither this node nor its nearest projecting\n             * is dirty then we can't skip.\n             */\n            if (this.isProjectionDirty || this.parent?.isProjectionDirty) {\n                canSkip = false;\n            }\n            /**\n             * If this is a shared layout animation and this node's shared projection is dirty then\n             * we can't skip.\n             */\n            if (isShared &&\n                (this.isSharedProjectionDirty || this.isTransformDirty)) {\n                canSkip = false;\n            }\n            /**\n             * If we have resolved the target this frame we must recalculate the\n             * projection to ensure it visually represents the internal calculations.\n             */\n            if (this.resolvedRelativeTargetAt === frameData.timestamp) {\n                canSkip = false;\n            }\n            if (canSkip)\n                return;\n            const { layout, layoutId } = this.options;\n            /**\n             * If this section of the tree isn't animating we can\n             * delete our target sources for the following frame.\n             */\n            this.isTreeAnimating = Boolean((this.parent && this.parent.isTreeAnimating) ||\n                this.currentAnimation ||\n                this.pendingAnimation);\n            if (!this.isTreeAnimating) {\n                this.targetDelta = this.relativeTarget = undefined;\n            }\n            if (!this.layout || !(layout || layoutId))\n                return;\n            /**\n             * Reset the corrected box with the latest values from box, as we're then going\n             * to perform mutative operations on it.\n             */\n            copyBoxInto(this.layoutCorrected, this.layout.layoutBox);\n            /**\n             * Record previous tree scales before updating.\n             */\n            const prevTreeScaleX = this.treeScale.x;\n            const prevTreeScaleY = this.treeScale.y;\n            /**\n             * Apply all the parent deltas to this box to produce the corrected box. This\n             * is the layout box, as it will appear on screen as a result of the transforms of its parents.\n             */\n            applyTreeDeltas(this.layoutCorrected, this.treeScale, this.path, isShared);\n            /**\n             * If this layer needs to perform scale correction but doesn't have a target,\n             * use the layout as the target.\n             */\n            if (lead.layout &&\n                !lead.target &&\n                (this.treeScale.x !== 1 || this.treeScale.y !== 1)) {\n                lead.target = lead.layout.layoutBox;\n                lead.targetWithTransforms = createBox();\n            }\n            const { target } = lead;\n            if (!target) {\n                /**\n                 * If we don't have a target to project into, but we were previously\n                 * projecting, we want to remove the stored transform and schedule\n                 * a render to ensure the elements reflect the removed transform.\n                 */\n                if (this.prevProjectionDelta) {\n                    this.createProjectionDeltas();\n                    this.scheduleRender();\n                }\n                return;\n            }\n            if (!this.projectionDelta || !this.prevProjectionDelta) {\n                this.createProjectionDeltas();\n            }\n            else {\n                copyAxisDeltaInto(this.prevProjectionDelta.x, this.projectionDelta.x);\n                copyAxisDeltaInto(this.prevProjectionDelta.y, this.projectionDelta.y);\n            }\n            /**\n             * Update the delta between the corrected box and the target box before user-set transforms were applied.\n             * This will allow us to calculate the corrected borderRadius and boxShadow to compensate\n             * for our layout reprojection, but still allow them to be scaled correctly by the user.\n             * It might be that to simplify this we may want to accept that user-set scale is also corrected\n             * and we wouldn't have to keep and calc both deltas, OR we could support a user setting\n             * to allow people to choose whether these styles are corrected based on just the\n             * layout reprojection or the final bounding box.\n             */\n            calcBoxDelta(this.projectionDelta, this.layoutCorrected, target, this.latestValues);\n            if (this.treeScale.x !== prevTreeScaleX ||\n                this.treeScale.y !== prevTreeScaleY ||\n                !axisDeltaEquals(this.projectionDelta.x, this.prevProjectionDelta.x) ||\n                !axisDeltaEquals(this.projectionDelta.y, this.prevProjectionDelta.y)) {\n                this.hasProjected = true;\n                this.scheduleRender();\n                this.notifyListeners(\"projectionUpdate\", target);\n            }\n            /**\n             * Increase debug counter for recalculated projections\n             */\n            if (statsBuffer.value) {\n                metrics.calculatedProjections++;\n            }\n        }\n        hide() {\n            this.isVisible = false;\n            // TODO: Schedule render\n        }\n        show() {\n            this.isVisible = true;\n            // TODO: Schedule render\n        }\n        scheduleRender(notifyAll = true) {\n            this.options.visualElement?.scheduleRender();\n            if (notifyAll) {\n                const stack = this.getStack();\n                stack && stack.scheduleRender();\n            }\n            if (this.resumingFrom && !this.resumingFrom.instance) {\n                this.resumingFrom = undefined;\n            }\n        }\n        createProjectionDeltas() {\n            this.prevProjectionDelta = createDelta();\n            this.projectionDelta = createDelta();\n            this.projectionDeltaWithTransform = createDelta();\n        }\n        setAnimationOrigin(delta, hasOnlyRelativeTargetChanged = false) {\n            const snapshot = this.snapshot;\n            const snapshotLatestValues = snapshot ? snapshot.latestValues : {};\n            const mixedValues = { ...this.latestValues };\n            const targetDelta = createDelta();\n            if (!this.relativeParent ||\n                !this.relativeParent.options.layoutRoot) {\n                this.relativeTarget = this.relativeTargetOrigin = undefined;\n            }\n            this.attemptToResolveRelativeTarget = !hasOnlyRelativeTargetChanged;\n            const relativeLayout = createBox();\n            const snapshotSource = snapshot ? snapshot.source : undefined;\n            const layoutSource = this.layout ? this.layout.source : undefined;\n            const isSharedLayoutAnimation = snapshotSource !== layoutSource;\n            const stack = this.getStack();\n            const isOnlyMember = !stack || stack.members.length <= 1;\n            const shouldCrossfadeOpacity = Boolean(isSharedLayoutAnimation &&\n                !isOnlyMember &&\n                this.options.crossfade === true &&\n                !this.path.some(hasOpacityCrossfade));\n            this.animationProgress = 0;\n            let prevRelativeTarget;\n            this.mixTargetDelta = (latest) => {\n                const progress = latest / 1000;\n                mixAxisDelta(targetDelta.x, delta.x, progress);\n                mixAxisDelta(targetDelta.y, delta.y, progress);\n                this.setTargetDelta(targetDelta);\n                if (this.relativeTarget &&\n                    this.relativeTargetOrigin &&\n                    this.layout &&\n                    this.relativeParent &&\n                    this.relativeParent.layout) {\n                    calcRelativePosition(relativeLayout, this.layout.layoutBox, this.relativeParent.layout.layoutBox);\n                    mixBox(this.relativeTarget, this.relativeTargetOrigin, relativeLayout, progress);\n                    /**\n                     * If this is an unchanged relative target we can consider the\n                     * projection not dirty.\n                     */\n                    if (prevRelativeTarget &&\n                        boxEquals(this.relativeTarget, prevRelativeTarget)) {\n                        this.isProjectionDirty = false;\n                    }\n                    if (!prevRelativeTarget)\n                        prevRelativeTarget = createBox();\n                    copyBoxInto(prevRelativeTarget, this.relativeTarget);\n                }\n                if (isSharedLayoutAnimation) {\n                    this.animationValues = mixedValues;\n                    mixValues(mixedValues, snapshotLatestValues, this.latestValues, progress, shouldCrossfadeOpacity, isOnlyMember);\n                }\n                this.root.scheduleUpdateProjection();\n                this.scheduleRender();\n                this.animationProgress = progress;\n            };\n            this.mixTargetDelta(this.options.layoutRoot ? 1000 : 0);\n        }\n        startAnimation(options) {\n            this.notifyListeners(\"animationStart\");\n            this.currentAnimation?.stop();\n            this.resumingFrom?.currentAnimation?.stop();\n            if (this.pendingAnimation) {\n                cancelFrame(this.pendingAnimation);\n                this.pendingAnimation = undefined;\n            }\n            /**\n             * Start the animation in the next frame to have a frame with progress 0,\n             * where the target is the same as when the animation started, so we can\n             * calculate the relative positions correctly for instant transitions.\n             */\n            this.pendingAnimation = frame.update(() => {\n                globalProjectionState.hasAnimatedSinceResize = true;\n                activeAnimations.layout++;\n                this.motionValue || (this.motionValue = motionValue(0));\n                this.currentAnimation = animateSingleValue(this.motionValue, [0, 1000], {\n                    ...options,\n                    velocity: 0,\n                    isSync: true,\n                    onUpdate: (latest) => {\n                        this.mixTargetDelta(latest);\n                        options.onUpdate && options.onUpdate(latest);\n                    },\n                    onStop: () => {\n                        activeAnimations.layout--;\n                    },\n                    onComplete: () => {\n                        activeAnimations.layout--;\n                        options.onComplete && options.onComplete();\n                        this.completeAnimation();\n                    },\n                });\n                if (this.resumingFrom) {\n                    this.resumingFrom.currentAnimation = this.currentAnimation;\n                }\n                this.pendingAnimation = undefined;\n            });\n        }\n        completeAnimation() {\n            if (this.resumingFrom) {\n                this.resumingFrom.currentAnimation = undefined;\n                this.resumingFrom.preserveOpacity = undefined;\n            }\n            const stack = this.getStack();\n            stack && stack.exitAnimationComplete();\n            this.resumingFrom =\n                this.currentAnimation =\n                    this.animationValues =\n                        undefined;\n            this.notifyListeners(\"animationComplete\");\n        }\n        finishAnimation() {\n            if (this.currentAnimation) {\n                this.mixTargetDelta && this.mixTargetDelta(animationTarget);\n                this.currentAnimation.stop();\n            }\n            this.completeAnimation();\n        }\n        applyTransformsToTarget() {\n            const lead = this.getLead();\n            let { targetWithTransforms, target, layout, latestValues } = lead;\n            if (!targetWithTransforms || !target || !layout)\n                return;\n            /**\n             * If we're only animating position, and this element isn't the lead element,\n             * then instead of projecting into the lead box we instead want to calculate\n             * a new target that aligns the two boxes but maintains the layout shape.\n             */\n            if (this !== lead &&\n                this.layout &&\n                layout &&\n                shouldAnimatePositionOnly(this.options.animationType, this.layout.layoutBox, layout.layoutBox)) {\n                target = this.target || createBox();\n                const xLength = calcLength(this.layout.layoutBox.x);\n                target.x.min = lead.target.x.min;\n                target.x.max = target.x.min + xLength;\n                const yLength = calcLength(this.layout.layoutBox.y);\n                target.y.min = lead.target.y.min;\n                target.y.max = target.y.min + yLength;\n            }\n            copyBoxInto(targetWithTransforms, target);\n            /**\n             * Apply the latest user-set transforms to the targetBox to produce the targetBoxFinal.\n             * This is the final box that we will then project into by calculating a transform delta and\n             * applying it to the corrected box.\n             */\n            transformBox(targetWithTransforms, latestValues);\n            /**\n             * Update the delta between the corrected box and the final target box, after\n             * user-set transforms are applied to it. This will be used by the renderer to\n             * create a transform style that will reproject the element from its layout layout\n             * into the desired bounding box.\n             */\n            calcBoxDelta(this.projectionDeltaWithTransform, this.layoutCorrected, targetWithTransforms, latestValues);\n        }\n        registerSharedNode(layoutId, node) {\n            if (!this.sharedNodes.has(layoutId)) {\n                this.sharedNodes.set(layoutId, new NodeStack());\n            }\n            const stack = this.sharedNodes.get(layoutId);\n            stack.add(node);\n            const config = node.options.initialPromotionConfig;\n            node.promote({\n                transition: config ? config.transition : undefined,\n                preserveFollowOpacity: config && config.shouldPreserveFollowOpacity\n                    ? config.shouldPreserveFollowOpacity(node)\n                    : undefined,\n            });\n        }\n        isLead() {\n            const stack = this.getStack();\n            return stack ? stack.lead === this : true;\n        }\n        getLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.lead || this : this;\n        }\n        getPrevLead() {\n            const { layoutId } = this.options;\n            return layoutId ? this.getStack()?.prevLead : undefined;\n        }\n        getStack() {\n            const { layoutId } = this.options;\n            if (layoutId)\n                return this.root.sharedNodes.get(layoutId);\n        }\n        promote({ needsReset, transition, preserveFollowOpacity, } = {}) {\n            const stack = this.getStack();\n            if (stack)\n                stack.promote(this, preserveFollowOpacity);\n            if (needsReset) {\n                this.projectionDelta = undefined;\n                this.needsReset = true;\n            }\n            if (transition)\n                this.setOptions({ transition });\n        }\n        relegate() {\n            const stack = this.getStack();\n            if (stack) {\n                return stack.relegate(this);\n            }\n            else {\n                return false;\n            }\n        }\n        resetSkewAndRotation() {\n            const { visualElement } = this.options;\n            if (!visualElement)\n                return;\n            // If there's no detected skew or rotation values, we can early return without a forced render.\n            let hasDistortingTransform = false;\n            /**\n             * An unrolled check for rotation values. Most elements don't have any rotation and\n             * skipping the nested loop and new object creation is 50% faster.\n             */\n            const { latestValues } = visualElement;\n            if (latestValues.z ||\n                latestValues.rotate ||\n                latestValues.rotateX ||\n                latestValues.rotateY ||\n                latestValues.rotateZ ||\n                latestValues.skewX ||\n                latestValues.skewY) {\n                hasDistortingTransform = true;\n            }\n            // If there's no distorting values, we don't need to do any more.\n            if (!hasDistortingTransform)\n                return;\n            const resetValues = {};\n            if (latestValues.z) {\n                resetDistortingTransform(\"z\", visualElement, resetValues, this.animationValues);\n            }\n            // Check the skew and rotate value of all axes and reset to 0\n            for (let i = 0; i < transformAxes.length; i++) {\n                resetDistortingTransform(`rotate${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n                resetDistortingTransform(`skew${transformAxes[i]}`, visualElement, resetValues, this.animationValues);\n            }\n            // Force a render of this element to apply the transform with all skews and rotations\n            // set to 0.\n            visualElement.render();\n            // Put back all the values we reset\n            for (const key in resetValues) {\n                visualElement.setStaticValue(key, resetValues[key]);\n                if (this.animationValues) {\n                    this.animationValues[key] = resetValues[key];\n                }\n            }\n            // Schedule a render for the next frame. This ensures we won't visually\n            // see the element with the reset rotate value applied.\n            visualElement.scheduleRender();\n        }\n        getProjectionStyles(styleProp) {\n            if (!this.instance || this.isSVG)\n                return undefined;\n            if (!this.isVisible) {\n                return hiddenVisibility;\n            }\n            const styles = {\n                visibility: \"\",\n            };\n            const transformTemplate = this.getTransformTemplate();\n            if (this.needsReset) {\n                this.needsReset = false;\n                styles.opacity = \"\";\n                styles.pointerEvents =\n                    resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                styles.transform = transformTemplate\n                    ? transformTemplate(this.latestValues, \"\")\n                    : \"none\";\n                return styles;\n            }\n            const lead = this.getLead();\n            if (!this.projectionDelta || !this.layout || !lead.target) {\n                const emptyStyles = {};\n                if (this.options.layoutId) {\n                    emptyStyles.opacity =\n                        this.latestValues.opacity !== undefined\n                            ? this.latestValues.opacity\n                            : 1;\n                    emptyStyles.pointerEvents =\n                        resolveMotionValue(styleProp?.pointerEvents) || \"\";\n                }\n                if (this.hasProjected && !hasTransform(this.latestValues)) {\n                    emptyStyles.transform = transformTemplate\n                        ? transformTemplate({}, \"\")\n                        : \"none\";\n                    this.hasProjected = false;\n                }\n                return emptyStyles;\n            }\n            const valuesToRender = lead.animationValues || lead.latestValues;\n            this.applyTransformsToTarget();\n            styles.transform = buildProjectionTransform(this.projectionDeltaWithTransform, this.treeScale, valuesToRender);\n            if (transformTemplate) {\n                styles.transform = transformTemplate(valuesToRender, styles.transform);\n            }\n            const { x, y } = this.projectionDelta;\n            styles.transformOrigin = `${x.origin * 100}% ${y.origin * 100}% 0`;\n            if (lead.animationValues) {\n                /**\n                 * If the lead component is animating, assign this either the entering/leaving\n                 * opacity\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity ??\n                            this.latestValues.opacity ??\n                            1\n                        : this.preserveOpacity\n                            ? this.latestValues.opacity\n                            : valuesToRender.opacityExit;\n            }\n            else {\n                /**\n                 * Or we're not animating at all, set the lead component to its layout\n                 * opacity and other components to hidden.\n                 */\n                styles.opacity =\n                    lead === this\n                        ? valuesToRender.opacity !== undefined\n                            ? valuesToRender.opacity\n                            : \"\"\n                        : valuesToRender.opacityExit !== undefined\n                            ? valuesToRender.opacityExit\n                            : 0;\n            }\n            /**\n             * Apply scale correction\n             */\n            for (const key in scaleCorrectors) {\n                if (valuesToRender[key] === undefined)\n                    continue;\n                const { correct, applyTo, isCSSVariable } = scaleCorrectors[key];\n                /**\n                 * Only apply scale correction to the value if we have an\n                 * active projection transform. Otherwise these values become\n                 * vulnerable to distortion if the element changes size without\n                 * a corresponding layout animation.\n                 */\n                const corrected = styles.transform === \"none\"\n                    ? valuesToRender[key]\n                    : correct(valuesToRender[key], lead);\n                if (applyTo) {\n                    const num = applyTo.length;\n                    for (let i = 0; i < num; i++) {\n                        styles[applyTo[i]] = corrected;\n                    }\n                }\n                else {\n                    // If this is a CSS variable, set it directly on the instance.\n                    // Replacing this function from creating styles to setting them\n                    // would be a good place to remove per frame object creation\n                    if (isCSSVariable) {\n                        this.options.visualElement.renderState.vars[key] = corrected;\n                    }\n                    else {\n                        styles[key] = corrected;\n                    }\n                }\n            }\n            /**\n             * Disable pointer events on follow components. This is to ensure\n             * that if a follow component covers a lead component it doesn't block\n             * pointer events on the lead.\n             */\n            if (this.options.layoutId) {\n                styles.pointerEvents =\n                    lead === this\n                        ? resolveMotionValue(styleProp?.pointerEvents) || \"\"\n                        : \"none\";\n            }\n            return styles;\n        }\n        clearSnapshot() {\n            this.resumeFrom = this.snapshot = undefined;\n        }\n        // Only run on root\n        resetTree() {\n            this.root.nodes.forEach((node) => node.currentAnimation?.stop());\n            this.root.nodes.forEach(clearMeasurements);\n            this.root.sharedNodes.clear();\n        }\n    };\n}\nfunction updateLayout(node) {\n    node.updateLayout();\n}\nfunction notifyLayoutUpdate(node) {\n    const snapshot = node.resumeFrom?.snapshot || node.snapshot;\n    if (node.isLead() &&\n        node.layout &&\n        snapshot &&\n        node.hasListeners(\"didUpdate\")) {\n        const { layoutBox: layout, measuredBox: measuredLayout } = node.layout;\n        const { animationType } = node.options;\n        const isShared = snapshot.source !== node.layout.source;\n        // TODO Maybe we want to also resize the layout snapshot so we don't trigger\n        // animations for instance if layout=\"size\" and an element has only changed position\n        if (animationType === \"size\") {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(axisSnapshot);\n                axisSnapshot.min = layout[axis].min;\n                axisSnapshot.max = axisSnapshot.min + length;\n            });\n        }\n        else if (shouldAnimatePositionOnly(animationType, snapshot.layoutBox, layout)) {\n            eachAxis((axis) => {\n                const axisSnapshot = isShared\n                    ? snapshot.measuredBox[axis]\n                    : snapshot.layoutBox[axis];\n                const length = calcLength(layout[axis]);\n                axisSnapshot.max = axisSnapshot.min + length;\n                /**\n                 * Ensure relative target gets resized and rerendererd\n                 */\n                if (node.relativeTarget && !node.currentAnimation) {\n                    node.isProjectionDirty = true;\n                    node.relativeTarget[axis].max =\n                        node.relativeTarget[axis].min + length;\n                }\n            });\n        }\n        const layoutDelta = createDelta();\n        calcBoxDelta(layoutDelta, layout, snapshot.layoutBox);\n        const visualDelta = createDelta();\n        if (isShared) {\n            calcBoxDelta(visualDelta, node.applyTransform(measuredLayout, true), snapshot.measuredBox);\n        }\n        else {\n            calcBoxDelta(visualDelta, layout, snapshot.layoutBox);\n        }\n        const hasLayoutChanged = !isDeltaZero(layoutDelta);\n        let hasRelativeLayoutChanged = false;\n        if (!node.resumeFrom) {\n            const relativeParent = node.getClosestProjectingParent();\n            /**\n             * If the relativeParent is itself resuming from a different element then\n             * the relative snapshot is not relavent\n             */\n            if (relativeParent && !relativeParent.resumeFrom) {\n                const { snapshot: parentSnapshot, layout: parentLayout } = relativeParent;\n                if (parentSnapshot && parentLayout) {\n                    const relativeSnapshot = createBox();\n                    calcRelativePosition(relativeSnapshot, snapshot.layoutBox, parentSnapshot.layoutBox);\n                    const relativeLayout = createBox();\n                    calcRelativePosition(relativeLayout, layout, parentLayout.layoutBox);\n                    if (!boxEqualsRounded(relativeSnapshot, relativeLayout)) {\n                        hasRelativeLayoutChanged = true;\n                    }\n                    if (relativeParent.options.layoutRoot) {\n                        node.relativeTarget = relativeLayout;\n                        node.relativeTargetOrigin = relativeSnapshot;\n                        node.relativeParent = relativeParent;\n                    }\n                }\n            }\n        }\n        node.notifyListeners(\"didUpdate\", {\n            layout,\n            snapshot,\n            delta: visualDelta,\n            layoutDelta,\n            hasLayoutChanged,\n            hasRelativeLayoutChanged,\n        });\n    }\n    else if (node.isLead()) {\n        const { onExitComplete } = node.options;\n        onExitComplete && onExitComplete();\n    }\n    /**\n     * Clearing transition\n     * TODO: Investigate why this transition is being passed in as {type: false } from Framer\n     * and why we need it at all\n     */\n    node.options.transition = undefined;\n}\nfunction propagateDirtyNodes(node) {\n    /**\n     * Increase debug counter for nodes encountered this frame\n     */\n    if (statsBuffer.value) {\n        metrics.nodes++;\n    }\n    if (!node.parent)\n        return;\n    /**\n     * If this node isn't projecting, propagate isProjectionDirty. It will have\n     * no performance impact but it will allow the next child that *is* projecting\n     * but *isn't* dirty to just check its parent to see if *any* ancestor needs\n     * correcting.\n     */\n    if (!node.isProjecting()) {\n        node.isProjectionDirty = node.parent.isProjectionDirty;\n    }\n    /**\n     * Propagate isSharedProjectionDirty and isTransformDirty\n     * throughout the whole tree. A future revision can take another look at\n     * this but for safety we still recalcualte shared nodes.\n     */\n    node.isSharedProjectionDirty || (node.isSharedProjectionDirty = Boolean(node.isProjectionDirty ||\n        node.parent.isProjectionDirty ||\n        node.parent.isSharedProjectionDirty));\n    node.isTransformDirty || (node.isTransformDirty = node.parent.isTransformDirty);\n}\nfunction cleanDirtyNodes(node) {\n    node.isProjectionDirty =\n        node.isSharedProjectionDirty =\n            node.isTransformDirty =\n                false;\n}\nfunction clearSnapshot(node) {\n    node.clearSnapshot();\n}\nfunction clearMeasurements(node) {\n    node.clearMeasurements();\n}\nfunction clearIsLayoutDirty(node) {\n    node.isLayoutDirty = false;\n}\nfunction resetTransformStyle(node) {\n    const { visualElement } = node.options;\n    if (visualElement && visualElement.getProps().onBeforeLayoutMeasure) {\n        visualElement.notify(\"BeforeLayoutMeasure\");\n    }\n    node.resetTransform();\n}\nfunction finishAnimation(node) {\n    node.finishAnimation();\n    node.targetDelta = node.relativeTarget = node.target = undefined;\n    node.isProjectionDirty = true;\n}\nfunction resolveTargetDelta(node) {\n    node.resolveTargetDelta();\n}\nfunction calcProjection(node) {\n    node.calcProjection();\n}\nfunction resetSkewAndRotation(node) {\n    node.resetSkewAndRotation();\n}\nfunction removeLeadSnapshots(stack) {\n    stack.removeLeadSnapshot();\n}\nfunction mixAxisDelta(output, delta, p) {\n    output.translate = mixNumber(delta.translate, 0, p);\n    output.scale = mixNumber(delta.scale, 1, p);\n    output.origin = delta.origin;\n    output.originPoint = delta.originPoint;\n}\nfunction mixAxis(output, from, to, p) {\n    output.min = mixNumber(from.min, to.min, p);\n    output.max = mixNumber(from.max, to.max, p);\n}\nfunction mixBox(output, from, to, p) {\n    mixAxis(output.x, from.x, to.x, p);\n    mixAxis(output.y, from.y, to.y, p);\n}\nfunction hasOpacityCrossfade(node) {\n    return (node.animationValues && node.animationValues.opacityExit !== undefined);\n}\nconst defaultLayoutTransition = {\n    duration: 0.45,\n    ease: [0.4, 0, 0.1, 1],\n};\nconst userAgentContains = (string) => typeof navigator !== \"undefined\" &&\n    navigator.userAgent &&\n    navigator.userAgent.toLowerCase().includes(string);\n/**\n * Measured bounding boxes must be rounded in Safari and\n * left untouched in Chrome, otherwise non-integer layouts within scaled-up elements\n * can appear to jump.\n */\nconst roundPoint = userAgentContains(\"applewebkit/\") && !userAgentContains(\"chrome/\")\n    ? Math.round\n    : noop;\nfunction roundAxis(axis) {\n    // Round to the nearest .5 pixels to support subpixel layouts\n    axis.min = roundPoint(axis.min);\n    axis.max = roundPoint(axis.max);\n}\nfunction roundBox(box) {\n    roundAxis(box.x);\n    roundAxis(box.y);\n}\nfunction shouldAnimatePositionOnly(animationType, snapshot, layout) {\n    return (animationType === \"position\" ||\n        (animationType === \"preserve-aspect\" &&\n            !isNear(aspectRatio(snapshot), aspectRatio(layout), 0.2)));\n}\nfunction checkNodeWasScrollRoot(node) {\n    return node !== node.root && node.scroll?.wasRoot;\n}\n\nexport { cleanDirtyNodes, createProjectionNode, mixAxis, mixAxisDelta, mixBox, propagateDirtyNodes };\n"], "mappings": ";AAAA,SAASA,WAAW,EAAEC,YAAY,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,QAAQ,YAAY;AACjM,SAASC,mBAAmB,EAAEC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAC/D,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,oBAAoB,QAAQ,oDAAoD;AACzF,SAASC,QAAQ,QAAQ,kCAAkC;AAC3D,SAASC,KAAK,QAAQ,uBAAuB;AAC7C,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,SAAS,QAAQ,6BAA6B;AACvD,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,sBAAsB;AACrE,SAASC,aAAa,EAAEC,YAAY,EAAEC,aAAa,EAAEC,eAAe,QAAQ,6BAA6B;AACzG,SAASC,UAAU,EAAEC,oBAAoB,EAAEC,eAAe,EAAEC,YAAY,EAAEC,MAAM,QAAQ,4BAA4B;AACpH,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,SAAS,EAAEC,WAAW,QAAQ,wBAAwB;AAC/D,SAASC,gBAAgB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,QAAQ,uBAAuB;AAC9G,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,wBAAwB,QAAQ,yBAAyB;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,YAAY,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,4BAA4B;AACnF,SAASC,qBAAqB,QAAQ,aAAa;AAEnD,MAAMC,OAAO,GAAG;EACZC,KAAK,EAAE,CAAC;EACRC,sBAAsB,EAAE,CAAC;EACzBC,qBAAqB,EAAE;AAC3B,CAAC;AACD,MAAMC,aAAa,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AACzC,MAAMC,gBAAgB,GAAG;EAAEC,UAAU,EAAE;AAAS,CAAC;AACjD;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAG,IAAI;AAC5B,IAAIC,EAAE,GAAG,CAAC;AACV,SAASC,wBAAwBA,CAACC,GAAG,EAAEC,aAAa,EAAEC,MAAM,EAAEC,qBAAqB,EAAE;EACjF,MAAM;IAAEC;EAAa,CAAC,GAAGH,aAAa;EACtC;EACA,IAAIG,YAAY,CAACJ,GAAG,CAAC,EAAE;IACnBE,MAAM,CAACF,GAAG,CAAC,GAAGI,YAAY,CAACJ,GAAG,CAAC;IAC/BC,aAAa,CAACI,cAAc,CAACL,GAAG,EAAE,CAAC,CAAC;IACpC,IAAIG,qBAAqB,EAAE;MACvBA,qBAAqB,CAACH,GAAG,CAAC,GAAG,CAAC;IAClC;EACJ;AACJ;AACA,SAASM,sCAAsCA,CAACC,cAAc,EAAE;EAC5DA,cAAc,CAACC,yBAAyB,GAAG,IAAI;EAC/C,IAAID,cAAc,CAACE,IAAI,KAAKF,cAAc,EACtC;EACJ,MAAM;IAAEN;EAAc,CAAC,GAAGM,cAAc,CAACG,OAAO;EAChD,IAAI,CAACT,aAAa,EACd;EACJ,MAAMU,QAAQ,GAAGrD,oBAAoB,CAAC2C,aAAa,CAAC;EACpD,IAAIW,MAAM,CAACC,2BAA2B,CAACF,QAAQ,EAAE,WAAW,CAAC,EAAE;IAC3D,MAAM;MAAEG,MAAM;MAAEC;IAAS,CAAC,GAAGR,cAAc,CAACG,OAAO;IACnDE,MAAM,CAACI,8BAA8B,CAACL,QAAQ,EAAE,WAAW,EAAE7D,KAAK,EAAE,EAAEgE,MAAM,IAAIC,QAAQ,CAAC,CAAC;EAC9F;EACA,MAAM;IAAEE;EAAO,CAAC,GAAGV,cAAc;EACjC,IAAIU,MAAM,IAAI,CAACA,MAAM,CAACT,yBAAyB,EAAE;IAC7CF,sCAAsC,CAACW,MAAM,CAAC;EAClD;AACJ;AACA,SAASC,oBAAoBA,CAAAC,IAAA,EAA6F;EAAA,IAA5F;IAAEC,oBAAoB;IAAEC,aAAa;IAAEC,aAAa;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAAL,IAAA;EACpH,OAAO,MAAMM,cAAc,CAAC;IACxBC,WAAWA,CAAA,EAAgD;MAAA,IAA/CtB,YAAY,GAAAuB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAAA,IAAEV,MAAM,GAAAU,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAG,CAAC;MACrD;AACZ;AACA;MACY,IAAI,CAACvB,EAAE,GAAGA,EAAE,EAAE;MACd;AACZ;AACA;MACY,IAAI,CAACgC,WAAW,GAAG,CAAC;MACpB;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;AACZ;AACA;AACA;MACY,IAAI,CAACtB,OAAO,GAAG,CAAC,CAAC;MACjB;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACuB,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,kBAAkB,GAAG,KAAK;MAC/B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACC,aAAa,GAAG,KAAK;MAC1B;AACZ;AACA;AACA;MACY,IAAI,CAACC,iBAAiB,GAAG,KAAK;MAC9B;AACZ;AACA;AACA;MACY,IAAI,CAACC,uBAAuB,GAAG,KAAK;MACpC;AACZ;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B;AACZ;AACA;MACY,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC,IAAI,CAACC,qBAAqB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,KAAK,GAAG,KAAK;MAClB;AACZ;AACA;AACA;MACY,IAAI,CAACC,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAACC,oBAAoB,GAAG,KAAK;MACjC;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACpC,yBAAyB,GAAG,KAAK;MACtC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAACqC,SAAS,GAAG;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MAC/B;AACZ;AACA;MACY,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B;MACA,IAAI,CAACC,eAAe,GAAG,KAAK;MAC5B,IAAI,CAACC,cAAc,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,CAAC;MACzC,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC,IAAI,CAACC,iBAAiB,GAAG,MAAM;QAC3B,IAAI,IAAI,CAACd,UAAU,EAAE;UACjB,IAAI,CAACA,UAAU,GAAG,KAAK;UACvB,IAAI,CAACe,iBAAiB,CAAC,CAAC;QAC5B;MACJ,CAAC;MACD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACC,gBAAgB,GAAG,MAAM;QAC1B,IAAI,CAACH,yBAAyB,GAAG,KAAK;QACtC;AAChB;AACA;AACA;QACgB,IAAIjH,WAAW,CAACqH,KAAK,EAAE;UACnBpE,OAAO,CAACC,KAAK,GACTD,OAAO,CAACE,sBAAsB,GAC1BF,OAAO,CAACG,qBAAqB,GACzB,CAAC;QACjB;QACA,IAAI,CAACF,KAAK,CAACoE,OAAO,CAACC,mBAAmB,CAAC;QACvC,IAAI,CAACrE,KAAK,CAACoE,OAAO,CAACE,kBAAkB,CAAC;QACtC,IAAI,CAACtE,KAAK,CAACoE,OAAO,CAACG,cAAc,CAAC;QAClC,IAAI,CAACvE,KAAK,CAACoE,OAAO,CAACI,eAAe,CAAC;QACnC,IAAI1H,WAAW,CAAC2H,oBAAoB,EAAE;UAClC3H,WAAW,CAAC2H,oBAAoB,CAAC1E,OAAO,CAAC;QAC7C;MACJ,CAAC;MACD;AACZ;AACA;MACY,IAAI,CAAC2E,wBAAwB,GAAG,GAAG;MACnC,IAAI,CAACC,YAAY,GAAG,KAAK;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,iBAAiB,GAAG,CAAC;MAC1B;AACZ;AACA;MACY;MACA,IAAI,CAACC,WAAW,GAAG,IAAIpB,GAAG,CAAC,CAAC;MAC5B,IAAI,CAAC7C,YAAY,GAAGA,YAAY;MAChC,IAAI,CAACK,IAAI,GAAGQ,MAAM,GAAGA,MAAM,CAACR,IAAI,IAAIQ,MAAM,GAAG,IAAI;MACjD,IAAI,CAACqD,IAAI,GAAGrD,MAAM,GAAG,CAAC,GAAGA,MAAM,CAACqD,IAAI,EAAErD,MAAM,CAAC,GAAG,EAAE;MAClD,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACsD,KAAK,GAAGtD,MAAM,GAAGA,MAAM,CAACsD,KAAK,GAAG,CAAC,GAAG,CAAC;MAC1C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACvC,IAAI,CAACF,IAAI,CAACE,CAAC,CAAC,CAAC5B,oBAAoB,GAAG,IAAI;MAC5C;MACA,IAAI,IAAI,CAACnC,IAAI,KAAK,IAAI,EAClB,IAAI,CAAClB,KAAK,GAAG,IAAIhC,QAAQ,CAAC,CAAC;IACnC;IACAkH,gBAAgBA,CAACC,IAAI,EAAEC,OAAO,EAAE;MAC5B,IAAI,CAAC,IAAI,CAAC3B,aAAa,CAAC4B,GAAG,CAACF,IAAI,CAAC,EAAE;QAC/B,IAAI,CAAC1B,aAAa,CAAC6B,GAAG,CAACH,IAAI,EAAE,IAAIxH,mBAAmB,CAAC,CAAC,CAAC;MAC3D;MACA,OAAO,IAAI,CAAC8F,aAAa,CAAC8B,GAAG,CAACJ,IAAI,CAAC,CAACK,GAAG,CAACJ,OAAO,CAAC;IACpD;IACAK,eAAeA,CAACN,IAAI,EAAW;MAC3B,MAAMO,mBAAmB,GAAG,IAAI,CAACjC,aAAa,CAAC8B,GAAG,CAACJ,IAAI,CAAC;MAAC,SAAAQ,IAAA,GAAAvD,SAAA,CAAAC,MAAA,EADpCuD,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAA1D,SAAA,CAAA0D,IAAA;MAAA;MAEzBJ,mBAAmB,IAAIA,mBAAmB,CAACK,MAAM,CAAC,GAAGH,IAAI,CAAC;IAC9D;IACAI,YAAYA,CAACb,IAAI,EAAE;MACf,OAAO,IAAI,CAAC1B,aAAa,CAAC4B,GAAG,CAACF,IAAI,CAAC;IACvC;IACA;AACR;AACA;IACQc,KAAKA,CAACC,QAAQ,EAAE;MACZ,IAAI,IAAI,CAACA,QAAQ,EACb;MACJ,IAAI,CAAC/C,KAAK,GAAGpG,YAAY,CAACmJ,QAAQ,CAAC,IAAI,CAAClJ,eAAe,CAACkJ,QAAQ,CAAC;MACjE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;MACxB,MAAM;QAAE1E,QAAQ;QAAED,MAAM;QAAEb;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACxD,IAAIT,aAAa,IAAI,CAACA,aAAa,CAACyF,OAAO,EAAE;QACzCzF,aAAa,CAACuF,KAAK,CAACC,QAAQ,CAAC;MACjC;MACA,IAAI,CAAChF,IAAI,CAAClB,KAAK,CAACwF,GAAG,CAAC,IAAI,CAAC;MACzB,IAAI,CAAC9D,MAAM,IAAI,IAAI,CAACA,MAAM,CAACc,QAAQ,CAACgD,GAAG,CAAC,IAAI,CAAC;MAC7C,IAAI,IAAI,CAACtE,IAAI,CAACyC,eAAe,KAAKpC,MAAM,IAAIC,QAAQ,CAAC,EAAE;QACnD,IAAI,CAACoB,aAAa,GAAG,IAAI;MAC7B;MACA,IAAIf,oBAAoB,EAAE;QACtB,IAAIuE,WAAW;QACf,MAAMC,mBAAmB,GAAGA,CAAA,KAAO,IAAI,CAACnF,IAAI,CAAC+B,qBAAqB,GAAG,KAAM;QAC3EpB,oBAAoB,CAACqE,QAAQ,EAAE,MAAM;UACjC,IAAI,CAAChF,IAAI,CAAC+B,qBAAqB,GAAG,IAAI;UACtCmD,WAAW,IAAIA,WAAW,CAAC,CAAC;UAC5BA,WAAW,GAAGnI,KAAK,CAACoI,mBAAmB,EAAE,GAAG,CAAC;UAC7C,IAAIvG,qBAAqB,CAACwG,sBAAsB,EAAE;YAC9CxG,qBAAqB,CAACwG,sBAAsB,GAAG,KAAK;YACpD,IAAI,CAACtG,KAAK,CAACoE,OAAO,CAACmC,eAAe,CAAC;UACvC;QACJ,CAAC,CAAC;MACN;MACA,IAAI/E,QAAQ,EAAE;QACV,IAAI,CAACN,IAAI,CAACsF,kBAAkB,CAAChF,QAAQ,EAAE,IAAI,CAAC;MAChD;MACA;MACA,IAAI,IAAI,CAACL,OAAO,CAACsF,OAAO,KAAK,KAAK,IAC9B/F,aAAa,KACZc,QAAQ,IAAID,MAAM,CAAC,EAAE;QACtB,IAAI,CAAC2D,gBAAgB,CAAC,WAAW,EAAEwB,KAAA,IAA+E;UAAA,IAA9E;YAAEC,KAAK;YAAEC,gBAAgB;YAAEC,wBAAwB;YAAEtF,MAAM,EAAEuF;UAAW,CAAC,GAAAJ,KAAA;UACzG,IAAI,IAAI,CAACK,sBAAsB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACC,MAAM,GAAG1E,SAAS;YACvB,IAAI,CAAC2E,cAAc,GAAG3E,SAAS;YAC/B;UACJ;UACA;UACA,MAAM4E,gBAAgB,GAAG,IAAI,CAAC/F,OAAO,CAACgG,UAAU,IAC5CzG,aAAa,CAAC0G,oBAAoB,CAAC,CAAC,IACpCC,uBAAuB;UAC3B,MAAM;YAAEC,sBAAsB;YAAEC;UAA2B,CAAC,GAAG7G,aAAa,CAAC8G,QAAQ,CAAC,CAAC;UACvF;AACpB;AACA;AACA;UACoB,MAAMC,gBAAgB,GAAG,CAAC,IAAI,CAACC,YAAY,IACvC,CAACxI,gBAAgB,CAAC,IAAI,CAACwI,YAAY,EAAEZ,SAAS,CAAC;UACnD;AACpB;AACA;AACA;AACA;UACoB;UACA;AACpB;AACA;AACA;AACA;UACoB,MAAMa,4BAA4B,GAAG,CAACf,gBAAgB,IAAIC,wBAAwB;UAClF,IAAI,IAAI,CAAC1F,OAAO,CAACyG,UAAU,IACvB,IAAI,CAACC,UAAU,IACfF,4BAA4B,IAC3Bf,gBAAgB,KACZa,gBAAgB,IAAI,CAAC,IAAI,CAACK,gBAAgB,CAAE,EAAE;YACnD,IAAI,IAAI,CAACD,UAAU,EAAE;cACjB,IAAI,CAACE,YAAY,GAAG,IAAI,CAACF,UAAU;cACnC,IAAI,CAACE,YAAY,CAACA,YAAY,GAAGzF,SAAS;YAC9C;YACA,MAAM0F,gBAAgB,GAAAC,aAAA,CAAAA,aAAA,KACfhL,kBAAkB,CAACiK,gBAAgB,EAAE,QAAQ,CAAC;cACjDgB,MAAM,EAAEZ,sBAAsB;cAC9Ba,UAAU,EAAEZ;YAAyB,EACxC;YACD,IAAI7G,aAAa,CAAC0H,kBAAkB,IAChC,IAAI,CAACjH,OAAO,CAACyG,UAAU,EAAE;cACzBI,gBAAgB,CAAC/J,KAAK,GAAG,CAAC;cAC1B+J,gBAAgB,CAACK,IAAI,GAAG,KAAK;YACjC;YACA,IAAI,CAACC,cAAc,CAACN,gBAAgB,CAAC;YACrC;AACxB;AACA;AACA;YACwB,IAAI,CAACO,kBAAkB,CAAC5B,KAAK,EAAEgB,4BAA4B,CAAC;UAChE,CAAC,MACI;YACD;AACxB;AACA;AACA;AACA;YACwB,IAAI,CAACf,gBAAgB,EAAE;cACnBL,eAAe,CAAC,IAAI,CAAC;YACzB;YACA,IAAI,IAAI,CAACiC,MAAM,CAAC,CAAC,IAAI,IAAI,CAACrH,OAAO,CAACsH,cAAc,EAAE;cAC9C,IAAI,CAACtH,OAAO,CAACsH,cAAc,CAAC,CAAC;YACjC;UACJ;UACA,IAAI,CAACf,YAAY,GAAGZ,SAAS;QACjC,CAAC,CAAC;MACN;IACJ;IACA4B,OAAOA,CAAA,EAAG;MACN,IAAI,CAACvH,OAAO,CAACK,QAAQ,IAAI,IAAI,CAACmH,UAAU,CAAC,CAAC;MAC1C,IAAI,CAACzH,IAAI,CAAClB,KAAK,CAAC4I,MAAM,CAAC,IAAI,CAAC;MAC5B,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;MAC3B,IAAI,CAAClH,MAAM,IAAI,IAAI,CAACA,MAAM,CAACc,QAAQ,CAACuG,MAAM,CAAC,IAAI,CAAC;MAChD,IAAI,CAAC7C,QAAQ,GAAG5D,SAAS;MACzB,IAAI,CAACmB,aAAa,CAACuF,KAAK,CAAC,CAAC;MAC1B9L,WAAW,CAAC,IAAI,CAACgH,gBAAgB,CAAC;IACtC;IACA;IACA+E,WAAWA,CAAA,EAAG;MACV,IAAI,CAACjG,qBAAqB,GAAG,IAAI;IACrC;IACAkG,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAClG,qBAAqB,GAAG,KAAK;IACtC;IACAmG,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACnG,qBAAqB,IAAI,IAAI,CAACC,qBAAqB;IACnE;IACA8D,sBAAsBA,CAAA,EAAG;MACrB,OAAQ,IAAI,CAACpE,kBAAkB,IAC1B,IAAI,CAACjB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACqF,sBAAsB,CAAC,CAAE,IACrD,KAAK;IACb;IACA;IACAqC,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,EACtB;MACJ,IAAI,CAACjG,UAAU,GAAG,IAAI;MACtB,IAAI,CAAClD,KAAK,IAAI,IAAI,CAACA,KAAK,CAACoE,OAAO,CAACiF,oBAAoB,CAAC;MACtD,IAAI,CAAC9G,WAAW,EAAE;IACtB;IACA+G,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAE5I;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,OAAOT,aAAa,IAAIA,aAAa,CAAC8G,QAAQ,CAAC,CAAC,CAAC+B,iBAAiB;IACtE;IACAZ,UAAUA,CAAA,EAA+B;MAAA,IAA9Ba,qBAAqB,GAAApH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACnC,IAAI,CAAClB,IAAI,CAACyC,eAAe,GAAG,IAAI;MAChC,IAAI,IAAI,CAACzC,IAAI,CAACiI,eAAe,CAAC,CAAC,EAAE;QAC7B,IAAI,CAAChI,OAAO,CAACsH,cAAc,IAAI,IAAI,CAACtH,OAAO,CAACsH,cAAc,CAAC,CAAC;QAC5D;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAIpH,MAAM,CAACI,8BAA8B,IACrC,CAAC,IAAI,CAACR,yBAAyB,EAAE;QACjCF,sCAAsC,CAAC,IAAI,CAAC;MAChD;MACA,CAAC,IAAI,CAACG,IAAI,CAACgC,UAAU,IAAI,IAAI,CAAChC,IAAI,CAACkI,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACxG,aAAa,EAClB;MACJ,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACvC,MAAMwE,IAAI,GAAG,IAAI,CAAC1E,IAAI,CAACE,CAAC,CAAC;QACzBwE,IAAI,CAACpG,oBAAoB,GAAG,IAAI;QAChCoG,IAAI,CAACC,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAID,IAAI,CAACtI,OAAO,CAACyG,UAAU,EAAE;UACzB6B,IAAI,CAACd,UAAU,CAAC,KAAK,CAAC;QAC1B;MACJ;MACA,MAAM;QAAEnH,QAAQ;QAAED;MAAO,CAAC,GAAG,IAAI,CAACJ,OAAO;MACzC,IAAIK,QAAQ,KAAKc,SAAS,IAAI,CAACf,MAAM,EACjC;MACJ,MAAMgI,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,CAACK,0BAA0B,GAAGJ,iBAAiB,GAC7CA,iBAAiB,CAAC,IAAI,CAAC1I,YAAY,EAAE,EAAE,CAAC,GACxCyB,SAAS;MACf,IAAI,CAACsH,cAAc,CAAC,CAAC;MACrBJ,qBAAqB,IAAI,IAAI,CAAC/D,eAAe,CAAC,YAAY,CAAC;IAC/D;IACA3B,MAAMA,CAAA,EAAG;MACL,IAAI,CAACF,eAAe,GAAG,KAAK;MAC5B,MAAMiG,gBAAgB,GAAG,IAAI,CAACV,eAAe,CAAC,CAAC;MAC/C;MACA;MACA;MACA,IAAIU,gBAAgB,EAAE;QAClB,IAAI,CAACX,aAAa,CAAC,CAAC;QACpB,IAAI,CAACjF,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACjE,KAAK,CAACoE,OAAO,CAAC0F,iBAAiB,CAAC;QACrC;MACJ;MACA,IAAI,CAAC,IAAI,CAAC5G,UAAU,EAAE;QAClB,IAAI,CAAClD,KAAK,CAACoE,OAAO,CAAC2F,kBAAkB,CAAC;MAC1C;MACA,IAAI,CAAC7G,UAAU,GAAG,KAAK;MACvB;AACZ;AACA;MACY,IAAI,CAAClD,KAAK,CAACoE,OAAO,CAAC4F,mBAAmB,CAAC;MACvC;AACZ;AACA;MACY;MACA,IAAI,CAAChK,KAAK,CAACoE,OAAO,CAAC6F,YAAY,CAAC;MAChC;AACZ;AACA;MACY;MACA,IAAI,CAACjK,KAAK,CAACoE,OAAO,CAAC8F,kBAAkB,CAAC;MACtC,IAAI,CAACjG,iBAAiB,CAAC,CAAC;MACxB;AACZ;AACA;AACA;AACA;MACY,MAAMkG,GAAG,GAAGhN,IAAI,CAACgN,GAAG,CAAC,CAAC;MACtB/M,SAAS,CAACuJ,KAAK,GAAG/I,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,EAAEuM,GAAG,GAAG/M,SAAS,CAACgN,SAAS,CAAC;MAChEhN,SAAS,CAACgN,SAAS,GAAGD,GAAG;MACzB/M,SAAS,CAACiN,YAAY,GAAG,IAAI;MAC7BhN,UAAU,CAACyG,MAAM,CAACwG,OAAO,CAAClN,SAAS,CAAC;MACpCC,UAAU,CAACkN,SAAS,CAACD,OAAO,CAAClN,SAAS,CAAC;MACvCC,UAAU,CAACmN,MAAM,CAACF,OAAO,CAAClN,SAAS,CAAC;MACpCA,SAAS,CAACiN,YAAY,GAAG,KAAK;IAClC;IACAI,SAASA,CAAA,EAAG;MACR,IAAI,CAAC,IAAI,CAAC7G,eAAe,EAAE;QACvB,IAAI,CAACA,eAAe,GAAG,IAAI;QAC3BtG,SAAS,CAACoN,IAAI,CAAC,IAAI,CAAC7G,cAAc,CAAC;MACvC;IACJ;IACAI,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACjE,KAAK,CAACoE,OAAO,CAACuG,aAAa,CAAC;MACjC,IAAI,CAAC7F,WAAW,CAACV,OAAO,CAACwG,mBAAmB,CAAC;IACjD;IACAC,wBAAwBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAAC9G,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrCxG,KAAK,CAACgN,SAAS,CAAC,IAAI,CAACrG,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC;MACvD;IACJ;IACA4G,yBAAyBA,CAAA,EAAG;MACxB;AACZ;AACA;AACA;AACA;MACYvN,KAAK,CAACwN,UAAU,CAAC,MAAM;QACnB,IAAI,IAAI,CAACnI,aAAa,EAAE;UACpB,IAAI,CAAC1B,IAAI,CAACuJ,SAAS,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACvJ,IAAI,CAAC8C,iBAAiB,CAAC,CAAC;QACjC;MACJ,CAAC,CAAC;IACN;IACA;AACR;AACA;IACQ4F,cAAcA,CAAA,EAAG;MACb,IAAI,IAAI,CAACoB,QAAQ,IAAI,CAAC,IAAI,CAAC9E,QAAQ,EAC/B;MACJ,IAAI,CAAC8E,QAAQ,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC9B,IAAI,IAAI,CAACD,QAAQ,IACb,CAACtM,UAAU,CAAC,IAAI,CAACsM,QAAQ,CAACE,WAAW,CAAC3H,CAAC,CAAC,IACxC,CAAC7E,UAAU,CAAC,IAAI,CAACsM,QAAQ,CAACE,WAAW,CAAC1H,CAAC,CAAC,EAAE;QAC1C,IAAI,CAACwH,QAAQ,GAAG1I,SAAS;MAC7B;IACJ;IACA2H,YAAYA,CAAA,EAAG;MACX,IAAI,CAAC,IAAI,CAAC/D,QAAQ,EACd;MACJ;MACA,IAAI,CAACwD,YAAY,CAAC,CAAC;MACnB,IAAI,EAAE,IAAI,CAACvI,OAAO,CAACgK,mBAAmB,IAAI,IAAI,CAAC3C,MAAM,CAAC,CAAC,CAAC,IACpD,CAAC,IAAI,CAAC5F,aAAa,EAAE;QACrB;MACJ;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACiF,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAAC3B,QAAQ,EAAE;QAC9C,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;UACvC,MAAMwE,IAAI,GAAG,IAAI,CAAC1E,IAAI,CAACE,CAAC,CAAC;UACzBwE,IAAI,CAACC,YAAY,CAAC,CAAC;QACvB;MACJ;MACA,MAAM0B,UAAU,GAAG,IAAI,CAAC7J,MAAM;MAC9B,IAAI,CAACA,MAAM,GAAG,IAAI,CAAC0J,OAAO,CAAC,KAAK,CAAC;MACjC,IAAI,CAACI,eAAe,GAAGrM,SAAS,CAAC,CAAC;MAClC,IAAI,CAAC4D,aAAa,GAAG,KAAK;MAC1B,IAAI,CAAC0I,eAAe,GAAGhJ,SAAS;MAChC,IAAI,CAACmD,eAAe,CAAC,SAAS,EAAE,IAAI,CAAClE,MAAM,CAACgK,SAAS,CAAC;MACtD,MAAM;QAAE7K;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtCT,aAAa,IACTA,aAAa,CAACqF,MAAM,CAAC,eAAe,EAAE,IAAI,CAACxE,MAAM,CAACgK,SAAS,EAAEH,UAAU,GAAGA,UAAU,CAACG,SAAS,GAAGjJ,SAAS,CAAC;IACnH;IACAoH,YAAYA,CAAA,EAAoB;MAAA,IAAnB8B,KAAK,GAAApJ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,SAAS;MAC1B,IAAIqJ,gBAAgB,GAAGC,OAAO,CAAC,IAAI,CAACvK,OAAO,CAACwK,YAAY,IAAI,IAAI,CAACzF,QAAQ,CAAC;MAC1E,IAAI,IAAI,CAAC0F,MAAM,IACX,IAAI,CAACA,MAAM,CAACrJ,WAAW,KAAK,IAAI,CAACrB,IAAI,CAACqB,WAAW,IACjD,IAAI,CAACqJ,MAAM,CAACJ,KAAK,KAAKA,KAAK,EAAE;QAC7BC,gBAAgB,GAAG,KAAK;MAC5B;MACA,IAAIA,gBAAgB,IAAI,IAAI,CAACvF,QAAQ,EAAE;QACnC,MAAM2F,MAAM,GAAG7J,iBAAiB,CAAC,IAAI,CAACkE,QAAQ,CAAC;QAC/C,IAAI,CAAC0F,MAAM,GAAG;UACVrJ,WAAW,EAAE,IAAI,CAACrB,IAAI,CAACqB,WAAW;UAClCiJ,KAAK;UACLK,MAAM;UACNC,MAAM,EAAE/J,aAAa,CAAC,IAAI,CAACmE,QAAQ,CAAC;UACpC6F,OAAO,EAAE,IAAI,CAACH,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,MAAM,GAAGA;QAChD,CAAC;MACL;IACJ;IACA5J,cAAcA,CAAA,EAAG;MACb,IAAI,CAACA,cAAc,EACf;MACJ,MAAM+J,gBAAgB,GAAG,IAAI,CAACpJ,aAAa,IACvC,IAAI,CAACS,oBAAoB,IACzB,IAAI,CAAClC,OAAO,CAACgK,mBAAmB;MACpC,MAAMc,aAAa,GAAG,IAAI,CAACX,eAAe,IAAI,CAACnM,WAAW,CAAC,IAAI,CAACmM,eAAe,CAAC;MAChF,MAAM/B,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,MAAM4C,sBAAsB,GAAG3C,iBAAiB,GAC1CA,iBAAiB,CAAC,IAAI,CAAC1I,YAAY,EAAE,EAAE,CAAC,GACxCyB,SAAS;MACf,MAAM6J,2BAA2B,GAAGD,sBAAsB,KAAK,IAAI,CAACvC,0BAA0B;MAC9F,IAAIqC,gBAAgB,IAChB,IAAI,CAAC9F,QAAQ,KACZ+F,aAAa,IACVtM,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,IAC/BsL,2BAA2B,CAAC,EAAE;QAClClK,cAAc,CAAC,IAAI,CAACiE,QAAQ,EAAEgG,sBAAsB,CAAC;QACrD,IAAI,CAAC7I,oBAAoB,GAAG,KAAK;QACjC,IAAI,CAAC+I,cAAc,CAAC,CAAC;MACzB;IACJ;IACAnB,OAAOA,CAAA,EAAyB;MAAA,IAAxBoB,eAAe,GAAAjK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC1B,MAAMkK,OAAO,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACrC,IAAIhB,SAAS,GAAG,IAAI,CAACiB,mBAAmB,CAACF,OAAO,CAAC;MACjD;AACZ;AACA;AACA;AACA;MACY,IAAID,eAAe,EAAE;QACjBd,SAAS,GAAG,IAAI,CAACc,eAAe,CAACd,SAAS,CAAC;MAC/C;MACAkB,QAAQ,CAAClB,SAAS,CAAC;MACnB,OAAO;QACHhJ,WAAW,EAAE,IAAI,CAACrB,IAAI,CAACqB,WAAW;QAClC2I,WAAW,EAAEoB,OAAO;QACpBf,SAAS;QACT1K,YAAY,EAAE,CAAC,CAAC;QAChB6L,MAAM,EAAE,IAAI,CAACnM;MACjB,CAAC;IACL;IACAgM,cAAcA,CAAA,EAAG;MAAA,IAAAI,YAAA;MACb,MAAM;QAAEjM;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,IAAI,CAACT,aAAa,EACd,OAAO1B,SAAS,CAAC,CAAC;MACtB,MAAM4N,GAAG,GAAGlM,aAAa,CAACmM,kBAAkB,CAAC,CAAC;MAC9C,MAAMC,eAAe,GAAG,EAAAH,YAAA,OAAI,CAACf,MAAM,cAAAe,YAAA,uBAAXA,YAAA,CAAaZ,OAAO,KAAI,IAAI,CAAChH,IAAI,CAACgI,IAAI,CAACC,sBAAsB,CAAC;MACtF,IAAI,CAACF,eAAe,EAAE;QAClB;QACA,MAAM;UAAElB;QAAO,CAAC,GAAG,IAAI,CAAC1K,IAAI;QAC5B,IAAI0K,MAAM,EAAE;UACRtN,aAAa,CAACsO,GAAG,CAACrJ,CAAC,EAAEqI,MAAM,CAACE,MAAM,CAACvI,CAAC,CAAC;UACrCjF,aAAa,CAACsO,GAAG,CAACpJ,CAAC,EAAEoI,MAAM,CAACE,MAAM,CAACtI,CAAC,CAAC;QACzC;MACJ;MACA,OAAOoJ,GAAG;IACd;IACAJ,mBAAmBA,CAACI,GAAG,EAAE;MAAA,IAAAK,aAAA;MACrB,MAAMC,gBAAgB,GAAGlO,SAAS,CAAC,CAAC;MACpCZ,WAAW,CAAC8O,gBAAgB,EAAEN,GAAG,CAAC;MAClC,KAAAK,aAAA,GAAI,IAAI,CAACrB,MAAM,cAAAqB,aAAA,eAAXA,aAAA,CAAalB,OAAO,EAAE;QACtB,OAAOmB,gBAAgB;MAC3B;MACA;AACZ;AACA;AACA;MACY,KAAK,IAAIjI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACvC,MAAMwE,IAAI,GAAG,IAAI,CAAC1E,IAAI,CAACE,CAAC,CAAC;QACzB,MAAM;UAAE2G,MAAM;UAAEzK;QAAQ,CAAC,GAAGsI,IAAI;QAChC,IAAIA,IAAI,KAAK,IAAI,CAACvI,IAAI,IAAI0K,MAAM,IAAIzK,OAAO,CAACwK,YAAY,EAAE;UACtD;AACpB;AACA;AACA;UACoB,IAAIC,MAAM,CAACG,OAAO,EAAE;YAChB3N,WAAW,CAAC8O,gBAAgB,EAAEN,GAAG,CAAC;UACtC;UACAtO,aAAa,CAAC4O,gBAAgB,CAAC3J,CAAC,EAAEqI,MAAM,CAACE,MAAM,CAACvI,CAAC,CAAC;UAClDjF,aAAa,CAAC4O,gBAAgB,CAAC1J,CAAC,EAAEoI,MAAM,CAACE,MAAM,CAACtI,CAAC,CAAC;QACtD;MACJ;MACA,OAAO0J,gBAAgB;IAC3B;IACAC,cAAcA,CAACP,GAAG,EAAyB;MAAA,IAAvBQ,aAAa,GAAAhL,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACrC,MAAMiL,cAAc,GAAGrO,SAAS,CAAC,CAAC;MAClCZ,WAAW,CAACiP,cAAc,EAAET,GAAG,CAAC;MAChC,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACvC,MAAMwE,IAAI,GAAG,IAAI,CAAC1E,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACmI,aAAa,IACd3D,IAAI,CAACtI,OAAO,CAACwK,YAAY,IACzBlC,IAAI,CAACmC,MAAM,IACXnC,IAAI,KAAKA,IAAI,CAACvI,IAAI,EAAE;UACpB3C,YAAY,CAAC8O,cAAc,EAAE;YACzB9J,CAAC,EAAE,CAACkG,IAAI,CAACmC,MAAM,CAACE,MAAM,CAACvI,CAAC;YACxBC,CAAC,EAAE,CAACiG,IAAI,CAACmC,MAAM,CAACE,MAAM,CAACtI;UAC3B,CAAC,CAAC;QACN;QACA,IAAI,CAAC7D,YAAY,CAAC8J,IAAI,CAAC5I,YAAY,CAAC,EAChC;QACJtC,YAAY,CAAC8O,cAAc,EAAE5D,IAAI,CAAC5I,YAAY,CAAC;MACnD;MACA,IAAIlB,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;QACjCtC,YAAY,CAAC8O,cAAc,EAAE,IAAI,CAACxM,YAAY,CAAC;MACnD;MACA,OAAOwM,cAAc;IACzB;IACAhB,eAAeA,CAACO,GAAG,EAAE;MACjB,MAAMU,mBAAmB,GAAGtO,SAAS,CAAC,CAAC;MACvCZ,WAAW,CAACkP,mBAAmB,EAAEV,GAAG,CAAC;MACrC,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACF,IAAI,CAAC1C,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACvC,MAAMwE,IAAI,GAAG,IAAI,CAAC1E,IAAI,CAACE,CAAC,CAAC;QACzB,IAAI,CAACwE,IAAI,CAACvD,QAAQ,EACd;QACJ,IAAI,CAACvG,YAAY,CAAC8J,IAAI,CAAC5I,YAAY,CAAC,EAChC;QACJjB,QAAQ,CAAC6J,IAAI,CAAC5I,YAAY,CAAC,IAAI4I,IAAI,CAACG,cAAc,CAAC,CAAC;QACpD,MAAM2D,SAAS,GAAGvO,SAAS,CAAC,CAAC;QAC7B,MAAMwO,OAAO,GAAG/D,IAAI,CAAC8C,cAAc,CAAC,CAAC;QACrCnO,WAAW,CAACmP,SAAS,EAAEC,OAAO,CAAC;QAC/BzO,mBAAmB,CAACuO,mBAAmB,EAAE7D,IAAI,CAAC5I,YAAY,EAAE4I,IAAI,CAACuB,QAAQ,GAAGvB,IAAI,CAACuB,QAAQ,CAACO,SAAS,GAAGjJ,SAAS,EAAEiL,SAAS,CAAC;MAC/H;MACA,IAAI5N,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;QACjC9B,mBAAmB,CAACuO,mBAAmB,EAAE,IAAI,CAACzM,YAAY,CAAC;MAC/D;MACA,OAAOyM,mBAAmB;IAC9B;IACAG,cAAcA,CAAC9G,KAAK,EAAE;MAClB,IAAI,CAAC+G,WAAW,GAAG/G,KAAK;MACxB,IAAI,CAACzF,IAAI,CAAC2J,wBAAwB,CAAC,CAAC;MACpC,IAAI,CAAChI,iBAAiB,GAAG,IAAI;IACjC;IACA8K,UAAUA,CAACxM,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,GAAA8G,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACL,IAAI,CAAC9G,OAAO,GACZA,OAAO;QACVyM,SAAS,EAAEzM,OAAO,CAACyM,SAAS,KAAKtL,SAAS,GAAGnB,OAAO,CAACyM,SAAS,GAAG;MAAI,EACxE;IACL;IACA9D,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC8B,MAAM,GAAGtJ,SAAS;MACvB,IAAI,CAACf,MAAM,GAAGe,SAAS;MACvB,IAAI,CAAC0I,QAAQ,GAAG1I,SAAS;MACzB,IAAI,CAACqH,0BAA0B,GAAGrH,SAAS;MAC3C,IAAI,CAACoL,WAAW,GAAGpL,SAAS;MAC5B,IAAI,CAAC0E,MAAM,GAAG1E,SAAS;MACvB,IAAI,CAACM,aAAa,GAAG,KAAK;IAC9B;IACAiL,kCAAkCA,CAAA,EAAG;MACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EACpB;MACJ;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACA,cAAc,CAACpJ,wBAAwB,KAC5CtH,SAAS,CAACgN,SAAS,EAAE;QACrB,IAAI,CAAC0D,cAAc,CAACxJ,kBAAkB,CAAC,IAAI,CAAC;MAChD;IACJ;IACAA,kBAAkBA,CAAA,EAA6B;MAAA,IAAAyJ,YAAA;MAAA,IAA5BC,kBAAkB,GAAA5L,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MACzC;AACZ;AACA;AACA;AACA;MACY,MAAM6L,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAACrL,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,GAAGoL,IAAI,CAACpL,iBAAiB,CAAC;MAC3E,IAAI,CAACE,gBAAgB,KAAK,IAAI,CAACA,gBAAgB,GAAGkL,IAAI,CAAClL,gBAAgB,CAAC;MACxE,IAAI,CAACD,uBAAuB,KAAK,IAAI,CAACA,uBAAuB,GAAGmL,IAAI,CAACnL,uBAAuB,CAAC;MAC7F,MAAMqL,QAAQ,GAAGzC,OAAO,CAAC,IAAI,CAAC3D,YAAY,CAAC,IAAI,IAAI,KAAKkG,IAAI;MAC5D;AACZ;AACA;AACA;MACY,MAAMG,OAAO,GAAG,EAAEJ,kBAAkB,IAC/BG,QAAQ,IAAI,IAAI,CAACrL,uBAAwB,IAC1C,IAAI,CAACD,iBAAiB,KAAAkL,YAAA,GACtB,IAAI,CAACrM,MAAM,cAAAqM,YAAA,eAAXA,YAAA,CAAalL,iBAAiB,IAC9B,IAAI,CAACwL,8BAA8B,IACnC,IAAI,CAACnN,IAAI,CAAC+B,qBAAqB,CAAC;MACpC,IAAImL,OAAO,EACP;MACJ,MAAM;QAAE7M,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACzC;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAACI,MAAM,IAAI,EAAEA,MAAM,IAAIC,QAAQ,CAAC,EACrC;MACJ,IAAI,CAACkD,wBAAwB,GAAGtH,SAAS,CAACgN,SAAS;MACnD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAACsD,WAAW,IAAI,CAAC,IAAI,CAACzG,cAAc,EAAE;QAC3C,MAAM6G,cAAc,GAAG,IAAI,CAACQ,0BAA0B,CAAC,CAAC;QACxD,IAAIR,cAAc,IACdA,cAAc,CAACvM,MAAM,IACrB,IAAI,CAACsD,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACiJ,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAAC5G,cAAc,GAAGjI,SAAS,CAAC,CAAC;UACjC,IAAI,CAACuP,oBAAoB,GAAGvP,SAAS,CAAC,CAAC;UACvCL,oBAAoB,CAAC,IAAI,CAAC4P,oBAAoB,EAAE,IAAI,CAAChN,MAAM,CAACgK,SAAS,EAAEuC,cAAc,CAACvM,MAAM,CAACgK,SAAS,CAAC;UACvGnN,WAAW,CAAC,IAAI,CAAC6I,cAAc,EAAE,IAAI,CAACsH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACT,cAAc,GAAG,IAAI,CAAC7G,cAAc,GAAG3E,SAAS;QACzD;MACJ;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC,IAAI,CAAC2E,cAAc,IAAI,CAAC,IAAI,CAACyG,WAAW,EACzC;MACJ;AACZ;AACA;MACY,IAAI,CAAC,IAAI,CAAC1G,MAAM,EAAE;QACd,IAAI,CAACA,MAAM,GAAGhI,SAAS,CAAC,CAAC;QACzB,IAAI,CAACwP,oBAAoB,GAAGxP,SAAS,CAAC,CAAC;MAC3C;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAACiI,cAAc,IACnB,IAAI,CAACsH,oBAAoB,IACzB,IAAI,CAACT,cAAc,IACnB,IAAI,CAACA,cAAc,CAAC9G,MAAM,EAAE;QAC5B,IAAI,CAAC6G,kCAAkC,CAAC,CAAC;QACzCjP,eAAe,CAAC,IAAI,CAACoI,MAAM,EAAE,IAAI,CAACC,cAAc,EAAE,IAAI,CAAC6G,cAAc,CAAC9G,MAAM,CAAC;QAC7E;AAChB;AACA;MACY,CAAC,MACI,IAAI,IAAI,CAAC0G,WAAW,EAAE;QACvB,IAAIhC,OAAO,CAAC,IAAI,CAAC3D,YAAY,CAAC,EAAE;UAC5B;UACA,IAAI,CAACf,MAAM,GAAG,IAAI,CAACmG,cAAc,CAAC,IAAI,CAAC5L,MAAM,CAACgK,SAAS,CAAC;QAC5D,CAAC,MACI;UACDnN,WAAW,CAAC,IAAI,CAAC4I,MAAM,EAAE,IAAI,CAACzF,MAAM,CAACgK,SAAS,CAAC;QACnD;QACA/M,aAAa,CAAC,IAAI,CAACwI,MAAM,EAAE,IAAI,CAAC0G,WAAW,CAAC;MAChD,CAAC,MACI;QACD;AAChB;AACA;QACgBtP,WAAW,CAAC,IAAI,CAAC4I,MAAM,EAAE,IAAI,CAACzF,MAAM,CAACgK,SAAS,CAAC;MACnD;MACA;AACZ;AACA;MACY,IAAI,IAAI,CAAC8C,8BAA8B,EAAE;QACrC,IAAI,CAACA,8BAA8B,GAAG,KAAK;QAC3C,MAAMP,cAAc,GAAG,IAAI,CAACQ,0BAA0B,CAAC,CAAC;QACxD,IAAIR,cAAc,IACdpC,OAAO,CAACoC,cAAc,CAAC/F,YAAY,CAAC,KAChC2D,OAAO,CAAC,IAAI,CAAC3D,YAAY,CAAC,IAC9B,CAAC+F,cAAc,CAAC3M,OAAO,CAACwK,YAAY,IACpCmC,cAAc,CAAC9G,MAAM,IACrB,IAAI,CAACnC,iBAAiB,KAAK,CAAC,EAAE;UAC9B,IAAI,CAACiJ,cAAc,GAAGA,cAAc;UACpC,IAAI,CAACD,kCAAkC,CAAC,CAAC;UACzC,IAAI,CAAC5G,cAAc,GAAGjI,SAAS,CAAC,CAAC;UACjC,IAAI,CAACuP,oBAAoB,GAAGvP,SAAS,CAAC,CAAC;UACvCL,oBAAoB,CAAC,IAAI,CAAC4P,oBAAoB,EAAE,IAAI,CAACvH,MAAM,EAAE8G,cAAc,CAAC9G,MAAM,CAAC;UACnF5I,WAAW,CAAC,IAAI,CAAC6I,cAAc,EAAE,IAAI,CAACsH,oBAAoB,CAAC;QAC/D,CAAC,MACI;UACD,IAAI,CAACT,cAAc,GAAG,IAAI,CAAC7G,cAAc,GAAG3E,SAAS;QACzD;MACJ;MACA;AACZ;AACA;MACY,IAAIxF,WAAW,CAACqH,KAAK,EAAE;QACnBpE,OAAO,CAACE,sBAAsB,EAAE;MACpC;IACJ;IACAqO,0BAA0BA,CAAA,EAAG;MACzB,IAAI,CAAC,IAAI,CAAC5M,MAAM,IACZ9B,QAAQ,CAAC,IAAI,CAAC8B,MAAM,CAACb,YAAY,CAAC,IAClChB,cAAc,CAAC,IAAI,CAAC6B,MAAM,CAACb,YAAY,CAAC,EAAE;QAC1C,OAAOyB,SAAS;MACpB;MACA,IAAI,IAAI,CAACZ,MAAM,CAAC+M,YAAY,CAAC,CAAC,EAAE;QAC5B,OAAO,IAAI,CAAC/M,MAAM;MACtB,CAAC,MACI;QACD,OAAO,IAAI,CAACA,MAAM,CAAC4M,0BAA0B,CAAC,CAAC;MACnD;IACJ;IACAG,YAAYA,CAAA,EAAG;MACX,OAAO/C,OAAO,CAAC,CAAC,IAAI,CAACzE,cAAc,IAC/B,IAAI,CAACyG,WAAW,IAChB,IAAI,CAACvM,OAAO,CAACyG,UAAU,KACvB,IAAI,CAACrG,MAAM,CAAC;IACpB;IACAgD,cAAcA,CAAA,EAAG;MAAA,IAAAmK,aAAA;MACb,MAAMT,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,MAAMC,QAAQ,GAAGzC,OAAO,CAAC,IAAI,CAAC3D,YAAY,CAAC,IAAI,IAAI,KAAKkG,IAAI;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAACvL,iBAAiB,KAAA6L,aAAA,GAAI,IAAI,CAAChN,MAAM,cAAAgN,aAAA,eAAXA,aAAA,CAAa7L,iBAAiB,EAAE;QAC1DuL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAID,QAAQ,KACP,IAAI,CAACrL,uBAAuB,IAAI,IAAI,CAACC,gBAAgB,CAAC,EAAE;QACzDqL,OAAO,GAAG,KAAK;MACnB;MACA;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAC1J,wBAAwB,KAAKtH,SAAS,CAACgN,SAAS,EAAE;QACvDgE,OAAO,GAAG,KAAK;MACnB;MACA,IAAIA,OAAO,EACP;MACJ,MAAM;QAAE7M,MAAM;QAAEC;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACzC;AACZ;AACA;AACA;MACY,IAAI,CAACuB,eAAe,GAAGgJ,OAAO,CAAE,IAAI,CAAChK,MAAM,IAAI,IAAI,CAACA,MAAM,CAACgB,eAAe,IACtE,IAAI,CAACoF,gBAAgB,IACrB,IAAI,CAAC6G,gBAAgB,CAAC;MAC1B,IAAI,CAAC,IAAI,CAACjM,eAAe,EAAE;QACvB,IAAI,CAACgL,WAAW,GAAG,IAAI,CAACzG,cAAc,GAAG3E,SAAS;MACtD;MACA,IAAI,CAAC,IAAI,CAACf,MAAM,IAAI,EAAEA,MAAM,IAAIC,QAAQ,CAAC,EACrC;MACJ;AACZ;AACA;AACA;MACYpD,WAAW,CAAC,IAAI,CAACiN,eAAe,EAAE,IAAI,CAAC9J,MAAM,CAACgK,SAAS,CAAC;MACxD;AACZ;AACA;MACY,MAAMqD,cAAc,GAAG,IAAI,CAACtL,SAAS,CAACC,CAAC;MACvC,MAAMsL,cAAc,GAAG,IAAI,CAACvL,SAAS,CAACE,CAAC;MACvC;AACZ;AACA;AACA;MACY/E,eAAe,CAAC,IAAI,CAAC4M,eAAe,EAAE,IAAI,CAAC/H,SAAS,EAAE,IAAI,CAACyB,IAAI,EAAEoJ,QAAQ,CAAC;MAC1E;AACZ;AACA;AACA;MACY,IAAIF,IAAI,CAAC1M,MAAM,IACX,CAAC0M,IAAI,CAACjH,MAAM,KACX,IAAI,CAAC1D,SAAS,CAACC,CAAC,KAAK,CAAC,IAAI,IAAI,CAACD,SAAS,CAACE,CAAC,KAAK,CAAC,CAAC,EAAE;QACpDyK,IAAI,CAACjH,MAAM,GAAGiH,IAAI,CAAC1M,MAAM,CAACgK,SAAS;QACnC0C,IAAI,CAACO,oBAAoB,GAAGxP,SAAS,CAAC,CAAC;MAC3C;MACA,MAAM;QAAEgI;MAAO,CAAC,GAAGiH,IAAI;MACvB,IAAI,CAACjH,MAAM,EAAE;QACT;AAChB;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAAC8H,mBAAmB,EAAE;UAC1B,IAAI,CAACC,sBAAsB,CAAC,CAAC;UAC7B,IAAI,CAAC3C,cAAc,CAAC,CAAC;QACzB;QACA;MACJ;MACA,IAAI,CAAC,IAAI,CAACd,eAAe,IAAI,CAAC,IAAI,CAACwD,mBAAmB,EAAE;QACpD,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACjC,CAAC,MACI;QACD1Q,iBAAiB,CAAC,IAAI,CAACyQ,mBAAmB,CAACvL,CAAC,EAAE,IAAI,CAAC+H,eAAe,CAAC/H,CAAC,CAAC;QACrElF,iBAAiB,CAAC,IAAI,CAACyQ,mBAAmB,CAACtL,CAAC,EAAE,IAAI,CAAC8H,eAAe,CAAC9H,CAAC,CAAC;MACzE;MACA;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY3E,YAAY,CAAC,IAAI,CAACyM,eAAe,EAAE,IAAI,CAACD,eAAe,EAAErE,MAAM,EAAE,IAAI,CAACnG,YAAY,CAAC;MACnF,IAAI,IAAI,CAACyC,SAAS,CAACC,CAAC,KAAKqL,cAAc,IACnC,IAAI,CAACtL,SAAS,CAACE,CAAC,KAAKqL,cAAc,IACnC,CAACzP,eAAe,CAAC,IAAI,CAACkM,eAAe,CAAC/H,CAAC,EAAE,IAAI,CAACuL,mBAAmB,CAACvL,CAAC,CAAC,IACpE,CAACnE,eAAe,CAAC,IAAI,CAACkM,eAAe,CAAC9H,CAAC,EAAE,IAAI,CAACsL,mBAAmB,CAACtL,CAAC,CAAC,EAAE;QACtE,IAAI,CAACmB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACyH,cAAc,CAAC,CAAC;QACrB,IAAI,CAAC3G,eAAe,CAAC,kBAAkB,EAAEuB,MAAM,CAAC;MACpD;MACA;AACZ;AACA;MACY,IAAIlK,WAAW,CAACqH,KAAK,EAAE;QACnBpE,OAAO,CAACG,qBAAqB,EAAE;MACnC;IACJ;IACA8O,IAAIA,CAAA,EAAG;MACH,IAAI,CAACpK,SAAS,GAAG,KAAK;MACtB;IACJ;IACAqK,IAAIA,CAAA,EAAG;MACH,IAAI,CAACrK,SAAS,GAAG,IAAI;MACrB;IACJ;IACAwH,cAAcA,CAAA,EAAmB;MAAA,IAAA8C,qBAAA;MAAA,IAAlBC,SAAS,GAAA/M,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC3B,CAAA8M,qBAAA,OAAI,CAAC/N,OAAO,CAACT,aAAa,cAAAwO,qBAAA,eAA1BA,qBAAA,CAA4B9C,cAAc,CAAC,CAAC;MAC5C,IAAI+C,SAAS,EAAE;QACX,MAAMtG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;QAC7BD,KAAK,IAAIA,KAAK,CAACuD,cAAc,CAAC,CAAC;MACnC;MACA,IAAI,IAAI,CAACrE,YAAY,IAAI,CAAC,IAAI,CAACA,YAAY,CAAC7B,QAAQ,EAAE;QAClD,IAAI,CAAC6B,YAAY,GAAGzF,SAAS;MACjC;IACJ;IACAyM,sBAAsBA,CAAA,EAAG;MACrB,IAAI,CAACD,mBAAmB,GAAG7P,WAAW,CAAC,CAAC;MACxC,IAAI,CAACqM,eAAe,GAAGrM,WAAW,CAAC,CAAC;MACpC,IAAI,CAACmQ,4BAA4B,GAAGnQ,WAAW,CAAC,CAAC;IACrD;IACAsJ,kBAAkBA,CAAC5B,KAAK,EAAwC;MAAA,IAAtCgB,4BAA4B,GAAAvF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAC1D,MAAM4I,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC9B,MAAMqE,oBAAoB,GAAGrE,QAAQ,GAAGA,QAAQ,CAACnK,YAAY,GAAG,CAAC,CAAC;MAClE,MAAMyO,WAAW,GAAArH,aAAA,KAAQ,IAAI,CAACpH,YAAY,CAAE;MAC5C,MAAM6M,WAAW,GAAGzO,WAAW,CAAC,CAAC;MACjC,IAAI,CAAC,IAAI,CAAC6O,cAAc,IACpB,CAAC,IAAI,CAACA,cAAc,CAAC3M,OAAO,CAACyG,UAAU,EAAE;QACzC,IAAI,CAACX,cAAc,GAAG,IAAI,CAACsH,oBAAoB,GAAGjM,SAAS;MAC/D;MACA,IAAI,CAAC+L,8BAA8B,GAAG,CAAC1G,4BAA4B;MACnE,MAAM4H,cAAc,GAAGvQ,SAAS,CAAC,CAAC;MAClC,MAAMwQ,cAAc,GAAGxE,QAAQ,GAAGA,QAAQ,CAAC0B,MAAM,GAAGpK,SAAS;MAC7D,MAAMmN,YAAY,GAAG,IAAI,CAAClO,MAAM,GAAG,IAAI,CAACA,MAAM,CAACmL,MAAM,GAAGpK,SAAS;MACjE,MAAMoN,uBAAuB,GAAGF,cAAc,KAAKC,YAAY;MAC/D,MAAM5G,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,MAAM6G,YAAY,GAAG,CAAC9G,KAAK,IAAIA,KAAK,CAAC+G,OAAO,CAACvN,MAAM,IAAI,CAAC;MACxD,MAAMwN,sBAAsB,GAAGnE,OAAO,CAACgE,uBAAuB,IAC1D,CAACC,YAAY,IACb,IAAI,CAACxO,OAAO,CAACyM,SAAS,KAAK,IAAI,IAC/B,CAAC,IAAI,CAAC7I,IAAI,CAACgI,IAAI,CAAC+C,mBAAmB,CAAC,CAAC;MACzC,IAAI,CAACjL,iBAAiB,GAAG,CAAC;MAC1B,IAAIkL,kBAAkB;MACtB,IAAI,CAACC,cAAc,GAAIC,MAAM,IAAK;QAC9B,MAAMC,QAAQ,GAAGD,MAAM,GAAG,IAAI;QAC9BE,YAAY,CAACzC,WAAW,CAACnK,CAAC,EAAEoD,KAAK,CAACpD,CAAC,EAAE2M,QAAQ,CAAC;QAC9CC,YAAY,CAACzC,WAAW,CAAClK,CAAC,EAAEmD,KAAK,CAACnD,CAAC,EAAE0M,QAAQ,CAAC;QAC9C,IAAI,CAACzC,cAAc,CAACC,WAAW,CAAC;QAChC,IAAI,IAAI,CAACzG,cAAc,IACnB,IAAI,CAACsH,oBAAoB,IACzB,IAAI,CAAChN,MAAM,IACX,IAAI,CAACuM,cAAc,IACnB,IAAI,CAACA,cAAc,CAACvM,MAAM,EAAE;UAC5B5C,oBAAoB,CAAC4Q,cAAc,EAAE,IAAI,CAAChO,MAAM,CAACgK,SAAS,EAAE,IAAI,CAACuC,cAAc,CAACvM,MAAM,CAACgK,SAAS,CAAC;UACjG6E,MAAM,CAAC,IAAI,CAACnJ,cAAc,EAAE,IAAI,CAACsH,oBAAoB,EAAEgB,cAAc,EAAEW,QAAQ,CAAC;UAChF;AACpB;AACA;AACA;UACoB,IAAIH,kBAAkB,IAClBzQ,SAAS,CAAC,IAAI,CAAC2H,cAAc,EAAE8I,kBAAkB,CAAC,EAAE;YACpD,IAAI,CAAClN,iBAAiB,GAAG,KAAK;UAClC;UACA,IAAI,CAACkN,kBAAkB,EACnBA,kBAAkB,GAAG/Q,SAAS,CAAC,CAAC;UACpCZ,WAAW,CAAC2R,kBAAkB,EAAE,IAAI,CAAC9I,cAAc,CAAC;QACxD;QACA,IAAIyI,uBAAuB,EAAE;UACzB,IAAI,CAACW,eAAe,GAAGf,WAAW;UAClCnR,SAAS,CAACmR,WAAW,EAAED,oBAAoB,EAAE,IAAI,CAACxO,YAAY,EAAEqP,QAAQ,EAAEL,sBAAsB,EAAEF,YAAY,CAAC;QACnH;QACA,IAAI,CAACzO,IAAI,CAAC2J,wBAAwB,CAAC,CAAC;QACpC,IAAI,CAACuB,cAAc,CAAC,CAAC;QACrB,IAAI,CAACvH,iBAAiB,GAAGqL,QAAQ;MACrC,CAAC;MACD,IAAI,CAACF,cAAc,CAAC,IAAI,CAAC7O,OAAO,CAACyG,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAU,cAAcA,CAACnH,OAAO,EAAE;MAAA,IAAAmP,qBAAA,EAAAC,kBAAA;MACpB,IAAI,CAAC9K,eAAe,CAAC,gBAAgB,CAAC;MACtC,CAAA6K,qBAAA,OAAI,CAACxI,gBAAgB,cAAAwI,qBAAA,eAArBA,qBAAA,CAAuBE,IAAI,CAAC,CAAC;MAC7B,CAAAD,kBAAA,OAAI,CAACxI,YAAY,cAAAwI,kBAAA,gBAAAA,kBAAA,GAAjBA,kBAAA,CAAmBzI,gBAAgB,cAAAyI,kBAAA,eAAnCA,kBAAA,CAAqCC,IAAI,CAAC,CAAC;MAC3C,IAAI,IAAI,CAAC7B,gBAAgB,EAAE;QACvBzR,WAAW,CAAC,IAAI,CAACyR,gBAAgB,CAAC;QAClC,IAAI,CAACA,gBAAgB,GAAGrM,SAAS;MACrC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACqM,gBAAgB,GAAGpR,KAAK,CAACuG,MAAM,CAAC,MAAM;QACvChE,qBAAqB,CAACwG,sBAAsB,GAAG,IAAI;QACnD9I,gBAAgB,CAAC+D,MAAM,EAAE;QACzB,IAAI,CAAC9D,WAAW,KAAK,IAAI,CAACA,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC;QACvD,IAAI,CAACqK,gBAAgB,GAAGhK,kBAAkB,CAAC,IAAI,CAACL,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAAwK,aAAA,CAAAA,aAAA,KAC/D9G,OAAO;UACVsP,QAAQ,EAAE,CAAC;UACXC,MAAM,EAAE,IAAI;UACZC,QAAQ,EAAGV,MAAM,IAAK;YAClB,IAAI,CAACD,cAAc,CAACC,MAAM,CAAC;YAC3B9O,OAAO,CAACwP,QAAQ,IAAIxP,OAAO,CAACwP,QAAQ,CAACV,MAAM,CAAC;UAChD,CAAC;UACDW,MAAM,EAAEA,CAAA,KAAM;YACVpT,gBAAgB,CAAC+D,MAAM,EAAE;UAC7B,CAAC;UACD4G,UAAU,EAAEA,CAAA,KAAM;YACd3K,gBAAgB,CAAC+D,MAAM,EAAE;YACzBJ,OAAO,CAACgH,UAAU,IAAIhH,OAAO,CAACgH,UAAU,CAAC,CAAC;YAC1C,IAAI,CAAC0I,iBAAiB,CAAC,CAAC;UAC5B;QAAC,EACJ,CAAC;QACF,IAAI,IAAI,CAAC9I,YAAY,EAAE;UACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;QAC9D;QACA,IAAI,CAAC6G,gBAAgB,GAAGrM,SAAS;MACrC,CAAC,CAAC;IACN;IACAuO,iBAAiBA,CAAA,EAAG;MAChB,IAAI,IAAI,CAAC9I,YAAY,EAAE;QACnB,IAAI,CAACA,YAAY,CAACD,gBAAgB,GAAGxF,SAAS;QAC9C,IAAI,CAACyF,YAAY,CAAC+I,eAAe,GAAGxO,SAAS;MACjD;MACA,MAAMuG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7BD,KAAK,IAAIA,KAAK,CAACkI,qBAAqB,CAAC,CAAC;MACtC,IAAI,CAAChJ,YAAY,GACb,IAAI,CAACD,gBAAgB,GACjB,IAAI,CAACuI,eAAe,GAChB/N,SAAS;MACrB,IAAI,CAACmD,eAAe,CAAC,mBAAmB,CAAC;IAC7C;IACAc,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACuB,gBAAgB,EAAE;QACvB,IAAI,CAACkI,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC1P,eAAe,CAAC;QAC3D,IAAI,CAACwH,gBAAgB,CAAC0I,IAAI,CAAC,CAAC;MAChC;MACA,IAAI,CAACK,iBAAiB,CAAC,CAAC;IAC5B;IACAG,uBAAuBA,CAAA,EAAG;MACtB,MAAM/C,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI;QAAEM,oBAAoB;QAAExH,MAAM;QAAEzF,MAAM;QAAEV;MAAa,CAAC,GAAGoN,IAAI;MACjE,IAAI,CAACO,oBAAoB,IAAI,CAACxH,MAAM,IAAI,CAACzF,MAAM,EAC3C;MACJ;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,KAAK0M,IAAI,IACb,IAAI,CAAC1M,MAAM,IACXA,MAAM,IACN0P,yBAAyB,CAAC,IAAI,CAAC9P,OAAO,CAAC+P,aAAa,EAAE,IAAI,CAAC3P,MAAM,CAACgK,SAAS,EAAEhK,MAAM,CAACgK,SAAS,CAAC,EAAE;QAChGvE,MAAM,GAAG,IAAI,CAACA,MAAM,IAAIhI,SAAS,CAAC,CAAC;QACnC,MAAMmS,OAAO,GAAGzS,UAAU,CAAC,IAAI,CAAC6C,MAAM,CAACgK,SAAS,CAAChI,CAAC,CAAC;QACnDyD,MAAM,CAACzD,CAAC,CAAC6N,GAAG,GAAGnD,IAAI,CAACjH,MAAM,CAACzD,CAAC,CAAC6N,GAAG;QAChCpK,MAAM,CAACzD,CAAC,CAAC8N,GAAG,GAAGrK,MAAM,CAACzD,CAAC,CAAC6N,GAAG,GAAGD,OAAO;QACrC,MAAMG,OAAO,GAAG5S,UAAU,CAAC,IAAI,CAAC6C,MAAM,CAACgK,SAAS,CAAC/H,CAAC,CAAC;QACnDwD,MAAM,CAACxD,CAAC,CAAC4N,GAAG,GAAGnD,IAAI,CAACjH,MAAM,CAACxD,CAAC,CAAC4N,GAAG;QAChCpK,MAAM,CAACxD,CAAC,CAAC6N,GAAG,GAAGrK,MAAM,CAACxD,CAAC,CAAC4N,GAAG,GAAGE,OAAO;MACzC;MACAlT,WAAW,CAACoQ,oBAAoB,EAAExH,MAAM,CAAC;MACzC;AACZ;AACA;AACA;AACA;MACYzI,YAAY,CAACiQ,oBAAoB,EAAE3N,YAAY,CAAC;MAChD;AACZ;AACA;AACA;AACA;AACA;MACYhC,YAAY,CAAC,IAAI,CAACuQ,4BAA4B,EAAE,IAAI,CAAC/D,eAAe,EAAEmD,oBAAoB,EAAE3N,YAAY,CAAC;IAC7G;IACA2F,kBAAkBA,CAAChF,QAAQ,EAAEiI,IAAI,EAAE;MAC/B,IAAI,CAAC,IAAI,CAAC3E,WAAW,CAACO,GAAG,CAAC7D,QAAQ,CAAC,EAAE;QACjC,IAAI,CAACsD,WAAW,CAACQ,GAAG,CAAC9D,QAAQ,EAAE,IAAIjC,SAAS,CAAC,CAAC,CAAC;MACnD;MACA,MAAMsJ,KAAK,GAAG,IAAI,CAAC/D,WAAW,CAACS,GAAG,CAAC/D,QAAQ,CAAC;MAC5CqH,KAAK,CAACrD,GAAG,CAACiE,IAAI,CAAC;MACf,MAAM8H,MAAM,GAAG9H,IAAI,CAACtI,OAAO,CAACqQ,sBAAsB;MAClD/H,IAAI,CAACgI,OAAO,CAAC;QACTtK,UAAU,EAAEoK,MAAM,GAAGA,MAAM,CAACpK,UAAU,GAAG7E,SAAS;QAClDoP,qBAAqB,EAAEH,MAAM,IAAIA,MAAM,CAACI,2BAA2B,GAC7DJ,MAAM,CAACI,2BAA2B,CAAClI,IAAI,CAAC,GACxCnH;MACV,CAAC,CAAC;IACN;IACAkG,MAAMA,CAAA,EAAG;MACL,MAAMK,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,OAAOD,KAAK,GAAGA,KAAK,CAACoF,IAAI,KAAK,IAAI,GAAG,IAAI;IAC7C;IACAC,OAAOA,CAAA,EAAG;MAAA,IAAA0D,cAAA;MACN,MAAM;QAAEpQ;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,OAAOK,QAAQ,GAAG,EAAAoQ,cAAA,OAAI,CAAC9I,QAAQ,CAAC,CAAC,cAAA8I,cAAA,uBAAfA,cAAA,CAAiB3D,IAAI,KAAI,IAAI,GAAG,IAAI;IAC1D;IACA4D,WAAWA,CAAA,EAAG;MAAA,IAAAC,eAAA;MACV,MAAM;QAAEtQ;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,OAAOK,QAAQ,IAAAsQ,eAAA,GAAG,IAAI,CAAChJ,QAAQ,CAAC,CAAC,cAAAgJ,eAAA,uBAAfA,eAAA,CAAiBC,QAAQ,GAAGzP,SAAS;IAC3D;IACAwG,QAAQA,CAAA,EAAG;MACP,MAAM;QAAEtH;MAAS,CAAC,GAAG,IAAI,CAACL,OAAO;MACjC,IAAIK,QAAQ,EACR,OAAO,IAAI,CAACN,IAAI,CAAC4D,WAAW,CAACS,GAAG,CAAC/D,QAAQ,CAAC;IAClD;IACAiQ,OAAOA,CAAA,EAA0D;MAAA,IAAzD;QAAErO,UAAU;QAAE+D,UAAU;QAAEuK;MAAuB,CAAC,GAAAtP,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC3D,MAAMyG,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EACLA,KAAK,CAAC4I,OAAO,CAAC,IAAI,EAAEC,qBAAqB,CAAC;MAC9C,IAAItO,UAAU,EAAE;QACZ,IAAI,CAACkI,eAAe,GAAGhJ,SAAS;QAChC,IAAI,CAACc,UAAU,GAAG,IAAI;MAC1B;MACA,IAAI+D,UAAU,EACV,IAAI,CAACwG,UAAU,CAAC;QAAExG;MAAW,CAAC,CAAC;IACvC;IACA6K,QAAQA,CAAA,EAAG;MACP,MAAMnJ,KAAK,GAAG,IAAI,CAACC,QAAQ,CAAC,CAAC;MAC7B,IAAID,KAAK,EAAE;QACP,OAAOA,KAAK,CAACmJ,QAAQ,CAAC,IAAI,CAAC;MAC/B,CAAC,MACI;QACD,OAAO,KAAK;MAChB;IACJ;IACA3I,oBAAoBA,CAAA,EAAG;MACnB,MAAM;QAAE3I;MAAc,CAAC,GAAG,IAAI,CAACS,OAAO;MACtC,IAAI,CAACT,aAAa,EACd;MACJ;MACA,IAAIuR,sBAAsB,GAAG,KAAK;MAClC;AACZ;AACA;AACA;MACY,MAAM;QAAEpR;MAAa,CAAC,GAAGH,aAAa;MACtC,IAAIG,YAAY,CAACqR,CAAC,IACdrR,YAAY,CAACsR,MAAM,IACnBtR,YAAY,CAACuR,OAAO,IACpBvR,YAAY,CAACwR,OAAO,IACpBxR,YAAY,CAACyR,OAAO,IACpBzR,YAAY,CAAC0R,KAAK,IAClB1R,YAAY,CAAC2R,KAAK,EAAE;QACpBP,sBAAsB,GAAG,IAAI;MACjC;MACA;MACA,IAAI,CAACA,sBAAsB,EACvB;MACJ,MAAMQ,WAAW,GAAG,CAAC,CAAC;MACtB,IAAI5R,YAAY,CAACqR,CAAC,EAAE;QAChB1R,wBAAwB,CAAC,GAAG,EAAEE,aAAa,EAAE+R,WAAW,EAAE,IAAI,CAACpC,eAAe,CAAC;MACnF;MACA;MACA,KAAK,IAAIpL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9E,aAAa,CAACkC,MAAM,EAAE4C,CAAC,EAAE,EAAE;QAC3CzE,wBAAwB,UAAAkS,MAAA,CAAUvS,aAAa,CAAC8E,CAAC,CAAC,GAAIvE,aAAa,EAAE+R,WAAW,EAAE,IAAI,CAACpC,eAAe,CAAC;QACvG7P,wBAAwB,QAAAkS,MAAA,CAAQvS,aAAa,CAAC8E,CAAC,CAAC,GAAIvE,aAAa,EAAE+R,WAAW,EAAE,IAAI,CAACpC,eAAe,CAAC;MACzG;MACA;MACA;MACA3P,aAAa,CAAC8J,MAAM,CAAC,CAAC;MACtB;MACA,KAAK,MAAM/J,GAAG,IAAIgS,WAAW,EAAE;QAC3B/R,aAAa,CAACI,cAAc,CAACL,GAAG,EAAEgS,WAAW,CAAChS,GAAG,CAAC,CAAC;QACnD,IAAI,IAAI,CAAC4P,eAAe,EAAE;UACtB,IAAI,CAACA,eAAe,CAAC5P,GAAG,CAAC,GAAGgS,WAAW,CAAChS,GAAG,CAAC;QAChD;MACJ;MACA;MACA;MACAC,aAAa,CAAC0L,cAAc,CAAC,CAAC;IAClC;IACAuG,mBAAmBA,CAACC,SAAS,EAAE;MAC3B,IAAI,CAAC,IAAI,CAAC1M,QAAQ,IAAI,IAAI,CAAC/C,KAAK,EAC5B,OAAOb,SAAS;MACpB,IAAI,CAAC,IAAI,CAACsC,SAAS,EAAE;QACjB,OAAOxE,gBAAgB;MAC3B;MACA,MAAMyS,MAAM,GAAG;QACXxS,UAAU,EAAE;MAChB,CAAC;MACD,MAAMkJ,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC,CAAC;MACrD,IAAI,IAAI,CAAClG,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,GAAG,KAAK;QACvByP,MAAM,CAACC,OAAO,GAAG,EAAE;QACnBD,MAAM,CAACE,aAAa,GAChB7U,kBAAkB,CAAC0U,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,aAAa,CAAC,IAAI,EAAE;QACtDF,MAAM,CAACG,SAAS,GAAGzJ,iBAAiB,GAC9BA,iBAAiB,CAAC,IAAI,CAAC1I,YAAY,EAAE,EAAE,CAAC,GACxC,MAAM;QACZ,OAAOgS,MAAM;MACjB;MACA,MAAM5E,IAAI,GAAG,IAAI,CAACC,OAAO,CAAC,CAAC;MAC3B,IAAI,CAAC,IAAI,CAAC5C,eAAe,IAAI,CAAC,IAAI,CAAC/J,MAAM,IAAI,CAAC0M,IAAI,CAACjH,MAAM,EAAE;QACvD,MAAMiM,WAAW,GAAG,CAAC,CAAC;QACtB,IAAI,IAAI,CAAC9R,OAAO,CAACK,QAAQ,EAAE;UACvByR,WAAW,CAACH,OAAO,GACf,IAAI,CAACjS,YAAY,CAACiS,OAAO,KAAKxQ,SAAS,GACjC,IAAI,CAACzB,YAAY,CAACiS,OAAO,GACzB,CAAC;UACXG,WAAW,CAACF,aAAa,GACrB7U,kBAAkB,CAAC0U,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,aAAa,CAAC,IAAI,EAAE;QAC1D;QACA,IAAI,IAAI,CAACpO,YAAY,IAAI,CAAChF,YAAY,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAE;UACvDoS,WAAW,CAACD,SAAS,GAAGzJ,iBAAiB,GACnCA,iBAAiB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GACzB,MAAM;UACZ,IAAI,CAAC5E,YAAY,GAAG,KAAK;QAC7B;QACA,OAAOsO,WAAW;MACtB;MACA,MAAMC,cAAc,GAAGjF,IAAI,CAACoC,eAAe,IAAIpC,IAAI,CAACpN,YAAY;MAChE,IAAI,CAACmQ,uBAAuB,CAAC,CAAC;MAC9B6B,MAAM,CAACG,SAAS,GAAGvT,wBAAwB,CAAC,IAAI,CAAC2P,4BAA4B,EAAE,IAAI,CAAC9L,SAAS,EAAE4P,cAAc,CAAC;MAC9G,IAAI3J,iBAAiB,EAAE;QACnBsJ,MAAM,CAACG,SAAS,GAAGzJ,iBAAiB,CAAC2J,cAAc,EAAEL,MAAM,CAACG,SAAS,CAAC;MAC1E;MACA,MAAM;QAAEzP,CAAC;QAAEC;MAAE,CAAC,GAAG,IAAI,CAAC8H,eAAe;MACrCuH,MAAM,CAACM,eAAe,MAAAT,MAAA,CAAMnP,CAAC,CAAC6P,MAAM,GAAG,GAAG,QAAAV,MAAA,CAAKlP,CAAC,CAAC4P,MAAM,GAAG,GAAG,QAAK;MAClE,IAAInF,IAAI,CAACoC,eAAe,EAAE;QAAA,IAAAgD,KAAA,EAAAC,qBAAA;QACtB;AAChB;AACA;AACA;QACgBT,MAAM,CAACC,OAAO,GACV7E,IAAI,KAAK,IAAI,IAAAoF,KAAA,IAAAC,qBAAA,GACPJ,cAAc,CAACJ,OAAO,cAAAQ,qBAAA,cAAAA,qBAAA,GACpB,IAAI,CAACzS,YAAY,CAACiS,OAAO,cAAAO,KAAA,cAAAA,KAAA,GACzB,CAAC,GACH,IAAI,CAACvC,eAAe,GAChB,IAAI,CAACjQ,YAAY,CAACiS,OAAO,GACzBI,cAAc,CAACK,WAAW;MAC5C,CAAC,MACI;QACD;AAChB;AACA;AACA;QACgBV,MAAM,CAACC,OAAO,GACV7E,IAAI,KAAK,IAAI,GACPiF,cAAc,CAACJ,OAAO,KAAKxQ,SAAS,GAChC4Q,cAAc,CAACJ,OAAO,GACtB,EAAE,GACNI,cAAc,CAACK,WAAW,KAAKjR,SAAS,GACpC4Q,cAAc,CAACK,WAAW,GAC1B,CAAC;MACnB;MACA;AACZ;AACA;MACY,KAAK,MAAM9S,GAAG,IAAIjB,eAAe,EAAE;QAC/B,IAAI0T,cAAc,CAACzS,GAAG,CAAC,KAAK6B,SAAS,EACjC;QACJ,MAAM;UAAEkR,OAAO;UAAEC,OAAO;UAAEC;QAAc,CAAC,GAAGlU,eAAe,CAACiB,GAAG,CAAC;QAChE;AAChB;AACA;AACA;AACA;AACA;QACgB,MAAMkT,SAAS,GAAGd,MAAM,CAACG,SAAS,KAAK,MAAM,GACvCE,cAAc,CAACzS,GAAG,CAAC,GACnB+S,OAAO,CAACN,cAAc,CAACzS,GAAG,CAAC,EAAEwN,IAAI,CAAC;QACxC,IAAIwF,OAAO,EAAE;UACT,MAAMG,GAAG,GAAGH,OAAO,CAACpR,MAAM;UAC1B,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2O,GAAG,EAAE3O,CAAC,EAAE,EAAE;YAC1B4N,MAAM,CAACY,OAAO,CAACxO,CAAC,CAAC,CAAC,GAAG0O,SAAS;UAClC;QACJ,CAAC,MACI;UACD;UACA;UACA;UACA,IAAID,aAAa,EAAE;YACf,IAAI,CAACvS,OAAO,CAACT,aAAa,CAACmT,WAAW,CAACC,IAAI,CAACrT,GAAG,CAAC,GAAGkT,SAAS;UAChE,CAAC,MACI;YACDd,MAAM,CAACpS,GAAG,CAAC,GAAGkT,SAAS;UAC3B;QACJ;MACJ;MACA;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACxS,OAAO,CAACK,QAAQ,EAAE;QACvBqR,MAAM,CAACE,aAAa,GAChB9E,IAAI,KAAK,IAAI,GACP/P,kBAAkB,CAAC0U,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEG,aAAa,CAAC,IAAI,EAAE,GAClD,MAAM;MACpB;MACA,OAAOF,MAAM;IACjB;IACAlI,aAAaA,CAAA,EAAG;MACZ,IAAI,CAAC9C,UAAU,GAAG,IAAI,CAACmD,QAAQ,GAAG1I,SAAS;IAC/C;IACA;IACAyR,SAASA,CAAA,EAAG;MACR,IAAI,CAAC7S,IAAI,CAAClB,KAAK,CAACoE,OAAO,CAAEqF,IAAI;QAAA,IAAAuK,qBAAA;QAAA,QAAAA,qBAAA,GAAKvK,IAAI,CAAC3B,gBAAgB,cAAAkM,qBAAA,uBAArBA,qBAAA,CAAuBxD,IAAI,CAAC,CAAC;MAAA,EAAC;MAChE,IAAI,CAACtP,IAAI,CAAClB,KAAK,CAACoE,OAAO,CAAC0F,iBAAiB,CAAC;MAC1C,IAAI,CAAC5I,IAAI,CAAC4D,WAAW,CAACkE,KAAK,CAAC,CAAC;IACjC;EACJ,CAAC;AACL;AACA,SAASiB,YAAYA,CAACR,IAAI,EAAE;EACxBA,IAAI,CAACQ,YAAY,CAAC,CAAC;AACvB;AACA,SAASC,kBAAkBA,CAACT,IAAI,EAAE;EAAA,IAAAwK,gBAAA;EAC9B,MAAMjJ,QAAQ,GAAG,EAAAiJ,gBAAA,GAAAxK,IAAI,CAAC5B,UAAU,cAAAoM,gBAAA,uBAAfA,gBAAA,CAAiBjJ,QAAQ,KAAIvB,IAAI,CAACuB,QAAQ;EAC3D,IAAIvB,IAAI,CAACjB,MAAM,CAAC,CAAC,IACbiB,IAAI,CAAClI,MAAM,IACXyJ,QAAQ,IACRvB,IAAI,CAACzD,YAAY,CAAC,WAAW,CAAC,EAAE;IAChC,MAAM;MAAEuF,SAAS,EAAEhK,MAAM;MAAE2J,WAAW,EAAEgJ;IAAe,CAAC,GAAGzK,IAAI,CAAClI,MAAM;IACtE,MAAM;MAAE2P;IAAc,CAAC,GAAGzH,IAAI,CAACtI,OAAO;IACtC,MAAMgN,QAAQ,GAAGnD,QAAQ,CAAC0B,MAAM,KAAKjD,IAAI,CAAClI,MAAM,CAACmL,MAAM;IACvD;IACA;IACA,IAAIwE,aAAa,KAAK,MAAM,EAAE;MAC1BxR,QAAQ,CAAEyU,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGjG,QAAQ,GACvBnD,QAAQ,CAACE,WAAW,CAACiJ,IAAI,CAAC,GAC1BnJ,QAAQ,CAACO,SAAS,CAAC4I,IAAI,CAAC;QAC9B,MAAM9R,MAAM,GAAG3D,UAAU,CAAC0V,YAAY,CAAC;QACvCA,YAAY,CAAChD,GAAG,GAAG7P,MAAM,CAAC4S,IAAI,CAAC,CAAC/C,GAAG;QACnCgD,YAAY,CAAC/C,GAAG,GAAG+C,YAAY,CAAChD,GAAG,GAAG/O,MAAM;MAChD,CAAC,CAAC;IACN,CAAC,MACI,IAAI4O,yBAAyB,CAACC,aAAa,EAAElG,QAAQ,CAACO,SAAS,EAAEhK,MAAM,CAAC,EAAE;MAC3E7B,QAAQ,CAAEyU,IAAI,IAAK;QACf,MAAMC,YAAY,GAAGjG,QAAQ,GACvBnD,QAAQ,CAACE,WAAW,CAACiJ,IAAI,CAAC,GAC1BnJ,QAAQ,CAACO,SAAS,CAAC4I,IAAI,CAAC;QAC9B,MAAM9R,MAAM,GAAG3D,UAAU,CAAC6C,MAAM,CAAC4S,IAAI,CAAC,CAAC;QACvCC,YAAY,CAAC/C,GAAG,GAAG+C,YAAY,CAAChD,GAAG,GAAG/O,MAAM;QAC5C;AAChB;AACA;QACgB,IAAIoH,IAAI,CAACxC,cAAc,IAAI,CAACwC,IAAI,CAAC3B,gBAAgB,EAAE;UAC/C2B,IAAI,CAAC5G,iBAAiB,GAAG,IAAI;UAC7B4G,IAAI,CAACxC,cAAc,CAACkN,IAAI,CAAC,CAAC9C,GAAG,GACzB5H,IAAI,CAACxC,cAAc,CAACkN,IAAI,CAAC,CAAC/C,GAAG,GAAG/O,MAAM;QAC9C;MACJ,CAAC,CAAC;IACN;IACA,MAAMgS,WAAW,GAAGpV,WAAW,CAAC,CAAC;IACjCJ,YAAY,CAACwV,WAAW,EAAE9S,MAAM,EAAEyJ,QAAQ,CAACO,SAAS,CAAC;IACrD,MAAM+I,WAAW,GAAGrV,WAAW,CAAC,CAAC;IACjC,IAAIkP,QAAQ,EAAE;MACVtP,YAAY,CAACyV,WAAW,EAAE7K,IAAI,CAAC0D,cAAc,CAAC+G,cAAc,EAAE,IAAI,CAAC,EAAElJ,QAAQ,CAACE,WAAW,CAAC;IAC9F,CAAC,MACI;MACDrM,YAAY,CAACyV,WAAW,EAAE/S,MAAM,EAAEyJ,QAAQ,CAACO,SAAS,CAAC;IACzD;IACA,MAAM3E,gBAAgB,GAAG,CAACzH,WAAW,CAACkV,WAAW,CAAC;IAClD,IAAIxN,wBAAwB,GAAG,KAAK;IACpC,IAAI,CAAC4C,IAAI,CAAC5B,UAAU,EAAE;MAClB,MAAMiG,cAAc,GAAGrE,IAAI,CAAC6E,0BAA0B,CAAC,CAAC;MACxD;AACZ;AACA;AACA;MACY,IAAIR,cAAc,IAAI,CAACA,cAAc,CAACjG,UAAU,EAAE;QAC9C,MAAM;UAAEmD,QAAQ,EAAEuJ,cAAc;UAAEhT,MAAM,EAAEiT;QAAa,CAAC,GAAG1G,cAAc;QACzE,IAAIyG,cAAc,IAAIC,YAAY,EAAE;UAChC,MAAMC,gBAAgB,GAAGzV,SAAS,CAAC,CAAC;UACpCL,oBAAoB,CAAC8V,gBAAgB,EAAEzJ,QAAQ,CAACO,SAAS,EAAEgJ,cAAc,CAAChJ,SAAS,CAAC;UACpF,MAAMgE,cAAc,GAAGvQ,SAAS,CAAC,CAAC;UAClCL,oBAAoB,CAAC4Q,cAAc,EAAEhO,MAAM,EAAEiT,YAAY,CAACjJ,SAAS,CAAC;UACpE,IAAI,CAACrM,gBAAgB,CAACuV,gBAAgB,EAAElF,cAAc,CAAC,EAAE;YACrD1I,wBAAwB,GAAG,IAAI;UACnC;UACA,IAAIiH,cAAc,CAAC3M,OAAO,CAACyG,UAAU,EAAE;YACnC6B,IAAI,CAACxC,cAAc,GAAGsI,cAAc;YACpC9F,IAAI,CAAC8E,oBAAoB,GAAGkG,gBAAgB;YAC5ChL,IAAI,CAACqE,cAAc,GAAGA,cAAc;UACxC;QACJ;MACJ;IACJ;IACArE,IAAI,CAAChE,eAAe,CAAC,WAAW,EAAE;MAC9BlE,MAAM;MACNyJ,QAAQ;MACRrE,KAAK,EAAE2N,WAAW;MAClBD,WAAW;MACXzN,gBAAgB;MAChBC;IACJ,CAAC,CAAC;EACN,CAAC,MACI,IAAI4C,IAAI,CAACjB,MAAM,CAAC,CAAC,EAAE;IACpB,MAAM;MAAEC;IAAe,CAAC,GAAGgB,IAAI,CAACtI,OAAO;IACvCsH,cAAc,IAAIA,cAAc,CAAC,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACIgB,IAAI,CAACtI,OAAO,CAACgG,UAAU,GAAG7E,SAAS;AACvC;AACA,SAAS+B,mBAAmBA,CAACoF,IAAI,EAAE;EAC/B;AACJ;AACA;EACI,IAAI3M,WAAW,CAACqH,KAAK,EAAE;IACnBpE,OAAO,CAACC,KAAK,EAAE;EACnB;EACA,IAAI,CAACyJ,IAAI,CAAC/H,MAAM,EACZ;EACJ;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI,CAAC+H,IAAI,CAACgF,YAAY,CAAC,CAAC,EAAE;IACtBhF,IAAI,CAAC5G,iBAAiB,GAAG4G,IAAI,CAAC/H,MAAM,CAACmB,iBAAiB;EAC1D;EACA;AACJ;AACA;AACA;AACA;EACI4G,IAAI,CAAC3G,uBAAuB,KAAK2G,IAAI,CAAC3G,uBAAuB,GAAG4I,OAAO,CAACjC,IAAI,CAAC5G,iBAAiB,IAC1F4G,IAAI,CAAC/H,MAAM,CAACmB,iBAAiB,IAC7B4G,IAAI,CAAC/H,MAAM,CAACoB,uBAAuB,CAAC,CAAC;EACzC2G,IAAI,CAAC1G,gBAAgB,KAAK0G,IAAI,CAAC1G,gBAAgB,GAAG0G,IAAI,CAAC/H,MAAM,CAACqB,gBAAgB,CAAC;AACnF;AACA,SAASyB,eAAeA,CAACiF,IAAI,EAAE;EAC3BA,IAAI,CAAC5G,iBAAiB,GAClB4G,IAAI,CAAC3G,uBAAuB,GACxB2G,IAAI,CAAC1G,gBAAgB,GACjB,KAAK;AACrB;AACA,SAAS4H,aAAaA,CAAClB,IAAI,EAAE;EACzBA,IAAI,CAACkB,aAAa,CAAC,CAAC;AACxB;AACA,SAASb,iBAAiBA,CAACL,IAAI,EAAE;EAC7BA,IAAI,CAACK,iBAAiB,CAAC,CAAC;AAC5B;AACA,SAASC,kBAAkBA,CAACN,IAAI,EAAE;EAC9BA,IAAI,CAAC7G,aAAa,GAAG,KAAK;AAC9B;AACA,SAASoH,mBAAmBA,CAACP,IAAI,EAAE;EAC/B,MAAM;IAAE/I;EAAc,CAAC,GAAG+I,IAAI,CAACtI,OAAO;EACtC,IAAIT,aAAa,IAAIA,aAAa,CAAC8G,QAAQ,CAAC,CAAC,CAACkN,qBAAqB,EAAE;IACjEhU,aAAa,CAACqF,MAAM,CAAC,qBAAqB,CAAC;EAC/C;EACA0D,IAAI,CAACxH,cAAc,CAAC,CAAC;AACzB;AACA,SAASsE,eAAeA,CAACkD,IAAI,EAAE;EAC3BA,IAAI,CAAClD,eAAe,CAAC,CAAC;EACtBkD,IAAI,CAACiE,WAAW,GAAGjE,IAAI,CAACxC,cAAc,GAAGwC,IAAI,CAACzC,MAAM,GAAG1E,SAAS;EAChEmH,IAAI,CAAC5G,iBAAiB,GAAG,IAAI;AACjC;AACA,SAASyB,kBAAkBA,CAACmF,IAAI,EAAE;EAC9BA,IAAI,CAACnF,kBAAkB,CAAC,CAAC;AAC7B;AACA,SAASC,cAAcA,CAACkF,IAAI,EAAE;EAC1BA,IAAI,CAAClF,cAAc,CAAC,CAAC;AACzB;AACA,SAAS8E,oBAAoBA,CAACI,IAAI,EAAE;EAChCA,IAAI,CAACJ,oBAAoB,CAAC,CAAC;AAC/B;AACA,SAASuB,mBAAmBA,CAAC/B,KAAK,EAAE;EAChCA,KAAK,CAAC8L,kBAAkB,CAAC,CAAC;AAC9B;AACA,SAASxE,YAAYA,CAACyE,MAAM,EAAEjO,KAAK,EAAEkO,CAAC,EAAE;EACpCD,MAAM,CAACE,SAAS,GAAGpX,SAAS,CAACiJ,KAAK,CAACmO,SAAS,EAAE,CAAC,EAAED,CAAC,CAAC;EACnDD,MAAM,CAACG,KAAK,GAAGrX,SAAS,CAACiJ,KAAK,CAACoO,KAAK,EAAE,CAAC,EAAEF,CAAC,CAAC;EAC3CD,MAAM,CAACxB,MAAM,GAAGzM,KAAK,CAACyM,MAAM;EAC5BwB,MAAM,CAACI,WAAW,GAAGrO,KAAK,CAACqO,WAAW;AAC1C;AACA,SAASC,OAAOA,CAACL,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EAClCD,MAAM,CAACxD,GAAG,GAAG1T,SAAS,CAACwX,IAAI,CAAC9D,GAAG,EAAE+D,EAAE,CAAC/D,GAAG,EAAEyD,CAAC,CAAC;EAC3CD,MAAM,CAACvD,GAAG,GAAG3T,SAAS,CAACwX,IAAI,CAAC7D,GAAG,EAAE8D,EAAE,CAAC9D,GAAG,EAAEwD,CAAC,CAAC;AAC/C;AACA,SAASzE,MAAMA,CAACwE,MAAM,EAAEM,IAAI,EAAEC,EAAE,EAAEN,CAAC,EAAE;EACjCI,OAAO,CAACL,MAAM,CAACrR,CAAC,EAAE2R,IAAI,CAAC3R,CAAC,EAAE4R,EAAE,CAAC5R,CAAC,EAAEsR,CAAC,CAAC;EAClCI,OAAO,CAACL,MAAM,CAACpR,CAAC,EAAE0R,IAAI,CAAC1R,CAAC,EAAE2R,EAAE,CAAC3R,CAAC,EAAEqR,CAAC,CAAC;AACtC;AACA,SAAS/E,mBAAmBA,CAACrG,IAAI,EAAE;EAC/B,OAAQA,IAAI,CAAC4G,eAAe,IAAI5G,IAAI,CAAC4G,eAAe,CAACkD,WAAW,KAAKjR,SAAS;AAClF;AACA,MAAM+E,uBAAuB,GAAG;EAC5B+N,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AACzB,CAAC;AACD,MAAMC,iBAAiB,GAAIC,MAAM,IAAK,OAAOC,SAAS,KAAK,WAAW,IAClEA,SAAS,CAACC,SAAS,IACnBD,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,MAAM,CAAC;AACtD;AACA;AACA;AACA;AACA;AACA,MAAMK,UAAU,GAAGN,iBAAiB,CAAC,cAAc,CAAC,IAAI,CAACA,iBAAiB,CAAC,SAAS,CAAC,GAC/EO,IAAI,CAACC,KAAK,GACVjY,IAAI;AACV,SAASkY,SAASA,CAAC5B,IAAI,EAAE;EACrB;EACAA,IAAI,CAAC/C,GAAG,GAAGwE,UAAU,CAACzB,IAAI,CAAC/C,GAAG,CAAC;EAC/B+C,IAAI,CAAC9C,GAAG,GAAGuE,UAAU,CAACzB,IAAI,CAAC9C,GAAG,CAAC;AACnC;AACA,SAAS5E,QAAQA,CAACG,GAAG,EAAE;EACnBmJ,SAAS,CAACnJ,GAAG,CAACrJ,CAAC,CAAC;EAChBwS,SAAS,CAACnJ,GAAG,CAACpJ,CAAC,CAAC;AACpB;AACA,SAASyN,yBAAyBA,CAACC,aAAa,EAAElG,QAAQ,EAAEzJ,MAAM,EAAE;EAChE,OAAQ2P,aAAa,KAAK,UAAU,IAC/BA,aAAa,KAAK,iBAAiB,IAChC,CAACpS,MAAM,CAACO,WAAW,CAAC2L,QAAQ,CAAC,EAAE3L,WAAW,CAACkC,MAAM,CAAC,EAAE,GAAG,CAAE;AACrE;AACA,SAASyL,sBAAsBA,CAACvD,IAAI,EAAE;EAAA,IAAAuM,YAAA;EAClC,OAAOvM,IAAI,KAAKA,IAAI,CAACvI,IAAI,MAAA8U,YAAA,GAAIvM,IAAI,CAACmC,MAAM,cAAAoK,YAAA,uBAAXA,YAAA,CAAajK,OAAO;AACrD;AAEA,SAASvH,eAAe,EAAE7C,oBAAoB,EAAEsT,OAAO,EAAE9E,YAAY,EAAEC,MAAM,EAAE/L,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}